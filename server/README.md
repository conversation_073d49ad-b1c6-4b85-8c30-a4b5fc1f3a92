# Platform Server

## Onboarding Steps

### 💻 Run in Docker Container OR in Localhost

1. Install Node.js
2. Install Docker Engine and docker compose supporting version 3.8 or higher.
3. Install git (not needed if you have a Mac)
4. Follow Docker framework setup instructions
5. Run Server App:</br>

#### Serve all apps at once inside docker containers:
1. `cd gp-server/server && git pull && npm install && npm run build`
2. `cd gp-docker-framework && git pull`
3. `copy content of gp-server/server/gp.sample.json  to your gp-volume/secrets/gp.json`
4. `cd gp-docker-framework && docker compose down && docker compose build && docker compose up`

#### Serve only the server app in localhost with `nodemon`:
1. Create .env file inside `gp-server` based on .env.sample
2. `cd gp-server/server && git pull && npm install && npm run watch`
3. `gp-server` will be reading the config file from `gp-server/server/secrets/gp.json` which is already setup to access docker services outside their containers.
4. `cd gp-docker-framework && docker compose down && docker compose build && docker compose up mongodb1 storage sendgrid http` Notice we are only running docker services that we need.


#### Review Secret file (gp.json)
##### Local Services (Mocked in Docker)
If a service is running locally, such as when it's mocked in Docker, you typically won't need to modify any configuration or token values. The defaults set in the gp.json file should suffice for running the service locally.

##### Network Services (Sandbox or Test APIs)
If a service is running over the network (e.g., via a sandbox or test APIs), you will likely need to update the respective configuration or token values in the gp.json file.

To gain access to a test accessToken, please reach out to a teammate. They will guide you through the process of obtaining and setting up the required access token for your development environment.

#### Other Notes
- Access sendGrid Email: http://localhost:5005/
- Access GCP Bucket Mock: Navigate to your gp-volume/storage

#### ✏️ MongoDB GUI

To be able to view you DB collections and data, you need a mongoDB GUI client to interact with.</br>

#### We recommend https://studio3t.com/ OR MongoDB Compass.

- Download and install Studio 3T
- Open Studio and create a new connection with:</br>
  server: `localhost`</br>
  Port: `27017`</br>
  user name: `mongoroot`</br>
  password: `password`</br>
  Authentication DB: `admin`</br>

### 📚 Tech Stack

- Nodejs
- Typescript
- Express
- mongoDB
- mongoose
- jest
- eslint
- prettier
- GCP Cloud run
- SendGrid Email
- Stripe


### 🗂️ gp-volume (Local)

- create two folders aka buckets ap-volume/local-cdn and ap-volume/local-temp-video-uploads

- To test emails locally using the email docker mock service:</br>
  Add email assets inside: VOLUME_PATH/storage/BUCKET_NAME/email/</br>
  email assets can be downloaded from GCP.

### Stripe local webhooks

```
# stripe login
# stripe listen --forward-to localhost:5004/api/stripe/webhook
```

The output will display a webhook signing secret beginning with `whsec_`.  Add this to your `secrets.stripe.webhookSecret` and restart the server.

### The event buffer / processor

Events coming in to the system are held in an event_buffer collection when submitted to `POST api/event/buffer (/json or /text)`.  A scheduled task will trigger `POST api/event/processor` where it will digest the `event_buffer`, adding the data to events and metrics.

The processor will remove the `event_buffer` document on successful processing or increase the number of attempts per document on failure. If the failure is caused by a database connection error, the counter will not increase.  After a determined number of failed attempts, the document will be considered invalid and deleted.  This is to avoid the buffer filling endlessly with data in the event there is a large series of invalid data.

When writing events from the buffer into appropriate metric destinations, it's important to use the createdAt date from the `event_buffer` document and not the current date to correctly gather the time when the event happened.
import {
	ClientSession,
	QueryOptions
} from "mongoose";
import path from "path";
import {
	IDBALInput,
	APIErrorName
} from "../../interfaces/apiTypes";
import { IShoppableCollection } from "../../modules/interactiveCollection/interactiveCollection.interface";
import { InteractiveCollectionDBModel } from "../../modules/interactiveCollection/interactiveCollectionDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { isAccessPermitted } from "../../utils/helpers/gp.helper";

const logTrace = path.basename(__filename);

export const readShoppableCollections = async (
	dbInput: IDBALInput
): Promise<IShoppableCollection[]> => {
	try {
		const sort = dbInput.sort
			? dbInput.sort.reduce<Record<string, any>>((sort, criteria) => {
				sort[criteria[0]] = criteria[1];
				return sort;
			}, {})
			: { createdAt: -1 };
		isAccessPermitted(dbInput);
		const documents = await InteractiveCollectionDBModel.find(dbInput.query).sort(sort)
			.session(dbInput.dbSession ?? null);

		return documents;
	} catch (error) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`Failed to read Shoppable Collections: ${error instanceof Error ? error.message : "Unknown error"}`
		);
	}
};

export const updateShoppableCollection = async (
	query: any,
	update: any,
	session: ClientSession | null
): Promise<IShoppableCollection | null> => {
	try {
		const options: QueryOptions<IShoppableCollection> = {
			new: true,
			upsert: false
		};

		const document: IShoppableCollection | null = await InteractiveCollectionDBModel.findOneAndUpdate(
			query,
			update,
			options
		).session(session);
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > updateShoppableCollection | Failed to update shoppableCollection. | ` +
			`${error.name} | ${error.message}`
		);
	}
};

export const deleteShoppableCollection = async (
	query: any, session: ClientSession | null
): Promise<IShoppableCollection | null> => {
	try {
		const options: QueryOptions<IShoppableCollection> = {};
		const document: IShoppableCollection | null = await InteractiveCollectionDBModel.findOneAndDelete(
			query, options
		).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<shoppableCollection.service> deleteShoppableCollection| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

// read un-redacted collection data as it is used by processUserEngagement
export const readShoppableCollection = async (
	query: any,
	session: ClientSession | null
): Promise<IShoppableCollection | null> => {
	try {
		const document: IShoppableCollection | null = await InteractiveCollectionDBModel.findOne(query).session(
			session
		);
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > readShoppableCollection | Failed to read shoppableCollection. ` +
			`| ${error.name} | ${error.message}`
		);
	}
};

export const aggregateShoppableCollection = async (
	query: any[],
	session: ClientSession | null
): Promise<any | null> => {
	try {
		const documents: any | null = await InteractiveCollectionDBModel.aggregate(
			query
		).session(session);
		return documents;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > aggregateShoppableCollection | ` +
			`Failed to aggregate shoppable collection. | ${error.name} | ${error.message}`
		);
	}
};

import * as path from "path";
import ejs from "ejs";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import { LocaleAPI } from "../../interfaces/apiTypes";
import {
	sendEmail,
	ISendEmail
} from "./sendEmail";
import { validateBackslash } from "../../utils/helpers/gp.helper";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

export interface EmailInput {
	template: string;
	to: string | string[];
	subject: string;
	data: any;
	locale: LocaleAPI;
}

const sendGridPath = "v3/mail/send";

export const sendTransactionalEmail = async ({
	template,
	to = "",
	subject = "",
	data,
	locale = LocaleAPI.EN_US
}: EmailInput): Promise<boolean | undefined> => {
	try {
		const secrets: ISecrets = await getSecrets();
		const emailData = {
			...data,
			...{
				template: template,
				email: to,
				host: validate<PERSON><PERSON><PERSON><PERSON>(secrets.cdn.host),
				showLegalLinks: "false",
				showSocialLinks: "false",
				privacyPolicyUrl: process.env.PRIVACY_POLICY_URL_EN,
				termsOfServiceUrl: process.env.TERMS_OF_SERVICE_URL_EN
			}
		};
		const html = await ejs.renderFile(
			path.resolve(
				__dirname,
				`../../assets/templates/email/${locale.trim().toLowerCase()}/layout.ejs`
			),
			emailData
		);
		// Data structure can be updated based on SendGrid API documentation
		//
		// https://sendgrid.com/docs/API_Reference/api_v3.html
		const msgData = {
			personalizations: [
				{
					to: [
						{
							email: to
						}
					],
					subject: subject
				}
			],
			from: {
				email: secrets.sendGrid.fromAddress
			},
			content: [
				{
					type: "text/html",
					value: html
				}
			],
			headers: {
				// "List-Unsubscribe": "<mailto:<EMAIL>>, <https://yourdomain.com/unsubscribe>",
				// "List-Unsubscribe-Post": "List-Unsubscribe=One-Click"
			}
		};

		const sendEmailInput: ISendEmail = {
			host: validateBackslash(secrets.sendGrid.host),
			path: sendGridPath,
			apiKey: secrets.sendGrid.apiKey,
			data: msgData
		};

		return await sendEmail(sendEmailInput);
	} catch (error: any) {
		gpLog({
			message: "Failed to contract the Email",
			objData: {
				error: error.message
			},
			trace: "email.service | sendTransactionalEmail",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};



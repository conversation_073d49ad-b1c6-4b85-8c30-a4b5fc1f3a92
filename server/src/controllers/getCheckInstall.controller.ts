import {
	Request,
	Response
} from "express";
import {
	parse,
	HTMLElement
} from "node-html-parser";
import { readAccount } from "../services/mongodb/account.service";
import {
	APIErrorName,
	Permission
} from "../interfaces/apiTypes";
import {
	getSecrets,
	ISecrets
} from "../modules/secrets/secrets.model";
import { APIError } from "../utils/helpers/apiError";
import { validateBackslash } from "../utils/helpers/gp.helper";

export const getCheckInstallController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		const accountDocument = await readAccount({
			query: {
				_id: req.accountToken.account._id
			},
			path: req.url,
			permissions: [
				Permission.ALL
			]
		});

		if (!accountDocument || accountDocument === null) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR,
				"Failed to read the account document with _id " +
				`${req.accountToken.account._id} supplied in access token`);
		}

		try {
			new URL(accountDocument.companyURL);
		} catch (err) {
			throw new APIError(APIErrorName.E_INVALID_INPUT,
				`the company url in the account is not a valid url: ${accountDocument.companyURL}`);
		}

		const response = await fetch(accountDocument.companyURL, {
			method: "GET"
		});

		if (response.status === 404) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
				`the document in the account companyURL at ${accountDocument.companyURL} could not be retrieved`);
		}

		const root = parse(await response.text());

		const scriptTags: HTMLElement[] = root.querySelectorAll("script");

		const secrets: ISecrets = await getSecrets();

		const scriptSource = `${validateBackslash(secrets.player.host)}core.js`;

		const tag: HTMLElement | undefined = scriptTags.find(
			(s) => s.attributes.src === scriptSource && Object.prototype.hasOwnProperty.call(s.attributes, "defer"));

		return res.json({
			"installed": tag ? true : false
		});
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

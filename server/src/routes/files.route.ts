import express from "express";
import { deleteFilesController } from "../modules/files/deleteFiles.controller";
import { putFilesController } from "../modules/files/putFiles.controller";
import { postFilesController } from "../modules/files/postFiles.controller";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { validateHeaders } from "../middleware/validateHeaders.mw";

const router = express.Router();

router.post("/", [decodeAccess], postFilesController);

router.put(
	"/",
	[express.raw({ limit: "8MB", type: ["*/*"] }), decodeAccess],
	putFilesController
);

router.delete(
	"/:filename",
	[express.json({ limit: "2MB" }), decodeAccess, validateHeaders],
	deleteFilesController
);

export default router;

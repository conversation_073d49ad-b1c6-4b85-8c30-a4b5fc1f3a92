import express from "express";
import { patchUserController } from "../modules/user/patchUser.controller";
import { getUsersController } from "../modules/user/getUsers.controller";
import { getUserController } from "../modules/user/getUser.controller";
import multer from "multer";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { isSchemaValid } from "../middleware/isSchemaValid.mw";
import { patchUserSchema } from "../middleware/users/patchUser.mw";

const router = express.Router();

router.get("/", [decodeAccess], getUsersController);

router.get(
	"/:userId",
	[express.json({ limit: "2MB" }), decodeAccess],
	getUserController
);

router.patch(
	"/:userId",
	[multer().none(), decodeAccess, isSchemaValid(patchUserSchema.data)],
	patchUserController
);

export default router;

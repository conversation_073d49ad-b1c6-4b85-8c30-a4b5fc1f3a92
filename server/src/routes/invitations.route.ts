import express from "express";
import { putInvitationController } from "../modules/invitation/putInvitation.controller";
import { postInvitationController } from "../modules/invitation/postInvitation.controller";
import { getInvitationsSelfController } from "../modules/invitation/getInvitationsSelf.controller";
import { getInvitationsController } from "../modules/invitation/getInvitations.controller";
import { deleteInvitationController } from "../modules/invitation/deleteInvitation.controller";

import multer from "multer";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { requireSuperWritePermission } from "../middleware/requireSuperWritePermission.mw";

const router = express.Router();

router.get("/", [decodeAccess], getInvitationsController);
router.get("/self", [decodeAccess], getInvitationsSelfController);
router.post("/", [multer().any(), decodeAccess, requireSuperWritePermission], postInvitationController);
router.put("/:invitationId", [multer().any(), decodeAccess], putInvitationController);
router.delete("/:invitationId", [
	multer().any(), decodeAccess, requireSuperWritePermission
], deleteInvitationController);

export default router;

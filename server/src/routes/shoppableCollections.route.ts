import express from "express";
import { getCollectionController } from "../modules/interactiveCollection/getCollection.controller";
import {
	deleteShoppableCollectionController
} from "../modules/interactiveCollection/deleteShoppableCollection.controller";
import { putShoppableCollectionController } from "../modules/interactiveCollection/putShoppableCollection.controller";
import { postCollectionController } from "../modules/interactiveCollection/postCollection.controller";
import { getCollectionsController } from "../modules/interactiveCollection/getCollections.controller";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { requireSuperWritePermission } from "../middleware/requireSuperWritePermission.mw";
import { isSchemaValid } from "../middleware/isSchemaValid.mw";
import {
	postCollectionSchema,
	getCollectionsSchema,
	putShoppableCollectionSchema
} from "../middleware/shoppableCollections.mw";

const router = express.Router();

router.post(
	"/",
	[
		express.json({ limit: "2MB" }),
		decodeAccess,
		requireSuperWritePermission,
		isSchemaValid(postCollectionSchema.data)
	],
	postCollectionController
);

router.get(
	"/",
	[
		express.json({ limit: "2MB" }),
		decodeAccess,
		isSchemaValid(getCollectionsSchema.data)
	],
	getCollectionsController
);

router.delete(
	"/:collectionId", decodeAccess, requireSuperWritePermission, deleteShoppableCollectionController
);

router.put(
	"/:collectionId",
	[express.json({ limit: "2MB" }),
		decodeAccess, requireSuperWritePermission, isSchemaValid(putShoppableCollectionSchema.data)],
	putShoppableCollectionController
);

router.get(
	"/:collectionId",
	[express.json({ limit: "2MB" }), decodeAccess],
	getCollectionController
);

export default router;

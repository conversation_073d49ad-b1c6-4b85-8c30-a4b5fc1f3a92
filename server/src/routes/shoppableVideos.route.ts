import express from "express";
import multer from "multer";
import { fetchProductController } from "../modules/interactiveVideo/fetchProduct.controller";
import { putShoppableVideoController } from "../modules/interactiveVideo/putInteractiveVideo.controller";
import { postVideoController } from "../modules/interactiveVideo/postInteractiveVideo.controller";
import { getVideosController } from "../modules/interactiveVideo/getVideos.controller";
import { getVideoController } from "../modules/interactiveVideo/getVideo.controller";
import { deleteVideoController } from "../modules/interactiveVideo/deleteVideo.controller";
import { InteractiveVideoLikeController } from "../modules/interactiveVideo/like/interactiveVideo.like.controller";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { isSchemaValid } from "../middleware/isSchemaValid.mw";
import { requireSuperWritePermission } from "../middleware/requireSuperWritePermission.mw";
import { postInteractiveVideoSchema } from "../modules/interactiveVideo/postInteractiveVideo.schema";
import { putInteractiveVideoSchema } from "../modules/interactiveVideo/putInteractiveVideo.schema";
import { getShoppableVideoSchema } from "../middleware/shoppableVideos.mw";
import { validateHeaders } from "../middleware/validateHeaders.mw";

const router = express.Router();
const interactiveVideoLikeController = new InteractiveVideoLikeController();

router.post(
	"/",
	[multer().any(), decodeAccess, requireSuperWritePermission, validateHeaders,
		isSchemaValid(postInteractiveVideoSchema.data)],
	postVideoController
);

router.put(
	"/:videoId",
	[multer().any(), decodeAccess,
		requireSuperWritePermission, validateHeaders, isSchemaValid(putInteractiveVideoSchema.data)],
	putShoppableVideoController
);

router.get(
	"/fetch-product",
	[decodeAccess],
	fetchProductController
);

router.get(
	"/",
	[express.json({ limit: "2MB" }), decodeAccess, isSchemaValid(getShoppableVideoSchema.data)],
	getVideosController
);

router.get(
	"/:videoId",
	[express.json({ limit: "2MB" }), decodeAccess],
	getVideoController
);

router.delete(
	"/:videoId",
	[express.json({ limit: "2MB" }), decodeAccess, requireSuperWritePermission],
	deleteVideoController
);

router.post(
	"/:videoId/like",
	[express.json({ limit: "2MB" })],
	interactiveVideoLikeController.post
);

export default router;

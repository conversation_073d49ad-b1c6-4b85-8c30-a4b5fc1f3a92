import { main } from "./index";
import dotenv from "dotenv";
import i18n from "i18n";
import * as MongoDB from "./mongo";
import * as ExpressServer from "./express";

jest.mock("dotenv", () => ({ config: jest.fn() }));
jest.mock("i18n", () => ({ configure: jest.fn() }));
jest.mock("./mongo", () => ({ initMongoDB: jest.fn().mockResolvedValue(true), formatMongoURIFromSecrets: jest.fn() }));
jest.mock("./express", () => ({
	initExpressServer: jest.fn().mockResolvedValue({ use: jest.fn() }),
	initExpressRoutes: jest.fn()
}));

describe("Black Box Testing | Server entry point index().", () => {
	const originalEnv = { ...process.env };
	let exitSpy: jest.SpyInstance;

	beforeAll(() => {
		exitSpy = jest.spyOn(process, "exit").mockImplementation((code?: string | number | null) => {
			throw new Error(`process.exit called with code: ${code}`);
		});
	});

	beforeEach(() => {
		jest.clearAllMocks();
		process.env = { ...originalEnv };
	});

	afterAll(() => {
		exitSpy.mockRestore();
		jest.restoreAllMocks();
		process.env = originalEnv;
	});

	it("Should call main() successfully.", async () => {
		await main();
		expect(dotenv.config).toHaveBeenCalled();
		expect(i18n.configure).toHaveBeenCalledWith(expect.objectContaining({ locales: ["en", "fr"] }));
		expect(MongoDB.initMongoDB).toHaveBeenCalled();
		expect(ExpressServer.initExpressServer).toHaveBeenCalledWith(3000);
		expect(exitSpy).not.toHaveBeenCalled();
	});

	it("Should fail to initialize the server entry point main(), then exit(1)", async () => {
		process.env.jobId = "some-invalid-job-id";
		await expect(main()).rejects.toThrow(/process\.exit called with code: 1/);
		expect(exitSpy).toHaveBeenCalledWith(1);
	});
});

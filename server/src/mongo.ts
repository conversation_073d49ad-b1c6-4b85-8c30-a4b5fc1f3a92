import mongoose, { ConnectOptions } from "mongoose";
import { UserModel } from "./modules/user/user.model";
import {
	getSecrets,
	ISecrets
} from "./modules/secrets/secrets.model";



export const initMongoDB = async (mongoURI: string): Promise<void> => {
	const secrets = await getSecrets();

	const mongoOptions: ConnectOptions = {
		socketTimeoutMS: 30000,
		keepAlive: true,
		autoIndex: false,
		retryWrites: false,
		dbName: secrets.mongo.dbName
	};

	return new Promise((resolve, reject) => {
		mongoose.set("strictQuery", false);
		mongoose
			.connect(mongoURI, mongoOptions)
			.then(async () => {
				await UserModel.createIndexes();
				resolve();
			})
			.catch((error) => {
				reject(error);
			});
	});
};

export const formatMongoURIFromSecrets = (secrets: ISecrets): string => {
	return `${secrets.mongo.mongoProtocol}://${secrets.mongo.mongoUsername}:` +
		`${secrets.mongo.mongoPassword}@${secrets.mongo.mongoHost}`;
};

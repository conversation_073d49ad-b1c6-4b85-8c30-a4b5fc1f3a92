<!DOCTYPE html>
<html>

<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>Simple Transactional Email</title>
  <style>
    /* -------------------------------------
          GLOBAL RESETS
      ------------------------------------- */

    /*All the styling goes here*/

    img {
      border: none;
      -ms-interpolation-mode: bicubic;
      max-width: 100%;
    }

    body {
      font-family: sans-serif;
      -webkit-font-smoothing: antialiased;
      font-size: 14px;
      line-height: 1.4;
      margin: 0;
      padding: 0;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
    }

    table {
      border-collapse: separate;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
      width: 100%;
    }

    table td {
      font-family: sans-serif;
      font-size: 14px;
      vertical-align: top;
    }

    /* -------------------------------------
          BODY & CONTAINER
      ------------------------------------- */

    .body {
      width: 100%;
    }

    body>table>thead>tr>td:first-child img {
      max-width: 300px;
    }

    /* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
    .container {
      display: block;
      margin: 0 auto !important;
      /* makes it centered */
      max-width: 580px;
      padding: 10px;
    }

    .outer-container {
      max-width: 580px;
      margin: 0 auto;
    }

    /* This should also be a block element, so that it will fill 100% of the .container */
    .content {
      box-sizing: border-box;
      display: block;
      margin: 0 auto;
      max-width: 580px;
      padding: 10px;
    }

    .content a {
      color: #000000;
      font-weight: bold;
    }

    /* -------------------------------------
          HEADER, FOOTER, MAIN
      ------------------------------------- */
    .main {
      background: #f7f7f7;
      border-radius: 10px;
      width: 100%;
    }

    .wrapper {
      box-sizing: border-box;
      padding: 20px;
    }

    .content-block {
      padding-bottom: 10px;
      padding-top: 10px;
    }

    .footer {
      clear: both;
      margin-top: 10px;
      width: 100%;
    }

    .footer td,
    .footer p,
    .footer span,
    .footer a {
      color: #999999;
      font-size: 12px;
    }

    /* -------------------------------------
          TYPOGRAPHY
      ------------------------------------- */
    h1,
    h2,
    h3,
    h4 {
      color: #000000;
      font-family: sans-serif;
      font-weight: 400;
      line-height: 1.4;
      margin: 0;
      margin-bottom: 30px;
    }

    h1 {
      font-size: 23px;
      font-weight: 300;
    }

    p,
    ul,
    ol {
      font-family: sans-serif;
      font-size: 14px;
      font-weight: normal;
      margin: 0;
      margin-bottom: 15px;
    }

    p li,
    ul li,
    ol li {
      list-style-position: inside;
      margin-left: 5px;
    }

    a {
      color: #3498db;
      text-decoration: underline;
    }

    /* -------------------------------------
          BUTTONS
      ------------------------------------- */
    .btn {
      box-sizing: border-box;
      width: 100%;
    }

    .btn>tbody>tr>td {
      padding-bottom: 15px;
    }

    .btn table {
      width: auto;
    }

    .btn table td {
      background-color: #ffffff;
      border-radius: 5px;
      text-align: center;
    }

    .btn a {
      background-color: #ffffff;
      border: solid 1px #3498db;
      border-radius: 5px;
      box-sizing: border-box;
      color: #3498db;
      cursor: pointer;
      display: inline-block;
      font-size: 14px;
      font-weight: bold;
      margin: 0;
      padding: 12px 25px;
      text-decoration: none;
      text-transform: capitalize;
    }

    .btn-primary table td {
      background-color: #3498db;
    }

    .btn-primary a {
      background-color: #3498db;
      border-color: #3498db;
      color: #ffffff;
    }

    /* -------------------------------------
          OTHER STYLES THAT MIGHT BE USEFUL
      ------------------------------------- */
    .last {
      margin-bottom: 0;
    }

    .first {
      margin-top: 0;
    }

    .align-center {
      text-align: center;
    }

    .align-right {
      text-align: right;
    }

    .align-left {
      text-align: left;
    }

    .clear {
      clear: both;
    }

    .mt0 {
      margin-top: 0;
    }

    .mb0 {
      margin-bottom: 0;
    }

    .preheader {
      color: transparent;
      display: none;
      height: 0;
      max-height: 0;
      max-width: 0;
      opacity: 0;
      overflow: hidden;
      mso-hide: all;
      visibility: hidden;
      width: 0;
    }

    .powered-by a {
      text-decoration: none;
    }

    hr {
      border: 0;
      border-bottom: 1px solid #f6f6f6;
      margin: 20px 0;
    }

    .header td {
      vertical-align: middle;
    }

    .social-icons {
      text-align: right;
    }

    .logo {
      max-width: 250px;
      height: auto;
      padding: 10px;
    }

    .social-icons>a {
      display: inline-block;
      width: 24px;
      height: auto;
      margin-left: 15px;
    }

    .footer {
      padding: 20px 0;
    }

    .footer a,
    .footer p,
    .footer span {
      color: #a7a7a7;
    }

    .footer a {
      font-weight: 600;
    }

    .margin-left-10 {
      margin-left: 10px;
    }

    .italic {
      font-style: italic;
    }

    .social-icons-footer {
      text-align: center;
      line-height: 56px;
    }

    .social-icons-footer-mobile {
      text-align: center;
      line-height: 56px;
      display: none;
    }

    .social-icons-footer a {
      width: 35px;
      height: auto;
      margin: 5px;
      display: inline-block;
    }

    .button {
      text-decoration: none;
      cursor: pointer;
      color: #fff;
      background-color: #0033FF;
      display: block;
      width: fit-content;
      margin: 26px 0;
      border-radius: 30px;
      font-size: 16px;
      font-weight: 600;
      border-top: 13px solid #0033FF;
      border-bottom: 13px solid #0033FF;
      border-left: 26px solid #0033FF;
      border-right: 26px solid #0033FF;
    }

    .gp-button {
      color: #fff !important;
    }

    .gp-text {
      color: rgb(68, 67, 67) !important;
    }

    h1 {
      font-weight: 700;
    }

    /* -------------------------------------
          RESPONSIVE AND MOBILE FRIENDLY STYLES
      ------------------------------------- */

    @media only screen and (max-width: 975px) {
      .social-icons-footer {
        display: none !important;
      }
      .social-icons-footer-mobile {
        display: table-row !important;
      }
      .social-icons-footer-mobile a {
        margin: 0 0.5rem;
        text-decoration: none;
      }
    }
    @media only screen and (max-width: 620px) {
      table.body h1 {
        font-size: 23px !important;
        margin-bottom: 10px !important;
      }

      table.body p,
      table.body ul,
      table.body ol,
      table.body td,
      table.body span,
      table.body a {
        font-size: 16px !important;
      }

      table.body .wrapper,
      table.body .article {
        padding: 10px !important;
      }

      table.body .content {
        padding: 0 !important;
      }

      table.body .container {
        padding: 0 !important;
        width: 100% !important;
      }

      table.body .main {
        border-left-width: 0 !important;
        border-radius: 0 !important;
        border-right-width: 0 !important;
      }

      table.body .btn table {
        width: 100% !important;
      }

      table.body .btn a {
        width: 100% !important;
      }

      table.body .img-responsive {
        height: auto !important;
        max-width: 100% !important;
        width: auto !important;
      }
    }

    /* -------------------------------------
          PRESERVE THESE STYLES IN THE HEAD
      ------------------------------------- */
    @media all {
      .ExternalClass {
        width: 100%;
      }

      .ExternalClass,
      .ExternalClass p,
      .ExternalClass span,
      .ExternalClass font,
      .ExternalClass td,
      .ExternalClass div {
        line-height: 100%;
      }

      .apple-link a {
        color: inherit !important;
        font-family: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
        text-decoration: none !important;
      }

      #MessageViewBody a {
        color: inherit;
        text-decoration: none;
        font-size: inherit;
        font-family: inherit;
        font-weight: inherit;
        line-height: inherit;
      }

      .btn-primary table td:hover {
        background-color: #34495e !important;
      }

      .btn-primary a:hover {
        background-color: #34495e !important;
        border-color: #34495e !important;
      }
    }
  </style>
</head>

<body>
  <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body">
    <tr>
      <td colspan="3" class="header">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="outer-container">
          <tr>
            <td>
              <img alt="logo" class="logo" width="100" src="<%=host%>email/logo-black.png" />
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td class="container">
        <div class="content">
          <!-- START CENTERED WHITE CONTAINER -->
          <table role="presentation" class="main">
            <!-- START MAIN CONTENT AREA -->
            <tr>
              <td class="wrapper">
                <div>
                  <%- include('partials/'+template) %>
                </div>
              </td>
            </tr>

            <!-- END MAIN CONTENT AREA -->
          </table>
          <!-- END CENTERED WHITE CONTAINER -->
        </div>
      </td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td colspan="3" class="footer">
        <!-- START FOOTER -->
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="outer-container">
          <tr>
            <td colspan="3" class="header">
              <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="outer-container">
                <tr>
                  <td>
                    <img alt="logo" class="logo" width="100" src="<%=host%>email/logo-grey.png" />
                  </td>
                </tr>
              </table>
            </td>
            <% if (showSocialLinks === 'true') { %>
              <td alignment="right" class="social-icons-footer">
                <a href="https://www.instagram.com/" target="_blank">
                  <img alt="Instagram" width="24" src="<%=host%>email/instagram-icon-grey.png" />
                </a>
                <a href="https://www.tiktok.com/" target="_blank">
                  <img alt="TikTok" width="24" src="<%=host%>email/tiktok-icon-grey.png" />
                </a>
                <a href="https://twitter.com" target="_blank">
                  <img alt="Twitter" width="24" src="<%=host%>email/x-icon-grey.png" />
                </a>
                <a href="https://www.youtube.com/" target="_blank">
                  <img alt="YouTube" width="24" src="<%=host%>email/youtube-icon-grey.png" />
                </a>
                <a href="https://www.facebook.com/" target="_blank">
                  <img alt="Facebook" width="24" src="<%=host%>email/facebook-icon-grey.png" />
                </a>
              </td>
            <% } %>
          </tr>
          <% if (showSocialLinks === 'true') { %>
            <tr>
              <td alignment="right" class="social-icons-footer-mobile">
                <a href="https://www.instagram.com/" target="_blank">
                  <img alt="Instagram" width="24" src="<%=host%>email/instagram-icon-grey.png" />
                </a>
                <a href="https://www.tiktok.com/" target="_blank">
                  <img alt="TikTok" width="24" src="<%=host%>email/tiktok-icon-grey.png" />
                </a>
                <a href="https://twitter.com/" target="_blank">
                  <img alt="Twitter" width="24" src="<%=host%>email/x-icon-grey.png" />
                </a>
                <a href="https://www.youtube.com/" target="_blank">
                  <img alt="YouTube" width="24" src="<%=host%>email/youtube-icon-grey.png" />
                </a>
                <a href="https://www.facebook.com/" target="_blank">
                  <img alt="Facebook" width="24" src="<%=host%>email/facebook-icon-grey.png" />
                </a>
              </td>
            </tr>
          <% } %>
          <tr>
            <td alignment="center">
              <% if (showLegalLinks === 'true') { %>
                <p class="margin-left-10">
                  <a href="<%=privacyPolicyUrl%>"
                    target="_blank">Privacy Policy</a>
                  &nbsp;|&nbsp;
                  <a href="<%=termsOfServiceUrl%>" target="_blank">Terms
                    and Conditions</a>
                </p>
              <% } %>
            </td>
          </tr>
          <tr>
            <td alignment="center">
              <p class="margin-left-10">&copy; 2025</p>
            </td>
          </tr>
        </table>
        <!-- END FOOTER -->
      </td>
    </tr>
  </table>
</body>

</html>
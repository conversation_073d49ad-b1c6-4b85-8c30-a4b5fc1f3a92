<div class="section-title">Your Videos</div>

<table class="video-table">
    <thead>
        <tr>
            <th>POSITION</th>
            <th>TITLE</th>
            <th>LENGTH</th>
            <th>PLAYS</th>
            <th>CLICKS</th>
            <th>EMAILS</th>
            <th>CALLS</th>
            <th>PLAYTIME</th>
            <th>SCORE</th>
        </tr>
    </thead>
    <tbody>
        <% (videos || []).forEach(function(video) { %>
        <tr>
            <td>
                <span class="video-position"><%= video.position %></span>
                <% if (video.change === 'up') { %>
                    <span class="video-position-change up">↑ <%= video.changeValue %></span>
                <% } else if (video.change === 'down') { %>
                    <span class="video-position-change down">↓ <%= video.changeValue %></span>
                <% } else { %>
                    <span class="video-position-change same">-</span>
                <% } %>
            </td>
            <td>
                <div class="video-info">
                    <div class="video-thumbnail"></div>
                    <div class="video-title"><%= video.title %></div>
                </div>
            </td>
            <td><%= video.length %></td>
            <td><%= video.plays %></td>
            <td><%= video.clicks %></td>
            <td><%= video.emails %></td>
            <td><%= video.calls %></td>
            <td><%= video.playtime %></td>
            <td><%= video.score %></td>
        </tr>
        <% }); %>
    </tbody>
</table>

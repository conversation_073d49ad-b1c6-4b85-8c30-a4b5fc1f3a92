<div id="metricsSection" class="section-title">Metrics</div>

<div class="additional-metrics">
    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">ENGAGEMENT RATE</span>
            <span class="additional-metric-change <%= (metricsData?.engagementRate?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metricsData?.engagementRate?.change || 0) >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData?.engagementRate?.change || 0) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.engagementRate?.value || '0%' %></div>
        <div class="additional-metric-description">IMPRESSIONS / ENGAGED SESSIONS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAY RATE</span>
            <span class="additional-metric-change <%= (metricsData?.playRate?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metricsData?.playRate?.change || 0) >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData?.playRate?.change || 0) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.playRate?.value || '0%' %></div>
        <div class="additional-metric-description">IMPRESSIONS / PLAYS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">CLICKTHROUGH RATE</span>
            <span class="additional-metric-change <%= (metricsData?.clickthroughRate?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metricsData?.clickthroughRate?.change || 0) >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData?.clickthroughRate?.change || 0) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.clickthroughRate?.value || '0%' %></div>
        <div class="additional-metric-description">PLAYS / CLICKS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">LEAD COUNT</span>
            <span class="additional-metric-change positive">↑<%= metricsData?.leadCount?.change || 0 %></span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.leadCount?.value || '0' %></div>
        <div class="additional-metric-description">LEADS TO DATE</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAYS PER SESSION</span>
            <span class="additional-metric-change <%= (metricsData?.playsPerSession?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metricsData?.playsPerSession?.change || 0) >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData?.playsPerSession?.change || 0) %>
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.playsPerSession?.value || '0' %></div>
        <div class="additional-metric-description">PLAYS / ENGAGED SESSION</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">AVG PLAYTIME</span>
            <span class="additional-metric-change <%= (metricsData?.avgPlaytime?.change || '').includes('-') ? 'negative' : 'positive' %>">
                <%= (metricsData?.avgPlaytime?.change || '').includes('-') ? '↓' : '↑' %><%= metricsData?.avgPlaytime?.change || '0m 0s' %>
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData?.avgPlaytime?.value || '0m 0s' %></div>
        <div class="additional-metric-description">PLAYTIME / ENGAGED SESSION</div>
    </div>
</div>

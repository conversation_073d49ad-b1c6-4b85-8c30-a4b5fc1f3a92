import {
	Request,
	Response
} from "express";
import {
	ILog,
	LogScope,
	gpLog
} from "../managers/gpLog.manager";
import { APIErrorName } from "../../interfaces/apiTypes";
import { ModelResponse } from "../../modules/modelResponse/modelResponse.model";

export class APIError extends Error {
	private readonly errorRef: string;
	private request: Request | null;
	private detail: string | null = null;
	private _suppressLog: boolean;
	private instanceOf: string;

	private getStatusCode (): number {
		switch (this.name) {
			case APIErrorName.E_INVALID_INPUT:
			case APIErrorName.E_SIGN_UP_INVALID_FORMAT:
			case APIErrorName.E_SIGN_UP_GENERIC:
			case APIErrorName.E_EMAIL_SIGN_IN_INVALID_FORMAT:
			case APIErrorName.E_EMAIL_SIGN_IN_GENERIC:
			case APIErrorName.E_SIGN_IN_INCORRECT:
			case APIErrorName.E_EMAIL_SIGN_INVALID_FORMAT:
			case APIErrorName.E_SIGN_IN_GENERIC:
			case APIErrorName.E_FORGOT_PASSWORD_NO_EMAIL:
			case APIErrorName.E_FORGOT_PASSWORD_GENERIC:
			case APIErrorName.E_VIDEO_LIBRARY_FILE_TOO_LARGE:
			case APIErrorName.E_VIDEO_LIBRARY_WRONG_FILE_TYPE:
			case APIErrorName.E_VIDEO_LIBRARY_VIDEO_NOT_PROCESSED:
			case APIErrorName.E_VIDEO_LIBRARY_CANT_LOAD_VIDEOS:
			case APIErrorName.E_VIDEO_LIBRARY_GENERIC:
			case APIErrorName.E_VIDEO_MANAGER_CANT_LOAD_VIDEOS:
			case APIErrorName.E_VIDEO_MANAGER_CANT_REORDER:
			case APIErrorName.E_VIDEO_MANAGER_GENERIC:
			case APIErrorName.E_VIDEO_DETAIL_NO_TITLE:
			case APIErrorName.E_VIDEO_DETAIL_NO_VIDEO:
			case APIErrorName.E_VIDEO_DETAIL_NO_TITLE_LINK:
			case APIErrorName.E_VIDEO_DETAIL_NO_LINK_IMAGE:
			case APIErrorName.E_VIDEO_DETAIL_FILE_SIZE:
			case APIErrorName.E_VIDEO_DETAIL_WRONG_FORMAT:
			case APIErrorName.E_VIDEO_DETAIL_CANT_PROCESS:
			case APIErrorName.E_VIDEO_DETAIL_CANT_SAVE:
			case APIErrorName.E_VIDEO_DETAIL_GENERIC:
			case APIErrorName.E_COLLECTIONS_CANT_LOAD:
			case APIErrorName.E_COLLECTIONS_CANT_DELETE:
			case APIErrorName.E_COLLECTIONS_GENERIC:
			case APIErrorName.E_COLLECTION_DETAIL_CANT_LOAD:
			case APIErrorName.E_COLLECTION_DETAIL_CANT_REORDER:
			case APIErrorName.E_COLLECTION_DETAIL_CANT_SAVE:
			case APIErrorName.E_COLLECTION_DETAIL_CANT_ADD_VIDEOS:
			case APIErrorName.E_COLLECTION_DETAIL_GENERIC:
			case APIErrorName.E_PROFILE_CANT_LOAD:
			case APIErrorName.E_PROFILE_CANT_SAVE:
			case APIErrorName.E_PROFILE_GENERIC:
			case APIErrorName.E_PROFILE_CANT_SEND_RESET:
			case APIErrorName.E_SETTINGS_CANT_LOAD:
			case APIErrorName.E_SETTINGS_CANT_SAVE:
			case APIErrorName.E_SETTINGS_GENERIC:
			case APIErrorName.E_SETTINGS_CANT_INVITE_USER:
			case APIErrorName.E_SETTINGS_INVALID_INVITE_EMAIL:
			case APIErrorName.E_SETTINGS_CANT_DELETE_USER:
			case APIErrorName.E_SETTINGS_WRONG_FILE_TYPE:
			case APIErrorName.E_PERFORMANCE_CANT_LOAD:
			case APIErrorName.E_PERFORMANCE_CANT_REFRESH:
			case APIErrorName.E_PERFORMANCE_NO_AVAILABLE_DATA:
			case APIErrorName.E_PERFORMANCE_TIMEOUT:
			case APIErrorName.E_PERFORMANCE_GENERIC:
			case APIErrorName.E_GENERAL_ERROR_CANT_LOAD:
			case APIErrorName.E_GENERAL_ERROR_GENERIC:
			case APIErrorName.E_GENERAL_ERROR_SIGNED_OUT:
			case APIErrorName.E_PASSWORD_COMPLEXITY:
			case APIErrorName.E_MISSING_OIDC_DATA:
			case APIErrorName.E_SCHEMA_VALIDATION_ERROR:
				return 400;
			case APIErrorName.E_NOT_AUTHENTICATED:
			case APIErrorName.E_MISSING_AUTHORIZATION:
			case APIErrorName.E_INVALID_AUTHORIZATION:
			case APIErrorName.E_INVALID_STRIPE_SIGNATURE:
				return 401;
			case APIErrorName.E_ACCESS_FORBIDDEN:
			case APIErrorName.E_AUTH_METHOD_NOT_SUPPORTED:
			case APIErrorName.E_USER_NOT_VERIFIED:
			case APIErrorName.E_REQUEST_FORBIDDEN:
			case APIErrorName.E_USER_LIMIT_REACHED:
				return 403;
			case APIErrorName.E_EMAIL_SIGN_IN_NO_EMAIL:
			case APIErrorName.E_DOCUMENT_NOT_FOUND:
			case APIErrorName.E_COLLECTION_NOT_FOUND:
			case APIErrorName.E_STRIPE_CUSTOMER_NOT_FOUND:
				return 404;
			case APIErrorName.E_SIGN_UP_EXISTING_EMAIL:
			case APIErrorName.E_ACCOUNT_EXISTS:
			case APIErrorName.E_KEY_EXISTS:
			case APIErrorName.E_RESOURCE_CONFLICT:
			case APIErrorName.E_UPDATE_EMAIL_CONFLICT:
			case APIErrorName.E_AUTH_EXISTS_DUPLICIATE:
			case APIErrorName.E_EMAIL_ALREADY_EXISTS:
			case APIErrorName.E_EMAIL_ALREADY_INVITED:
			case APIErrorName.E_VIDEO_PROFILE_EXISTS:
				return 409;
			case APIErrorName.E_FILE_TOO_LARGE:
				return 413;
			case APIErrorName.E_IMPRESSION_LIMIT_REACHED:
				return 429;
			case APIErrorName.E_MISSING_CUSTOMER_ID:
			case APIErrorName.E_INTERNAL_ERROR:
			case APIErrorName.E_SERVICE_FAILED:
				return 500;
			case APIErrorName.E_NOT_IMPLEMENTED:
				return 501;
			default:
				return 500;
		}
	}


	private generateErrorRef(): string {
		const timestamp = new Date().getTime().toString(36);
		const randomPart = Math.random().toString(36).slice(2, 7);
		return `${timestamp}${randomPart}`.toUpperCase();
	}

	private static errorToMessage (error: unknown): string {
		if (error instanceof Error) {
			return error.message;
		} else if (typeof error === "string") {
			return error;
		}

		try {
			return JSON.stringify(error);
		} catch (error: unknown) {
			return "Unknown error";
		}
	}

	constructor (name: APIErrorName, error: unknown) {
		const message: string = APIError.errorToMessage(error);

		super(message);
		this.name = name;
		this.errorRef = this.generateErrorRef();
		this.request = null;
		this.detail = null;
		this._suppressLog = false;
		this.instanceOf = error?.constructor.name || "Unknown";

		return this;
	}

	public isMongoNotConnectedError (): boolean {
		return (this.instanceOf === "MongoNotConnectedError");
	}

	public suppressLog (): APIError {
		this._suppressLog = true;
		return this;
	}

	public suppressLogIf (errorNames: string[]): APIError {
		if (errorNames.includes(this.name as APIErrorName)) {
			return this.suppressLog();
		}

		return this;
	}

	public suppressLogIfNot (errorNames: string[]): APIError {
		if (!errorNames.includes(this.name as APIErrorName)) {
			return this.suppressLog();
		}

		return this;
	}

	public setDetail (detail: object): APIError {
		try {
			if (this.detail) {
				this.detail = this.detail + "\n" + JSON.stringify(detail);
			} else {
				this.detail = JSON.stringify(detail);
			}
		} catch (error: unknown) {
			this.detail = "failed to stringify detail";
		}

		return this;
	}

	public setRequest (request: Request): APIError {
		this.request = request;
		return this;
	}

	public setResponse (response: Response): Response {
		return response.status(this.getStatusCode()).json({
			error: this.name,
			errorRef: this.errorRef,
			message: this.message,
			detail: this.detail
		});
	}

	public throwIf (errorNames: string[]): void {
		if (errorNames.includes(this.name as APIErrorName)) {
			throw this;
		}
	}

	public throwIfNot (errorNames: string[]): void {
		if (!errorNames.includes(this.name as APIErrorName)) {
			throw this;
		}
	}

	public log (): APIError {
		if (this._suppressLog) {
			return this;
		}

		if (!this.shouldExcludeFromLog()) {
			const iLogInput: ILog = {
				message: this.name + ": " + this.message,
				objData: this.createObjData(),
				trace: this.stack,
				scope: LogScope.ERROR
			};

			gpLog(iLogInput);
		}

		return this;
	}

	public static fromUnknownError (error: unknown): APIError {
		if (error instanceof APIError) {
			return error;
		}

		if (error instanceof ModelResponse) {
			const apiError: APIError = error.getError();
			return apiError;
		}

		if (error instanceof Error) {
			if (error.name === "ValidationError") {
				return new APIError(APIErrorName.E_INVALID_INPUT, error);
			}
		}

		return new APIError(APIErrorName.E_SERVICE_FAILED, error);
	}

	public static fromJoiError (error: unknown): APIError {
		interface JoiError { path: any[]; code: string; local: { label: string; }; message: string; }

		const anyError = error as any;
		const errorMap = anyError.map((e: JoiError) => {
			const path = e.path.join(".");
			const code = e.code || "unknown_error";
			const label = e.local?.label || "field";
			const message = e.message || `Validation failed for "${label}" with code "${code}".`;

			return `Path: "${path}", Message: "${message}"`;
		});

		const errorString = errorMap.join("\n");

		return new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, errorString);
	}

	private shouldExcludeFromLog (): boolean {
		const excludedErrors = [
			APIErrorName.E_SIGN_IN_INCORRECT,
			APIErrorName.E_SIGN_UP_EXISTING_EMAIL,
			APIErrorName.E_STRIPE_CUSTOMER_NOT_FOUND,
			APIErrorName.E_SCHEMA_VALIDATION_ERROR,
			APIErrorName.E_IMPRESSION_LIMIT_REACHED
		];

		return excludedErrors.includes(this.name as APIErrorName);
	}

	private createObjData (): unknown {
		const objData = {
			detail: this.detail,
			errorRef: this.errorRef,
			request: {}
		};

		if (this.request) {
			objData["request"] = {
				method: this.request.method,
				url: this.request.originalUrl,
				path: this.request.path,
				protocol: this.request.protocol,
				host: this.request.get("host"),
				ip: this.request.ip,
				userAgent: this.request.get("User-Agent"),
				params: this.request.params
			};
		}

		return objData;
	}
}

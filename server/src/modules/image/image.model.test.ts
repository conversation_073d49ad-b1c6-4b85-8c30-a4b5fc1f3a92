import {
	ImageModel,
	ImageOptimize
} from "./image.model";
import mongoose from "mongoose";
import { BucketModel } from "../bucket/bucket.model";
import { promises as fsPromises } from "fs";
import { APIError } from "../../utils/helpers/apiError";

describe("ImageModel", () => {
	const accountId = "60d5ec49f6d9a1e68e9c07d5";
	const userId = "60d5ec49f6d9a1e68e9c07d6";
	let session: mongoose.ClientSession | null;
	let imageModel: ImageModel;

	beforeEach(() => {
		session = null;
		imageModel = new ImageModel(session);
		jest.clearAllMocks();
	});

	it("should optimize an image and return file information", async () => {
		const objectId = new mongoose.Types.ObjectId().toString();
		const objSpy = jest.spyOn(mongoose.Types.ObjectId.prototype, "toString").mockReturnValue(objectId);
		const imageData: ImageOptimize = {
			// eslint-disable-next-line max-len
			sourceURL: "http://domain.tld/src/__tests__/sample-files/255-character-long-filename-test-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456.jpg",
			imageWidthPx: 300,
			imageHeightPx: 300
		};

		const result = await (imageModel as any).optimizeImage(accountId, userId, imageData);

		expect(result).toHaveProperty("filename");
		expect(result).toHaveProperty("imageURL");
		expect(result.filename).toHaveProperty("length");
		expect(result.filename.length).toBeLessThanOrEqual(BucketModel.getMaxFileNameLength());
		// eslint-disable-next-line max-len
		expect(result.filename).toMatch(`255-character-long-filename-test-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopq-${objectId}_300x300.jpg`);
		// eslint-disable-next-line max-len
		expect(result.imageURL).toContain(`255-character-long-filename-test-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopqrstuvwxyz0123456789-abcdefghijklmnopq-${objectId}_300x300.jpg`);
		objSpy.mockRestore();
	});

	it("Should optimize an image and return file information But Fail to remove temp file", async () => {
		const objectId = new mongoose.Types.ObjectId().toString();
		const objSpy = jest.spyOn(mongoose.Types.ObjectId.prototype, "toString").mockReturnValue(objectId);
		const unlinkSpy = jest.spyOn(fsPromises, "unlink").mockRejectedValue(new Error("Failed to unlink file"));
		const apiErrorLogSpy = jest.spyOn(APIError.prototype, "log").mockImplementation();
		const imageData: ImageOptimize = {
			sourceURL: "http://domain.tld/src/__tests__/sample-files/test-image.jpg",
			imageWidthPx: 300,
			imageHeightPx: 300
		};

		const result = await (imageModel as any).optimizeImage(accountId, userId, imageData);

		expect(result).toHaveProperty("filename");
		expect(result).toHaveProperty("imageURL");
		expect(result.filename).toHaveProperty("length");
		expect(result.filename.length).toBeLessThanOrEqual(BucketModel.getMaxFileNameLength());
		expect(result.filename).toMatch(`test-image-${objectId}_300x300.jpg`);
		expect(result.imageURL).toContain(`test-image-${objectId}_300x300.jpg`);
		expect(unlinkSpy).toHaveBeenCalledTimes(1);
		expect(apiErrorLogSpy).toHaveBeenCalledTimes(1);

		unlinkSpy.mockRestore();
		apiErrorLogSpy.mockRestore();
		objSpy.mockRestore();
	});
});

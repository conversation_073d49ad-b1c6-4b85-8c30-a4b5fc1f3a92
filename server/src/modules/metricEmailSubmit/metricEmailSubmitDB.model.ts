import mongoose, {
	ObjectId,
	Document,
	Schema
} from "mongoose";

export interface MetricEmailSubmit extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	videoId: ObjectId;
}

const MetricEmailSubmitSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	videoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricEmailSubmitDBModel = mongoose.model<MetricEmailSubmit>(
	"metric_email_submit",
	MetricEmailSubmitSchema,
	"metric_email_submit"
);

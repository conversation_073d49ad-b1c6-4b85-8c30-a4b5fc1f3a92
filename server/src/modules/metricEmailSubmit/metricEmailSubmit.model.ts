import mongoose, { ClientSession } from "mongoose";
import {
	MetricEmailSubmitDBModel,
	MetricEmailSubmit
} from "./metricEmailSubmitDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";

export class MetricEmailSubmitModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async createOne (videoId: string, accountId: string, createdAt: Date): Promise<MetricEmailSubmit> {
		try {
			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const document = await new MetricEmailSubmitDBModel({
				createdAt: createdAt,
				videoId: new mongoose.Types.ObjectId(videoId),
				accountId: new mongoose.Types.ObjectId(accountId)
			}).save(options);

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async registerEmailSubmit (videoId: string, accountId: string, createdAt: Date): Promise<void> {
		try {
			const interactiveVideoModel: InteractiveVideoModel = new InteractiveVideoModel(null);
			const videoDocument = await interactiveVideoModel.incrementEmailSubmitCount(videoId);
			await this.createOne(videoDocument._id.toString(), accountId, createdAt);
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

}

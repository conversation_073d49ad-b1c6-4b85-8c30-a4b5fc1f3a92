import mongoose from "mongoose";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { LocaleAPI } from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { AccountModel } from "../account/account.model";
import { MetricEmailSubmitModel } from "./metricEmailSubmit.model";
import { IVideo } from "../video/video.interfaces";
import { MetricEmailSubmitDBModel } from "./metricEmailSubmitDB.model";
import { InteractiveVideoDBModel } from "../interactiveVideo/interactiveVideoDB.model";

describe("White Box Testing | metricEmailSubmit Model", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accountId: string;
	let interactiveVideoId: string;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		const accountToken = await testHelper.getAccountToken(accountId, accessToken);
		const video: IVideo = await testHelper.createVideo(account._id.toString());
		const videoId = video._id.toString();
		const shoppableVideo = await testHelper.createShoppableVideo(videoId, accountToken, accessToken);
		interactiveVideoId = shoppableVideo._id.toString();
	});

	beforeEach(async () => {
		const accountModel = new AccountModel(null);
		await accountModel.resetUsage(accountId);
	});

	it("should register email submit metric successfully.", async () =>{
		const currentInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});
		const emailSubmitCount = currentInteractiveVideo?.emailSubmitCount || 0;

		const metricEmailSubmitModel = new MetricEmailSubmitModel(null);
		await metricEmailSubmitModel.registerEmailSubmit(interactiveVideoId, accountId, new Date());

		const latestEmailSubmitDocument = await MetricEmailSubmitDBModel.findOne({
			accountId: new mongoose.Types.ObjectId(accountId),
			videoId: new mongoose.Types.ObjectId(interactiveVideoId)
		}).sort({ createdAt: -1 });

		const updateInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});

		expect(latestEmailSubmitDocument).not.toBeNull();
		expect(latestEmailSubmitDocument?.accountId?.toString()).toBe(accountId);
		expect(latestEmailSubmitDocument?.videoId?.toString()).toBe(interactiveVideoId);
		expect(updateInteractiveVideo?.emailSubmitCount).toBe(emailSubmitCount + 1);
	});

	it("should register two parallel email submit requests successfully.", async () =>{
		const currentInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});
		const emailSubmitCount = currentInteractiveVideo?.emailSubmitCount || 0;
		const metricEmailSubmitModel = new MetricEmailSubmitModel(null);
		const request1 = metricEmailSubmitModel.registerEmailSubmit(interactiveVideoId, accountId, new Date());
		const request2 = metricEmailSubmitModel.registerEmailSubmit(interactiveVideoId, accountId, new Date());
		const [result1, result2] = await Promise.allSettled([request1, request2]);

		const updateInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});

		expect(result1.status).toBe("fulfilled");
		expect(result2.status).toBe("fulfilled");
		expect(updateInteractiveVideo?.emailSubmitCount).toBe(emailSubmitCount + 2);
	});
});

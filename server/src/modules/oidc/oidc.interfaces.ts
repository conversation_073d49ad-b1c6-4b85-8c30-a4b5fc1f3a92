export interface OIDCAuthConfig {
	accessToken?: string;
	idToken?: string;
	clientId: string;
}

export interface OIDCUserInfo {
	sub: string;
	email: string;
}

export interface DecodedToken {
	iss: string;
	aud: string;
	sub: string;
	exp: number;
	iat: number;
}

export interface OIDCResultTokens {
	oidcAccessToken: string;
	oidcRefreshToken: string;
	oidcIdToken: string;
}

export interface ExchangeTokensInput {
	grantType: "authorization_code" | "refresh_token";
	code?: string;
	refreshToken?: string;
}

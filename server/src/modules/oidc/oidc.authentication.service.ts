import { ClientSession } from "mongoose";
import { OIDCModel } from "./oidc.model";
import { UserModel } from "../user/user.model";
import { SignUpModel } from "../signup/signup.model";
import { createAccessToken } from "../../utils/tokens";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IUser } from "../user/user.interfaces";
import { IAuthentication } from "../../modules/authentication/authentication.interface";
import { OIDCAuthentication } from "../authentication/oidc.authentication.model";

interface OIDCAuthResult {
	statusCode: number;
	accessToken: string;
}

export class OIDCAuthenticationService {
	constructor(private session: ClientSession | null) {}

	async authenticateWithOIDC(
		oidcClientId: string,
		oidcAccessToken: string,
		oidcIdToken: string,
		inviteToken?: string
	): Promise<OIDCAuthResult> {
		const oidcModel = new OIDCModel({
			clientId: oidcClientId,
			accessToken: oidcAccessToken,
			idToken: oidcIdToken
		});

		const decodedIdToken = await oidcModel.verifyIdToken();
		const userInfo = await oidcModel.getUserInfo();

		if (!userInfo.email) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Email address is required");
		}

		const authModel = new OIDCAuthentication(this.session);
		let authDocument = await authModel.readOneBySub(decodedIdToken.sub);


		if (!authDocument) {
			authDocument = await authModel.findAndUpdateForOIDC(userInfo.email, decodedIdToken.sub);
		}

		return await this.authenticateOrCreateOIDCUser(authDocument, userInfo.email, decodedIdToken.sub, inviteToken);
	}

	private async authenticateOrCreateOIDCUser(
		authDocument: IAuthentication | null,
		email: string,
		sub: string,
		inviteToken?: string
	): Promise<OIDCAuthResult> {
		let userDocument: IUser | null = null;
		let statusCode = 200;

		if (authDocument) {
			const userModel = new UserModel(this.session);
			userDocument = await userModel.readOneById(authDocument.userId.toString());
		} else {
			statusCode = 201;
			const signupModel = new SignUpModel(this.session);
			const signupResult = await signupModel.createFromOIDC({
				email: email,
				sub: sub,
				inviteToken: inviteToken
			});

			authDocument = signupResult.authenticationDoc;
			userDocument = signupResult.userDoc;
		}

		const accessToken = await createAccessToken(authDocument, userDocument);

		if (!accessToken) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to create access token");
		}

		return {
			statusCode,
			accessToken
		};
	}
}

import jwksClient from "jwks-rsa";
import jwt from "jsonwebtoken";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import {
	getSecrets,
	OIDCConfig
} from "../secrets/secrets.model";
import {
	OIDCAuthConfig,
	OIDCUserInfo,
	OIDCResultTokens,
	ExchangeTokensInput,
	DecodedToken
} from "./oidc.interfaces";
import { AuthorizationServer } from "./oidc.authorization.server";

export class OIDCModel {
	private providerConfig: OIDCConfig | null = null;

	constructor(private authConfig: OIDCAuthConfig) {
		if (!this.authConfig.clientId) {
			throw new APIError(APIErrorName.E_MISSING_OIDC_DATA, "OIDC clientId must be specified");
		}
	}

	private assertTokens(): { accessToken: string; idToken: string } {
		if (!this.authConfig.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_OIDC_DATA, "OIDC Access token must be set");
		}
		if (!this.authConfig.idToken) {
			throw new APIError(APIErrorName.E_MISSING_OIDC_DATA, "OIDC ID token must be set");
		}
		return {
			accessToken: this.authConfig.accessToken,
			idToken: this.authConfig.idToken
		};
	}

	private async getProviderConfig(clientId: string): Promise<OIDCConfig> {
		if (this.providerConfig) {
			return this.providerConfig;
		}
		const secrets = await getSecrets();
		const config = secrets.oidc.find(config => config.clientId === clientId);
		if (!config) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, `Unsupported OIDC clientId: ${clientId}`);
		}
		this.providerConfig = {
			provider: config.provider,
			userInfoEndpoint: config.userInfoEndpoint,
			jwksEndpoint: config.jwksEndpoint,
			tokenEndpoint: config.tokenEndpoint,
			clientId: config.clientId,
			clientSecret: config.clientSecret,
			redirectEndpoint: config.redirectEndpoint
		};
		return this.providerConfig;
	}

	private decodeIdToken(): DecodedToken {
		const { idToken } = this.assertTokens();
		const decoded = jwt.decode(idToken);
		if (!decoded || typeof decoded === "string") {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Invalid OIDC ID token");
		}
		return decoded as DecodedToken;
	}

	private getPublicKey(jwksEndpoint: string, header: jwt.JwtHeader, callback: jwt.SigningKeyCallback): void {
		const client = jwksClient({ jwksUri: jwksEndpoint });

		client.getSigningKey(header.kid, (err: Error | null, key?: jwksClient.SigningKey) => {
			if (err) {
				gpLog({
					message: "Error getting signing key",
					objData: { error: err },
					trace: "OIDCAuthenticator.getPublicKey",
					scope: LogScope.ERROR
				});
				return callback(err);
			}

			if (!key) {
				return callback(new Error("Signing key not found"));
			}

			const signingKey = key.getPublicKey();
			callback(null, signingKey);
		});
	}

	public async verifyIdToken(): Promise<DecodedToken> {
		const { idToken } = this.assertTokens();
		const providerConfig = await this.getProviderConfig(this.authConfig.clientId);
		const decodedIdToken = this.decodeIdToken();

		return new Promise((resolve, reject) => {
			jwt.verify(
				idToken,
				(header: jwt.JwtHeader, callback: jwt.SigningKeyCallback) => {
					this.getPublicKey(providerConfig.jwksEndpoint, header, callback);
				},
				{
					issuer: decodedIdToken.iss,
					audience: decodedIdToken.aud,
					algorithms: ["RS256"]
				},
				(err, decoded) => {
					if (err) {
						reject(new Error(`Invalid ID token: ${err.message}`));
					} else if (!decoded) {
						reject(new Error("ID token verification failed."));
					} else {
						resolve(decoded as DecodedToken);
					}
				}
			);
		});
	}

	public async getUserInfo(): Promise<OIDCUserInfo> {
		const { accessToken } = this.assertTokens();
		const providerConfig = await this.getProviderConfig(this.authConfig.clientId);
		const authorizationServer = new AuthorizationServer(providerConfig);
		return await authorizationServer.fetchUserInfo(accessToken);
	}

	private stripTrailingSlash(url: string): string {
		return url.replace(/\/$/, "");
	}

	private async exchangeTokens(input: ExchangeTokensInput): Promise<OIDCResultTokens> {
		const providerConfig = await this.getProviderConfig(this.authConfig.clientId);
		const searchParams = {
			// scope: "openid email profile",
			// eslint-disable-next-line camelcase
			grant_type: input.grantType,
			// eslint-disable-next-line camelcase
			access_type: "offline",
			// eslint-disable-next-line camelcase
			client_id: providerConfig.clientId,
			// eslint-disable-next-line camelcase
			client_secret: providerConfig.clientSecret,
			// eslint-disable-next-line camelcase
			redirect_uri: this.stripTrailingSlash(providerConfig.redirectEndpoint),
			...(input.code && { code: input.code }),
			// eslint-disable-next-line camelcase
			...(input.refreshToken && { refresh_token: input.refreshToken })
		};

		const body = new URLSearchParams(searchParams);
		const authorizationServer = new AuthorizationServer(providerConfig);
		const tokens = await authorizationServer.fetchTokens(body);

		if (!tokens.access_token) {
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED, "Failed to return oidc access_token");
		}

		if (!tokens.refresh_token) {
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED, "Failed to return oidc refresh_token");
		}

		if (!tokens.id_token) {
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED, "Failed to return oidc id_token");
		}

		return {
			oidcAccessToken: tokens.access_token,
			oidcRefreshToken: tokens.refresh_token,
			oidcIdToken: tokens.id_token
		};
	}

	public async submitCode(code: string): Promise<OIDCResultTokens> {
		return this.exchangeTokens({ grantType: "authorization_code", code });
	}

	public async refreshAccessToken(refreshToken: string): Promise<OIDCResultTokens> {
		const result = await this.exchangeTokens({ grantType: "refresh_token", refreshToken });
		if (!result.oidcRefreshToken) {
			result.oidcRefreshToken = refreshToken;
		}
		return result;
	}
}

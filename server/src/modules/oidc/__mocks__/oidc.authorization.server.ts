/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable camelcase */

import { OIDCConfig } from "../../secrets/secrets.model";
import { OIDCUserInfo } from "../oidc.interfaces";
import jwt from "jsonwebtoken";
import { readFileSync } from "fs";
import path from "path";

const privateKeyPath = path.join(__dirname, "private_key_mock.pem");
const publicKeyPath = path.join(__dirname, "public_key_mock.pem");

// Mock Implementation of AuthorizationServer
export class AuthorizationServerMock {
	public static accessTokenUserMap: Map<string, OIDCUserInfo> = new Map();
	private static privateKey = readFileSync(privateKeyPath, "utf8");
	public static publicKey = readFileSync(publicKeyPath, "utf8");

	constructor(private providerConfig: OIDCConfig) {}

	public async fetchUserInfo(accessToken: string): Promise<OIDCUserInfo> {
		const userInfo: OIDCUserInfo | undefined = AuthorizationServerMock.accessTokenUserMap.get(accessToken);

		if (!userInfo) {
			throw new AuthorizationServerMockError(
				"accessToken passed to fetchUserInfo was not generated by AuthorizationServerMock.generateTokens");
		}

		return Promise.resolve({ email: userInfo.email, sub: userInfo.sub });
	}

	public async fetchTokens(body: any): Promise<any> {
		const tokens = {
			access_token: "mockedAccessToken",
			refresh_token: "mockedRefreshToken",
			id_token: "mockedIdToken"
		};

		return tokens;
	}

	public static generateTokens(clientId: string, subject: string, email: string):
	{ idToken:string, accessToken:string } {
		const now = Math.floor(Date.now() / 1000);

		const idToken = jwt.sign(
			{
				iss: "https://oidc-provider.tld",
				sub: subject,
				aud: clientId,
				exp: now + 3600,
				iat: now,
				// eslint-disable-next-line camelcase
				auth_time: now,
				nonce: "example-nonce-value",
				email: email,
				// eslint-disable-next-line camelcase
				email_verified: true
			},
			AuthorizationServerMock.privateKey,
			{ algorithm: "RS256" }
		);

		const accessToken = jwt.sign(
			{
				iss: "https://oidc-provider.tld",
				sub: subject,
				aud: clientId,
				exp: now + 3600,
				iat: now,
				scope: "openid email profile"
			},
			AuthorizationServerMock.privateKey,
			{ algorithm: "RS256" }
		);

		this.accessTokenUserMap.set(accessToken, { email: email, sub: subject });

		return { idToken, accessToken };
	}
}

class AuthorizationServerMockError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "AuthorizationServerMockError";
		this.stack = (new Error()).stack;
	}
}

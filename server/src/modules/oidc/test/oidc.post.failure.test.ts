import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../signup/signup.interfaces";
import { AuthorizationServerMock } from "../__mocks__/oidc.authorization.server";
import { OIDCModel } from "../oidc.model";
import { OIDCUserInfo } from "../oidc.interfaces";
import { APIError } from "../../../utils/helpers/apiError";


describe("OIDC Authentication Tests Negative Mode | POST /api/oauth/oidc", () => {
	let expressApp: express.Express;
	const oidcPostApi = "/api/oauth/oidc";
	const email = "<EMAIL>";
	const sub = "user-sub";
	const oidcClientId = "client-id-1";
	const { idToken: oidcIdToken, accessToken: oidcAccessToken } =
		AuthorizationServerMock.generateTokens(oidcClientId, "subject", email);

	async function performSignup(app: express.Express): Promise<void> {
		const signupPayload: ISignupPayload = {
			email: "<EMAIL>",
			password: "Password",
			firstName: "oidc",
			lastName: "post-test",
			companyName: "Oidc Post Test Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true
		};

		const signUpResponse = await supertest(app)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		if (signUpResponse.status !== 200) {
			console.error("Signup failed. Response:", signUpResponse.body);
			throw new Error(`Signup failed with status ${signUpResponse.status}`);
		}
		expect(signUpResponse.body).toHaveProperty("accessToken");
	}


	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		await performSignup(expressApp);
	});

	it("should fail to authenticate a new user and return 500.", testNewUserAuthenticationFailure);
	it("should fail to authenticate a new user with missing email and return 400.",
		testNewUserAuthenticationWithMissingEmail);
	it("should return 400 for missing required fields.", testMissingRequiredFields);
	it("should return 400 for invalid API version.", testInvalidApiVersion);

	async function testNewUserAuthenticationFailure(): Promise<void> {
		jest.spyOn(OIDCModel.prototype, "getUserInfo").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to fetch user info: some info")
		);

		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "1")
			.field("oidcIdToken", oidcIdToken)
			.field("oidcAccessToken", oidcAccessToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}

	async function testNewUserAuthenticationWithMissingEmail(): Promise<void> {
		jest.spyOn(OIDCModel.prototype, "getUserInfo").mockResolvedValueOnce(
			{
				sub: sub
			} as OIDCUserInfo);

		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "1")
			.field("oidcIdToken", oidcIdToken)
			.field("oidcAccessToken", oidcAccessToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testMissingRequiredFields(): Promise<void> {
		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "1");

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testInvalidApiVersion(): Promise<void> {
		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "0");

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}
});

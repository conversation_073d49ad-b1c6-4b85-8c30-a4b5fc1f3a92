import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { AuthorizationServer } from "../oidc.authorization.server";
import { APIError } from "../../../utils/helpers/apiError";

describe("OIDC Token Refresh Tests Negative Mode | PUT /api/oauth/oidc/token", () => {
	let expressApp: express.Express;
	const oidcTokenPutApi = "/api/oauth/oidc/token";
	const oidcClientId = "client-id-1";
	const oidcRefreshToken = "valid-refresh-token";

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	afterAll(() => {
		jest.restoreAllMocks();
	});

	it("should return 400 for missing required fields", testMissingRequiredFields);
	it("should return 400 for invalid API version", testInvalidApiVersion);
	it("should return 500 for unsupported OIDC clientId", testInvalidOidcClientId);
	it("should handle OIDC provider errors and return 500", testOidcProviderError);
	it("should handle network errors and return 500", testNetworkError);
	it("should handle missing access token in response and return 500", testMissingAccessTokenInResponse);

	async function testMissingRequiredFields(): Promise<void> {
		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1");

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testInvalidApiVersion(): Promise<void> {
		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "0")
			.field("oidcRefreshToken", oidcRefreshToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testInvalidOidcClientId(): Promise<void> {
		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1")
			.field("oidcRefreshToken", oidcRefreshToken)
			.field("oidcClientId", oidcClientId + "-invalid");

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_INTERNAL_ERROR);
	}

	async function testOidcProviderError(): Promise<void> {
		jest.spyOn(AuthorizationServer.prototype, "fetchTokens").mockRejectedValueOnce(
			new APIError(
				APIErrorName.E_SERVICE_FAILED,
				"Failed to exchange OIDC tokens: Some Text. Error details: error description"
			)
		);

		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1")
			.field("oidcRefreshToken", oidcRefreshToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}

	async function testNetworkError(): Promise<void> {
		jest.spyOn(AuthorizationServer.prototype, "fetchTokens").mockRejectedValueOnce(
			new Error("Network error")
		);

		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1")
			.field("oidcRefreshToken", oidcRefreshToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}

	async function testMissingAccessTokenInResponse(): Promise<void> {
		jest.spyOn(AuthorizationServer.prototype, "fetchTokens").mockResolvedValueOnce(
			{
				// eslint-disable-next-line camelcase
				refresh_token: "value does not matter",
				// eslint-disable-next-line camelcase
				id_token: "value does not matter"
			}
		);

		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1")
			.field("oidcRefreshToken", oidcRefreshToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}
});

import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";

describe("OIDC Token Refresh Tests Positive Mode| PUT /api/oauth/oidc/token", () => {
	let expressApp: express.Express;
	const oidcTokenPutApi = "/api/oauth/oidc/token";
	const oidcClientId = "client-id-1";

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});


	it("should successfully refresh tokens and return 200", testSuccessfulTokenRefresh);

	async function testSuccessfulTokenRefresh(): Promise<void> {
		const res = await supertest(expressApp)
			.put(oidcTokenPutApi)
			.set("x-api-version", "1")
			.field("oidcRefreshToken", "mockedRefreshToken")
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(200);
	}
});

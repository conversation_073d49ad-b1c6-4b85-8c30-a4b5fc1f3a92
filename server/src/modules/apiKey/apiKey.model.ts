import {
	ClientSession,
	FilterQuery,
	QueryOptions
} from "mongoose";
import {
	APIKeyCreateData,
	<PERSON><PERSON><PERSON>
} from "./apiKey.interfaces";
import { APIKeyDBModel } from "./apiKeyDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";

export class APIKeyModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public async createOne(
		data: APIKeyCreateData
	): Promise<IKey> {
		try {
			const key = new APIKeyDBModel(data);
			const document = await key.save({ session: this.session });
			return document;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, error);
		}
	}


	public async readManyByAccountId(accountId: string): Promise<IKey[]> {
		try {
			const filter: FilterQuery<IKey> = {
				accountId: accountId
			};

			const documents: IKey[] = await APIKeyDBModel.find(filter).session(
				this.session
			);

			for (const document of documents) {
				if (!document.meta) {
					document.meta = {};
				}
			}

			return documents;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, error);
		}
	}
}

export const readKey = async (
	query: any,
	session: ClientSession | null
): Promise<IKey | null> => {
	try {
		const document: IKey | null = await APIKeyDBModel.findOne(query).session(
			session
		);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<keys.service> readKey| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const deleteKey = async (
	query: any,
	session: ClientSession | null
): Promise<IKey | null> => {
	try {
		const options: QueryOptions<IKey> = {};

		const document: IKey | null = await APIKeyDBModel.findOneAndDelete(
			query,
			options
		).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<keys.service> deleteKey | ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

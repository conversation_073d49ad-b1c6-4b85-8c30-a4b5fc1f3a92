import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import { v4 as uuidv4 } from "uuid";
import { IAuthenticationOptions } from "../authentication/authentication.interface";
import {
	APIKeyCreateData,
	APIKeyPostRequest,
	IKey
} from "../../modules/apiKey/apiKey.interfaces";
import { APIKeyPostValidator } from "../../modules/apiKey/apiKey.joi";
import {
	APIKeyModel,
	deleteKey,
	readKey
} from "./apiKey.model";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import { ApikeyAuthentication } from "../authentication/apikey.authentication.model";
import { AuthenticationModel } from "../authentication/authentication.model";
import { UserModel } from "../user/user.model";
import { AccountModel } from "../account/account.model";

export class APIKeyController {
	async delete (request: Request, response: Response): Promise<Response> {
		try {
			if (!request.headers["x-api-version"]) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Missing API version"
				);
			}

			const apiVersion = Number(request.headers["x-api-version"]);
			if (isNaN(apiVersion) || apiVersion < 1) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Invalid API version"
				);
			}

			if (!request.accountToken) {
				throw new APIError(
					APIErrorName.E_MISSING_AUTHORIZATION,
					"Missing x-account-token header"
				);
			}

			await startDBTransaction(response.locals.session);

			const keyDocument = await readKey(
				{
					_id: request.params.keyId
				},
				response.locals.session
			);

			if (!keyDocument || keyDocument === null) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					`Failed to read the key document with _id ${request.params.keyId}`
				);
			}

			if (keyDocument.accountId.toString() !== request.accountToken.account._id) {
				throw new APIError(
					APIErrorName.E_REQUEST_FORBIDDEN,
					"Mismatch between accountId in accountToken and accountId in key document"
				);
			}

			const deletedKey: IKey | null = await deleteKey(
				{
					_id: request.params.keyId
				},
				response.locals.session
			);

			if (!deletedKey) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Key not found"
				);
			}

			const authModel = new AuthenticationModel(null);
			await authModel.delete(keyDocument.authenticationId.toString());

			await completeDBTransaction(response.locals.session);
			return response.send({});
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	async list (request: Request, response: Response): Promise<Response> {
		try {
			if (!request.headers["x-api-version"]) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Missing API version"
				);
			}

			const apiVersion = Number(request.headers["x-api-version"]);
			if (isNaN(apiVersion) || apiVersion < 1) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Invalid API version"
				);
			}

			if (!request.accountToken) {
				throw new APIError(
					APIErrorName.E_MISSING_AUTHORIZATION,
					"Missing x-account-token header"
				);
			}

			const apiKeyModel = new APIKeyModel(null);
			const docs = await apiKeyModel.readManyByAccountId(request.accountToken.account._id);

			return response.send(docs);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	async post (request: Request, response: Response): Promise<Response> {
		let apiKeyPostRequest: APIKeyPostRequest;
		try {
			apiKeyPostRequest = await APIKeyPostValidator.validateAsync({
				meta: request.body.meta
			});
		} catch (error: unknown) {
			const apiError: APIError = new APIError(
				APIErrorName.E_INVALID_INPUT,
				error
			);

			return apiError.log().setResponse(response);
		}

		try {
			if (!request.headers["x-api-version"]) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Missing API version"
				);
			}

			const apiVersion = Number(request.headers["x-api-version"]);
			if (isNaN(apiVersion) || apiVersion < 1) {
				throw new APIError(
					APIErrorName.E_INVALID_INPUT,
					"Invalid API version"
				);
			}

			if (!request.accountToken) {
				throw new APIError(
					APIErrorName.E_MISSING_AUTHORIZATION,
					"Missing x-account-token header"
				);
			}

			await startDBTransaction(response.locals.session);

			const secrets: ISecrets = await getSecrets();
			const gpSecretKey = secrets.hashkey.key;

			if (!gpSecretKey) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to read secrets.hashkey?.key");
			}

			const partialApiKey = uuidv4();

			const authenticationId = request.accessToken?.authenticationId as string;

			const authModel = new AuthenticationModel(response.locals.session);
			const userAuthDocument = await authModel.readOneById(authenticationId);

			const userId: string | undefined = request.accessToken?.userId;
			const accountId: string = request.accountToken.account._id;

			if (!userId){
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Missing userId in access token."
				);
			}

			const userModel = new UserModel(response.locals.session);
			const userDocument = await userModel.readOneById(userId);

			if (!accountId){
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Missing accountId in access token."
				);
			}

			const accountModel = new AccountModel(response.locals.session);
			const accountDocument = await accountModel.readOneById(accountId);

			const options: IAuthenticationOptions = {
				verified: userAuthDocument.verified,
				legalAgreement: userAuthDocument.legalAgreement
			};

			const authenticationModel = new ApikeyAuthentication(response.locals.session);
			const authenticationDocument = await authenticationModel.createOne(
				userDocument._id.toString(),
				accountDocument._id.toString(),
				partialApiKey,
				options);

			const createKeyData: APIKeyCreateData = {
				accountId: request.accountToken.account._id,
				authenticationId: authenticationDocument._id.toString(),
				keyLast4: partialApiKey.slice(-4),
				createdBy: userId,
				meta: apiKeyPostRequest.meta
			};
			const keyModel = new APIKeyModel(null);
			const keyDocument = await keyModel.createOne(createKeyData);

			const apikey = `${keyDocument._id.toString()}-${partialApiKey}`;

			await completeDBTransaction(response.locals.session);
			return response.send({ apikey: apikey, key: keyDocument });
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}
}

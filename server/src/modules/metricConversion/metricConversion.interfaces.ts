import mongoose, { Document } from "mongoose";

export interface MetricConversionCreateOne {
	userSessionId: string,
	orderItemsCount: number
}

export interface IMetricConversion extends Document {
	_id: mongoose.Types.ObjectId;
	accountId: mongoose.Types.ObjectId;
	userSessionId: mongoose.Types.ObjectId;
	orderItemsCount: number;
}

export interface IPostMetricConversionPayload {
	accountId: string;
	userSessionId: string;
	orderItemsCount: number;
	collectionIds: string[];
}

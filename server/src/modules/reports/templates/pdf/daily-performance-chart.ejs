<div id="dailyPerformanceSection" class="section-title">Daily Performance</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>EMAILS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ea4335;"></div>
            <span>CALLS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #34a853;"></div>
            <span>CLICKS</span>
        </div>

    </div>
    <div class="chart-subtitle">COUNT</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('<%= chartId %>').getContext('2d');

        const emailsData = <%- JSON.stringify(chartData?.emails || []) %>;
        const callsData = <%- JSON.stringify(chartData?.calls || []) %>;
        const clicksData = <%- JSON.stringify(chartData?.clicks || []) %>;

        // Calculate dynamic max value with buffer
        const maxDataValue = Math.max(
            ...emailsData,
            ...callsData,
            ...clicksData,
            0
        );
        const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 5); // At least 5 minimum
        const stepSize = Math.max(Math.ceil(dynamicMax / 4), 1); // Divide into ~4 steps

        const chartData = {
            labels: <%- JSON.stringify(chartData?.labels || []) %>,
            datasets: [
                {
                    label: 'EMAILS',
                    data: emailsData,
                    borderColor: '#4285f4',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#4285f4',
                    pointBorderColor: '#4285f4',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CALLS',
                    data: callsData,
                    borderColor: '#ea4335',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#ea4335',
                    pointBorderColor: '#ea4335',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CLICKS',
                    data: clicksData,
                    borderColor: '#34a853',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#34a853',
                    pointBorderColor: '#34a853',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                }
            ]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: dynamicMax,
                        ticks: {
                            stepSize: stepSize,
                            color: '#666',
                            font: {
                                size: 11
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                }
            }
        };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>

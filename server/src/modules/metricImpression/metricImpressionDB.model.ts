import mongoose, { Schema } from "mongoose";
import { IMetricImpression } from "./metricImpression.interfaces";

const MetricImpressionSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	collectionId: { type: Schema.Types.ObjectId, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	collectionShare: { type: Schema.Types.ObjectId, required: false },
	videoShare: { type: Schema.Types.ObjectId, required: false },
	createdAt: { type: Schema.Types.Date, required: true }
});

export const MetricImpressionDBModel = mongoose.model<IMetricImpression>(
	"metric_impressions",
	MetricImpressionSchema
);

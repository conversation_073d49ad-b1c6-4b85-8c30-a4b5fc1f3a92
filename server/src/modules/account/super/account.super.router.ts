import express, {
	Express,
	Request,
	Response
} from "express";
import { AccountSuperController } from "./account.super.controller";
import { decodeAccess } from "../../../middleware/decodeAccess.mw";
import { validateHeaders } from "../../../middleware/validateHeaders.mw";

export class AccountSuperRouter {
	private controller: AccountSuperController = new AccountSuperController();
	private router = express.Router();

	constructor () {
		this.router.get(
			"/",
			[express.json({ limit: "2MB" }), decodeAccess, validateHeaders],
			(request: Request, response: Response) => {
				return this.controller.get(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/accounts/super", this.router);
	}
}

import mongoose, { ClientSession } from "mongoose";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IAccount } from "./account.interfaces";
import { AccountDBModel } from "./accountDB.model";
import { MetricInteractiveVideoModel } from "../metricInteractiveVideo/metricInteractiveVideo.model";
import { MetricImpressionModel } from "../metricImpression/metricImpression.model";
import { MetricInteractiveCollectionModel } from "../metricInteractiveCollection/metricInteractiveCollection.model";
import { MetricVideoClickModel } from "../metricVideoClick/metricVideoClick.model";
import { MetricConversionModel } from "../metricConversion/metricConversion.model";
import { MetricVideoPlayTimeModel } from "../metricVideoPlayTime/metricVideoPlayTime.model";

interface AccountDailyMetrics {
	dailyMetricsUpdatedAt: number;
	totalVideosCreatedDaily: number;
	latestSnippetImpressionDateDaily: number | undefined;
	totalVideoImpressionsDaily: number;
	totalCollectionsCreatedDaily: number;
	totalVideoPlaysDaily: number;
	totalPlaytimeDaily: number;
	totalVideoCTAClicksDaily: number;
	totalConversionDaily: number;
}

interface AccountWeeklyMetrics {
	weeklyMetricsUpdatedAt: number;
	totalVideosCreatedWeekly: number;
	totalCollectionsCreatedWeekly: number;
	totalVideoPlaysWeekly: number;
	totalPlaytimeWeekly: number;
	totalConversionWeekly: number;
}

interface AccountCombinedMetrics extends Partial<AccountDailyMetrics>, Partial<AccountWeeklyMetrics> {}

export class AccountMetricModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	private readAccountsPendingWeeklyUpdate = async(): Promise<ModelResponse<IAccount[]>> => {
		const now = new Date();
		const startOfWeek = new Date(Date.UTC(
			now.getUTCFullYear(),
			now.getUTCMonth(),
			now.getUTCDate() - now.getUTCDay() + 1,
			0,
			0,
			0,
			0
		));
		const startOfWeekTimestamp = startOfWeek.getTime();
		const pipeline = [
			{
				$match: {
					$or: [
						{ weeklyMetricsUpdatedAt: { $exists: false } },
						{ weeklyMetricsUpdatedAt: null },
						{ weeklyMetricsUpdatedAt: { $lt: startOfWeekTimestamp } }
					]
				}
			}
		];

		const documents: IAccount[] = await AccountDBModel.aggregate(pipeline).session(this.session);

		if (!documents || documents.length === 0) {
			return new ModelResponse<IAccount[]>(
				[],
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "No accounts pending weekly updates are found.")
			);
		}
		return new ModelResponse<IAccount[]>(documents, null);
	};

	private fetchWeeklyMetrics = async ():Promise<ModelResponse<Map<string, AccountWeeklyMetrics>>> => {
		try {
			const WeeklyAccountsResponse = await this.readAccountsPendingWeeklyUpdate();
			WeeklyAccountsResponse.throwIfError();
			const weeklyAccounts = WeeklyAccountsResponse.getData();

			const accountWeeklyMetricsMap: Map<string, AccountWeeklyMetrics> = new Map(weeklyAccounts.map(account => [
				account._id.toString(), {
					weeklyMetricsUpdatedAt: Date.now(),
					totalVideosCreatedWeekly: 0,
					totalCollectionsCreatedWeekly: 0,
					totalVideoPlaysWeekly: 0,
					totalPlaytimeWeekly: 0,
					totalConversionWeekly: 0
				}
			]));

			const metricIVModel = new MetricInteractiveVideoModel(null);
			const metricIVWeeklyResponse = await metricIVModel.readTotalPrevWeekByAccounts(weeklyAccounts);
			if (!metricIVWeeklyResponse.hasError()){
				metricIVWeeklyResponse.getData().forEach((total, accountId) => {
					const metrics = accountWeeklyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalVideosCreatedWeekly = total;
						accountWeeklyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricICModel = new MetricInteractiveCollectionModel(null);
			const metricICWeeklyResponse = await metricICModel.readTotalPrevWeekByAccounts(weeklyAccounts);
			if (!metricICWeeklyResponse.hasError()){
				metricICWeeklyResponse.getData().forEach((total, accountId) => {
					const metrics = accountWeeklyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalCollectionsCreatedWeekly = total;
						accountWeeklyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricConversionModel = new MetricConversionModel(null);
			const metricConversionWeeklyResponse =
            await metricConversionModel.readTotalPrevWeekByAccounts(weeklyAccounts);
			if (!metricConversionWeeklyResponse.hasError()){
				metricConversionWeeklyResponse.getData().forEach((total, accountId) => {
					const metrics = accountWeeklyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalConversionWeekly = total;
						accountWeeklyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricVideoPlayTimeModel = new MetricVideoPlayTimeModel(null);
			const metricVideoPlayTimeResponse =
            await metricVideoPlayTimeModel.readTotalPrevWeekByAccounts(weeklyAccounts);
			if (!metricVideoPlayTimeResponse.hasError()){
				metricVideoPlayTimeResponse.getData().forEach((totals, accountId) => {
					const metrics = accountWeeklyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalVideoPlaysWeekly = totals.totalVideoPlaysWeekly;
						metrics.totalPlaytimeWeekly = totals.totalPlayTimeWeekly;
						accountWeeklyMetricsMap.set(accountId, metrics);
					}
				});
			}

			return new ModelResponse(accountWeeklyMetricsMap, null);
		} catch (error) {
			return ModelResponse.fromUnknownError(error);
		}
	};

	private readAccountsPendingDailyUpdate = async (): Promise<ModelResponse<IAccount[]>> => {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfTodayTimestamp = startOfToday.getTime();

		const pipeline = [
			{
				$match: {
					$or: [
						{ dailyMetricsUpdatedAt: { $exists: false } },
						{ dailyMetricsUpdatedAt: null },
						{ dailyMetricsUpdatedAt: { $lt: startOfTodayTimestamp } }
					]
				}
			}
		];

		const documents: IAccount[] = await AccountDBModel.aggregate(pipeline).session(this.session);

		if (!documents || documents.length === 0) {
			return new ModelResponse<IAccount[]>(
				[],
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "No accounts pending daily updates are found.")
			);
		}
		return new ModelResponse<IAccount[]>(documents, null);
	};

	private fetchDailyMetrics = async (): Promise<ModelResponse<Map<string, AccountDailyMetrics>>> => {
		try {
			const dailyAccountsResponse = await this.readAccountsPendingDailyUpdate();
			dailyAccountsResponse.throwIfError();
			const dailyAccounts = dailyAccountsResponse.getData();

			const accountDailyMetricsMap: Map<string, AccountDailyMetrics> = new Map(dailyAccounts.map(account => [
				account._id.toString(), {
					dailyMetricsUpdatedAt: Date.now(),
					totalVideosCreatedDaily: 0,
					latestSnippetImpressionDateDaily: undefined,
					totalVideoImpressionsDaily: 0,
					totalCollectionsCreatedDaily: 0,
					totalVideoPlaysDaily: 0,
					totalPlaytimeDaily: 0,
					totalVideoCTAClicksDaily: 0,
					totalConversionDaily: 0
				}
			]));

			const metricIVModel = new MetricInteractiveVideoModel(null);
			const metricIVDailyResponse = await metricIVModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (!metricIVDailyResponse.hasError()){
				metricIVDailyResponse.getData().forEach((total, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalVideosCreatedDaily = total;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricICModel = new MetricInteractiveCollectionModel(null);
			const metricICDailyResponse = await metricICModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (!metricICDailyResponse.hasError()){
				metricICDailyResponse.getData().forEach((total, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalCollectionsCreatedDaily = total;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricImpressionModel = new MetricImpressionModel(null);
			const metricImpressionDailyMap = await metricImpressionModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (metricImpressionDailyMap){
				metricImpressionDailyMap.forEach((impressions, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.latestSnippetImpressionDateDaily =
						impressions.latestSnippetImpressionDateDaily.getTime();
						metrics.totalVideoImpressionsDaily = impressions.totalVideoImpressionsDaily;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricVideoClickModel = new MetricVideoClickModel(null);
			const metricVideoClickDailyResponse = await metricVideoClickModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (!metricVideoClickDailyResponse.hasError()){
				metricVideoClickDailyResponse.getData().forEach((total, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalVideoCTAClicksDaily = total;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricConversionModel = new MetricConversionModel(null);
			const metricConversionDailyResponse =
            await metricConversionModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (!metricConversionDailyResponse.hasError()){
				metricConversionDailyResponse.getData().forEach((total, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalConversionDaily = total;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			const metricVideoPlayTimeModel = new MetricVideoPlayTimeModel(null);
			const metricVideoPlayTimeResponse =
            await metricVideoPlayTimeModel.readTotalPrevDayByAccounts(dailyAccounts);
			if (!metricVideoPlayTimeResponse.hasError()){
				metricVideoPlayTimeResponse.getData().forEach((totals, accountId) => {
					const metrics = accountDailyMetricsMap.get(accountId);
					if (metrics) {
						metrics.totalVideoPlaysDaily = totals.totalVideoPlaysDaily;
						metrics.totalPlaytimeDaily = totals.totalPlayTimeDaily;
						accountDailyMetricsMap.set(accountId, metrics);
					}
				});
			}

			return new ModelResponse(accountDailyMetricsMap, null);
		} catch (error) {
			return ModelResponse.fromUnknownError(error);
		}
	};

	private combineDailyWeeklyMetrics =
		(dailyMetricsMap: Map<string, AccountDailyMetrics>, weeklyMetricsMap: Map<string, AccountWeeklyMetrics>):
		Map<string, AccountCombinedMetrics> => {
			const combinedMetricsMap: Map<string, AccountCombinedMetrics> = new Map();

			for (const [accountId, dailyMetrics] of dailyMetricsMap) {
				const weeklyMetrics = weeklyMetricsMap.get(accountId) || {};
				const combinedMetrics: AccountCombinedMetrics = {
					...dailyMetrics,
					...weeklyMetrics
				};
				combinedMetricsMap.set(accountId, combinedMetrics);
			}

			for (const [accountId, weeklyMetrics] of weeklyMetricsMap) {
				if (!combinedMetricsMap.has(accountId)) {
					const dailyMetrics = dailyMetricsMap.get(accountId) || {};
					const combinedMetrics: AccountCombinedMetrics = {
						...dailyMetrics,
						...weeklyMetrics
					};
					combinedMetricsMap.set(accountId, combinedMetrics);
				}
			}

			return combinedMetricsMap;
		};

	private updateTotalMetricsInBulk =
		async (accountMetricsMap: Map<string, AccountCombinedMetrics>): Promise<ModelResponse<void>> => {
			const bulkUpdateOperations = Array.from(accountMetricsMap).map(([accountId, metrics]) => ({
				updateOne: {
					filter: { _id: new mongoose.Types.ObjectId(accountId) },
					update: { $set: metrics }
				}
			}));

			if (bulkUpdateOperations.length > 0){
				const bulkWriteOptions = this.session ? { session: this.session } : undefined;
				await AccountDBModel.bulkWrite(bulkUpdateOperations, bulkWriteOptions);
			}
			return new ModelResponse(null, null);
		};

	public computeDailyWeeklyMetrics = async (): Promise<ModelResponse<void>> => {
		try {
			const accountsDailyMetricsResponse = await this.fetchDailyMetrics();
			const accountsDailyMetricsMap = accountsDailyMetricsResponse.hasError() ?
				new Map : accountsDailyMetricsResponse.getData();

			const accountsWeeklyMetricsResponse = await this.fetchWeeklyMetrics();

			const accountsWeeklyMetricsMap = accountsWeeklyMetricsResponse.hasError() ?
				new Map : accountsWeeklyMetricsResponse.getData();

			const accountsCombinedMetricsMap =
            this.combineDailyWeeklyMetrics(accountsDailyMetricsMap, accountsWeeklyMetricsMap);

			await this.updateTotalMetricsInBulk(accountsCombinedMetricsMap);
			return new ModelResponse(null, null);
		} catch (error) {
			return ModelResponse.fromUnknownError(error);
		}
	};

	public fetchAccountsPendingSync = async (): Promise<AsyncIterable<IAccount>> => {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfTodayTimestamp = startOfToday.getTime();

		const pipeline = [
			{
				$match: {
					$or: [
						{ dailySyncUpdatedAt: { $exists: false } },
						{ dailySyncUpdatedAt: null },
						{ dailySyncUpdatedAt: { $lt: startOfTodayTimestamp } }
					]
				}
			}
		];

		return AccountDBModel.aggregate(pipeline).cursor({ batchSize: 100 });
	};

	public updateDailySyncUpdatedAtInBulk = async (accounts: IAccount[]): Promise<ModelResponse<void>> => {
		const bulkWriteOperations = [];
		for (const account of accounts) {
			const bulkUpdateOperation = {
				updateOne: {
					filter: {
						_id: account._id
					},
					update: {
						$set: {
							dailySyncUpdatedAt: Date.now()
						}
					}
				}
			};
			bulkWriteOperations.push(bulkUpdateOperation);
		}

		if (bulkWriteOperations.length > 0){
			const bulkWriteOptions = this.session ? { session: this.session } : undefined;
			await AccountDBModel.bulkWrite(bulkWriteOperations, bulkWriteOptions);
		}
		return new ModelResponse(null, null);
	};



}

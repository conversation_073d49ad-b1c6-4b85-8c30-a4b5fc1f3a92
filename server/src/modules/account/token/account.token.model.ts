import jwt, { JwtPayload } from "jsonwebtoken";
import { IAccountToken } from "../account.interfaces";
import { AuthenticationModel } from "../../authentication/authentication.model";
import { getSecrets } from "../../secrets/secrets.model";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";

export class AccountTokenModel {
	public static verifyAccountTokenId (accountToken: IAccountToken, accountId: string): void {
		if (accountToken.account._id !== accountId) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"account token does not match account id");
		}
	}

	public static async verifyAndDecode (accountToken: string): Promise<IAccountToken> {
		const saltedHash = await this.createSaltedHash(accountToken);
		const decodedJWT = jwt.verify(accountToken, saltedHash);

		if (!decodedJWT) {
			throw new Error("Failed to verify account token");
		}

		return decodedJWT as IAccountToken;
	}

	private static async createSaltedHash (accountToken: string): Promise<string> {
		const decodedAccountToken = jwt.decode(accountToken) as JwtPayload;

		if (!decodedAccountToken?.userId) {
			throw new Error("Failed to retrieve userId from provided account token");
		}

		const authenticationId = decodedAccountToken.authenticationId;
		const authModel = new AuthenticationModel(null);
		const authDocument = await authModel.readOneById(authenticationId);

		const secrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		const saltedHash = `${gpSecretKey}${authDocument.salt}`;
		return saltedHash;
	}
}

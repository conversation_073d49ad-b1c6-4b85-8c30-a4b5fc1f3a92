import express, {
	Express,
	Request,
	Response
} from "express";
import { AccountSelfController } from "./account.self.controller";

import { decodeAccess } from "../../../middleware/decodeAccess.mw";

export class AccountSelfRouter {
	private controller: AccountSelfController = new AccountSelfController();
	private router = express.Router();

	constructor () {
		this.router.get(
			"/",
			[decodeAccess],
			(request: Request, response: Response) => {
				return this.controller.get(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/accounts/self", this.router);
	}
}

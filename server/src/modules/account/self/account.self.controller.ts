import {
	Request,
	Response
} from "express";
import { APIError } from "../../../utils/helpers/apiError";
import { IAccount } from "../account.interfaces";
import {
	APIErrorName,
	Permission
} from "../../../interfaces/apiTypes";
import { readAccounts } from "../../../services/mongodb/account.service";
import { AuthenticationModel } from "../../../modules/authentication/authentication.model";

export class AccountSelfController {
	async delete (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async get (request: Request, response: Response): Promise<Response> {
		return getAccountsSelfController(request, response);
	}

	async list (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async post (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async patch (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async put (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}
}

const getAccountsSelfController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing Authorization header");
		}

		if (!req.accessToken.userId) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Missing userId in access token");
		}

		// Read the authentication record from the database for the user defined in the access token
		const authModel = new AuthenticationModel(null);
		const authenticationRecord = await authModel.readOneById(req.accessToken.authenticationId);

		const accountIds: any = authenticationRecord.accounts.map((account) => account._id);

		const accountsDocuments: IAccount[] | null = await readAccounts({
			query: {
				_id: { $in: accountIds }
			},
			path: req.url,
			permissions: [Permission.ALL]
		});

		if (!accountsDocuments) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "the accounts documents do not exist");
		}

		return res.send({ accounts: accountsDocuments });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

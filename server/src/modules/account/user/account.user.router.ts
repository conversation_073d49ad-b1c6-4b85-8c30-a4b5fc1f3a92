import express, {
	Express,
	Request,
	Response
} from "express";
import { AccountUsersController } from "./account.user.controller";
import { postSignupSchema } from "../account.validator";
import { decodeAccess } from "../../../middleware/decodeAccess.mw";
import { isSchemaValid } from "../../../middleware/isSchemaValid.mw";
import { requireSuperWritePermission } from "../../../middleware/requireSuperWritePermission.mw";

export class AccountUsersRouter {
	private controller: AccountUsersController = new AccountUsersController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/post-signup",
			[
				express.json({ limit: "2MB" }),
				decodeAccess,
				isSchemaValid(postSignupSchema.data)
			],
			(request: Request, response: Response) => {
				return this.controller.post(request, response);
			}
		);

		this.router.delete(
			"/:userId",
			[
				decodeAccess,
				requireSuperWritePermission
			],
			(request: Request, response: Response) => {
				return this.controller.delete(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/accounts/users", this.router);
	}
}

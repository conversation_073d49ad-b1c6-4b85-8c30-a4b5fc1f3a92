import Stripe from "stripe";
import {
	SubscriptionDefaults,
	Subscription
} from "./subscription.interfaces";
import { getSecrets } from "../secrets/secrets.model";
import { StripeSubscriptionModel } from "../stripe/subscription/subscription.model";
import { StripeInvoicesModel } from "../stripe/invoice/invoice.model";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";

export class SubscriptionModel {
	public async fromStripeSubscription (stripeSubscriptionId: string): Promise<Subscription> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripeSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);

		if (!stripeSubscription) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Subscription not found").setDetail({
				"function": "SubscriptionModel.fromStripeSubscription",
				"stripeSubscriptionId": stripeSubscriptionId
			});
		}

		const stripeInvoicesModel = new StripeInvoicesModel();
		const invoices = await stripeInvoicesModel.getInvoices(stripeSubscription.customer as string);

		const firstPaymentDate = await this.getFirstPaymentDate(invoices);
		const lastPaymentDate = await this.getLastPaymentDate(invoices);

		const customer = await stripe.customers.retrieve(stripeSubscription.customer as string);

		if (customer.deleted === true) {
			throw new Error(
				`customer ${stripeSubscription.customer} has been deleted when attempting create subscription data`
			);
		}

		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const clockTime = await stripeSubscriptionModel.getClockTime(stripeSubscription);

		const trialStartDate = stripeSubscription.trial_start ?
			this.normalizeToMilliseconds(stripeSubscription.trial_start) : null;

		const trialEndDate = stripeSubscription.trial_end ?
			this.normalizeToMilliseconds(stripeSubscription.trial_end) : null;

		const nextBillingDate = stripeSubscription.current_period_end ?
			this.normalizeToMilliseconds(stripeSubscription.current_period_end) : null;

		let pendingProductId: string | null = null;
		let pendingPriceId: string | null = null;
		let pendingChangeDate: number | null = null;

		if (stripeSubscription.schedule) {
			const schedule = await stripe.subscriptionSchedules.retrieve(stripeSubscription.schedule as string);
			if (schedule.phases && schedule.phases.length > 1) {
				const nextPhase = schedule.phases[1];
				if (clockTime < nextPhase.start_date) {
					if (nextPhase.items && nextPhase.items.length > 0) {
						pendingPriceId = nextPhase.items[0].price as string;
						const price = await stripe.prices.retrieve(pendingPriceId);
						pendingProductId = price.product as string;
						pendingChangeDate = this.normalizeToMilliseconds(nextPhase.start_date);
					}
				}
			}
		}

		const currentPrice = stripeSubscription.items.data[0].price;
		const currentProductId = currentPrice.product as string;
		const currentProduct = await stripe.products.retrieve(currentProductId);
		const type = currentProduct.metadata?.type ?? SubscriptionDefaults.type;

		const trialActive = stripeSubscription.trial_end !== null &&
			stripeSubscription.trial_end !== undefined &&
			this.normalizeToMilliseconds(stripeSubscription.trial_end) > this.normalizeToMilliseconds(clockTime);

		const hasPaymentMethod = customer.invoice_settings.default_payment_method !== null;

		const trialDaysTotal = this.getTrialDaysTotal(trialStartDate, trialEndDate);

		const subscription: Subscription = {
			stripePriceId: stripeSubscription.items.data[0].price.id,
			stripeProductId: currentProductId,
			stripeSubscriptionId: stripeSubscription.id,
			hideVanityBranding: (stripeSubscription?.metadata?.hideVanityBranding === "true"),
			type: type,
			enableConversionMetrics: (stripeSubscription?.metadata?.enableConversionMetrics === "true"),
			enableEngagementMetrics: (stripeSubscription?.metadata?.enableEngagementMetrics === "true"),
			maxClicksMetricPerMonth: Number(
				stripeSubscription?.metadata?.maxClicksMetricPerMonth ??
				SubscriptionDefaults.maxClicksMetricPerMonth
			),
			maxImpressionsPerCycle: Number(
				stripeSubscription?.metadata?.maxImpressionsPerCycle ??
				SubscriptionDefaults.maxImpressionsPerCycle
			),
			maxInteractiveCollectionLimit: Number(
				stripeSubscription?.metadata?.maxInteractiveCollectionLimit ??
				SubscriptionDefaults.maxInteractiveCollectionLimit
			),
			maxInteractiveVideoLimit: Number(
				stripeSubscription?.metadata?.maxInteractiveVideoLimit ??
				SubscriptionDefaults.maxInteractiveVideoLimit
			),
			maxPlaysMetricPerMonth: Number(
				stripeSubscription?.metadata?.maxPlaysMetricPerMonth ??
				SubscriptionDefaults.maxPlaysMetricPerMonth
			),
			maxUserLimit: Number(
				stripeSubscription?.metadata?.maxUserLimit ??
				SubscriptionDefaults.maxUserLimit
			),
			maxVideoProductLinksLimit: Number(
				stripeSubscription?.metadata?.maxVideoProductLinksLimit ??
				SubscriptionDefaults.maxVideoProductLinksLimit
			),
			trialActive: trialActive,
			firstPaymentDate: firstPaymentDate,
			lastPaymentDate: lastPaymentDate,
			hasPaymentMethod: hasPaymentMethod,
			allowLandscape: (stripeSubscription?.metadata?.allowLandscape === "true"),
			allowCTALead: (stripeSubscription?.metadata?.allowCTALead === "true"),
			allowThemes: (stripeSubscription?.metadata?.allowThemes === "true"),
			allowSharing: (stripeSubscription?.metadata?.allowSharing === "true"),
			trialAvailable: (customer.metadata?.trialAvailable !== "false"),
			trialStartDate: trialStartDate,
			trialEndDate: trialEndDate,
			trialDaysTotal: trialDaysTotal,
			pendingChangeDate: pendingChangeDate,
			pendingChangePriceId: pendingPriceId,
			pendingChangeProductId: pendingProductId,
			nextBillingDate: nextBillingDate,
			clockTime: stripeSubscription.test_clock ? this.normalizeToMilliseconds(clockTime) : null,
			price: stripeSubscription.items.data[0].price.unit_amount ?? 0
		};

		return subscription;
	}

	public async getFirstPaymentDate (invoices: Stripe.Invoice[]): Promise<number | null> {
		let firstPaymentDate = null;
		for (const invoice of invoices) {
			if (invoice.amount_paid > 0) {
				if (firstPaymentDate === null || invoice.created < firstPaymentDate) {
					firstPaymentDate = invoice.created;
				}
			}
		}

		if (firstPaymentDate !== null) {
			firstPaymentDate = this.normalizeToMilliseconds(firstPaymentDate);
		}

		return firstPaymentDate;
	}

	public async getLastPaymentDate (invoices: Stripe.Invoice[]): Promise<number | null> {
		let lastPaymentDate = null;
		for (const invoice of invoices) {
			if (invoice.amount_paid > 0) {
				if (lastPaymentDate === null || invoice.created > lastPaymentDate) {
					lastPaymentDate = invoice.created;
				}
			}
		}

		if (lastPaymentDate !== null) {
			lastPaymentDate = this.normalizeToMilliseconds(lastPaymentDate);
		}

		return lastPaymentDate;
	}

	private getTrialDaysTotal (trialStartDate: number | null, trialEndDate: number | null): number {
		if (trialStartDate === null || trialEndDate === null) {
			return 0;
		}

		const trialDaysTotal = Math.floor((trialEndDate - trialStartDate) / (1000 * 60 * 60 * 24));
		return trialDaysTotal;
	}

	private normalizeToMilliseconds(timestamp: number): number {
		const millisecondsThreshold = 10000000000;
		const secondToMillisecondMultiplier = 1000;
		if (timestamp < millisecondsThreshold) {
			timestamp *= secondToMillisecondMultiplier;
		}
		return timestamp;
	}
}

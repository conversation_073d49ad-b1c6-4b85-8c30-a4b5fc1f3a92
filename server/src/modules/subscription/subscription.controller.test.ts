/* eslint-disable max-lines-per-function */
import TestHelper from "../../__tests__/mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import Stripe from "../../__mocks__/stripe";
import { StripePriceModel } from "../stripe/price/price.model";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../signup/signup.interfaces";

describe("/api/subscriptions", () => {
	let expressApp: express.Express;
	let subscriptionId: string;
	let accessToken: string;
	let accountToken: string;
	let accountId: string;
	const stripe = new Stripe("");

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const userPayload = {
			firstName: "Johnny",
			lastName: "Subscription",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "Subscription Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(userPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		subscriptionId = account.subscription.stripeSubscriptionId;

		accountToken = await testHelper.getAccountToken(accountId, accessToken);
	});

	it("PUT PRO price.unit_amount > 0", async () => {
		// in order for the change in subscription to work there needs to
		// be a default payment method.
		const paymentParams = {
			type: "card",
			card: {
				brand: "Visa",
				last4: "4242"
			}
		};
		const payment = await stripe.paymentMethods.create(paymentParams);

		// attach payment method
		const res = await supertest(expressApp)
			.post("/api/stripe/payment")
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send({
				paymentMethodId: payment.id
			});

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const pricePro = await getAnyPaidPrice();

		const resSubscriptions = await supertest(expressApp)
			.put("/api/subscriptions/" + subscriptionId)
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.accept("json")
			.send({
				priceId: pricePro.id
			});

		expect(resSubscriptions.statusCode).toBe(200);
		expect(resSubscriptions).toHaveProperty("body");

		// There is nothing updated in the database for this event.

		// for now just test stripe mock
		const updatedSubscription = await stripe.subscriptions.retrieve(subscriptionId);
		const updatedPrice = updatedSubscription.items.data[0].price;
		expect(updatedPrice.unit_amount).toBe(pricePro.unit_amount);
		expect(updatedPrice.id).toBe(pricePro.id);
		expect(updatedPrice.product).toBe(pricePro.product);
	});

	it("PUT Free price.unit_amount === 0", async () => {
		// update stripe mock subscripiton to be paid
		const pricePro = await getAnyPaidPrice();
		const subscripton = await stripe.subscriptions.retrieve(subscriptionId);
		subscripton.items.data = [{
			price: pricePro
		}];

		const stripePriceModel = new StripePriceModel();
		const priceFree = await stripePriceModel.getFreePrice();

		const resSubscriptions = await supertest(expressApp)
			.put("/api/subscriptions/" + subscriptionId)
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.accept("json")
			.send({
				priceId: priceFree.id
			});

		expect(resSubscriptions.statusCode).toBe(200);
		expect(resSubscriptions).toHaveProperty("body");

		// Because the Price requested above is free
		// a update is made to a stripe.subscriptionschedule.
		// The price change requested above does not happen until the
		// end of the current subscription defined by the phases in
		// the subscription schedule.

		// subscriptionschedule is not implemented in the stripe mock.
	});

	const getAnyPaidPrice = async (): Promise<any> => {
		const prices = await stripe.prices.list();
		const pricePaid = prices.data.find((price: { unit_amount: number; metadata: { gp: string; }; }) =>
			price.unit_amount > 0 && price.metadata.gp?.toLocaleLowerCase().trim() === "true"
		);

		return pricePaid;
	};
});

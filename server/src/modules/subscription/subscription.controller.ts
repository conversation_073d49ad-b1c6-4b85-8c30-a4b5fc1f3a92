import express, {
	type Request,
	type Response
} from "express";
import { StripeSubscriptionModel } from "../stripe/subscription/subscription.model";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { SubscriptionPutJoi } from "./subscription.joi";
import { AccountModel } from "../account/account.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { SubscriptionModel } from "./subscription.model";

export class SubscriptionController extends Controller {
	constructor() {
		super();

		this.router.put(
			"/:id",
			express.json(),
			async (request: Request, response: Response) => {
				try {
					const validatedRequest = await SubscriptionPutJoi.validateAsync({
						apiVersion: request.headers["x-api-version"],
						accountToken: request.headers["x-account-token"],
						accessToken: request.headers.authorization,
						subscriptionId: request.params.id,
						priceId: request.body.priceId
					});

					await this.verifyAccessToken(validatedRequest.accessToken);
					const accountToken = await this.verifyAccountToken(validatedRequest.accountToken);
					await this.verifySubscriptionId(accountToken.account._id, validatedRequest.subscriptionId);

					const stripeSubscriptionModel = new StripeSubscriptionModel();
					const stripeSubscription = await stripeSubscriptionModel.updateSubscription(
						validatedRequest.subscriptionId,
						validatedRequest.priceId
					);

					const subscriptionModel = new SubscriptionModel();
					const subscription = await subscriptionModel.fromStripeSubscription(stripeSubscription.id);

					return response.json(subscription);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async verifySubscriptionId (accountId: string, subscriptionId: string): Promise<void> {
		const accountModel = new AccountModel(null);
		const account = await accountModel.readOneById(accountId);

		if (account.subscription.stripeSubscriptionId !== subscriptionId) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "invalid subscription id");
		}
	}
}

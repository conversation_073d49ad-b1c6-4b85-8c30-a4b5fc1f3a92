export interface Subscription {
	allowLandscape: boolean;
	allowCTALead: boolean;
	allowThemes: boolean;
	allowSharing: boolean;
	stripeSubscriptionId: string;
	stripeProductId: string;
	stripePriceId: string;
	hideVanityBranding: boolean;
	type: string;
	enableConversionMetrics: boolean;
	enableEngagementMetrics: boolean;
	maxClicksMetricPerMonth: number;
	maxImpressionsPerCycle: number;
	maxInteractiveCollectionLimit: number;
	maxInteractiveVideoLimit: number;
	maxPlaysMetricPerMonth: number;
	maxVideoProductLinksLimit: number;
	maxUserLimit: number;
	hasPaymentMethod: boolean;
	firstPaymentDate: number | null;
	lastPaymentDate: number | null;
	trialActive: boolean;
	trialAvailable: boolean;
	trialStartDate: number | null;
	trialEndDate: number | null;
	trialDaysTotal: number;
	pendingChangeDate: number | null;
	pendingChangePriceId: string | null;
	pendingChangeProductId: string | null;
	nextBillingDate: number | null;
	clockTime: number | null;
	price: number;
}

export const SubscriptionDefaults = {
	hideVanityBranding: false,
	type: "basic",
	enableConversionMetrics: false,
	enableEngagementMetrics: false,
	maxClicksMetricPerMonth: 50000,
	maxImpressionsPerCycle: 5000,
	maxInteractiveCollectionLimit: 500,
	maxInteractiveVideoLimit: 500,
	maxPlaysMetricPerMonth: 50000,
	maxVideoProductLinksLimit: 10,
	trialActive: false,
	hasPaymentMethod: false,
	firstPaymentDate: null,
	lastPaymentDate: null,
	maxUserLimit: 10
};

export interface SubscriptionPutRequest {
	subscriptionId: string;
	priceId: string;
	apiVersion: string;
	accessToken: string;
	accountToken: string;
}

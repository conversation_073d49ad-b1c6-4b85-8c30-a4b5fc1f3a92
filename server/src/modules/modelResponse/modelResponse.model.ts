import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";

export class ModelResponse<T> {
	private data: T | null;
	private error: APIError | null;

	constructor (data: T | null, error: APIError | null = null) {
		this.data = data;
		this.error = error;
	}

	public static fromUnknownError<T> (error: unknown): ModelResponse<T> {
		if (error instanceof APIError) {
			return new ModelResponse<T>(
				null,
				error
			);
		}

		return new ModelResponse<T>(
			null,
			new APIError(APIErrorName.E_SERVICE_FAILED, error)
		);
	}

	getData (): T {
		if (this.data === null) {
			throw new Error(`attempted to use null ${typeof this.data} data`);
		}

		return this.data;
	}

	getError (): APIError {
		if (this.error === null) {
			throw new Error(`attempted to use null ${typeof this.error} error`);
		}

		return this.error;
	}

	hasError (): boolean {
		return this.error !== null;
	}

	throwIfError (): void {
		if (this.error !== null) {
			throw this.error;
		}
	}
}

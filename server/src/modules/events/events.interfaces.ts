import { BaseRequest } from "../base/base.interfaces";

export interface IPostEventsPayload extends BaseRequest {
	eventName: string;
	appId: string;
	accountId: string;
	playId: string;
	appSessionId: string;
	user: {
		sessionId: string;
		fingerprint: string;
		dbUserId: string;
	};
	device: {
		type: string;
		osName: string;
		osVersion: string;
	};
	installation: {
		url: string;
		success: boolean;
		error: boolean;
	};
	eventDimensions: {
		browserCurrentURL: string;
		browserReferrerURL: string;
		browserName: string;
		browserVersion: string;
		browserLocale: string;
		browserPixelWidth: number;
		browserPixelHeight: number;
		browserUserAgent: string;
		collectionId: string;
		videoId: string;
		videoSecondsLength: number;
		videoSecondsPosition: number;
		productActionedURL: string;
		videoPercentagePlayed: number;
		previousVideoPercentagePlayed: number;
		productId: string;
		videoState: "unloaded" | "loaded";
		endedMethod: "next" | "previous" | "exit" | "loop";
		videoPlayStatus: "playing" | "stopped";
	};
	snippet: {
		type: string;
	};
	isShareMode: boolean;
}

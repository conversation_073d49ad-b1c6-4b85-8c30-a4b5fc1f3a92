import Joi from "joi";
import { IPostEventsPayload } from "./events.interfaces";
import {
	EventAppIds,
	EventNames,
	EventSnippetTypes
} from "../../modules/events/events.enums";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";

export const PostEventJoi = Joi.object<IPostEventsPayload>({
	eventName: Joi.string().valid(...Object.values(EventNames)).required(),
	appId: Joi.string().valid(...Object.values(EventAppIds)).required(),
	accountId: Joi.string().hex().length(24),
	playId: Joi.string().hex().length(24),
	appSessionId: Joi.string().hex().length(24),
	user: Joi.object().keys({
		sessionId: Joi.string().hex().length(24).required(),
		fingerprint: Joi.string().min(1),
		dbUserId: Joi.string().hex().length(24)
	}).min(1),
	device: Joi.object().keys({
		type: Joi.string().default("unknown"),
		osName: Joi.string().default("unknown"),
		osVersion: Joi.string().default("unknown")
	}),
	installation: Joi.object().keys({
		url: Joi.string().uri().required(),
		success: Joi.boolean().required(),
		error: Joi.boolean().required()
	}).min(3)
		.when("eventName", {
			is: EventNames.INSTALLATION_CHECK,
			then: Joi.required(),
			otherwise: Joi.forbidden()
		})
		.custom((doc, helpers) => {
			if (doc.success && doc.error) {
				return helpers.error("any.invalid");
			}
			return doc;
		}),
	eventDimensions: Joi.object().keys({
		browserCurrentURL: Joi.string(),
		browserReferrerURL: Joi.string(),
		browserName: Joi.string().min(1),
		browserVersion: Joi.string().min(1),
		browserLocale: Joi.string().min(1),
		browserPixelWidth: Joi.number().integer(),
		browserPixelHeight: Joi.number().integer(),
		browserUserAgent: Joi.string().min(1),
		collectionId: Joi.string().hex().length(24),
		videoId: Joi.string().hex().length(24),
		videoSecondsLength: Joi.number().integer().min(1),
		videoSecondsPosition: Joi.number().integer(),
		videoPercentagePlayed: Joi.number().integer(),
		previousVideoPercentagePlayed: Joi.number().integer(),
		productActionedURL: Joi.string().uri(),
		productId: Joi.string().hex().length(24),
		videoState: Joi.string().valid("unloaded", "loaded"),
		endedMethod: Joi.string().valid("next", "previous", "exit", "loop"),
		videoPlayStatus: Joi.string().valid("playing", "stopped")
	}).min(1),
	snippet: Joi.object().keys({
		type: Joi.string().valid(...Object.values(EventSnippetTypes)).required()
	}).min(1),
	isShareMode: Joi.boolean().optional()
}).error(errors => {
	const messages = errors.map(err => err.message).join(", ");
	return new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, messages);
});

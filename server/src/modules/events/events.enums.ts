export enum EventNames {
	CREATE_ACCOUNT_IMPRESSION = "create_account_impression",
	ACCEPT_TERMS_PRESS = "accept_terms_press",
	CREATE_ACCOUNT_PRESS = "create_account_press",
	VERIFY_ACCOUNT_IMPRESSION = "verify_account_impression",
	VERIFY_CONTINUE_PRESS = "verify_continue_press",
	COPY_SCRIPT_PRESS = "copy_script_press",
	CONTINUE_SCRIPT_PRESS = "continue_script_press",
	INSTALLATION_CHECK = "installation_check",
	SKIP_SCRIPT_PRESS = "skip_script_press",
	CREATE_VIDEO_PRESS = "create_video_press",
	CREATE_VIDEO_MAIN_PRESS = "create_video_main_press",
	GENERATE_EMBED_PRESS = "generate_embed_press",
	COPY_EMBED_PRESS = "copy_embed_press",
	THUMBNAIL_CTA_IMPRESSION = "thumbnailCTA_impression",
	THUMBNAIL_CTA_PRESS = "thumbnailCTA_press",
	SHOPPABLE_VIDEO_EXIT = "shoppable_video_exit",
	FEATURED_LINK_PRESS = "featured_link_press",
	FEATURED_PHONE_PRESS = "featured_phone_press",
	FEATURED_EMAIL_SUBMIT = "featured_email_submit",
	CONTINUE_LINK_PRESS = "continue_link_press",
	SNIPPET_IMPRESSION = "snippet_impression",
	HOVER_PLAY_START = "hover_play_start",
	VIDEO_PLAY_POSITION = "video_play_position",
	SNIPPET_VIEWABLE_IMPRESSION = "snippet_viewable_impression",
	VIDEO_SHARE_LINK_COPY = "video_share_link_copy",
	COLLECTION_SHARE_LINK_COPY = "collection_share_link_copy",
	VIDEO_EMBED_COPY_THUMBNAIL = "video_embed_copy_thumbnail"
}

export enum EventAppIds {
	GP_PLAYER = "gp-player",
	GP_ADMIN = "gp-admin",
	GP_SERVER = "gp-server"
}

export enum EventSnippetTypes {
	CAROUSEL = "carousel",
	WIDGET = "widget",
	ONSITE = "onsite",
	PLAYER = "player"
}

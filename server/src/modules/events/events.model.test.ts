/* eslint-disable max-lines */
import mongoose from "mongoose";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import { randomBytes } from "crypto";
import { EventModel } from "./events.model";
import { MetricVideoPlayTimeDBModel } from "../../modules/metricVideoPlayTime/metricVideoPlayTimeDB.model";
import { IPostEventsPayload } from "./events.interfaces";
import { MetricImpressionDBModel } from "../metricImpression/metricImpressionDB.model";
import { MetricVisibleImpressionDBModel } from "../metricVisibleImpression/metricVisibleImpression.db.model";
import { AccountModel } from "../account/account.model";
import {
	EventAppIds,
	EventNames,
	EventSnippetTypes
} from "./events.enums";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";
import {
	IShoppableVideoProduct,
	VideoDisplayModeEnum,
	IShoppableVideo
} from "../interactiveVideo/interactiveVideo.interface";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { IAccount } from "../account/account.interfaces";
import { EventBuffer } from "../eventBuffer/eventBuffer.interfaces";
import { EventBufferModel } from "../eventBuffer/eventBuffer.model";
import { IVideo } from "../video/video.interfaces";
import { createVideo } from "../../services/mongodb/video.service";
import supertest from "supertest";
import { IShoppableCollection } from "../interactiveCollection/interactiveCollection.interface";
import { IMetricUserEngagement } from "../metricUserEnagement/metricUserEngagement.interfaces";
import { updateShoppableCollection } from "../../services/mongodb/shoppableCollection.service";
import { MetricUserEngagementModel } from "../metricUserEnagement/metricUserEnagement.model";
import { APIError } from "../../utils/helpers/apiError";
import { MetricVideoEngagementDBModel } from "../metricVideoEngagement/metricVideoEngagement.db.model";

let expressApp: express.Express;
let testHelper: TestHelper;
let accountId: string;
let accessToken: string;
let accountToken: string;
let collectionId: string;
let sessionId: string;
let interactiveVideo: IShoppableVideo;

const payloadMock = {
	eventName: "",
	appId: EventAppIds.GP_PLAYER,
	accountId: "",
	user: {
		sessionId: "64395eb0167ce5666bc80b04",
		fingerprint: "something",
		dbUserId: ""
	},
	device: {
		type: "Macintosh",
		osName: "MacOS",
		osVersion: "13.2.1"
	},
	eventDimensions: {
		browserCurrentURL: "http://domain.tld/path1",
		browserReferrerURL: "http://domain.tld/path2",
		browserName: "Chrome",
		browserVersion: "112.0.5615.49 (Official Build) (x86_64)",
		browserLocale: "en_US",
		browserPixelWidth: 1200,
		browserPixelHeight: 900,
		browserUserAgent: "something",
		videoId: "",
		productId: "",
		collectionId: "",
		videoSecondsLength: 10,
		videoSecondsPosition: 7,
		endedMethod: "previous",
		videoState: "loaded",
		videoPlayStatus: "stopped"
	}
};

describe("White Box Testing | Events Model", () => {
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResult = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResult.accessToken;
		const account = await testHelper.getAccount(signupResult.accessToken);
		accountId = account._id.toString();
		accountToken = await testHelper.getAccountToken(accountId, accessToken);
		collectionId = account.defaultCollectionId.toString();
		sessionId = randomBytes(12).toString("hex");

		const video: IVideo = await testHelper.createVideo(account._id.toString());
		const videoId = video._id.toString();
		interactiveVideo = await testHelper.createShoppableVideo(videoId, accountToken, accessToken);
		payloadMock.accountId = accountId;
		payloadMock.user.dbUserId = account.ownerUserId.toString();
		payloadMock.eventDimensions.videoId = randomBytes(12).toString("hex");
		payloadMock.eventDimensions.productId = randomBytes(12).toString("hex");
		payloadMock.eventDimensions.collectionId = collectionId;
		payloadMock.eventDimensions.videoId = interactiveVideo._id.toString();
	});

	beforeEach(async () => {
		const accountModel = new AccountModel(null);
		await accountModel.resetUsage(accountId);
	});

	it("should create a new metric_impressions document with correct collectionShare Property.",
		testMetricImpressionsCollectionShare
	);
	it("should create a new metric_impressions document with correct videoShare Property.",
		testMetricImpressionsVideoShare
	);
	it("should create a new metric_impressions document with correct collectionId Property.",
		testMetricImpressionsCollection
	);
	it("should create a new metric_impressions document with correct videoId Property.",
		testMetricImpressionsVideo
	);
	it("should create a new metric_visible_impressions document with correct collectionId Property.",
		testMetricVisibleImpressionsCollection
	);
	it("should create a new metric_visible_impressions document with correct videoId Property.",
		testMetricVisibleImpressionsVideo
	);
	it("should create a new metric_video_play_time document with correct collectionShare Property.",
		testMetricVideoPlayTimeCollectionShare
	);
	it("should create a new metric_video_play_time document with correct videoShare Property.",
		testMetricVideoPlayTimeVideoShare
	);
	it("should increment impression count by 1 with two parallel requests and fail on max limit reached",
		testIncrementOnceAndFailWhenMaxImpressionsReached
	);
	it("should increment impression count by 2 with two parallel requests when below max limit",
		testIncrementTwiceWithParallelRequests
	);
	it("should increment product link click counts across multiple products in a video with parallel requests",
		testAsyncIncrementProductLinkClickCount
	);
	it("should increment product link click counts across multiple products in a video and sync correctly",
		testSyncIncrementImpressionCount
	);
	it("should process user engagement for collection",
		testProcessUserEngagementForCollection
	);
	it("should record VIDEO_PLAY_POSITION event given shareMode is true",
		testVideoPlayPositionEventWithShareMode
	);
	it("should record SNIPPET_IMPRESSION event given shareMode is true",
		testSnippetImpressionEventWithShareMode
	);
	it("should record VIDEO_EMBED_COPY_THUMBNAIL event",
		testVideoEmbedCopyThumbnailEvent
	);
	it("should record COLLECTION_SHARE_LINK_COPY event",
		testCollectionShareLinkCopyEvent
	);
	it("should record VIDEO_SHARE_LINK_COPY event",
		testVideoShareLinkCopyEvent
	);
	it("should pass for device object without data",
		testDeviceObjectWithoutData
	);
	it("should pass when snippet object is supplied with valid type",
		testSnippetObjectWithValidType
	);
	it("should pass when createMetricImpression is at it's plan limit",
		testCreateMetricImpressionAtPlanLimit
	);
	it("should process FEATURED_LINK_PRESS event correctly",
		testProcessFeaturedLinkPress
	);
	it("should process VIDEO_PLAY_POSITION event correctly",
		testProcessVideoPlayPosition
	);
	it("should process THUMBNAIL_CTA_PRESS event correctly",
		testProcessThumbnailCtaPress
	);
	it("should fail if no event name is provided",
		testFailIfNoEventName
	);
	it("should fail if no app id is provided",
		testFailNoAppId
	);
	it("should fail if an invalid event name is provided",
		testFailInvalidEventName
	);
	it("should fail if an invalid app id is provided",
		testFailInvalidAppId
	);
	it("should fail if a user object is provided without data",
		testFailUserObjectWithoutData
	);
	it("should fail if event dimensions object is provided without data",
		testFailEventDimensionsObjectWithoutData
	);
	it("should fail if the account is invalid",
		testFailEventAccountInvalid
	);
	it("should fail if the snippet object has no data",
		testFailSnippetObjectWithoutData
	);
	it("should fail if the snippet object has an invalid type",
		testFailSnippetObjectInvalidType
	);
	it("should create a new metric_video_play_time document with valid current time",
		testMetricVideoPlayTimeVideoValidCurrentTime);

	it("should not create a new metric_video_play_time document with invalid current time",
		testMetricVideoPlayTimeVideoInvalidCurrentTime);
});

async function testMetricImpressionsCollectionShare(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			collectionId: collectionId
		},
		isShareMode: true
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestImpressionDocument =
	await MetricImpressionDBModel.findOne(
		{ collectionShare: new mongoose.Types.ObjectId(payloadMock.eventDimensions.collectionId) }
	).sort({ createdAt: -1 });
	expect(latestImpressionDocument).not.toBeNull();
	expect(latestImpressionDocument?.collectionShare?.toString()).toBe(payloadMock.eventDimensions.collectionId);
	expect(latestImpressionDocument?.videoShare).toBe(undefined);
}

async function testMetricImpressionsVideoShare(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoId: randomBytes(12).toString("hex")
		},
		isShareMode: true
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestImpressionDocument =
	await MetricImpressionDBModel.findOne(
		{ videoShare: new mongoose.Types.ObjectId(payloadMock.eventDimensions.videoId) }
	).sort({ createdAt: -1 });
	expect(latestImpressionDocument).not.toBeNull();
	expect(latestImpressionDocument?.videoShare?.toString()).toBe(payloadMock.eventDimensions.videoId);
	expect(latestImpressionDocument?.collectionShare).toBe(undefined);
}

async function testMetricImpressionsCollection(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			collectionId: collectionId
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestImpressionDocument =
	await MetricImpressionDBModel.findOne(
		{ collectionId: new mongoose.Types.ObjectId(payloadMock.eventDimensions.collectionId) }
	).sort({ createdAt: -1 });
	expect(latestImpressionDocument).not.toBeNull();
	expect(latestImpressionDocument?.collectionId?.toString()).toBe(payloadMock.eventDimensions.collectionId);
	expect(latestImpressionDocument?.videoId).toBe(undefined);
	expect(latestImpressionDocument?.videoShare).toBe(undefined);
	expect(latestImpressionDocument?.collectionShare).toBe(undefined);
}

async function testMetricImpressionsVideo(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoId: randomBytes(12).toString("hex")
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestImpressionDocument =
	await MetricImpressionDBModel.findOne(
		{ videoId: new mongoose.Types.ObjectId(payloadMock.eventDimensions.videoId) }
	).sort({ createdAt: -1 });
	expect(latestImpressionDocument).not.toBeNull();
	expect(latestImpressionDocument?.videoId?.toString()).toBe(payloadMock.eventDimensions.videoId);
	expect(latestImpressionDocument?.collectionId).toBe(undefined);
	expect(latestImpressionDocument?.videoShare).toBe(undefined);
	expect(latestImpressionDocument?.collectionShare).toBe(undefined);
}

async function testMetricVisibleImpressionsCollection(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			collectionId: collectionId
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetVisibleImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestVisibleImpressionDocument =
	await MetricVisibleImpressionDBModel.findOne(
		{ collectionId: new mongoose.Types.ObjectId(payloadMock.eventDimensions.collectionId) }
	).sort({ createdAt: -1 });
	expect(latestVisibleImpressionDocument).not.toBeNull();
	expect(latestVisibleImpressionDocument?.collectionId?.toString()).toBe(payloadMock.eventDimensions.collectionId);
	expect(latestVisibleImpressionDocument?.videoId).toBe(undefined);
}

async function testMetricVisibleImpressionsVideo(): Promise<void> {
	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoId: randomBytes(12).toString("hex")
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	await eventModel.processSnippetVisibleImpression(payloadMock as IPostEventsPayload, account, new Date());

	const latestVisibleImpressionDocument =
	await MetricVisibleImpressionDBModel.findOne(
		{ videoId: new mongoose.Types.ObjectId(payloadMock.eventDimensions.videoId) }
	).sort({ createdAt: -1 });
	expect(latestVisibleImpressionDocument).not.toBeNull();
	expect(latestVisibleImpressionDocument?.videoId?.toString()).toBe(payloadMock.eventDimensions.videoId);
	expect(latestVisibleImpressionDocument?.collectionId).toBe(undefined);
}

async function testMetricVideoPlayTimeCollectionShare(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);
	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test-play-time",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoSecondsLength: 21,
			videoSecondsPosition: 15,
			videoPercentagePlayed: 71,
			videoState: "loaded",
			videoPlayStatus: "stopped",
			endedMethod: "next",
			collectionId: collectionId,
			videoId: videoDocument._id.toString()
		},
		isShareMode: true
	};



	const eventModel = new EventModel(null);
	await eventModel.processVideoPosition(payloadMock as IPostEventsPayload, account, new Date());

	const latestVideoPlayTimeDocument =
	await MetricVideoPlayTimeDBModel.findOne(
		{ collectionShare: new mongoose.Types.ObjectId(payloadMock.eventDimensions.collectionId) }
	).sort({ createdAt: -1 });
	expect(latestVideoPlayTimeDocument).not.toBeNull();
	expect(latestVideoPlayTimeDocument?.collectionShare?.toString()).toBe(payloadMock.eventDimensions.collectionId);
	expect(latestVideoPlayTimeDocument?.videoShare).toBe(undefined);
}

async function testMetricVideoPlayTimeVideoShare(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);
	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test-play-time-video-share",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoSecondsLength: 21,
			videoSecondsPosition: 15,
			videoPercentagePlayed: 71,
			videoState: "loaded",
			videoPlayStatus: "stopped",
			endedMethod: "next",
			videoId: videoDocument._id.toString()
		},
		isShareMode: true
	};



	const eventModel = new EventModel(null);
	await eventModel.processVideoPosition(payloadMock as IPostEventsPayload, account, new Date());

	const latestVideoPlayTimeDocument =
	await MetricVideoPlayTimeDBModel.findOne(
		{ videoShare: new mongoose.Types.ObjectId(payloadMock.eventDimensions.videoId) }
	).sort({ createdAt: -1 });
	expect(latestVideoPlayTimeDocument).not.toBeNull();
	expect(latestVideoPlayTimeDocument?.videoShare?.toString()).toBe(payloadMock.eventDimensions.videoId);
	expect(latestVideoPlayTimeDocument?.collectionShare).toBe(undefined);
}

async function testIncrementTwiceWithParallelRequests(): Promise<void> {
	const maxImpressions = 10;
	const initialImpressions = maxImpressions - 2;

	const accountModel = new AccountModel(null);
	await accountModel.updateOneById(accountId, {
		totalImpressionsCurrentCycle: initialImpressions,
		"subscription.maxImpressionsPerCycle": maxImpressions
	});

	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoId: randomBytes(12).toString("hex")
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	const request1 = eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());
	const request2 = eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());
	const [result1, result2] = await Promise.allSettled([request1, request2]);
	expect(result1.status).toBe("fulfilled");
	expect(result2.status).toBe("fulfilled");

	const updatedAccount = await accountModel.readOneById(accountId);
	expect(updatedAccount.totalImpressionsCurrentCycle).toBe(initialImpressions + 2);
}

async function testIncrementOnceAndFailWhenMaxImpressionsReached(): Promise<void> {
	const maxImpressions = 10;
	const initialImpressions = maxImpressions - 1;

	const accountModel = new AccountModel(null);
	await accountModel.updateOneById(accountId, {
		totalImpressionsCurrentCycle: initialImpressions,
		"subscription.maxImpressionsPerCycle": maxImpressions
	});

	const payloadMock = {
		accountId: accountId,
		user: {
			sessionId: sessionId
		},
		eventDimensions: {
			videoId: randomBytes(12).toString("hex")
		}
	};

	const account = await accountModel.readOneById(accountId);

	const eventModel = new EventModel(null);
	const request1 = eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());
	const request2 = eventModel.processSnippetImpression(payloadMock as IPostEventsPayload, account, new Date());
	const [result1, result2] = await Promise.allSettled([request1, request2]);
	expect(result1.status).toBe("fulfilled");
	expect(result2.status).toBe("fulfilled");

	const updatedAccount = await accountModel.readOneById(accountId);
	expect(updatedAccount.totalImpressionsCurrentCycle).toBe(maxImpressions);
}

async function testAsyncIncrementProductLinkClickCount(): Promise<void> {
	const startTime = Date.now();
	const account = await new AccountModel(null).readOneById(accountId);

	const productFirst = {
		_id: new mongoose.Types.ObjectId(),
		title: "first",
		url: "test",
		productThumbnail: "test",
		productDescription: "test",
		subTitle: "0"
	};

	const productSecond = {
		_id: new mongoose.Types.ObjectId(),
		title: "second",
		url: "test2",
		productThumbnail: "test2",
		productDescription: "test2",
		subTitle: "0"
	};

	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [
			productFirst,
			productSecond
		],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	const productIdFirst = videoDocument.products[0]._id.toString();
	const payloadProductFirst = {
		accountId: accountId,
		eventName: EventNames.FEATURED_LINK_PRESS,
		appId: EventAppIds.GP_PLAYER,
		eventDimensions: {
			productId: productIdFirst,
			videoId: videoDocument._id.toString()
		}
	};

	const productIdSecond = videoDocument.products[1]._id.toString();
	const payloadProductSecond = {
		accountId: accountId,
		eventName: EventNames.FEATURED_LINK_PRESS,
		appId: EventAppIds.GP_PLAYER,
		eventDimensions: {
			productId: videoDocument.products[1]._id.toString(),
			videoId: videoDocument._id.toString()
		}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payloadProductFirst)
	};

	const eventBuffer2: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payloadProductSecond)
	};

	const eventModel = new EventModel(null);
	const event1 = eventModel.createOne(eventBuffer);
	const event2 = eventModel.createOne(eventBuffer);
	const event3 = eventModel.createOne(eventBuffer2);

	await Promise.all([event1, event2, event3]);

	const updatedVideoDocument = await interactiveVideoModel.readOneById(videoDocument._id.toString());
	expect(updatedVideoDocument.linkClicks).toBeDefined();
	expect(updatedVideoDocument.linkClicks.length).toBe(2);


	const linkClickProductFirst = updatedVideoDocument.linkClicks.find(
		linkClick => linkClick.productId.toString() === productIdFirst);
	if (linkClickProductFirst === undefined) {
		throw new Error("linkClickProductFirst is undefined");
	}

	const linkClickProductSecond = updatedVideoDocument.linkClicks.find(
		linkClick => linkClick.productId.toString() === productIdSecond);
	if (linkClickProductSecond === undefined) {
		throw new Error("linkClickProductSecond is undefined");
	}

	expect(linkClickProductFirst.clickCount).toBeDefined();
	expect(linkClickProductFirst.clickCount).toBe(2);
	expect(linkClickProductFirst.productId).toBeDefined();
	expect(linkClickProductFirst.productId.toString()).toBe(productIdFirst);
	expect(linkClickProductFirst.productTitle).toBeDefined();
	expect(linkClickProductFirst.productTitle).toBe(productFirst.title);
	expect(linkClickProductFirst.productURL).toBeDefined();
	expect(linkClickProductFirst.productURL).toBe(productFirst.url);
	expect(linkClickProductFirst.productImageURL).toBeDefined();
	expect(linkClickProductFirst.productImageURL).toBe(productFirst.productThumbnail);

	expect(linkClickProductSecond.clickCount).toBeDefined();
	expect(linkClickProductSecond.clickCount).toBe(1);
	expect(linkClickProductSecond.productId).toBeDefined();
	expect(linkClickProductSecond.productId.toString()).toBe(productIdSecond);
	expect(linkClickProductSecond.productTitle).toBeDefined();
	expect(linkClickProductSecond.productTitle).toBe(productSecond.title);
	expect(linkClickProductSecond.productURL).toBeDefined();
	expect(linkClickProductSecond.productURL).toBe(productSecond.url);
	expect(linkClickProductSecond.productImageURL).toBeDefined();
	expect(linkClickProductSecond.productImageURL).toBe(productSecond.productThumbnail);

	expect(updatedVideoDocument.updatedAt).toBeDefined();
	expect(updatedVideoDocument.updatedAt).toBeGreaterThan(startTime);
}

async function testSyncIncrementImpressionCount(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);

	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: <IShoppableVideoProduct[]>[
			{
				title: "test",
				url: "test",
				productThumbnail: "test",
				productDescription: "test",
				subTitle: "0"
			},
			{
				title: "test2",
				url: "test2",
				productThumbnail: "test2",
				productDescription: "test2",
				subTitle: "0"
			}
		],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	const payload = {
		accountId: accountId,
		eventName: EventNames.FEATURED_LINK_PRESS,
		appId: EventAppIds.GP_PLAYER,
		eventDimensions: {
			productId: videoDocument.products[0]._id.toString(),
			videoId: videoDocument._id.toString()
		}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);
	await eventModel.createOne(eventBuffer);

	const updatedVideoDocument = await interactiveVideoModel.readOneById(videoDocument._id.toString());

	expect(updatedVideoDocument.linkClicks).toBeDefined();
	expect(updatedVideoDocument.linkClicks.length).toBe(1);
	expect(updatedVideoDocument.linkClicks[0].clickCount).toBeDefined();
	expect(updatedVideoDocument.linkClicks[0].clickCount).toBe(1);

	const eventBuffer2: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	await eventModel.createOne(eventBuffer2);

	const updatedVideoDocument2 = await interactiveVideoModel.readOneById(videoDocument._id.toString());

	expect(updatedVideoDocument2.linkClicks).toBeDefined();
	expect(updatedVideoDocument2.linkClicks.length).toBe(1);
	expect(updatedVideoDocument2.linkClicks[0].clickCount).toBeDefined();
	expect(updatedVideoDocument2.linkClicks[0].clickCount).toBe(2);

	const payload2 = {
		accountId: accountId,
		eventName: EventNames.FEATURED_LINK_PRESS,
		appId: EventAppIds.GP_PLAYER,
		eventDimensions: {
			productId: videoDocument.products[1]._id.toString(),
			videoId: videoDocument._id.toString()
		}
	};

	const eventBuffer3: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload2)
	};

	await eventModel.createOne(eventBuffer3);

	const updatedVideoDocument3 = await interactiveVideoModel.readOneById(videoDocument._id.toString());

	expect(updatedVideoDocument3.linkClicks).toBeDefined();
	expect(updatedVideoDocument3.linkClicks.length).toBe(2);
	expect(updatedVideoDocument3.linkClicks[0].clickCount).toBeDefined();
	expect(updatedVideoDocument3.linkClicks[0].clickCount).toBe(2);
	expect(updatedVideoDocument3.linkClicks[1].clickCount).toBeDefined();
	expect(updatedVideoDocument3.linkClicks[1].clickCount).toBe(1);
}

async function testProcessUserEngagementForCollection(): Promise<void> {
	class TestEventModel extends EventModel {
		public async processUserEngagementForCollection(
			payload: IPostEventsPayload,
			account: IAccount
		): Promise<void> {
			return super.processUserEngagementForCollection(payload, account, new Date());
		}
	}

	const account = await new AccountModel(null).readOneById(accountId);
	const collectionModel = new InteractiveCollectionModel(null);

	const eventModel = new TestEventModel(null);

	const payload: IPostEventsPayload = <IPostEventsPayload>{
		accountId: accountId,
		eventName: EventNames.THUMBNAIL_CTA_PRESS,
		appId: randomBytes(12).toString("hex"),
		snippet: {
			type: EventSnippetTypes.PLAYER
		},
		eventDimensions: {
			collectionId: collectionId
		},
		user: {
			sessionId: sessionId
		}
	};

	await expect(eventModel.processUserEngagementForCollection(payload, account)).resolves.toBeUndefined();

	const updatedCollection = await collectionModel.readOneById(collectionId);

	expect(updatedCollection.userSessionCount).toBe(1);
	expect(updatedCollection.userCVR).toBe(0);
}

async function testVideoPlayPositionEventWithShareMode(): Promise<void> {
	const payload = {
		...payloadMock,
		appSessionId: "63d40a42558835fedb0234ab",
		isShareMode: true,
		eventName: EventNames.VIDEO_PLAY_POSITION
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}

async function testSnippetImpressionEventWithShareMode(): Promise<void> {
	const payload = {
		...payloadMock,
		appSessionId: "63d40a42558835fedb0234ab",
		isShareMode: true,
		eventName: EventNames.SNIPPET_IMPRESSION
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}

async function testVideoEmbedCopyThumbnailEvent(): Promise<void> {
	const payload = {
		...payloadMock,
		eventName: EventNames.VIDEO_EMBED_COPY_THUMBNAIL
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}



async function testCollectionShareLinkCopyEvent(): Promise<void> {
	const payload = {
		...payloadMock,
		eventName: EventNames.COLLECTION_SHARE_LINK_COPY
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}

async function testVideoShareLinkCopyEvent(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);
	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test-video-share-link-copy",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);
	const videoId = videoDocument._id.toString();
	const payload = {
		...payloadMock,
		eventName: EventNames.VIDEO_SHARE_LINK_COPY,
		eventDimensions: {
			...payloadMock.eventDimensions,
			videoId: videoId
		}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);
	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}

async function testDeviceObjectWithoutData(): Promise<void> {
	const payload = {
		eventName: EventNames.CREATE_ACCOUNT_IMPRESSION,
		appId: EventAppIds.GP_PLAYER,
		device: {}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).resolves.toBeUndefined();
}

async function testSnippetObjectWithValidType(): Promise<void> {
	const payload = {
		...payloadMock,
		eventName: EventNames.SNIPPET_IMPRESSION,
		snippet: { type: EventSnippetTypes.CAROUSEL }
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	await eventModel.createOne(eventBuffer);

	const account = await testHelper.getAccount(accessToken);
	// expect to increment by 1
	expect(account.totalImpressionsCurrentCycle).toBe(1);
}

async function testCreateMetricImpressionAtPlanLimit(): Promise<void> {
	let account: IAccount;
	account = await testHelper.getAccount(accessToken);
	const maxImpressions = account.subscription.maxImpressionsPerCycle;

	const accountModel = new AccountModel(null);
	await accountModel.setUsage(accountId, maxImpressions);

	const payload = {
		...payloadMock,
		eventName: EventNames.SNIPPET_IMPRESSION,
		snippet: { type: EventSnippetTypes.CAROUSEL }
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	await eventModel.createOne(eventBuffer);

	account = await testHelper.getAccount(accessToken);
	// expect it to not increment by 1
	expect(account.totalImpressionsCurrentCycle).toBe(maxImpressions);
}

async function testProcessFeaturedLinkPress(): Promise<void> {
	const video: IVideo = await createVideo(
		{
			accountId: accountId,
			videoWidthPx: 0,
			videoHeightPx: 0,
			publicVideoURL: "http://example.tld/example.mp4",
			videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
		},
		null
	);
	const videoId = video._id.toString();

	const shoppableVideoCreateData = {
		title: "abcd",
		ctaText: "Shop Now",
		videoDisplayMode: "portrait",
		showTitle: true,
		videoPosterImageURL: "http://example.tld/example.jpg",
		gifURL: "http://example.tld/example.gif",
		phone: "+***********",
		email: "<EMAIL>",
		videoPosterPlayEmbedURL: "http://example.tld/example-play.jpg"
	};

	const productLinkData = {
		productURL: "http://example.tld/example",
		productTitle: "product title",
		productDescription: "very nice product",
		subTitle: "$100",
		productImageURL: "http://example.tld/product-img.jpg"
	};

	const resPostShoppableVideos = await supertest(expressApp)
		.post("/api/shoppable-videos")
		.set("Authorization", `Bearer ${accessToken}`)
		.set("x-api-version", "4")
		.set("x-account-token", accountToken)
		.field("title", shoppableVideoCreateData.title)
		.field("videoId", videoId)
		.field("videoPosterImageURL", shoppableVideoCreateData.videoPosterImageURL)
		.field("gifURL", shoppableVideoCreateData.gifURL)
		.field("phone", shoppableVideoCreateData.phone)
		.field("email", shoppableVideoCreateData.email)
		.field("videoPosterPlayEmbedURL", shoppableVideoCreateData.videoPosterPlayEmbedURL)
		.field("ctaText", shoppableVideoCreateData.ctaText)
		.field("videoDisplayMode", shoppableVideoCreateData.videoDisplayMode)
		.field("showTitle", shoppableVideoCreateData.showTitle)
		.field("productURL0", productLinkData.productURL)
		.field("productTitle0", productLinkData.productTitle)
		.field("productDescription0", productLinkData.productDescription)
		.field("subTitle0", productLinkData.subTitle)
		.field("productImageURL0", productLinkData.productImageURL);

	expect(resPostShoppableVideos.statusCode).toBe(201);
	expect(resPostShoppableVideos.body.shoppableVideo).toBeTruthy();
	const shoppableVideo = resPostShoppableVideos.body.shoppableVideo;
	const shoppableVideoId = shoppableVideo._id.toString();

	const productLink = shoppableVideo.products[0];
	const productLinkId = productLink._id.toString();

	const payload = {
		...payloadMock,
		eventName: EventNames.FEATURED_LINK_PRESS
	};

	payload.eventDimensions.videoId = shoppableVideoId;
	payload.eventDimensions.productId = productLinkId;

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	await eventModel.createOne(eventBuffer);
}

async function testProcessVideoPlayPosition(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);
	const interactiveVideoModel = new InteractiveVideoModel(null);
	const interactiveVideo = await interactiveVideoModel.createOne({
		title: "test-play-position",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	expect(interactiveVideo.videoTotalSeconds).toBe(0);
	expect(interactiveVideo.videoPlayCount).toBe(0);
	expect(interactiveVideo.videoPlayDurationSeconds).toBe(0);
	expect(interactiveVideo.videoScore).toBe(0);
	expect(interactiveVideo.likes).toBe(0);

	expect(interactiveVideo.videoUniquePlayDurationSeconds).toBe(0);
	expect(interactiveVideo.videoUniquePlayCount).toBe(0);

	expect(interactiveVideo.playPercentCount80).toBe(0);

	const videoId = interactiveVideo._id.toString();
	const payload = {
		...payloadMock,
		eventName: EventNames.VIDEO_PLAY_POSITION,
		eventDimensions: {
			videoSecondsLength: 10,
			videoSecondsPosition: 9,
			videoPercentagePlayed: 90,
			videoState: "loaded",
			videoPlayStatus: "stopped",
			endedMethod: "next",
			videoId: videoId
		}
	};

	const expectedMetrics = {
		videoTotalSeconds: 10,
		videoPlayCount: 1,
		videoUniquePlayCount: 1,
		videoPlayDurationSeconds: 9,
		videoUniquePlayDurationSeconds: 9,
		videoScore: 90,
		playPercentCount20: 0,
		playPercentCount40: 0,
		playPercentCount60: 0,
		playPercentCount80: 0,
		playPercentCount100: 1
	};


	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	await eventModel.createOne(eventBuffer);

	const updatedInteractiveVideo = await interactiveVideoModel.readOneById(videoId);
	expect(updatedInteractiveVideo).not.toBeNull();
	expect(updatedInteractiveVideo.videoScore).toBe(expectedMetrics.videoScore);
	expect(updatedInteractiveVideo.playPercentCount20).toBe(expectedMetrics.playPercentCount20);
	expect(updatedInteractiveVideo.playPercentCount40).toBe(expectedMetrics.playPercentCount40);
	expect(updatedInteractiveVideo.playPercentCount60).toBe(expectedMetrics.playPercentCount60);
	expect(updatedInteractiveVideo.playPercentCount80).toBe(expectedMetrics.playPercentCount80);
	expect(updatedInteractiveVideo.playPercentCount100).toBe(expectedMetrics.playPercentCount100);
	expect(updatedInteractiveVideo.videoTotalSeconds).toBe(expectedMetrics.videoTotalSeconds);
	expect(updatedInteractiveVideo.videoPlayDurationSeconds).toBe(expectedMetrics.videoPlayDurationSeconds);
	expect(updatedInteractiveVideo.videoPlayCount).toBe(expectedMetrics.videoPlayCount);
	expect(updatedInteractiveVideo.videoUniquePlayDurationSeconds).toBe(expectedMetrics.videoUniquePlayDurationSeconds);
	expect(updatedInteractiveVideo.videoUniquePlayCount).toBe(expectedMetrics.videoUniquePlayCount);


	const metricVideoEngagement = await MetricVideoEngagementDBModel.findOne({ videoId: videoId });
	expect(metricVideoEngagement).not.toBeNull();
	expect(metricVideoEngagement?.videoId.toString()).toBe(videoId);
	expect(metricVideoEngagement?.playPercentCount100).toBe(updatedInteractiveVideo.playPercentCount100);
	expect(metricVideoEngagement?.playPercentCount80).toBe(updatedInteractiveVideo.playPercentCount80);
	expect(metricVideoEngagement?.playPercentCount60).toBe(updatedInteractiveVideo.playPercentCount60);
	expect(metricVideoEngagement?.playPercentCount40).toBe(updatedInteractiveVideo.playPercentCount40);
	expect(metricVideoEngagement?.playPercentCount20).toBe(updatedInteractiveVideo.playPercentCount20);
	expect(metricVideoEngagement?.videoScore).toBe(updatedInteractiveVideo.videoScore);
	expect(metricVideoEngagement?.createdAt.getTime()).toBe(eventBuffer.createdAt.getTime());
	expect(metricVideoEngagement?.updatedAt.getTime()).toBe(eventBuffer.createdAt.getTime());
}

async function testProcessThumbnailCtaPress(): Promise<void> {
	let collection: IShoppableCollection;
	let metrics: IMetricUserEngagement[];

	const payload = { ...payloadMock };
	payload.eventName = EventNames.THUMBNAIL_CTA_PRESS;
	payload.user.sessionId = sessionId;

	const collectionModel = new InteractiveCollectionModel(null);
	collection = await collectionModel.readOneById(payload.eventDimensions.collectionId);

	expect(collection.userCVR).toBe(0);

	const metricModel = new MetricUserEngagementModel(null);
	metrics = await metricModel.readManyByAccountId(accountId);

	const currentMetricsLength = metrics.length;

	await updateShoppableCollection(
		{ _id: payload.eventDimensions.collectionId },
		{ $set: { orderCount: 1 } },
		null
	);

	const eventPayload = { ...payload, snippet: { type: EventSnippetTypes.ONSITE } };

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(eventPayload)
	};

	const eventModel = new EventModel(null);

	await eventModel.createOne(eventBuffer);

	collection = await collectionModel.readOneById(payload.eventDimensions.collectionId);
	expect(collection.userSessionCount).toBe(1);
	expect(collection.userCVR).toBeGreaterThan(0);

	metrics = await metricModel.readManyByAccountId(accountId);
	expect(metrics.length).toBe(currentMetricsLength + 1);

	const metric:IMetricUserEngagement = metrics[0];
	expect(metric.collectionId?.toString()).toBe(payload.eventDimensions.collectionId);
	expect(metric.userSessionId.toString()).toBe(payload.user.sessionId);

	const eventPayload2 = {
		...payload,
		snippet: {
			type: EventSnippetTypes.PLAYER
		}
	};

	const eventBuffer2: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(eventPayload2)
	};

	await eventModel.createOne(eventBuffer2);

	metrics = await metricModel.readManyByAccountId(accountId);
	expect(metrics.length).toBe(currentMetricsLength + 2);

	const playerMetric:IMetricUserEngagement = metrics[metrics.length - 1];
	expect(playerMetric.videoId?.toString()).toBe(payload.eventDimensions.videoId);
	expect(playerMetric.userSessionId.toString()).toBe(payload.user.sessionId);
}

async function testFailIfNoEventName(): Promise<void> {
	const payload = {
		appId: EventAppIds.GP_PLAYER
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailNoAppId(): Promise<void> {
	const payload = {
		eventName: EventNames.CREATE_ACCOUNT_IMPRESSION
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailInvalidEventName(): Promise<void> {
	const payload = {
		eventName: "invalid"
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailInvalidAppId(): Promise<void> {
	const payload = {
		eventName: EventNames.CREATE_ACCOUNT_IMPRESSION,
		appId: "invalid"
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailUserObjectWithoutData(): Promise<void> {
	const payload = {
		eventName: EventNames.CREATE_ACCOUNT_IMPRESSION,
		appId: EventAppIds.GP_PLAYER,
		user: {}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailEventDimensionsObjectWithoutData(): Promise<void> {
	const payload = {
		eventName: EventNames.CREATE_ACCOUNT_IMPRESSION,
		appId: EventAppIds.GP_PLAYER,
		eventDimensions: {}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailEventAccountInvalid(): Promise<void> {
	const payload = {
		...payloadMock,
		accountId: randomBytes(12).toString("hex"),
		eventName: EventNames.VERIFY_ACCOUNT_IMPRESSION
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	await expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
	);
}

async function testFailSnippetObjectWithoutData(): Promise<void> {
	const payload = {
		eventName: EventNames.SNIPPET_IMPRESSION,
		appId: EventAppIds.GP_PLAYER,
		snippet: {}
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}

async function testFailSnippetObjectInvalidType(): Promise<void> {
	const payload = {
		eventName: EventNames.SNIPPET_IMPRESSION,
		appId: EventAppIds.GP_PLAYER,
		snippet: { type: "unsupported_type" }
	};

	const eventBuffer: EventBuffer = {
		_id: new mongoose.Types.ObjectId(),
		createdAt: new Date(),
		attempts: 0,
		buffer: EventBufferModel.toBinaryBuffer(payload)
	};

	const eventModel = new EventModel(null);

	expect(eventModel.createOne(eventBuffer)).rejects.toStrictEqual(
		new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, "")
	);
}


async function testMetricVideoPlayTimeVideoValidCurrentTime(): Promise<void> {
	const account = await new AccountModel(null).readOneById(accountId);
	const interactiveVideoModel = new InteractiveVideoModel(null);
	const videoDocument = await interactiveVideoModel.createOne({
		title: "test-play-time",
		description: "description",
		videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
		videoURL: "test",
		products: [],
		ctaText: "test",
		videoPosterPlayEmbedURL: "test",
		videoPosterURL: "test",
		showTitle: true,
		gifURL: "test"
	}, account);

	const appSessionId = randomBytes(12).toString("hex");
	const videoId = videoDocument._id.toString();
	const playId = randomBytes(12).toString("hex");
	const payloadMock = {
		accountId: accountId,
		appSessionId: appSessionId,
		playId: playId,
		user: {
			sessionId: randomBytes(12).toString("hex")
		},
		eventDimensions: {
			videoSecondsLength: 10,
			videoSecondsPosition: 9,
			videoPercentagePlayed: 90,
			videoState: "loaded",
			videoPlayStatus: "stopped",
			endedMethod: "next",
			videoId: videoId
		}
	};



	const eventModel = new EventModel(null);
	await eventModel.processVideoPosition(payloadMock as IPostEventsPayload, account, new Date());

	const videoPlayTimeDocument = await MetricVideoPlayTimeDBModel.findOne({
		accountId: new mongoose.Types.ObjectId(accountId),
		videoId: new mongoose.Types.ObjectId(videoId),
		appSessionId: new mongoose.Types.ObjectId(appSessionId),
		playId: new mongoose.Types.ObjectId(playId)
	});

	expect(videoPlayTimeDocument).not.toBeNull();
	expect(videoPlayTimeDocument?.totalPlayTimeSeconds).toBe(payloadMock.eventDimensions.videoSecondsPosition);
}

async function testMetricVideoPlayTimeVideoInvalidCurrentTime(): Promise<void> {
	const appSessionId = randomBytes(12).toString("hex");
	const videoId = randomBytes(12).toString("hex");
	const playId = randomBytes(12).toString("hex");
	const payloadMock = {
		accountId: accountId,
		appSessionId: appSessionId,
		playId: playId,
		user: {
			sessionId: randomBytes(12).toString("hex")
		},
		eventDimensions: {
			videoSecondsLength: 10,
			videoSecondsPosition: 11,
			videoPercentagePlayed: 110,
			videoState: "loaded",
			videoPlayStatus: "stopped",
			endedMethod: "next",
			videoId: videoId
		}
	};

	const account = await new AccountModel(null).readOneById(accountId);

	const eventModel = new EventModel(null);
	const errorMessage = `Invalid playedPercent=110 for videoId=${videoId} and appSessionId=${appSessionId}`;

	await expect(eventModel.processVideoPosition(payloadMock as IPostEventsPayload, account, new Date()))
		.rejects.toThrow(errorMessage);

	const videoPlayTimeDocument = await MetricVideoPlayTimeDBModel.findOne({
		accountId: new mongoose.Types.ObjectId(accountId),
		videoId: new mongoose.Types.ObjectId(videoId),
		appSessionId: new mongoose.Types.ObjectId(appSessionId),
		playId: new mongoose.Types.ObjectId(playId)
	});

	expect(videoPlayTimeDocument).toBeNull();
}

import mongoose, { ClientSession } from "mongoose";
import {
	IMetricVideoShareCreateInput,
	IMetricVideoShareCreated
} from "./metricVideoShare.interface";
import { MetricVideoShareCreatedDBModel } from "./metricVideoShareDB.model";

export class MetricVideoShareModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async createOne(
		createData: IMetricVideoShareCreateInput
	): Promise<IMetricVideoShareCreated> {
		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new MetricVideoShareCreatedDBModel({
			createdAt: createData.createdAt,
			accountId: createData.accountId,
			videoId: createData.videoId
		}).save(options);

		return newDocument;
	}
}

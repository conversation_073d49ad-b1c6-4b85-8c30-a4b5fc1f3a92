import Joi from "joi";
import {
	PaymentDeleteRequest,
	PaymentListRequest,
	PaymentPostRequest
} from "./payment.interfaces";
import {
	AccessTokenJoi,
	VersionJoi
} from "../base/base.joi";

export const PaymentPostValidator = Joi.object<PaymentPostRequest>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	accountToken: Joi.string().required(),
	paymentMethodId: Joi.string().required()
});

export const PaymentListValidator = Joi.object<PaymentListRequest>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	accountToken: Joi.string().required()
});

export const PaymentDeleteValidator = Joi.object<PaymentDeleteRequest>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	accountToken: Joi.string().required(),
	paymentMethodId: Joi.string().required()
});

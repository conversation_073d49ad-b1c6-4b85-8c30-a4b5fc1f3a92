import {
	Document,
	ObjectId
} from "mongoose";
import {
	JobsType,
	JobsStatus
} from "../../modules/job/jobs.enums";

export interface IJob extends Document {
	_id: ObjectId;
	tempFilename: string;
	accountId?: ObjectId;
	userId?: ObjectId;
	type: JobsType;
	status: JobsStatus;
	statusMessage: string;
	createdAt: number;
	updatedAt: number;
	sourceURL?: string;
	progressPercent?: number;
	nextStatusCheck?: number;
	videoId?: ObjectId;
	imageWidthPx?: number;
	imageHeightPx?: number;
	imageURL?: string;
	callbackInfo?: CallbackInfo;
}

export interface CallbackInfo {
	callbackUrl?: string;
	callbackData?: string;
}

export interface IJobInput {
	imageWidthPx?: number;
	imageHeightPx?: number;
}

export interface IJobPayload {
	jobType: string;
	sourceURL: string;
	imageWidthPx: number;
	imageHeightPx: number;
}

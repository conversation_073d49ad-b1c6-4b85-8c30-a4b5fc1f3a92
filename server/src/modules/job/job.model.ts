import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { JobDBModel } from "./jobDB.model";
import { JobsType } from "../job/jobs.enums";
import { IJob } from "./job.interfaces";
import { AccountMetricModel } from "../../modules/account/account.metric.model";
import { VideoEncodeModel } from "../video/encode/video.encode.model";
export class JobModel {
	private session: ClientSession | null;
	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public static async createIndexes (): Promise<void> {
		await JobDBModel.collection.createIndexes([
			{ key: { tempFilename: 1 }, name: "tempFilename_index", unique: true }
		]);
	}

	public static async getIndexes (): Promise<unknown> {
		return await JobDBModel.collection.indexes();
	}

	async readOneByTempFilename (tempFilename: string): Promise<IJob> {
		const filter: FilterQuery<IJob> = {
			tempFilename: tempFilename
		};
		const document = await JobDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async readOneById (_id: string): Promise<IJob> {
		const filter: FilterQuery<IJob> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const document = await JobDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async runJobById (jobId: string): Promise<void> {
		const job = await this.readOneById(jobId);

		if (job.type === JobsType.ENCODE_VIDEO) {
			const videoEncodeModel = new VideoEncodeModel(job);
			await videoEncodeModel.startEncoder();
		} else if (job.type === JobsType.SYNC_ACCOUNTS) {
			const accountMetricModel = new AccountMetricModel(null);
			await accountMetricModel.computeDailyWeeklyMetrics();
		} else {
			throw new APIError(APIErrorName.E_INVALID_INPUT, `Unsupported job type ${job.type}`);
		}
	}

}



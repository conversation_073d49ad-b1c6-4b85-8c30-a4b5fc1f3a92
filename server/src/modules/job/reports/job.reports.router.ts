import express, {
	Express,
	Request,
	Response
} from "express";
import { JobReportsController } from "./job.reports.controller";

export class JobReportsRouter {
	private controller: JobReportsController = new JobReportsController();
	private router = express.Router();

	constructor() {
		this.router.get(
			"/deliver/monthly",
			[express.json({ limit: "2MB" })],
			(request: Request, response: Response) => {
				return this.controller.deliverMonthly(request, response);
			}
		);
	}

	public use = (expressServer: Express): void => {
		expressServer.use("/api/jobs/reports", this.router);
	};
}

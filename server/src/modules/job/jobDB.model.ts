import mongoose, { Schema } from "mongoose";
import { <PERSON><PERSON><PERSON> } from "./job.interfaces";

const JobSchema: Schema = new Schema({
	tempFilename: { type: String, required: true, unique: true },
	accountId: { type: Schema.Types.ObjectId, required: false },
	userId: { type: Schema.Types.ObjectId, required: false },
	type: { type: String, required: true },
	status: { type: String, required: true },
	statusMessage: { type: String, required: true },
	createdAt: { type: Number, default: () => Date.now() },
	updatedAt: { type: Number, default: () => Date.now() },
	sourceURL: { type: String, required: false },
	progressPercent: { type: Number, required: false },
	nextStatusCheck: { type: Number, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	imageWidthPx: { type: Schema.Types.Number, required: false },
	imageHeightPx: { type: Schema.Types.Number, required: false },
	imageURL: { type: Schema.Types.String, required: false },
	callbackInfo: { type: { callbackUrl: String, callbackData: String }, required: false }
},
{ timestamps: true }
);

export const JobDBModel = mongoose.model<IJob>(
	"Jobs",
	JobSchema
);

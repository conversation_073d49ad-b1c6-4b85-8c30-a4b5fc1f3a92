import supertest from "supertest";
import mongoose from "mongoose";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { AuthenticationDBModel } from "../authentication/authenticationDBModel";
import { SignInMethod } from "../signin/signin.interfaces";
import { VideoProfileCreateOnePayload } from "./videoProfile.joi";
import {
	LevelEnum,
	pixelFormatEnum,
	CRFEnum,
	PresetEnum,
	AudioBitrateEnum
} from "./videoProfile.enum";
import { VideoProfileDBModel } from "./videoProfile.db.model";
import { VideoDBModel } from "../video/videoDB.model";
import { randomBytes } from "crypto";

// eslint-disable-next-line max-lines-per-function
describe("BlackBox testing | VideoProfile API Controller.", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let superAccessToken: string;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};
	const signupSuperEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	const createPayload = (): VideoProfileCreateOnePayload => ({
		accessToken: "",
		apiVersion: 3,
		name: `baseline-${Date.now()}`,
		default: false,
		level: LevelEnum.L3_0,
		pixelFormat: pixelFormatEnum.NV12,
		constantRateFactor: CRFEnum.BALANCED,
		preset: PresetEnum.MEDIUM,
		audioBitrate: AudioBitrateEnum.A128k,
		scale: 1080
	});

	beforeAll(async () => {

		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);

		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		const superToken = await testHelper.signupEmail(signupSuperEmailPayload);
		const superAuth = await testHelper.getAuthentication(superToken.accessToken);
		await AuthenticationDBModel.findOneAndUpdate({ _id: superAuth._id }, { super: true }, { new: true });
		const signingRes = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "2")
			.send({ method: SignInMethod.REFRESH_TOKEN, token: superToken.refreshToken });

		superAccessToken = signingRes.body.accessToken;
	});

	it("Should fail to create new videoProfile due to missing payload data | 400 [E_INVALID_INPUT]", async () => {
		const payload = createPayload();
		const res = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail to create new videoProfile due to non super user | 403 [E_REQUEST_FORBIDDEN]", async () => {
		const payload = createPayload();
		payload.accessToken = accessToken;
		const res = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should successfully create a new videoProfile on top of the existing one. | 201 ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const res = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(res.statusCode).toBe(201);
		expect(res.body.name).toEqual(payload.name);
		expect(res.body.default).toEqual(false);
		expect(res.body.level).toEqual(payload.level);
		expect(res.body.pixelFormat).toEqual(payload.pixelFormat);
		expect(res.body.constantRateFactor).toEqual(payload.constantRateFactor);
		expect(res.body.preset).toEqual(payload.preset);
		expect(res.body.audioBitrate).toEqual(payload.audioBitrate);
	});

	it("Should fail to create a new videoProfile due to existing name | 409 [E_VIDEO_PROFILE_EXISTS]", async () => {
		const firstPayload = createPayload();
		firstPayload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", firstPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${firstPayload.accessToken}`)
			.send(firstPayload);
		expect(firstRes.statusCode).toBe(201);

		const secondPayload = createPayload();
		secondPayload.accessToken = superAccessToken;
		secondPayload.name = firstPayload.name;
		const secondRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", secondPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${secondPayload.accessToken}`)
			.send(secondPayload);
		expect(secondRes.statusCode).toBe(409);
		expect(secondRes.body.error).toEqual(APIErrorName.E_VIDEO_PROFILE_EXISTS);
	});

	it("Should successfully mark new default and unsetDefaultFromOthers | 201", async () => {
		const firstPayload = createPayload();
		firstPayload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", firstPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${firstPayload.accessToken}`)
			.send({ ...firstPayload, default: true });
		expect(firstRes.statusCode).toBe(201);

		let firstVideoProfileDocument =
        await VideoProfileDBModel.findOne({ _id: new mongoose.Types.ObjectId(firstRes.body._id) });
		expect(firstVideoProfileDocument?.default).toEqual(true);

		const secondPayload = createPayload();
		secondPayload.accessToken = superAccessToken;
		secondPayload.default = true;
		const secondRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", secondPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${secondPayload.accessToken}`)
			.send(secondPayload);
		expect(secondRes.statusCode).toBe(201);

		firstVideoProfileDocument = await VideoProfileDBModel.findOne({ _id: firstVideoProfileDocument?._id });
		expect(firstVideoProfileDocument?.default).toEqual(false);

		const secondVideoProfileDocument =
        await VideoProfileDBModel.findOne({ _id: new mongoose.Types.ObjectId(secondRes.body._id) });
		expect(secondVideoProfileDocument?.default).toEqual(true);
	});

	it("Should fail to update a videoProfile due to invalid _Id in payload data | 400 [E_INVALID_INPUT]", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const res = await supertest(expressApp)
			.put("/api/video-profiles/123")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail to update videoProfile due to non super user | 403 [E_REQUEST_FORBIDDEN]", async () => {
		const payload = createPayload();
		payload.accessToken = accessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${superAccessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.put(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({});
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should successfully update a videoProfile | 200 ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const updateName = `baseline-${Date.now()}`;
		const res = await supertest(expressApp)
			.put(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({ name: updateName });
		expect(res.statusCode).toBe(200);
		expect(res.body.name).toEqual(updateName);
		const videoProfileDocument =
		await VideoProfileDBModel.findOne({ _id: new mongoose.Types.ObjectId(firstRes.body._id) });
		expect(videoProfileDocument?.name).toEqual(updateName);
	});

	it("Should fail to update a videoProfile with already existing name of a different videoProfile | 409 ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;
			const firstRes = await supertest(expressApp)
				.post("/api/video-profiles")
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send(payload);
			expect(firstRes.statusCode).toBe(201);

			const existingProfileName = firstRes.body.name;

			const secondRes = await supertest(expressApp)
				.post("/api/video-profiles")
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send({ ...payload, name: `baseline-${Date.now()}` });
			expect(secondRes.statusCode).toBe(201);

			const res = await supertest(expressApp)
				.put(`/api/video-profiles/${secondRes.body._id}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send({ name: existingProfileName });
			expect(res.statusCode).toBe(409);
			expect(res.body.error).toEqual(APIErrorName.E_VIDEO_PROFILE_EXISTS);

		});

	it("Should fail to update a videoProfile with already existing name of a different videoProfile | 409 ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;
			const firstRes = await supertest(expressApp)
				.post("/api/video-profiles")
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send(payload);
			expect(firstRes.statusCode).toBe(201);

			await new VideoDBModel({
				accountId: new mongoose.Types.ObjectId(randomBytes(12).toString("hex")),
				videoProfile: new mongoose.Types.ObjectId(firstRes.body._id),
				videoHeightPx: 1080,
				videoWidthPx: 1920
			}).save();

			const res = await supertest(expressApp)
				.put(`/api/video-profiles/${firstRes.body._id}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send({ name: "newName" });
			expect(res.statusCode).toBe(403);
			expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);

		});

	it("Should fail to update a videoProfile with already existing name of a different videoProfile | 409 ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;
			const firstRes = await supertest(expressApp)
				.post("/api/video-profiles")
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send(payload);
			expect(firstRes.statusCode).toBe(201);

			await new VideoDBModel({
				accountId: new mongoose.Types.ObjectId(randomBytes(12).toString("hex")),
				videoProfile: new mongoose.Types.ObjectId(firstRes.body._id),
				videoHeightPx: 1080,
				videoWidthPx: 1920
			}).save();

			const res = await supertest(expressApp)
				.put(`/api/video-profiles/${firstRes.body._id}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send({ name: "newName" });
			expect(res.statusCode).toBe(403);
			expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);

		});

	it("Should successfully update existing default to be true and unsetDefaultFromOthers | 200", async () => {
		const firstPayload = createPayload();
		firstPayload.accessToken = superAccessToken;
		firstPayload.default = true;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", firstPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${firstPayload.accessToken}`)
			.send(firstPayload);
		expect(firstRes.statusCode).toBe(201);

		let firstVideoProfileDocument =
        await VideoProfileDBModel.findOne({ _id: new mongoose.Types.ObjectId(firstRes.body._id) });
		expect(firstVideoProfileDocument?.default).toEqual(true);

		const secondPayload = createPayload();
		secondPayload.accessToken = superAccessToken;
		secondPayload.default = false;
		const secondRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", secondPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${secondPayload.accessToken}`)
			.send(secondPayload);
		expect(secondRes.statusCode).toBe(201);

		let secondVideoProfileDocument =
        await VideoProfileDBModel.findOne({ _id: new mongoose.Types.ObjectId(secondRes.body._id) });
		expect(secondVideoProfileDocument?.default).toEqual(false);

		const res = await supertest(expressApp)
			.put(`/api/video-profiles/${secondRes.body._id}`)
			.set("x-api-version", secondPayload.apiVersion.toString())
			.set("Authorization", `Bearer ${secondPayload.accessToken}`)
			.send({ default: true });
		expect(res.statusCode).toBe(200);

		secondVideoProfileDocument = await VideoProfileDBModel.findOne({ _id: secondVideoProfileDocument?._id });
		expect(secondVideoProfileDocument?.default).toEqual(true);

		firstVideoProfileDocument = await VideoProfileDBModel.findOne({ _id: firstVideoProfileDocument?._id });
		expect(firstVideoProfileDocument?.default).toEqual(false);
	});

	it("Should fail to list all videoProfiles due to invalid payload data | 400 [E_INVALID_INPUT]", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const res = await supertest(expressApp)
			.get("/api/video-profiles")
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail to list all videoProfiles due to non super user | 403 [E_REQUEST_FORBIDDEN]", async () => {
		const payload = createPayload();
		payload.accessToken = accessToken;
		const res = await supertest(expressApp)
			.get("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({});
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should successfully list all videoProfiles | 200 ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.get("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(200);
		expect(res.body.length > 1).toBeTruthy();
	});

	it("Should fail to read a videoProfile due to invalid _Id in payload data | 400 [E_INVALID_INPUT]", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const res = await supertest(expressApp)
			.get("/api/video-profiles/123")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail to read a videoProfile due to non super user | 403 [E_REQUEST_FORBIDDEN]", async () => {
		const payload = createPayload();
		payload.accessToken = accessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${superAccessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.get(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should successfully read a videoProfile | 200 ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.put(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(200);
		expect(res.body.name).toEqual(payload.name);
		expect(res.body.default).toEqual(firstRes.body.default);
		expect(res.body.level).toEqual(payload.level);
		expect(res.body.pixelFormat).toEqual(payload.pixelFormat);
	});

	it("Should fail to read a videoProfile that does not exist in the collection | 404 [E_DOCUMENT_NOT_FOUND] ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;

			const res = await supertest(expressApp)
				.put(`/api/video-profiles/${randomBytes(12).toString("hex")}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send();
			expect(res.statusCode).toBe(404);
			expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
		});

	it("Should fail to delete a videoProfile due to invalid _Id in payload data | 400 [E_INVALID_INPUT]", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const res = await supertest(expressApp)
			.delete("/api/video-profiles/123")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail to delete a videoProfile due to non super user | 403 [E_REQUEST_FORBIDDEN]", async () => {
		const payload = createPayload();
		payload.accessToken = accessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${superAccessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.delete(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should successfully delete a videoProfile | 204 ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send(payload);
		expect(firstRes.statusCode).toBe(201);

		const secondRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({ ...payload, name: `baseline-${Date.now()}` });
		expect(secondRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.delete(`/api/video-profiles/${secondRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(204);
	});

	it("Should fail to delete a videoProfile that does not exist in the collection | 404 [E_DOCUMENT_NOT_FOUND] ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;

			const res = await supertest(expressApp)
				.delete(`/api/video-profiles/${randomBytes(12).toString("hex")}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send();
			expect(res.statusCode).toBe(404);
			expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
		});

	it("Should fail to delete a default videoProfile | 403 [E_REQUEST_FORBIDDEN] ", async () => {
		const payload = createPayload();
		payload.accessToken = superAccessToken;
		const firstRes = await supertest(expressApp)
			.post("/api/video-profiles")
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send({ ...payload, default: true });
		expect(firstRes.statusCode).toBe(201);

		const res = await supertest(expressApp)
			.delete(`/api/video-profiles/${firstRes.body._id}`)
			.set("x-api-version", payload.apiVersion.toString())
			.set("Authorization", `Bearer ${payload.accessToken}`)
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should fail to delete a videoProfile that is referenced in videos collection | 403 [E_REQUEST_FORBIDDEN] ",
		async () => {
			const payload = createPayload();
			payload.accessToken = superAccessToken;
			const firstRes = await supertest(expressApp)
				.post("/api/video-profiles")
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send(payload);
			expect(firstRes.statusCode).toBe(201);

			await new VideoDBModel({
				accountId: new mongoose.Types.ObjectId(randomBytes(12).toString("hex")),
				videoProfile: new mongoose.Types.ObjectId(firstRes.body._id),
				videoHeightPx: 1080,
				videoWidthPx: 1920
			}).save();

			const res = await supertest(expressApp)
				.delete(`/api/video-profiles/${firstRes.body._id}`)
				.set("x-api-version", payload.apiVersion.toString())
				.set("Authorization", `Bearer ${payload.accessToken}`)
				.send();
			expect(res.statusCode).toBe(403);
			expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
		});

});

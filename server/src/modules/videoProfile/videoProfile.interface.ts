import {
	ObjectId,
	Document
} from "mongoose";
import {
	LevelEnum,
	pixelFormatEnum,
	CRFEnum,
	PresetEnum,
	AudioBitrateEnum
} from "./videoProfile.enum";

export interface VideoProfile extends Document {
	_id: ObjectId;
	name: string;
	default: boolean;
	level: LevelEnum;
	pixelFormat: pixelFormatEnum;
	constantRateFactor: CRFEnum;
	preset: PresetEnum;
	audioBitrate: AudioBitrateEnum;
	scale: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface VideoProfileCreate {
	name: string;
	default: boolean;
	level: LevelEnum;
	pixelFormat: pixelFormatEnum;
	constantRateFactor: CRFEnum;
	preset: PresetEnum;
	audioBitrate: AudioBitrateEnum;
	scale: number;
}

export interface VideoProfileUpdate {
	name?: string;
	default?: boolean;
	level?: LevelEnum;
	pixelFormat?: pixelFormatEnum;
	constantRateFactor?: CRFEnum;
	preset?: PresetEnum;
	audioBitrate?: AudioBitrateEnum;
	scale?: number;
}

import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	VideoProfileCreateOnePayload,
	VideoProfileUpdateOnePayload,
	VideoProfileCreateOnePayloadSchema,
	VideoProfileUpdateOnePayloadSchema,
	VideoProfileGetDeleteOnePayloadSchema,
	VideoProfileGetDeleteOnePayload
} from "./videoProfile.joi";
import { VideoProfileModel } from "./videoProfile.model";
import { BaseAccessJoi } from "../base/base.joi";
import { BaseAccessRequest } from "../base/base.interfaces";


export class VideoProfileController extends Controller {
	constructor() {
		super();
		this.router.post("/", [express.json()], this.post.bind(this));
		this.router.put("/:id", [express.json()], this.put.bind(this));
		this.router.get("/", this.list.bind(this));
		this.router.get("/:id", this.get.bind(this));
		this.router.delete("/:id", this.delete.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoProfileCreateOnePayload;
		try {
			validPayload = await VideoProfileCreateOnePayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				name: request.body.name,
				default: request.body.default,
				level: request.body.level,
				pixelFormat: request.body.pixelFormat,
				constantRateFactor: request.body.constantRateFactor,
				preset: request.body.preset,
				audioBitrate: request.body.audioBitrate,
				scale: request.body.scale
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(accessToken);

			const videoProfileModel = new VideoProfileModel(response.locals.session);
			const createdProfile = await videoProfileModel.createOne({
				name: validPayload.name,
				default: validPayload.default,
				level: validPayload.level,
				pixelFormat: validPayload.pixelFormat,
				constantRateFactor: validPayload.constantRateFactor,
				preset: validPayload.preset,
				audioBitrate: validPayload.audioBitrate,
				scale: validPayload.scale
			});
			return response.status(201).json(createdProfile);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async put(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoProfileUpdateOnePayload;
		try {
			validPayload = await VideoProfileUpdateOnePayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				videoProfileId: request.params.id,
				name: request.body.name,
				default: request.body.default,
				level: request.body.level,
				pixelFormat: request.body.pixelFormat,
				constantRateFactor: request.body.constantRateFactor,
				preset: request.body.preset,
				audioBitrate: request.body.audioBitrate,
				scale: request.body.scale
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(accessToken);

			const videoProfileModel = new VideoProfileModel(response.locals.session);
			const updatedProfile = await videoProfileModel.updateOneById(validPayload.videoProfileId, {
				name: validPayload.name,
				default: validPayload.default,
				level: validPayload.level,
				pixelFormat: validPayload.pixelFormat,
				constantRateFactor: validPayload.constantRateFactor,
				preset: validPayload.preset,
				audioBitrate: validPayload.audioBitrate,
				scale: validPayload.scale
			});
			return response.status(200).json(updatedProfile);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async list(request: Request, response: Response): Promise<Response> {
		let validPayload: BaseAccessRequest;
		try {
			validPayload = await BaseAccessJoi.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(accessToken);

			const videoProfileModel = new VideoProfileModel(response.locals.session);
			const profiles = await videoProfileModel.readAll();
			return response.status(200).json(profiles);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async get(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoProfileGetDeleteOnePayload;
		try {
			validPayload = await VideoProfileGetDeleteOnePayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				videoProfileId: request.params.id
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(accessToken);

			const videoProfileModel = new VideoProfileModel(response.locals.session);
			const profile = await videoProfileModel.readOneById(validPayload.videoProfileId);
			return response.status(200).json(profile);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async delete(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoProfileGetDeleteOnePayload;
		try {
			validPayload = await VideoProfileGetDeleteOnePayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				videoProfileId: request.params.id
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(accessToken);

			const videoProfileModel = new VideoProfileModel(response.locals.session);
			await videoProfileModel.deleteOneById(validPayload.videoProfileId);
			return response.status(204).send();
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}
}

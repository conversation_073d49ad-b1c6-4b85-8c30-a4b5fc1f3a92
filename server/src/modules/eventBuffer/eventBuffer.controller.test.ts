import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { EventBufferDBModel } from "./eventBufferDB.model";

const expressApp = createServer();
initExpressRoutes(expressApp);

describe("EventBufferController", () => {
	it("should accept a new event in the API to store in the buffer", async () => {
		const data = { test: "data" };
		const response = await supertest(expressApp)
			.post("/api/event/buffer")
			.set("x-api-version", "1")
			.send(data);

		if (response.statusCode !== 201) {
			console.error(response.body);
		}

		expect(response.statusCode).toBe(201);
		expect(response.body).toBeDefined();
		expect(response.body.id).toBeDefined();

		// check that the event was stored in the database
		const eventBuffer = await EventBufferDBModel.findById(response.body.id);
		expect(eventBuffer).toBeDefined();
		expect(eventBuffer?.buffer).toBeDefined();
		if (!eventBuffer?.buffer) {
			throw new Error("Event buffer is empty");
		}

		// check that the data stored in the database matches what was sent
		const buffer = JSON.parse(eventBuffer.buffer.toString());
		expect(buffer).toStrictEqual(data);
	});

	it("should reject the request if the api version is less than 1", async () => {
		const response = await supertest(expressApp)
			.post("/api/event/buffer")
			.set("x-api-version", "0")
			.send({});

		expect(response.statusCode).toBe(400);
	});

	it("should reject the request if the api version is not supplied", async () => {
		const response = await supertest(expressApp)
			.post("/api/event/buffer")
			.send({});

		expect(response.statusCode).toBe(400);
	});
});

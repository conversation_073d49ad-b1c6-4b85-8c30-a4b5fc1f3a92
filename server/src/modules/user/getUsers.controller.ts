import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IAccount } from "../account/account.interfaces";
import { readAccount2 } from "../../services/mongodb/account.service";
import { IUser } from "./user.interfaces";
import { UserModel } from "./user.model";

export const getUsersController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing account token");
		}

		const account: IAccount | null = await readAccount2({
			_id: req.accountToken.account._id
		}, null);

		if (!account) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "the account does not exist");
		}

		const userModel = new UserModel(null);
		const users = await userModel.readManyByAccountId(account._id.toString());

		users.forEach((user: IUser) => {
			user.isOwner = (account.ownerUserId?.toString() === user._id.toString());
		});

		return res.status(200).send({ users: users });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

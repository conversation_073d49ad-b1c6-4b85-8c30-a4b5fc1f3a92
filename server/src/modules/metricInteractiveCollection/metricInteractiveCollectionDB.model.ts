import mongoose, { Schema } from "mongoose";
import { IMetricInteractiveCollectionCreated } from "./metricInteractiveCollection.interface";

const MetricInteractiveCollectionCreatedSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	interactiveCollectionId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		default: () => new Date()
	}
}
);

export const MetricInteractiveCollectionCreatedDBModel = mongoose.model<IMetricInteractiveCollectionCreated>(
	"metric_interactive_collection_created",
	MetricInteractiveCollectionCreatedSchema,
	"metric_interactive_collection_created"
);

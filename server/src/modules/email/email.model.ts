import * as path from "path";
import ejs from "ejs";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import { validateBackslash } from "../../utils/helpers/gp.helper";
import {
	EmailInput,
	EmailRenderOptions,
	SendGridEmailData
} from "./email.interface";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";

export class EmailModel {
	private readonly sendGridPath: string = "v3/mail/send";

	public async sendTransactionalEmail(input: EmailInput): Promise<void> {
		const secrets = await getSecrets();
		const emailHtml = await this.renderEmailTemplate({
			...input.data,
			template: input.template,
			host: this.getHost(secrets),
			locale: input.locale
		});
		const emailData = this.buildSendGridEmailData(input, emailHtml, secrets.sendGrid.fromAddress);
		await this.sendEmail(emailData, secrets);
	}

	private async renderEmailTemplate(options: EmailRenderOptions): Promise<string> {
		const templatePath = path.resolve(
			__dirname,
			`../../assets/templates/email/${options.locale.trim().toLowerCase()}/layout.ejs`
		);
		const templateData = {
			...options,
			showLegalLinks: "false",
			showSocialLinks: "false",
			privacyPolicyUrl: process.env.PRIVACY_POLICY_URL_EN,
			termsOfServiceUrl: process.env.TERMS_OF_SERVICE_URL_EN
		};
		return await ejs.renderFile(templatePath, templateData);
	}

	private buildSendGridEmailData(input: EmailInput, htmlContent: string, fromAddress: string): SendGridEmailData {
		return {
			personalizations: [
				{
					to: Array.isArray(input.to)
						? input.to.map(email => ({ email }))
						: [{ email: input.to }],
					subject: input.subject
				}
			],
			from: {
				email: fromAddress
			},
			content: [
				{
					type: "text/html",
					value: htmlContent
				}
			]
		};
	}

	private async sendEmail(emailData: SendGridEmailData, secrets: ISecrets): Promise<void> {
		const sendGridHost = this.getSendGridHost(secrets);

		try {
			const response = await fetch(`${sendGridHost}${this.sendGridPath}`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${secrets.sendGrid.apiKey}`,
					"Content-Type": "application/json"
				},
				body: JSON.stringify(emailData)
			});

			if (!response.ok) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED,
					`sendEmail response is not ok, status=${response.status}`);
			}

		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`Failed to send email. ${(error as Error).message}`);
		}
	}

	private getHost(secrets: ISecrets): string {
		return validateBackslash(secrets.cdn.host);
	}

	private getSendGridHost(secrets: ISecrets): string {
		return validateBackslash(secrets.sendGrid.host);
	}
}

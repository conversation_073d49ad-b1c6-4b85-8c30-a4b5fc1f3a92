import { LocaleAPI } from "../../interfaces/apiTypes";

export interface EmailInput {
	template: EmailTemplateEnum;
	to: string | string[];
	subject: string;
	data: Record<string, any>;
	locale: LocaleAPI;
}

export interface EmailRenderOptions {
	template: string;
	host: string;
	locale: LocaleAPI;
}

export interface SendGridEmailData {
	personalizations: Array<{
		to: Array<{ email: string }>;
		subject: string;
	}>;
	from: { email: string };
	content: Array<{ type: string; value: string }>;
}

export enum EmailTemplateEnum {
	EMAIL_LEAD = "email-lead",
	ACCOUNT_INVITATION = "account-invitation",
}


import express, { type Express } from "express";
import jwt from "jsonwebtoken";
import { AuthenticationModel } from "../authentication/authentication.model";
import { IAccessToken } from "../accessToken/accessToken.interface";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { IAccountToken } from "../account/account.interfaces";

export class Controller {
	protected router = express.Router();

	public use (expressServer: Express, paths: string | string[]): void {
		if (typeof paths === "string") {
			expressServer.use(paths, this.router);
			return;
		}

		paths.forEach((path) => {
			expressServer.use(path, this.router);
		});
	}

	protected async verifyAPIVersion (apiVersion: number, minAPIVersion: number): Promise<void> {
		if (!apiVersion) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"missing x-api-version version header"
			);
		}

		if (apiVersion < minAPIVersion) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				`API version must be greater than or equal to ${minAPIVersion}`
			);
		}
	}

	protected async verifyAccountToken (accountToken: string): Promise<IAccountToken> {
		const decodedAccountToken = await this.decodeToken<IAccountToken>(accountToken);
		const saltedHash = await this.getSaltedHash(decodedAccountToken.authenticationId);

		const verifiedAccountToken = jwt.verify(accountToken, saltedHash);

		if (!verifiedAccountToken) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION,
				"invalid account token");
		}

		return verifiedAccountToken as IAccountToken;
	}

	protected async verifyAccessToken (accessToken: string): Promise<IAccessToken> {
		const decodedAccessToken = await this.decodeToken<IAccessToken>(accessToken);
		const saltedHash = await this.getSaltedHash(decodedAccessToken.authenticationId);

		const verifiedAccessToken = jwt.verify(accessToken, saltedHash);

		if (!verifiedAccessToken) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION,
				"invalid access token");
		}

		return verifiedAccessToken as IAccessToken;
	}

	protected assertSuperUserAccess(accessToken: IAccessToken): void {
		if (!accessToken.super) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"User is not allowed to make this request.").suppressLog();
		}
	}

	private async decodeToken <T>(token: string): Promise<T> {
		const decodedToken = jwt.decode(token) as T;

		if (!decodedToken) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "invalid token");
		}

		return decodedToken;
	}

	private async getSaltedHash (authenticationId: string): Promise<string> {
		const authenticationModel = new AuthenticationModel(null);

		const authenticationDocument = await authenticationModel.readOneById(authenticationId);

		const secrets: ISecrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		if (!gpSecretKey) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "failed to read the hash key");
		}

		const saltedHash = `${gpSecretKey}${authenticationDocument.salt}`;

		return saltedHash;
	}
}

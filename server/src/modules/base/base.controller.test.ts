import { Controller } from "./base.controller";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import express, {
	Request,
	Response
} from "express";
import { BaseJoi } from "./base.joi";
import { APIError } from "../../utils/helpers/apiError";
import supertest from "supertest";

class TestController extends Controller {
	constructor() {
		super();

		this.router.post(
			"/",
			express.json(),
			async (request: Request, response: Response) => {
				try {
					const validatedRequest = await BaseJoi.validateAsync({
						apiVersion: request.headers["x-api-version"],
						accessToken: request.headers.authorization,
						accountToken: request.headers["x-account-token"]
					});

					await this.verifyAPIVersion(validatedRequest.apiVersion, 1);
					await this.verifyAccessToken(validatedRequest.accessToken);
					await this.verifyAccountToken(validatedRequest.accountToken);

					return response.json({});
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}
}

const expressApp = createServer();
const controller = new TestController();
controller.use(expressApp, "/test/test");

initExpressRoutes(expressApp);

describe("base controller", () => {
	let accessToken: string;
	let accountToken: string;

	beforeAll(async () => {
		const signupRes = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: "<EMAIL>",
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (signupRes.statusCode !== 200) {
			console.error(signupRes.body);
		}

		accessToken = signupRes.body.accessToken;

		const accountsRes = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3");

		if (accountsRes.statusCode !== 200) {
			console.error(accountsRes.body);
		}

		expect(accountsRes.statusCode).toBe(200);

		const accountTokenRes = await supertest(expressApp)
			.post("/api/accounts/token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3")
			.field("accountId", accountsRes.body.accounts[0]._id);

		if (accountTokenRes.statusCode !== 200) {
			console.error(accountTokenRes.body);
		}

		expect(signupRes.statusCode).toBe(200);

		accountToken = accountTokenRes.body.token;
	});

	it("should test the base controller functions", async () => {
		const res = await supertest(expressApp)
			.post("/test/test")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send({});

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
	});
});

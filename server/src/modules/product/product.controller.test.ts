/* eslint-disable max-lines-per-function */
import TestHelper from "../../__tests__/mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { Product } from "./product.interfaces";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../signup/signup.interfaces";

describe("/api/products", () => {
	let expressApp: express.Express;
	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("GET", async () => {
		const userPayload = {
			firstName: "Johnny",
			lastName: "Products",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "ProductController Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		const { accessToken } = await testHelper.createUserAndAccount(userPayload);

		const resProduct = await supertest(expressApp)
			.get("/api/products")
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(resProduct.statusCode).toBe(200);
		expect(resProduct).toHaveProperty("body");

		const products = resProduct.body;

		const basicProduct = products?.find((product: { type: string; }) => product.type === "basic");
		expectProduct(basicProduct, "basic");

		const proProduct = products?.find((product: { type: string; }) => product.type === "pro");
		expectProduct(proProduct, "pro");

	});

	const expectProduct = (
		productData: Product,
		expectedType: string): void => {
		expect(productData).toBeDefined();
		expect(productData).not.toBeNull();
		expect(productData).toHaveProperty("type");
		expect(productData.type).toBe(expectedType);
		expect(productData).toHaveProperty("prices");
	};
});

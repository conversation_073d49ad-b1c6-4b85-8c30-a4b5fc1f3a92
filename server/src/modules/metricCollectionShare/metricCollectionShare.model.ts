import mongoose, { ClientSession } from "mongoose";
import {
	IMetricCollectionShareCreateInput,
	IMetricCollectionShareCreated
} from "./metricCollectionShare.interface";
import { MetricCollectionShareCreatedDBModel } from "./metricCollectionShareDB.model";

export class MetricCollectionShareModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async createOne(
		createData: IMetricCollectionShareCreateInput
	): Promise<IMetricCollectionShareCreated> {
		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new MetricCollectionShareCreatedDBModel({
			createdAt: createData.createdAt,
			accountId: createData.accountId,
			collectionId: createData.collectionId
		}).save(options);

		return newDocument;
	}
}

import mongoose, { Schema } from "mongoose";
import { IMetricCollectionShareCreated } from "./metricCollectionShare.interface";

const MetricCollectionShareCreatedSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	collectionId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricCollectionShareCreatedDBModel = mongoose.model<IMetricCollectionShareCreated>(
	"collection_share_link_copy_metric",
	MetricCollectionShareCreatedSchema,
	"collection_share_link_copy_metric"
);

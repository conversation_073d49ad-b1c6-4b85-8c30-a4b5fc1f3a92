export class Cache {
	private cache: Map<string, boolean>;
	private cacheTTLms: number;
	private cacheCleanupInterval: NodeJS.Timeout | undefined;

	constructor (cacheTTLms: number) {
		this.cache = new Map<string, boolean>();
		this.cacheTTLms = cacheTTLms;
	}

	set (key: string, value: boolean): void {
		clearTimeout(this.cacheCleanupInterval);
		this.cache.set(key, value);
		this.cacheCleanupInterval = setTimeout(() => {
			this.cache.delete(key);
		}, this.cacheTTLms);
	}

	get (key: string): boolean | undefined {
		return this.cache.get(key);
	}
}

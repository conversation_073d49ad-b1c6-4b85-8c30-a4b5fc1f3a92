import mongoose, {
	<PERSON>lientSession,
	FilterQuery,
	PipelineStage,
	UpdateQuery,
	QueryOptions
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricVideoPlayTimeDBModel } from "./metricVideoPlayTimeDB.model";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import {
	MetricVideoPlayTimeCreateOne,
	IMetricVideoPlayTime,
	IVideoPlayTimeInput,
	IVideoPlayTimeOutput
} from "./metricVideoPlayTime.interfaces";
import { PlayPercentCounts } from "./metricVideoPlayTime.enum";

export class MetricVideoPlayTimeModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	private determineFieldToUpdate(percentage: number): PlayPercentCounts {
		if (percentage >= 0 && percentage <= 20) {
			return PlayPercentCounts.playPercentCount20;
		} else if (percentage >= 21 && percentage <= 40) {
			return PlayPercentCounts.playPercentCount40;
		} else if (percentage >= 41 && percentage <= 60) {
			return PlayPercentCounts.playPercentCount60;
		} else if (percentage >= 61 && percentage <= 80) {
			return PlayPercentCounts.playPercentCount80;
		} else if (percentage >= 81 && percentage <= 100) {
			return PlayPercentCounts.playPercentCount100;
		}
		throw new APIError(APIErrorName.E_INVALID_METRIC_INPUT, `invalid percentage=${percentage}`).suppressLog();
	}

	private computePercentage(position: number, length: number): number {
		if (length === 0) {
			throw new APIError(APIErrorName.E_INVALID_METRIC_INPUT,
				"Cannot computePercentage with videoSecondsLength=0");
		}
		return Math.floor((position / length) * 100);
	}

	private async getCurrentMaxPlayTime(accountId: string, videoId: string, appSessionId: string): Promise<number> {
		const pipeline: PipelineStage[] = [
			{
				$match: {
					accountId: new mongoose.Types.ObjectId(accountId),
					videoId: new mongoose.Types.ObjectId(videoId),
					appSessionId: new mongoose.Types.ObjectId(appSessionId)
				}
			},
			{ $group: { _id: null, maxPlayTimeSeconds: { $max: "$totalPlayTimeSeconds" } } }
		];

		const result = await MetricVideoPlayTimeDBModel.aggregate(pipeline).session(this.session);
		return result?.[0]?.maxPlayTimeSeconds ?? 0;
	}

	private computePercentDifferences(
		videoSecondsPosition: number, videoSecondsLength: number, currentMaxPlayTime: number)
		: {
			newPercentToInc: PlayPercentCounts | null,
			oldPercentToDec: PlayPercentCounts | null,
			oldPlayDurationSeconds: number | null
		} {

		let newPercentToInc: PlayPercentCounts | null = null;
		let oldPercentToDec: PlayPercentCounts | null = null;
		let oldPlayDurationSeconds: number | null = null;

		if (videoSecondsPosition > currentMaxPlayTime) {
			const newPlayedPercent = this.computePercentage(videoSecondsPosition, videoSecondsLength);
			newPercentToInc = this.determineFieldToUpdate(newPlayedPercent);

			if (currentMaxPlayTime > 0) {
				const oldPlayedPercent = this.computePercentage(currentMaxPlayTime, videoSecondsLength);
				oldPercentToDec = this.determineFieldToUpdate(oldPlayedPercent);
				oldPlayDurationSeconds = currentMaxPlayTime;
			}

			if (newPercentToInc === oldPercentToDec) {
				newPercentToInc = null;
				oldPercentToDec = null;
				oldPlayDurationSeconds = null;
			}
		}
		return { newPercentToInc, oldPercentToDec, oldPlayDurationSeconds };
	}


	private async upsertSinglePlayTime(input: IVideoPlayTimeInput,
		account: IAccount, createdAt: Date): Promise<{ wasCreated: boolean; oldDocPlayTimeSeconds: number }> {
		const { accountId, videoId, playId, appSessionId, videoSecondsPosition, videoPlayStatus } = input;

		const filter: FilterQuery<IMetricVideoPlayTime> = {
			accountId: new mongoose.Types.ObjectId(accountId),
			videoId: new mongoose.Types.ObjectId(videoId),
			playId: new mongoose.Types.ObjectId(playId),
			appSessionId: new mongoose.Types.ObjectId(appSessionId)
		};
		const existingVideoPlayTime = await MetricVideoPlayTimeDBModel.findOne(filter).session(this.session);

		if (existingVideoPlayTime) {
			const oldPosition = existingVideoPlayTime.totalPlayTimeSeconds;
			if (videoSecondsPosition >= oldPosition) {
				existingVideoPlayTime.totalPlayTimeSeconds = videoSecondsPosition;
				existingVideoPlayTime.videoPlayStatus = videoPlayStatus;
				existingVideoPlayTime.updatedAt = new Date();
				await existingVideoPlayTime.save({ session: this.session });
			}
			return { wasCreated: false, oldDocPlayTimeSeconds: oldPosition };
		}
		await this.createOne(
			{
				videoId: videoId,
				playId: playId,
				videoShare: input.videoShare,
				collectionShare: input.collectionShare,
				appSessionId: appSessionId,
				totalPlayTimeSeconds: videoSecondsPosition,
				videoPlayStatus: videoPlayStatus,
				createdAt: createdAt,
				updatedAt: createdAt
			},
			account
		);
		return { wasCreated: true, oldDocPlayTimeSeconds: 0 };
	}

	async upsertVideoPlayTimeAndComputeMetrics(input: IVideoPlayTimeInput,
		account: IAccount, createdAt: Date): Promise<IVideoPlayTimeOutput> {
		const { accountId, videoId, appSessionId, videoSecondsPosition, videoSecondsLength } = input;

		const currentMaxPlayTime = await this.getCurrentMaxPlayTime(accountId, videoId, appSessionId);

		const { newPercentToInc, oldPercentToDec, oldPlayDurationSeconds } =
			this.computePercentDifferences(videoSecondsPosition, videoSecondsLength, currentMaxPlayTime);

		const { wasCreated, oldDocPlayTimeSeconds } = await this.upsertSinglePlayTime(input, account, createdAt);

		return {
			newPercentToInc, oldPercentToDec, oldPlayDurationSeconds, wasCreated, oldDocPlayTimeSeconds
		};
	}

	async countDocuments(filter: FilterQuery<IMetricVideoPlayTime>): Promise<number> {
		const count: number = await MetricVideoPlayTimeDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async isPlaysMetricLimitReached(account: IAccount): Promise<boolean> {
		const startOfMonth = new Date();
		startOfMonth.setDate(1);
		startOfMonth.setHours(0, 0, 0, 0);

		const startOfMonthISOString = startOfMonth.toISOString();

		const now = new Date();
		now.setHours(23, 59, 59, 999);

		const nowISOString = now.toISOString();

		const filter: FilterQuery<IMetricVideoPlayTime> = {
			accountId: account._id,
			createdAt: {
				$gte: startOfMonthISOString,
				$lte: nowISOString
			}
		};

		const documentCount = await this.countDocuments(filter);
		const limit = account.subscription.maxPlaysMetricPerMonth;

		return documentCount >= limit;
	}

	async createOne(
		createData: MetricVideoPlayTimeCreateOne,
		account: IAccount
	): Promise<IMetricVideoPlayTime> {

		if (await this.isPlaysMetricLimitReached(account)) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Metric video plays limit reached");
		}

		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new MetricVideoPlayTimeDBModel({
			videoId: new mongoose.Types.ObjectId(createData.videoId),
			appSessionId: new mongoose.Types.ObjectId(createData.appSessionId),
			playId: new mongoose.Types.ObjectId(createData.playId),
			totalPlayTimeSeconds: createData.totalPlayTimeSeconds,
			videoPlayStatus: createData.videoPlayStatus,
			videoShare: createData.videoShare
				? new mongoose.Types.ObjectId(createData.videoShare)
				: undefined,
			collectionShare: createData.collectionShare ?
				new mongoose.Types.ObjectId(createData.collectionShare)
				: undefined,
			accountId: account._id,
			createdAt: createData.createdAt,
			updatedAt: createData.updatedAt
		}).save(options);
		return newDocument;
	}


	async readTotalPrevDayByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, {
		totalVideoPlaysDaily: number; totalPlayTimeDaily: number;
	}>>> {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfPreviousDay = new Date(startOfToday.getTime() - (24 * 60 * 60 * 1000));
		const endOfPreviousDay = new Date(startOfToday.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousDay, $lte: endOfPreviousDay }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalVideoPlaysDaily: { $sum: 1 },
					totalPlayTimeDaily: { $sum: "$totalPlayTimeSeconds" }
				}
			}
		];

		const results = await MetricVideoPlayTimeDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, { totalVideoPlaysDaily: number; totalPlayTimeDaily: number; }>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_video_play_times found for the previous day")
			);
		}

		const totalPlayMetricsByAccountId: Map<string, { totalVideoPlaysDaily: number; totalPlayTimeDaily: number; }> =
			new Map(
				results.map(item => [
					item._id.toString(),
					{
						totalVideoPlaysDaily: item.totalVideoPlaysDaily,
						totalPlayTimeDaily: item.totalPlayTimeDaily
					}
				])
			);

		return new ModelResponse<Map<string, { totalVideoPlaysDaily: number; totalPlayTimeDaily: number; }>>(
			totalPlayMetricsByAccountId, null
		);

	}



	async readTotalPrevWeekByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, {
		totalVideoPlaysWeekly: number; totalPlayTimeWeekly: number;
	}>>> {
		const now = new Date();
		const startOfThisWeek = new Date(Date.UTC(
			now.getUTCFullYear(),
			now.getUTCMonth(),
			now.getUTCDate() - now.getUTCDay() + 1
		));
		const startOfPreviousWeek = new Date(
			startOfThisWeek.getTime() - (7 * 24 * 60 * 60 * 1000)
		);
		const endOfPreviousWeek = new Date(startOfThisWeek.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousWeek, $lte: endOfPreviousWeek }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalVideoPlaysWeekly: { $sum: 1 },
					totalPlayTimeWeekly: { $sum: "$totalPlayTimeSeconds" }
				}
			}
		];

		const results = await MetricVideoPlayTimeDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, { totalVideoPlaysWeekly: number; totalPlayTimeWeekly: number; }>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_video_play_times found for the previous week")
			);
		}

		const totalPlayMetricsByAccountId: Map<string, {
			totalVideoPlaysWeekly: number; totalPlayTimeWeekly: number;
		}> =
			new Map(
				results.map(item => [
					item._id.toString(),
					{
						totalVideoPlaysWeekly: item.totalVideoPlaysWeekly,
						totalPlayTimeWeekly: item.totalPlayTimeWeekly
					}
				])
			);

		return new ModelResponse<Map<string, { totalVideoPlaysWeekly: number; totalPlayTimeWeekly: number; }>>(
			totalPlayMetricsByAccountId, null
		);

	}

	public async countTotalPlaysByDateRange(accountId: string, start: Date, end: Date): Promise<number> {
		const filter: FilterQuery<IMetricVideoPlayTime> = {
			accountId: new mongoose.Types.ObjectId(accountId),
			videoPlayStatus: "stopped",
			createdAt: { $gte: start, $lte: end }
		};
		const count = await MetricVideoPlayTimeDBModel.countDocuments(filter).session(this.session);
		return count;
	}

	public async sumPlayTimeSecondsByDateRange(accountId: string, start: Date, end: Date): Promise<number> {
		const pipeline: PipelineStage[] = [
			{
				$match: {
					accountId: new mongoose.Types.ObjectId(accountId),
					videoPlayStatus: "stopped",
					createdAt: { $gte: start, $lte: end }
				}
			},
			{
				$group: { _id: null, total: { $sum: "$totalPlayTimeSeconds" } }
			}
		];

		const result = await MetricVideoPlayTimeDBModel.aggregate(pipeline).session(this.session);
		return (result && result[0] && result[0].total) || 0;
	}

	public async markPendingPlaytimeAsStopped(): Promise<number> {
		const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
		const filter: FilterQuery<IMetricVideoPlayTime> = {
			$or: [
				{ videoPlayStatus: { $exists: false } },
				{ videoPlayStatus: "playing", updatedAt: { $lt: oneHourAgo } }
			]
		};
		const update: UpdateQuery<IMetricVideoPlayTime> = {
			$set: { videoPlayStatus: "stopped", updatedAt: new Date() }
		};
		const options: QueryOptions = { session: this.session };

		const result = await MetricVideoPlayTimeDBModel.updateMany(filter, update, options);
		return result.modifiedCount;
	}
}

import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IPostInvitationPayload,
	InvitationEmailInput,
	PostInvitationResponse
} from "../../modules/invitation/invitation.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { InvitationModel } from "./invitation.model";

export const postInvitationController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing account token");
		}

		const requestBody = req.body as IPostInvitationPayload;

		if (!requestBody.email) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing email parameter in input");
		}

		if (!requestBody.locale) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing locale parameter in input");
		}

		if (!requestBody.callbackEndpoint) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing callbackEndpoint parameter in input");
		}

		const invitationModel = new InvitationModel(null);
		const invitationResult = await invitationModel.createOne(
			req.accountToken.account._id,
			requestBody.email
		);

		const invitationEmail: InvitationEmailInput = {
			email: requestBody.email,
			companyName: req.accountToken.account.companyName,
			callbackEndpoint: requestBody.callbackEndpoint,
			locale: requestBody.locale,
			invitationToken: invitationResult.token
		};

		await invitationModel.sendEmail(invitationEmail);

		const invitationResponse: PostInvitationResponse = {
			_id: invitationResult.document._id,
			accountId: invitationResult.document.accountId,
			email: invitationResult.document.email,
			status: invitationResult.document.status,
			createdAt: invitationResult.document.createdAt,
			userId: invitationResult.document.userId
		};

		return res.status(200).send({
			invitation: invitationResponse
		});
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

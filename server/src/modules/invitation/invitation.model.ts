import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import {
	IInvitation,
	InvitationCreateOne,
	InvitationCreateOneResult,
	InvitationEmailInput
} from "./invitation.interfaces";
import { InvitationDBModel } from "./invitationDB.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { getSecrets } from "../secrets/secrets.model";
import { IAccount } from "../account/account.interfaces";
import { IInvitationToken } from "../invitation/invitation.interfaces";
import jwt from "jsonwebtoken";
import { AccountModel } from "../account/account.model";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../services/mongodb/transaction.service";
import { AccountUserModel } from "../account/user/account.user.model";
import { readInvitations } from "../../services/mongodb/invitations.service";
import { InvitationStatus } from "./invitation.enum";
import bcrypt from "bcryptjs";
import { UserModel } from "../user/user.model";
import { createInvitationToken } from "../../utils/tokens";
import {
	EmailInput,
	EmailTemplateEnum
} from "../email/email.interface";
import { sendTransactionalEmail } from "../../services/email/email.service";

export class InvitationModel {

	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async createOne (accountId: string, email: string): Promise<InvitationCreateOneResult> {
		try {
			await startDBTransaction(this.session);

			const accountModel = new AccountModel(this.session);
			const accountDocument = await accountModel.readOneById(accountId);

			const accountUserModel: AccountUserModel = new AccountUserModel(this.session);
			if (await accountUserModel.isUserLimitReached(accountDocument)) {
				throw new APIError(APIErrorName.E_USER_LIMIT_REACHED, "User limit reached").suppressLog();
			}

			email = email.toLowerCase().trim();

			const accountUser = await accountUserModel.readOneByEmail(email, accountId);
			if (accountUser) {
				throw new APIError(APIErrorName.E_EMAIL_ALREADY_EXISTS, "Email already exists")
					.suppressLog()
					.setDetail({
						email: email
					});
			}

			const invitations: IInvitation[] = await readInvitations({
				email: email,
				accountId: new mongoose.Types.ObjectId(accountId)
			}, null);

			if (invitations.length > 0) {
				throw new APIError(APIErrorName.E_EMAIL_ALREADY_INVITED, "Email already invited").suppressLog();
			}

			const salt = await bcrypt.genSalt(10);

			const createData: InvitationCreateOne = {
				accountId: accountId,
				email: email,
				status: InvitationStatus.PENDING,
				salt: salt
			};

			const invitationDBModel = new InvitationDBModel(createData);
			const invitationDocument = await invitationDBModel.save({ session: this.session });

			const userModel = new UserModel(this.session);
			const userExists = await userModel.exists(email);

			const invitationToken = await createInvitationToken(invitationDocument, accountDocument, userExists);

			await completeDBTransaction(this.session);

			const result: InvitationCreateOneResult = {
				document: invitationDocument,
				token: invitationToken
			};

			return result;
		} catch (error: unknown) {
			await cancelDBTransaction(this.session);
			throw error;
		}
	}

	async countDocuments (filter: FilterQuery<IInvitation>): Promise<number> {
		const count: number = await InvitationDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	public async readOneById (_id: string): Promise<IInvitation> {
		const filter: FilterQuery<IInvitation> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const document = await InvitationDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Invitation document not found.");
		}
		return document;
	}

	async verifyInvitationToken(inviteToken: string, email: string):Promise<IAccount> {

		const secrets = await getSecrets();

		const invitationToken = <IInvitationToken> jwt.decode(inviteToken);
		if (!invitationToken) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid invitation token");
		}
		const invitationDocument = await this.readOneById(invitationToken.invitationId);

		try {
			jwt.verify(inviteToken, `${secrets.hashkey}${invitationDocument.salt}`);
		} catch (error) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid or expired invitation token");
		}

		if (email !== invitationToken.email) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Email does not match invitation");
		}

		const accountModel = new AccountModel(this.session);
		const accountDocument = await accountModel.readOneById(invitationDocument.accountId.toString());
		return accountDocument;
	}

	async sendEmail (input: InvitationEmailInput): Promise<void> {
		const email = input.email.toLowerCase().trim();

		const emailInput: EmailInput = {
			template: EmailTemplateEnum.ACCOUNT_INVITATION,
			to: email,
			subject: "Account Invitation",
			data: {
				companyName: input.companyName,
				link: `${input.callbackEndpoint}?invite=${input.invitationToken}`
			},
			locale: input.locale
		};

		const emailSent = await sendTransactionalEmail(emailInput);

		if (!emailSent) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, `Failed to send email to: ${email}`);
		}
	}
}

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { aggregateInvitations } from "../../services/mongodb/invitations.service";
import { InvitationStatus } from "../../modules/invitation/invitation.enum";
import { IInvitation } from "../../modules/invitation/invitation.interfaces";
import { UserModel } from "../user/user.model";

export const getInvitationsSelfController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing access token");
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(req.accessToken.userId);

		const invitations: IInvitation[] = await aggregateInvitations([
			{
				$match: {
					email: userDocument.email,
					status: {
						$in: [InvitationStatus.PENDING, InvitationStatus.DECLINED]
					}
				}
			},
			{
				$lookup: {
					from: "accounts",
					localField: "accountId",
					foreignField: "_id",
					as: "account"
				}
			},
			{
				$unset: [
					"salt"
				]
			}
		], null);

		return res.status(200).send({ invitations: invitations });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

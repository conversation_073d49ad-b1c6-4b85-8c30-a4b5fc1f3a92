import mongoose, { Schema } from "mongoose";
import { IInvitation } from "./invitation.interfaces";

const InvitationSchema: Schema = new Schema({
	accountId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	createdAt: {
		type: Date,
		default: new Date().toISOString(),
		required: true
	},
	email: {
		type: String,
		required: true
	},
	status: {
		type: String,
		required: true
	},
	salt: {
		type: String,
		required: true
	},
	userId: {
		type: Schema.Types.ObjectId,
		required: false
	}
});

export const InvitationDBModel = mongoose.model<IInvitation>("Invitations", InvitationSchema);

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import mongoose from "mongoose";
import { AuthenticationModel } from "../../modules/authentication/authentication.model";
import { updateInvitation } from "../../services/mongodb/invitations.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { InvitationStatus } from "../../modules/invitation/invitation.enum";
import {
	IPutInvitationPayload,
	IInvitation
} from "../../modules/invitation/invitation.interfaces";
import { UserModel } from "../user/user.model";

export const putInvitationController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing access token");
		}

		const requestBody = req.body as IPutInvitationPayload;

		if (!requestBody.status) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing status");
		}

		if (requestBody.status !== InvitationStatus.ACCEPTED && requestBody.status !== InvitationStatus.DECLINED) {
			throw new APIError(APIErrorName.E_INVALID_INPUT,
				"Invalid status input received. Only values of 'accepted' or" +
				` 'declined' are allowed. received ${requestBody.status}`);
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(req.accessToken.userId);

		await startDBTransaction(res.locals.session);

		const invitationDocument: IInvitation | null = await updateInvitation({
			_id: req.params.invitationId,
			email: userDocument.email
		}, {
			status: requestBody.status,
			userId: new mongoose.Types.ObjectId(req.accessToken.userId)
		}, res.locals.session);

		if (!invitationDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Invitation not found");
		}

		if (invitationDocument.status === InvitationStatus.ACCEPTED) {
			const userId = req.accessToken.userId;
			const accountId = invitationDocument.accountId.toString();
			const authenticationModel = new AuthenticationModel(res.locals.session);
			await authenticationModel.attachAccountByUserId(userId, accountId);
		}

		await completeDBTransaction(res.locals.session);

		return res.status(200).send({
			invitation: <IInvitation>{
				_id: invitationDocument._id,
				accountId: invitationDocument.accountId,
				email: invitationDocument.email,
				status: invitationDocument.status,
				createdAt: invitationDocument.createdAt,
				userId: invitationDocument.userId
			}
		});
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

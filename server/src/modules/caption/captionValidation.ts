import Jo<PERSON> from "joi";
import { CaptionData } from "./caption.interface";

export const validateCaptionData = (value: string, helpers: Joi.CustomHelpers): string | Joi.ErrorReport => {
	try {
		const captionData: CaptionData = JSON.parse(value);

		if (captionData.textColor) {
			const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
			if (!hexColorRegex.test(captionData.textColor)) {
				return helpers.error("captionData.invalidTextColor");
			}
		}

		if (captionData.backgroundColor) {
			const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
			if (!hexColorRegex.test(captionData.backgroundColor)) {
				return helpers.error("captionData.invalidBackgroundColor");
			}
		}

		return value;
	} catch (error) {
		return helpers.error("captionData.invalidJSON");
	}
};

export const captionDataValidationMessages = {
	"captionData.invalidJSON": "Invalid JSON format for captionData",
	"captionData.invalidTextColor": "textColor must be a valid 24-bit or 32-bit hex color",
	"captionData.invalidBackgroundColor": "backgroundColor must be a valid 24-bit or 32-bit hex color"
};

import { Schema } from "mongoose";

const CaptionTextSchema: Schema = new Schema(
	{
		startTime: { type: Number, required: true },
		endTime: { type: Number, required: true },
		text: { type: String, required: true }
	}
);

export const CaptionSchema: Schema = new Schema(
	{
		enabled: { type: Boolean, required: true },
		captionText: { type: [CaptionTextSchema], required: true },
		textColor: { type: String, required: true },
		backgroundColor: { type: String, required: true },
		fontSize: { type: Number, required: true },
		xPos: { type: Number, required: true },
		yPos: { type: Number, required: true }
	}
);

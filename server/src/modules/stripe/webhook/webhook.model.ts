import Stripe from "stripe";
import { StripeSubscriptionModel } from "../subscription/subscription.model";
import { AccountModel } from "../../account/account.model";
import { IAccount } from "../../account/account.interfaces";
import { getSecrets } from "../../secrets/secrets.model";
import { StripePriceModel } from "../price/price.model";
import { StripeCustomerModel } from "../customer/customer.model";
import { StripePaymentModel } from "../payment/payment.model";
import { StripeInvoicesModel } from "../invoice/invoice.model";
import { UserModel } from "../../user/user.model";
import { AccountManifestModel } from "../../account/manifest/account.manifest.model";

export class StripeWebhookModel {
	public async constructEvent (signature: string, payload: string): Promise<Stripe.Event> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const event: Stripe.Event = stripe.webhooks.constructEvent(
			payload,
			signature,
			secrets.stripe.webhookSecret
		);

		return event;
	}

	public async handleEvent (event: Stripe.Event): Promise<unknown> {
		if (!event.type) {
			return;
		}

		if (event.type === "customer.subscription.created") {
			return await this.handleSubscriptionCreated(event);
		}

		if (event.type === "customer.subscription.deleted") {
			return await this.handleSubscriptionDeleted(event);
		}

		if (event.type === "customer.subscription.updated") {
			return await this.handleSubscriptionUpdated(event);
		}

		if (event.type === "customer.created") {
			return await this.handleCustomerCreated(event);
		}

		if (event.type === "customer.updated") {
			return await this.handleCustomerUpdated(event);
		}

		if (event.type === "customer.deleted") {
			return await this.handleCustomerDeleted(event);
		}

		if (event.type === "invoice.payment_failed") {
			return await this.handleInvoicePaymentFailed(event);
		}

		if (event.type === "invoice.created") {
			return await this.handleInvoiceCreated(event);
		}

		if (event.type === "invoice.deleted") {
			return await this.handleInvoiceDeleted(event);
		}

		if (event.type === "payment_method.attached") {
			return await this.handlePaymentMethodAttached(event);
		}

		if (event.type === "payment_method.detached") {
			return await this.handlePaymentMethodDetached(event);
		}

		if (event.type === "payment_method.updated") {
			return await this.handlePaymentMethodUpdated(event);
		}

		if (event.type === "payment_method.automatically_updated") {
			return await this.handlePaymentMethodUpdated(event);
		}

		if (event.type === "test_helpers.test_clock.ready") {
			return await this.handleTestClockReady(event);
		}
	}

	private async isTestClockRunning(stripe: Stripe, subscription: Stripe.Subscription): Promise<boolean> {
		if (!subscription.test_clock) {
			return false;
		}

		const testClock = await stripe.testHelpers.testClocks.retrieve(subscription.test_clock as string);

		return testClock.status === "advancing";
	}

	private async waitForTestClockToComplete(
		stripe: Stripe,
		subscription: Stripe.Subscription,
		intervalMs: number
	): Promise<void> {
		if (!subscription?.test_clock) {
			return;
		}

		return new Promise<void>((resolve, reject) => {
			const intervalId = setInterval(async () => {
				try {
					const isRunning = await this.isTestClockRunning(stripe, subscription);
					if (!isRunning) {
						clearInterval(intervalId);
						resolve();
					}
				} catch (error) {
					clearInterval(intervalId);
					reject(error);
				}
			}, intervalMs);
		});
	}

	private async voidPendingInvoices (stripe: Stripe, customerId: string): Promise<void> {
		const stripeInvoicesModel = new StripeInvoicesModel();
		const pendingInvoices = await stripeInvoicesModel.getInvoices(customerId, "open");

		const promises = pendingInvoices.map(async (pendingInvoice) => {
			await stripe.invoices.voidInvoice(pendingInvoice.id);
		});

		await Promise.all(promises);
	}

	private async getProduct (productId: string): Promise<Stripe.Product | null> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		try {
			return await stripe.products.retrieve(productId);
		} catch (error: unknown) {
			if (error instanceof Error &&
				"type" in error &&
				(error as any).type === "StripeInvalidRequestError" &&
				(error as any).code === "resource_missing") {
				return null;
			}

			throw error;
		}
	}

	private isProduct (product: Stripe.Product): boolean {
		return product?.metadata?.gp === "true";
	}

	private isSubscription (subscription: Stripe.Subscription): boolean {
		return subscription?.metadata?.gp === "true";
	}

	private async handleInvoicePaymentFailed (event: Stripe.Event): Promise<Stripe.Subscription | null> {
		const invoice: Stripe.Invoice = event.data.object as Stripe.Invoice;

		const productId = invoice?.lines?.data[0]?.price?.product as string;

		const product = await this.getProduct(productId);

		if (!product) {
			return null;
		}

		if (!this.isProduct(product)) {
			return null;
		}

		if (invoice.attempt_count > 0) {
			const secrets = await getSecrets();
			const stripe = new Stripe(secrets.stripe.privateKey);

			await this.voidPendingInvoices(stripe, invoice.customer as string);

			const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);

			await this.waitForTestClockToComplete(stripe, subscription, 1000);

			const stripePriceModel = new StripePriceModel();
			const freePrice = await stripePriceModel.getFreePrice();

			const stripeSubscriptionModel = new StripeSubscriptionModel();
			return await stripeSubscriptionModel.updateSubscription(subscription.id, freePrice.id, true);
		}

		return null;
	}

	private async handleSubscriptionCreated (event: Stripe.Event): Promise<boolean> {
		const subscription = event.data.object as Stripe.Subscription;

		if (!this.isSubscription(subscription)) {
			return false;
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const customer = await stripe.customers.retrieve(subscription.customer as string);

		if (customer.deleted === true) {
			return true;
		}

		await stripe.customers.update(customer.id, {
			metadata: {
				billingCycleStart: subscription.current_period_start.toString()
			}
		});

		return true;
	}

	private async handleSubscriptionUpdated (event: Stripe.Event): Promise<boolean> {
		const updatedSubscription = event.data.object as Stripe.Subscription;

		if (!this.isSubscription(updatedSubscription)) {
			return false;
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		await this.waitForTestClockToComplete(stripe, updatedSubscription, 1000);

		const customer = await stripe.customers.retrieve(updatedSubscription.customer as string);

		if (customer.deleted === true) {
			return true;
		}

		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);

		await accountModel.setSubscription(account._id.toString(), updatedSubscription.id);

		const previousAttributes = event.data.previous_attributes as Stripe.Subscription;

		if (previousAttributes.current_period_start !== updatedSubscription.current_period_start) {
			const customer = await stripe.customers.retrieve(updatedSubscription.customer as string);
			if (customer.deleted === true) {
				return true;
			}

			const currentStart = Number(customer.metadata.billingCycleStart || "0");

			if (currentStart !== updatedSubscription.current_period_start) {
				await stripe.customers.update(customer.id, {
					metadata: {
						billingCycleStart: updatedSubscription.current_period_start.toString()
					}
				});
			}
		}

		return true;
	}

	private async handleSubscriptionDeleted (event: Stripe.Event): Promise<boolean> {
		const subscription: Stripe.Subscription = event.data.object as Stripe.Subscription;

		if (!this.isSubscription(subscription)) {
			return false;
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const customer = await stripe.customers.retrieve(subscription.customer as string);
		if (customer.deleted === true) {
			return true;
		}

		try {
			const accountModel = new AccountModel(null);
			await accountModel.readOneByStripeCustomer(customer.id);
		} catch (error: unknown) {
			return true;
		}

		await this.waitForTestClockToComplete(stripe, subscription, 1000);

		const priceModel = new StripePriceModel();
		const freePrice = await priceModel.getFreePrice();

		const subscriptionModel = new StripeSubscriptionModel();
		const newSubscription = await subscriptionModel.createSubscription(customer.id, freePrice);
		await this.setSubscription(customer.id, newSubscription.id);
		return true;
	}

	private async handleCustomerCreated (event: Stripe.Event): Promise<void> {
		const customer = event.data.object as Stripe.Customer;
		await this.onBillingCycleStart(customer);
	}

	private async handleCustomerUpdated (event: Stripe.Event): Promise<void> {
		const customer = event.data.object as Stripe.Customer | Stripe.DeletedCustomer;

		if (customer.deleted === true) {
			return;
		}

		const previousAttributes = event.data.previous_attributes as Stripe.Customer;

		if (previousAttributes.metadata?.billingCycleStart !== undefined) {
			if (previousAttributes.metadata?.billingCycleStart !== customer.metadata?.billingCycleStart) {
				await this.onBillingCycleStart(customer);
			}
		}

		await this.setSubscription(customer.id);
	}

	private async handleCustomerDeleted (event: Stripe.Event): Promise<void> {
		// Note: there is a lot of duplication here with the AccountModel.create function.
		// This could likely be consolidated into a single function.
		// The difference is in the account creation process, which is not necessary here.

		const deletedCustomer = event.data.object as Stripe.DeletedCustomer;

		const accountModel = new AccountModel(null);
		let accountDocument: IAccount = await accountModel.readOneByStripeCustomer(deletedCustomer.id);

		const userModel = new UserModel(null);
		const user = await userModel.readOneById(accountDocument.ownerUserId.toString());

		const stripePriceModel = new StripePriceModel();
		const freePrice = await stripePriceModel.getFreePrice();

		const stripeCustomerModel = new StripeCustomerModel();
		const stripeCustomer = await stripeCustomerModel.createCustomer(
			user.email,
			accountDocument.companyName,
			accountDocument.subscription.trialAvailable
		);

		await accountModel.updateOneById(accountDocument._id.toString(), {
			stripeCustomerId: stripeCustomer.id
		});

		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const subscription = await stripeSubscriptionModel.createSubscription(stripeCustomer.id, freePrice);

		const stripeInvoicesModel = new StripeInvoicesModel();
		const invoices = await stripeInvoicesModel.getInvoices(stripeCustomer.id, "paid");

		await stripeCustomerModel.setAccount(accountDocument);

		accountDocument = await accountModel.setSubscription(accountDocument._id.toString(), subscription.id);

		accountDocument = await accountModel.setInvoices(accountDocument._id.toString(), invoices);

		const accountManifestModel = new AccountManifestModel(accountDocument);
		await accountManifestModel.update();
	}

	private async handleInvoiceCreated (event: Stripe.Event): Promise<void> {
		const invoice = event.data.object as Stripe.Invoice;
		const customerId = invoice.customer as string | null;

		if (!customerId) {
			return;
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripeInvoicesModel = new StripeInvoicesModel();
		const invoices = await stripeInvoicesModel.getInvoices(customerId, "paid");

		const customer = await stripe.customers.retrieve(customerId);

		if (customer.deleted === true) {
			return;
		}

		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);

		await accountModel.setInvoices(account._id.toString(), invoices);
	}

	private async handleInvoiceDeleted (event: Stripe.Event): Promise<void> {
		const invoice = event.data.object as Stripe.Invoice;
		const customerId = invoice.customer as string;

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripeInvoicesModel = new StripeInvoicesModel();
		const invoices = await stripeInvoicesModel.getInvoices(customerId, "paid");

		const customer = await stripe.customers.retrieve(customerId);

		if (customer.deleted === true) {
			return;
		}

		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);
		await accountModel.setInvoices(account._id.toString(), invoices);
	}

	private async handlePaymentMethodAttached (event: Stripe.Event): Promise<void> {
		const paymentMethod = event.data.object as Stripe.PaymentMethod;
		const customerId = paymentMethod.customer as string | null;

		if (!customerId) {
			return;
		}

		const stripeCustomerModel = new StripeCustomerModel();
		await stripeCustomerModel.setDefaultPaymentMethod(customerId, paymentMethod.id);

		const stripePaymentModel = new StripePaymentModel(null);
		await stripePaymentModel.removeAllPaymentMethodsExcept(customerId, paymentMethod.id);

		await this.setSubscription(customerId);
	}

	private async handlePaymentMethodDetached (event: Stripe.Event): Promise<void> {
		const previousPaymentMethod = event.data.previous_attributes as Stripe.PaymentMethod;
		const customerId = previousPaymentMethod.customer as string | null;

		if (!customerId) {
			return;
		}

		await this.setSubscription(customerId);
	}

	private async handlePaymentMethodUpdated (event: Stripe.Event): Promise<void> {
		const paymentMethod = event.data.object as Stripe.PaymentMethod;
		const customerId = paymentMethod.customer as string | null;

		if (!customerId) {
			return;
		}

		await this.setSubscription(customerId);
	}

	private async handleTestClockReady (event: Stripe.Event): Promise<void> {
		const clock = event.data.object as Stripe.TestHelpers.TestClock;

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const customers = await stripe.customers.list({
			// eslint-disable-next-line camelcase
			test_clock: clock.id
		});

		const promises = customers.data.map(async (customer) => {
			await this.setSubscription(customer.id);
		});

		await Promise.all(promises);
	}

	private async onBillingCycleStart (customer: Stripe.Customer): Promise<void> {
		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);
		await accountModel.resetUsage(account._id.toString());
	}

	/**
	* Sets the subscription for a customer. If `subscriptionId` is not provided,
	* the function uses the current Stripe subscription associated with `customerId`.
	*/
	async setSubscription (customerId: string, subscriptionId?: string): Promise<void> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);
		const customer = await stripe.customers.retrieve(customerId);

		if (customer.deleted === true) {
			return;
		}

		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);


		if (subscriptionId == null) {
			subscriptionId = account.subscription.stripeSubscriptionId;
		}

		await accountModel.setSubscription(account._id.toString(), subscriptionId);
	}
}

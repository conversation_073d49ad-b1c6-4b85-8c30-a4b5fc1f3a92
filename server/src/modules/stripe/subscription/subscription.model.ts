import Stripe from "stripe";
import { getSecrets } from "../../secrets/secrets.model";
import { StripeProductMetadata } from "./subscription.interfaces";

export class StripeSubscriptionModel {
	public async createSubscription (customerId: string, price: Stripe.Price): Promise<Stripe.Subscription> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		// get the product associated with the price
		const product = await stripe.products.retrieve(price.product as string);

		const subscription: Stripe.Subscription = await stripe.subscriptions.create({
			customer: customerId,
			items: [
				{
					price: price.id
				}
			],
			// eslint-disable-next-line camelcase
			trial_settings: {
				// eslint-disable-next-line camelcase
				end_behavior: {
					// eslint-disable-next-line camelcase
					missing_payment_method: "create_invoice"
				}
			},
			metadata: this.createMetadataFromProductAndPrice(product, price),
			// eslint-disable-next-line camelcase
			proration_behavior: "always_invoice"
		});

		return subscription;
	}

	public async getClockTime (subscription: Stripe.Subscription): Promise<number> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		let clockTime = Math.ceil(Date.now() / 1000);
		if (subscription.test_clock) {
			const clock = await stripe.testHelpers.testClocks.retrieve(subscription.test_clock as string);
			clockTime = clock.frozen_time;
		}

		return clockTime;
	}

	public async updateSubscription (
		subscriptionId: string,
		priceId: string,
		freeImmediate = false
	): Promise<Stripe.Subscription> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		let subscription: Stripe.Subscription = await stripe.subscriptions.retrieve(subscriptionId);

		const customer = await stripe.customers.retrieve(subscription.customer as string);
		if (customer.deleted === true) {
			throw new Error(`Customer with ID ${subscription.customer} has been deleted`);
		}

		const price = await stripe.prices.retrieve(priceId);

		const product = await stripe.products.retrieve(price.product as string);

		const trialAvailable = await this.trialAvailable(customer, price);
		const trialActive = await this.trialActive(subscription);

		let trialEnd: number | "now" | undefined = undefined;

		const clockTime = await this.getClockTime(subscription);

		if (trialAvailable) {
			trialEnd = clockTime + 14 * 24 * 60 * 60;
		} else {
			if (trialActive) {
				if (subscription.trial_end) {
					if (clockTime < subscription.trial_end) {
						trialEnd = subscription.trial_end;
					}
				}
			}
		}

		if (price.unit_amount === 0 && !freeImmediate) {
			if (subscription.schedule) {
				await stripe.subscriptionSchedules.release(subscription.schedule as string);
			}

			const subscriptionSchedule = await this.createOrGetSubscriptionSchedule(stripe, subscription.id);

			const currentProduct = await stripe.products.retrieve(subscription.items.data[0].price.product as string);
			const currentPrice = await stripe.prices.retrieve(subscription.items.data[0].price.id);

			await stripe.subscriptionSchedules.update(subscriptionSchedule.id, {
				phases: [
					{
						items: [
							{
								price: subscription.items.data[0].price.id,
								quantity: 1
							}
						],
						// eslint-disable-next-line camelcase
						trial_end: trialEnd,
						// eslint-disable-next-line camelcase
						proration_behavior: "always_invoice",
						// eslint-disable-next-line camelcase
						collection_method: "charge_automatically",
						// eslint-disable-next-line camelcase
						start_date: subscription.current_period_start,
						// eslint-disable-next-line camelcase
						end_date: subscription.current_period_end,
						metadata: this.createMetadataFromProductAndPrice(currentProduct, currentPrice)
					},
					{
						items: [
							{
								price: priceId,
								quantity: 1
							}
						],
						// eslint-disable-next-line camelcase
						start_date: subscription.current_period_end,
						metadata: this.createMetadataFromProductAndPrice(product, price)
					}
				],
				// eslint-disable-next-line camelcase
				end_behavior: "release"
			});
		} else {
			if (!trialAvailable && !customer.invoice_settings.default_payment_method && price.unit_amount !== 0) {
				throw new Error("No payment method available");
			}

			if (subscription.schedule) {
				await stripe.subscriptionSchedules.release(subscription.schedule as string);
			}

			subscription = await stripe.subscriptions.update(subscriptionId, {
				items: [
					{
						id: subscription.items.data[0].id,
						price: priceId,
						quantity: 1,
						// eslint-disable-next-line camelcase
						tax_rates: undefined
					}
				],
				// eslint-disable-next-line camelcase
				trial_end: trialEnd,
				// eslint-disable-next-line camelcase
				proration_behavior: "always_invoice",
				// eslint-disable-next-line camelcase
				collection_method: "charge_automatically",
				// eslint-disable-next-line camelcase
				trial_settings: {
					// eslint-disable-next-line camelcase
					end_behavior: {
						// eslint-disable-next-line camelcase
						missing_payment_method: "create_invoice"
					}
				},
				metadata: this.createMetadataFromProductAndPrice(product, price)
			});

			if (trialAvailable) {
				await stripe.customers.update(subscription.customer as string, {
					metadata: {
						trialAvailable: "false"
					}
				});
			}
		}

		subscription = await stripe.subscriptions.retrieve(subscriptionId);
		return subscription;
	}

	public async deleteSubscription (subscriptionId: string): Promise<void> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);
		await stripe.subscriptions.cancel(subscriptionId);
	}

	private async createOrGetSubscriptionSchedule (
		stripe: Stripe, subscriptionId: string
	): Promise<Stripe.SubscriptionSchedule> {
		const subscription = await stripe.subscriptions.retrieve(subscriptionId);
		if (!subscription.schedule) {
			return await stripe.subscriptionSchedules.create({
				// eslint-disable-next-line camelcase
				from_subscription: subscription.id
			});
		}

		return await stripe.subscriptionSchedules.retrieve(subscription.schedule as string);
	}

	private customerTrialAvailable (customer: Stripe.Customer): boolean {
		return (customer.metadata.trialAvailable?.toLocaleLowerCase().trim() === "true");
	}

	private productTrialAvailable (product: Stripe.Product): boolean {
		return (product.metadata.trialEnabled?.toLocaleLowerCase().trim() === "true");
	}

	private async trialAvailable (customer: Stripe.Customer, price: Stripe.Price): Promise<boolean> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);
		const product = await stripe.products.retrieve(price.product as string);
		return this.productTrialAvailable(product) && price.unit_amount !== 0 && this.customerTrialAvailable(customer);
	}

	private async trialActive (subscription: Stripe.Subscription): Promise<boolean> {
		const clockTime = await this.getClockTime(subscription);
		return subscription.trial_end !== null &&
			subscription.trial_end !== undefined &&
			subscription.trial_end > clockTime;
	}

	private createMetadataFromProductAndPrice (product: Stripe.Product, price: Stripe.Price): Stripe.Metadata {
		const stripeProductMetadata: StripeProductMetadata = {
			gp: "true",
			hideVanityBranding: (product.metadata.hideVanityBranding === "true").toString(),
			enableConversionMetrics: (product.metadata.enableConversionMetrics === "true").toString(),
			enableEngagementMetrics: (product.metadata.enableEngagementMetrics === "true").toString(),
			maxInteractiveCollectionLimit: product.metadata.maxInteractiveCollectionLimit,
			maxInteractiveVideoLimit: product.metadata.maxInteractiveVideoLimit,
			maxVideoProductLinksLimit: product.metadata.maxVideoProductLinksLimit,
			type: product.metadata.type,
			allowThemes: (product.metadata.allowThemes === "true").toString(),
			allowSharing: (product.metadata.allowSharing === "true").toString(),
			allowLandscape: (product.metadata.allowLandscape === "true").toString(),
			allowCTALead: (product.metadata.allowCTALead === "true").toString(),
			maxImpressionsPerCycle: price.metadata?.maxImpressionsPerCycle
		};

		return stripeProductMetadata as unknown as Stripe.Metadata;
	}
}

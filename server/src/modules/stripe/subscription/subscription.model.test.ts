import { StripeCustomerModel } from "../customer/customer.model";
import { StripePriceModel } from "../price/price.model";
import { StripeSubscriptionModel } from "./subscription.model";

describe("stripe subscription model", () => {
	it("should create a stripe subscription", async () => {
		const stripeCustomerModel = new StripeCustomerModel();
		const customer = await stripeCustomerModel.createCustomer("<EMAIL>", "Nobody", true);

		const stripePriceModel = new StripePriceModel();
		const price = await stripePriceModel.getFreePrice();

		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const subscription = await stripeSubscriptionModel.createSubscription(customer.id, price);

		expect(subscription).toBeDefined();
		expect(subscription).toHaveProperty("id");
		expect(subscription.metadata.hideVanityBranding).toBe("false");
		expect(subscription.metadata.enableConversionMetrics).toBe("false");
		expect(subscription.metadata.enableEngagementMetrics).toBe("false");
		expect(subscription.metadata.type).toBe("basic");
		expect(subscription.metadata.allowThemes).toBe("false");
		expect(subscription.metadata.allowSharing).toBe("false");
		expect(subscription.metadata.hideVanityBranding).toBe("false");
		expect(subscription.metadata.allowLandscape).toBe("false");
		expect(subscription.metadata.allowCTALead).toBe("false");
	});
});

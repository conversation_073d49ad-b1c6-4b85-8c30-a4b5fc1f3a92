import Stripe from "stripe";
import { getSecrets } from "../../secrets/secrets.model";

export class StripeInvoicesModel {
	constructor (public invoices: Stripe.Invoice[] = []) {}

	public async getInvoices (
		customerId: string,
		status: Stripe.InvoiceListParams.Status | undefined = undefined
	): Promise<Stripe.Invoice[]> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const invoices = await stripe.invoices.list({
			customer: customerId,
			status: status
		});

		this.invoices = invoices.data;
		return this.invoices;
	}
}

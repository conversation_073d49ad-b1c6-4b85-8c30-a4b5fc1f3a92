import Stripe from "stripe";
import { getSecrets } from "../../secrets/secrets.model";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";

export class StripePriceModel {
	constructor(public price: Stripe.Price | null = null) {}

	public async getFreePrice(): Promise<Stripe.Price> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const priceParams: Stripe.PriceListParams = {
			active: true,
			limit: 100
		};

		const prices = await stripe.prices.list(priceParams);

		this.price = prices.data.find((price) => {
			return price.unit_amount === 0 && price.metadata.gp?.toLocaleLowerCase().trim() === "true";
		}) ?? null;

		if (!this.price) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "A zero cost price could not be found")
				.setDetail({
					"method": "StripePriceModel.getFreePrice",
					"prices": prices.data
				}).log();
		}

		return this.price;
	}

	public async getEnterprisePrice(): Promise<Stripe.Price> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const priceParams: Stripe.PriceListParams = {
			active: true,
			limit: 100
		};

		const prices = await stripe.prices.list(priceParams);
		this.price = prices.data.find((price) => {
			return price.metadata.gp?.toLocaleLowerCase().trim() === "true" &&
				price.metadata.type?.toLocaleLowerCase().trim() === "enterprise";
		}) ?? null;

		if (!this.price) {
			// Get all products
			const products = await stripe.products.list({
				active: true,
				limit: 100
			});

			// Find an enterprise product
			const enterpriseProduct = products.data.find(product =>
				product.metadata.gp?.toLocaleLowerCase().trim() === "true" &&
				product.metadata.type?.toLocaleLowerCase().trim() === "enterprise"
			);

			if (enterpriseProduct) {
				// Find a price for this product
				this.price = prices.data.find(price =>
					price.id === enterpriseProduct.default_price
				) ?? null;
			}
		}

		if (!this.price) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "An enterprise price could not be found")
				.setDetail({
					"method": "StripePriceModel.getEnterprisePrice",
					"prices": prices.data
				}).log();
		}

		return this.price;
	}
}

import TestHelper from "../../../__tests__/mocks/testHelper";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import Stripe from "../../../__mocks__/stripe";
import { IAccount } from "../../../modules/account/account.interfaces";
import { AccountModel } from "../../../modules/account/account.model";
import { StripePaymentModel } from "./payment.model";
import { ISignupPayload } from "../../signup/signup.interfaces";


describe("stripe payment model", () => {
	let expressApp: express.Express;
	let accessToken: string;
	let accountId: string;
	let stripeSubscriptionId: string;
	const accountModel = new AccountModel(null);
	const stripe = new Stripe("");

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const userPayload = {
			firstName: "Johnny",
			lastName: "payment.model",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "payment.model Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(userPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		stripeSubscriptionId = account.subscription.stripeSubscriptionId;
	});

	/**
	 * The flow of this test is
	 * 1.  Create a paymentMethod in the stripe backend
	 * 2.  Attach the payment method ot the stripe custoemr in the stripe backend
	 * 3.  Update the gp subscription based on the changes above
	 */
	it("create a payment method", async () => {
		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.hasPaymentMethod).toBe(false);

		const paymentParams = {
			type: "card",
			card: {
				brand: "Visa",
				last4: "5676"
			}
		};
		const payment = await stripe.paymentMethods.create(paymentParams);

		const stripePaymentModel = new StripePaymentModel(null);
		const paymentMethod = await stripePaymentModel.attach(accountId, payment.id);
		expect(paymentMethod).toBeDefined();
		expect(paymentMethod).toHaveProperty("id");

		await accountModel.setSubscription(accountId, stripeSubscriptionId);

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.hasPaymentMethod).toBe(true);
	});
});

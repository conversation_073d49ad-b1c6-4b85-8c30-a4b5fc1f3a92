import Stripe from "stripe";
import { AccountModel } from "../../account/account.model";
import { ClientSession } from "mongoose";
import { StripeCustomerModel } from "../customer/customer.model";
import { CardPaymentMethod } from "../../payment/payment.interfaces";
import { getSecrets } from "../../secrets/secrets.model";
import { IAccount } from "../../account/account.interfaces";

export class StripePaymentModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public async attach (accountId: string, stripePaymentMethodId: string): Promise<Stripe.PaymentMethod> {
		const accountModel = new AccountModel(this.session);
		const account = await accountModel.readOneById(accountId);

		const stripeCustomerModel = new StripeCustomerModel();
		const stripeCustomer = await stripeCustomerModel.getCustomer(account.stripeCustomerId);

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const paymentMethodParams: Stripe.PaymentMethodAttachParams = {
			customer: stripeCustomer.id
		};

		const paymentMethod = await stripe.paymentMethods.attach(
			stripePaymentMethodId,
			paymentMethodParams
		);

		await stripeCustomerModel.setDefaultPaymentMethod(stripeCustomer.id, paymentMethod.id);

		return paymentMethod;
	}

	async readOneById (paymentMethodId: string): Promise<Stripe.PaymentMethod> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
		return paymentMethod;
	}

	async readByCustomer (customer: Stripe.Customer): Promise<CardPaymentMethod[]> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripePaymentMethods = await stripe.paymentMethods.list({
			customer: customer.id,
			type: "card"
		});

		const cardPaymentMethods: (CardPaymentMethod | undefined)[] = stripePaymentMethods.data.filter(
			(paymentMethod) => {
				return paymentMethod.type === "card";
			}
		).map((paymentMethod) => {
			const card = paymentMethod.card;
			if (card) {
				const cardPaymentMethod: CardPaymentMethod = {
					id: paymentMethod.id,
					brand: card.brand,
					last4: card.last4,
					default: customer.invoice_settings.default_payment_method === paymentMethod.id
				};
				return cardPaymentMethod;
			}
		});

		if (!cardPaymentMethods) {
			throw new Error("Failed to retrieve payment methods");
		}

		return cardPaymentMethods as CardPaymentMethod[];
	}

	async readByCustomerId (customerID: string): Promise<CardPaymentMethod[]> {
		const stripeCustomerModel = new StripeCustomerModel();
		const stripeCustomer = await stripeCustomerModel.getCustomer(customerID);

		const stripePaymentMethods = await this.readByCustomer(stripeCustomer);

		return stripePaymentMethods;
	}

	public async deleteOneById (paymentMethodId: string): Promise<Stripe.PaymentMethod> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		let paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
		const customer = await stripe.customers.retrieve(paymentMethod.customer as string);

		paymentMethod = await stripe.paymentMethods.detach(paymentMethodId);

		if (customer.deleted === true) {
			return paymentMethod;
		}

		const accountModel = new AccountModel(null);
		const account: IAccount = await accountModel.readOneByStripeCustomer(customer.id);

		await accountModel.setSubscription(account._id.toString(), account.subscription.stripeSubscriptionId);

		return paymentMethod;
	}

	async deleteAllByCustomerId (customerId: string): Promise<void> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripePaymentMethods = await stripe.paymentMethods.list({
			customer: customerId
		});

		const promises: Promise<Stripe.Response<Stripe.PaymentMethod>>[] = [];
		for (const paymentMethod of stripePaymentMethods.data) {
			promises.push(stripe.paymentMethods.detach(paymentMethod.id));
		}

		await Promise.all(promises);
	}

	async deleteAllByCustomer (customer: Stripe.Customer): Promise<void> {
		await this.deleteAllByCustomerId(customer.id);
	}

	public async removeAllPaymentMethodsExcept (customerId: string, paymentMethodId: string): Promise<void> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripePaymentMethods = await stripe.paymentMethods.list({
			customer: customerId
		});

		const promises: Promise<Stripe.Response<Stripe.PaymentMethod>>[] = [];
		for (const paymentMethod of stripePaymentMethods.data) {
			if (paymentMethod.id !== paymentMethodId) {
				promises.push(stripe.paymentMethods.detach(paymentMethod.id));
			}
		}

		await Promise.all(promises);
	}
}

import Stripe from "stripe";
import { IAccount } from "../../account/account.interfaces";
import { getSecrets } from "../../secrets/secrets.model";

export class StripeCustomerModel {
	constructor (public customer: Stripe.Customer | null = null) {}

	public async createCustomer (email: string, name: string, trialAvailable: boolean): Promise<Stripe.Customer> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		this.customer = await stripe.customers.create({
			email: email,
			name: name,
			metadata: {
				trialAvailable: trialAvailable ? "true" : "false"
			}
		});

		return this.customer;
	}

	public async getCustomer (customerId: string): Promise<Stripe.Customer> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const customer = await stripe.customers.retrieve(customerId);

		if (customer.deleted === true) {
			throw new Error(`Customer with ID ${customerId} has been deleted`);
		}

		this.customer = customer;

		return this.customer;
	}

	public async deleteCustomer (id: string | null = null): Promise<Stripe.DeletedCustomer> {
		if (!id) {
			if (!this.customer) {
				throw new Error("unable to delete customer property that is null");
			}

			id = this.customer.id;
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);
		const deletedCustomer = await stripe.customers.del(id);
		this.customer = null;
		return deletedCustomer;
	}

	public async setAccount (account: IAccount): Promise<void> {
		if (!this.customer) {
			throw new Error("unable to set account on customer property that is null");
		}

		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		await stripe.customers.update(this.customer.id, {
			metadata: {
				accountId: account.id
			}
		});
	}

	async setDefaultPaymentMethod (
		stripeCustomerId: string,
		paymentMethodId: string
	): Promise<Stripe.Customer> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const updateParams: Stripe.CustomerUpdateParams = {
			// eslint-disable-next-line camelcase
			invoice_settings: {
				// eslint-disable-next-line camelcase
				default_payment_method: paymentMethodId
			}
		};

		const stripeCustomer = await stripe.customers.update(stripeCustomerId, updateParams);

		return stripeCustomer;
	}
}

import mongoose from "mongoose";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { LocaleAPI } from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import { AccountModel } from "../account/account.model";
import { MetricPhonePressModel } from "./metricPhonePress.model";
import { IVideo } from "../video/video.interfaces";
import { MetricPhonePressDBModel } from "./metricPhonePressDB.model";
import { InteractiveVideoDBModel } from "../interactiveVideo/interactiveVideoDB.model";

describe("White Box Testing | metricPhonePress Model", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accountId: string;
	let interactiveVideoId: string;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		const accountToken = await testHelper.getAccountToken(accountId, accessToken);
		const video: IVideo = await testHelper.createVideo(account._id.toString());
		const videoId = video._id.toString();
		const shoppableVideo = await testHelper.createShoppableVideo(videoId, accountToken, accessToken);
		interactiveVideoId = shoppableVideo._id.toString();
	});

	beforeEach(async () => {
		const accountModel = new AccountModel(null);
		await accountModel.resetUsage(accountId);
	});

	it("should register phone press metric successfully.", async () =>{
		const currentInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});
		const phonePressCount = currentInteractiveVideo?.phonePressCount || 0;

		const metricPhonePressModel = new MetricPhonePressModel(null);
		await metricPhonePressModel.registerPhonePress(interactiveVideoId, accountId, new Date());

		const latestPhonePressDocument = await MetricPhonePressDBModel.findOne({
			accountId: new mongoose.Types.ObjectId(accountId),
			videoId: new mongoose.Types.ObjectId(interactiveVideoId)
		}).sort({ createdAt: -1 });

		const updateInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});

		expect(latestPhonePressDocument).not.toBeNull();
		expect(latestPhonePressDocument?.accountId?.toString()).toBe(accountId);
		expect(latestPhonePressDocument?.videoId?.toString()).toBe(interactiveVideoId);
		expect(updateInteractiveVideo?.phonePressCount).toBe(phonePressCount + 1);
	});

	it("should register two parallel phone press requests successfully.", async () =>{
		const currentInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});
		const phonePressCount = currentInteractiveVideo?.phonePressCount || 0;
		const metricPhonePressModel = new MetricPhonePressModel(null);
		const request1 = metricPhonePressModel.registerPhonePress(interactiveVideoId, accountId, new Date());
		const request2 = metricPhonePressModel.registerPhonePress(interactiveVideoId, accountId, new Date());
		const [result1, result2] = await Promise.allSettled([request1, request2]);

		const updateInteractiveVideo = await InteractiveVideoDBModel.findOne({
			_id: new mongoose.Types.ObjectId(interactiveVideoId)
		});

		expect(result1.status).toBe("fulfilled");
		expect(result2.status).toBe("fulfilled");
		expect(updateInteractiveVideo?.phonePressCount).toBe(phonePressCount + 2);
	});
});

export interface VideoMetadata {
	width: number;
	height: number;
	fileSizeBytes: number;
	durationSeconds: number;
	videoCodec: string;
	videoBitrate: number;
	frameRate: number;
	audioCodec: string;
	audioSampleRate: number;
	audioChannels: number;
}

export interface BucketUploadResult {
	path: string;
	url: string;
}

export interface TempFiles {
	videoPath: string;
	flacAudioPath: string;
	trimmedVideoPath: string;
	gifPath: string;
	posterPath: string;
	playEmbedPosterPath: string;
}

import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import Jo<PERSON> from "joi";
import {
	VersionJoi,
	AccessTokenJoi
} from "../../base/base.joi";
import { BaseRequest } from "../../base/base.interfaces";
import { AccountModel } from "../../account/account.model";
import { VideoModel } from "../video.model";


interface VideoSignedUrlPayload extends BaseRequest {
	apiVersion: number;
	fileName: string;
}

const VideoSignedUrlPayloadSchema = Joi.object<VideoSignedUrlPayload>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	accountToken: Joi.string().required(),
	fileName: Joi.string().min(1).required()
});

export class VideoSignedURLController extends Controller {
	constructor() {
		super();
		this.router.post("/", [express.json()], this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoSignedUrlPayload;
		try {
			validPayload = await VideoSignedUrlPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization,
				fileName: request.body.fileName
			}
			);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await this.verifyAccessToken(validPayload.accessToken);
			const accountToken = await this.verifyAccountToken(validPayload.accountToken);

			const accountModel: AccountModel = new AccountModel(null);
			const accountDocument = await accountModel.readOneById(accountToken.account._id);

			const videoModel: VideoModel = new VideoModel(null);
			const result = await videoModel.generateTempVideoSignedUrl(accountDocument, validPayload.fileName);

			return response.status(200).json(result);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}


}

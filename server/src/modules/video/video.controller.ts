import {
	Request,
	Response
} from "express";
import Jo<PERSON> from "joi";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { BaseRequest } from "../base/base.interfaces";
import { validateHeaders } from "../../middleware/validateHeaders.mw";
import { BaseJoi } from "../base/base.joi";
import { AccountModel } from "../account/account.model";
import { VideoModel } from "./video.model";
import { decodeAccess } from "../../middleware/decodeAccess.mw";
import mongoose from "mongoose";
import { readAccount2 } from "../../services/mongodb/account.service";
import { UserModel } from "../user/user.model";
import { IVideo } from "./video.interfaces";
import {
	deleteVideo,
	readVideo
} from "../../services/mongodb/video.service";
import { countShoppableVideos } from "../../services/mongodb/shoppableVideo.service";
import { deleteAssetsFromCloud } from "../../services/gp/bucket.service";

interface GetVideosPayload extends BaseRequest {
	cursor?: string;
	limit?: number;
}

const GetVideosPayloadSchema = BaseJoi.append<GetVideosPayload>({
	cursor: Joi.string().optional(),
	limit: Joi.number().optional().default(20)
});

export class VideoController extends Controller {
	constructor() {
		super();
		this.router.get("/", this.list.bind(this));
		this.router.get("/:videoId", [decodeAccess, validateHeaders], this.get.bind(this));
		this.router.delete("/:videoId", [decodeAccess, validateHeaders], this.delete.bind(this));
	}

	private async list(request: Request, response: Response): Promise<Response> {
		let validPayload: GetVideosPayload;

		try {
			validPayload = await GetVideosPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization,
				cursor: request.query.cursor,
				limit: request.query.limit ? parseInt(request.query.limit as string, 10) : 20
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await this.verifyAccessToken(validPayload.accessToken);
			const accountToken = await this.verifyAccountToken(validPayload.accountToken);

			const accountModel = new AccountModel(null);
			const account = await accountModel.readOneById(accountToken.account._id);

			const videoModel = new VideoModel(null);
			const videos = await videoModel.paginateVideos(account._id.toString(),
				validPayload.limit,
				validPayload.cursor);

			return response.status(200).json({ videos });

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async get(request: Request, response: Response): Promise<Response> {
		try {
			const videoId = request.params.videoId;
			const accountId = request.accountToken?.account?._id;

			if (!accountId) {
				throw new APIError(
					APIErrorName.E_MISSING_AUTHORIZATION,
					"Missing accountId in account token"
				);
			}

			const accountDocument = await readAccount2(
				{
					_id: new mongoose.Types.ObjectId(accountId)
				},
				null
			);

			if (!accountDocument) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"account not found."
				);
			}

			if (!request.accessToken?.userId) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "missing userId in access token");
			}

			const userModel = new UserModel(null);
			// throws if not found
			await userModel.readOneById(request.accessToken.userId);

			const videoDocument: IVideo | null = await readVideo(
				{
					_id: new mongoose.Types.ObjectId(videoId)
				},
				null
			);

			if (!videoDocument) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"video not found."
				);
			}

			return response.status(200).send({
				video: <IVideo>videoDocument
			});
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async delete (request: Request, response: Response): Promise<Response> {
		try {
			const videoId = request.params.videoId;
			const accountId = request.accountToken?.account?._id;

			if (!accountId) {
				throw new APIError(
					APIErrorName.E_MISSING_AUTHORIZATION,
					"Missing accountId in account token"
				);
			}

			const accountDocument = await readAccount2(
				{
					_id: new mongoose.Types.ObjectId(accountId)
				},
				null
			);

			if (!accountDocument) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"account not found."
				);
			}

			if (!request.accessToken?.userId) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "missing userId in access token");
			}

			const userModel = new UserModel(null);
			// will throw if not found
			await userModel.readOneById(request.accessToken.userId);

			const videoDocument: IVideo | null = await readVideo(
				{
					_id: new mongoose.Types.ObjectId(videoId)
				},
				null
			);

			if (!videoDocument) {
				throw new APIError(
					APIErrorName.E_DOCUMENT_NOT_FOUND,
					"video not found."
				);
			}

			const matchQuery = {
				accountId: new mongoose.Types.ObjectId(accountId),
				videoURL: videoDocument.publicVideoURL
			};

			const totalShoppableVideos = await countShoppableVideos(
				matchQuery,
				null
			);

			if (totalShoppableVideos > 0) {
				throw new APIError(
					APIErrorName.E_RESOURCE_CONFLICT,
					"Video has references in shoppableVideos collection."
				).suppressLog();
			}

			await deleteVideo({ _id: videoDocument._id }, null);
			await deleteAssetsFromCloud([
				videoDocument.videoFileLocation,
				videoDocument.posterFileLocation,
				videoDocument.posterPlayEmbedFileLocation,
				videoDocument.gifFileLocation
			]);

			return response.status(200).send({
				video: <IVideo>videoDocument
			});

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}
}

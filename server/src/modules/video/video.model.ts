import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { IAccount } from "../account/account.interfaces";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import {
	IVideo,
	VideoCreateData
} from "./video.interfaces";
import { VideoDBModel } from "./videoDB.model";
import { BucketModel } from "../bucket/bucket.model";
import { getSecrets } from "../secrets/secrets.model";
import {
	VideoJobModel,
	VideoJobStatus
} from "./job/video.job.model";
import { CallbackInfo } from "../job/job.interfaces";

export const MAX_FILE_SIZE_BYTES = 512 * 1024 * 1024;

export class VideoModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments(filter: FilterQuery<IVideo>): Promise<number> {
		const count: number = await VideoDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async isLimitReached(account: IAccount): Promise<boolean> {
		const filter: FilterQuery<IVideo> = {
			accountId: account._id
		};

		const documentCount = await this.countDocuments(filter);
		const limit = account.subscription.maxInteractiveVideoLimit;

		return documentCount >= limit;
	}

	async readOneById(_id: string): Promise<IVideo> {
		try {
			const filter: FilterQuery<IVideo> = {
				_id: new mongoose.Types.ObjectId(_id)
			};

			const document = await VideoDBModel.findOne(filter).session(this.session);

			if (!document) {
				throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Video document not found");
			}

			return document;
		} catch (error: unknown) {
			if (error instanceof APIError) {
				throw error;
			}
			throw new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message);
		}
	}

	async createOne(createData: VideoCreateData): Promise<IVideo> {
		const options: mongoose.QueryOptions = { session: this.session };
		const video = await new VideoDBModel(createData).save(options);
		return video;
	}

	async generateTempVideoSignedUrl(account: IAccount, fileName: string):
	Promise<{ signedURL: string, tempFilename: string; }> {
		if (await this.isLimitReached(account)) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Video limit reached").suppressLog();
		}
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.tempBucketName);
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const tempFilename = BucketModel.sanitizeFileName(fileName, suffix);

		const signedURL = await bucketModel.getSignedUrlForUpload({
			version: "v4",
			action: "resumable",
			expires: Date.now() + 15 * 60 * 1000,
			contentType: "video/*",
			queryParams: { name: tempFilename }
		}, tempFilename);

		return {
			signedURL: signedURL,
			tempFilename: tempFilename
		};
	}


	async startEncodeJob(accountId: string, userId: string, fileLocation: string, callbackInfo?: CallbackInfo):
	Promise<VideoJobStatus> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.tempBucketName);
		const fileSize = await bucketModel.getFileSize(fileLocation);
		if (fileSize > MAX_FILE_SIZE_BYTES) {
			throw new APIError(APIErrorName.E_FILE_TOO_LARGE, "File is too large. Maximum size allowed is 512MB.");
		}

		const videoJobModel = new VideoJobModel(this.session);
		const job = await videoJobModel.createVideoEncodeJob(accountId, userId, fileLocation, callbackInfo);
		return {
			tempFilename: fileLocation,
			status: job.status,
			statusMessage: job.statusMessage,
			progressPercent: job.progressPercent,
			createdAt: job.createdAt,
			updatedAt: job.updatedAt,
			nextStatusCheck: job.nextStatusCheck
		};
	}


	async startVideoURLEncodeJob(account: IAccount, userId: string, fileUrl: string,
		callbackInfo?: CallbackInfo): Promise<VideoJobStatus> {
		if (await this.isLimitReached(account)) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Video limit reached").suppressLog();
		}
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.tempBucketName);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const tempFileLocation = await bucketModel.uploadFileToBucketFromURL(
			fileUrl,
			"video/*",
			cacheControl
		);
		return this.startEncodeJob(account._id.toString(), userId, tempFileLocation, callbackInfo);
	}

	async paginateVideos(accountId: string, limit = 20, cursor?: string): Promise<IVideo[]> {
		const filter: FilterQuery<IVideo> = {
			accountId: new mongoose.Types.ObjectId(accountId)
		};
		if (cursor) {
			filter._id = { $lt: new mongoose.Types.ObjectId(cursor) };
		}
		return await VideoDBModel.find(filter).sort({ _id: -1 }).limit(limit).session(this.session);
	}
}

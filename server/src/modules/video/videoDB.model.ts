import mongoose, { Schema } from "mongoose";
import { IVideo } from "./video.interfaces";
import { CaptionSchema } from "../caption/captionDB.model";

const VideoSchema: Schema = new Schema(
	{
		createdAt: {
			type: Schema.Types.Date,
			default: () => new Date()
		},
		updatedAt: {
			type: Schema.Types.Date,
			default: () => new Date()
		},
		accountId: {
			type: Schema.Types.ObjectId,
			required: true
		},
		publicPosterURL: {
			type: Schema.Types.String,
			required: false
		},
		publicGifURL: {
			type: Schema.Types.String,
			required: false
		},
		publicPosterPlayEmbedURL: {
			type: Schema.Types.String,
			required: false
		},
		publicVideoURL: {
			type: Schema.Types.String,
			required: false
		},
		videoFileLocation: {
			type: Schema.Types.String,
			required: false
		},
		posterFileLocation: {
			type: Schema.Types.String,
			required: false
		},
		posterPlayEmbedFileLocation: {
			type: Schema.Types.String,
			required: false
		},
		gifFileLocation: {
			type: Schema.Types.String,
			required: false
		},
		videoWidthPx: {
			type: Schema.Types.Number,
			required: true
		},
		videoHeightPx: {
			type: Schema.Types.Number,
			required: true
		},
		fileSizeBytes: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		durationSeconds: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		videoCodec: {
			type: Schema.Types.String,
			required: false,
			default: ""
		},
		videoBitrate: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		frameRate: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		audioCodec: {
			type: Schema.Types.String,
			required: false,
			default: ""
		},
		audioSampleRate: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		audioChannels: {
			type: Schema.Types.Number,
			required: false,
			default: 0
		},
		videoProfile: {
			type: Schema.Types.ObjectId,
			required: true
		},
		captionData: {
			type: CaptionSchema,
			required: false
		}
	},
	{
		timestamps: true
	}
);

export const VideoDBModel = mongoose.model<IVideo>("videos", VideoSchema);

import {
	Document,
	ObjectId
} from "mongoose";
import { CaptionData } from "../caption/caption.interface";

export interface IVideo extends Document {
	_id: ObjectId;
	createdAt: Date;
	updatedAt: Date;
	accountId: ObjectId;
	publicPosterURL: string;
	publicGifURL: string;
	publicPosterPlayEmbedURL: string;
	publicVideoURL: string;
	posterFileLocation: string;
	posterPlayEmbedFileLocation: string;
	gifFileLocation: string;
	videoFileLocation: string;
	videoWidthPx: number;
	videoHeightPx: number;
	fileSizeBytes: number;
	durationSeconds: number;
	videoCodec: string;
	videoBitrate: number;
	frameRate: number;
	audioCodec: string;
	audioSampleRate: number;
	audioChannels: number;
	videoProfile: ObjectId;
	flacAudioFileLocation?: string;
	publicFlacAudioURL?: string;
	captionData?: CaptionData;
}

export interface IVideosPayload {
	fileName: string;
	fileType: string;
	fileUrl: string;
}

export interface VideoCreateData {
	accountId: ObjectId;
	publicPosterURL: string;
	publicGifURL: string;
	publicPosterPlayEmbedURL: string;
	publicVideoURL: string;
	posterFileLocation: string;
	posterPlayEmbedFileLocation: string;
	gifFileLocation: string;
	videoFileLocation: string;
	videoWidthPx: number;
	videoHeightPx: number;
	fileSizeBytes: number;
	durationSeconds: number;
	videoCodec: string;
	videoBitrate: number;
	frameRate: number;
	audioCodec: string;
	audioSampleRate: number;
	audioChannels: number;
	videoProfile: ObjectId;
	captionData?: CaptionData;
}

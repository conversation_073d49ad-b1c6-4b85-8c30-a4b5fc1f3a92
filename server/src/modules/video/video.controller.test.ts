import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { IAccount } from "../account/account.interfaces";
import { IVideo } from "./video.interfaces";

describe("GET /api/videos/", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let video: IVideo;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		video = await testHelper.createVideo(account._id.toString());
	});
	it("successfully returns video job status | 200", async () => {
		const res = await supertest(expressApp)
			.get("/api/videos")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(200);
		expect(res.body.videos.length).toBe(1);
		expect(res.body.videos[0]).toBeDefined();
		expect(res.body.videos[0]._id).toBeDefined();
		expect(res.body.videos[0].accountId).toBeDefined();
		expect(res.body.videos[0].publicVideoURL).toBeDefined();
		expect(res.body.videos[0]._id).toBe(video._id.toString());
		expect(res.body.videos[0].accountId).toBe(account._id.toString());
		expect(res.body.videos[0].publicVideoURL).toBe(video.publicVideoURL);


	});

	it("Missing x-api-version in the payload data | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.get("/api/videos")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});
});

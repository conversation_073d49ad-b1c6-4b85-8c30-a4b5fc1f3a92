import mongoose, {
	ClientSession,
	SaveOptions,
	FilterQuery,
	ObjectId
} from "mongoose";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";

import { JobDBModel } from "../../job/jobDB.model";
import {
	JobsStatus,
	JobsType
} from "../../job/jobs.enums";
import {
	CallbackInfo,
	IJob
} from "../../job/job.interfaces";
import { getSecrets } from "../../secrets/secrets.model";
import { JobModel } from "../../job/job.model";
import { JobContainerModel } from "../../job/job.container.model";

interface UpdateJobData {
	status?: JobsStatus;
	statusMessage?: string;
	progressPercent?: number;
	videoId?: ObjectId;
	nextStatusCheck?: number;
}

export interface VideoJobStatus {
	tempFilename: string;
	status: string;
	statusMessage: string;
	progressPercent?: number;
	createdAt: number;
	updatedAt: number;
	nextStatusCheck?: number;
	videoId?: ObjectId;
}

export const VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS = 5000;
export const VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS = 1000;

export class VideoJobModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async readProcessingVideosByAccountId(_id: string): Promise<IJob[]> {
		const filter: FilterQuery<IJob> = {
			accountId: new mongoose.Types.ObjectId(_id),
			type: JobsType.ENCODE_VIDEO,
			status: { $in: [JobsStatus.CREATED, JobsStatus.RUNNING] }
		};
		const documents = await JobDBModel.find(filter).sort({ _id: -1 }).session(this.session);
		return documents;
	}


	async createVideoEncodeJob(accountId: string, userId: string, tempFilename: string,
		callbackInfo?: CallbackInfo): Promise<IJob> {
		try {
			const options: SaveOptions = { session: this.session };

			const data = {
				tempFilename,
				type: JobsType.ENCODE_VIDEO,
				status: JobsStatus.CREATED,
				statusMessage: "video encode job has been created and is waiting to start.",
				progressPercent: 0,
				accountId: new mongoose.Types.ObjectId(accountId),
				userId: new mongoose.Types.ObjectId(userId),
				callbackInfo: callbackInfo
			};

			const job = await new JobDBModel(data).save(options);
			const secrets = await getSecrets();

			const jobContainerModel = new JobContainerModel(this.session, secrets.storage.isLocal);
			await jobContainerModel.runJobWorker(job._id.toString());
			return job;
		} catch (error: unknown) {
			if (error instanceof Error && error.name === "MongoServerError" && (error as any).code === 11000) {
				throw new APIError(APIErrorName.E_FILE_UPLOAD_EXISTING_FILENAME, "FILENAME already exists");
			}
			throw error;
		}
	}

	async readVideoJobStatusByTempFilename(tempFilename: string): Promise<VideoJobStatus> {
		const now = Date.now();
		const jobModel = new JobModel(this.session);
		const job = await jobModel.readOneByTempFilename(tempFilename);
		const minimumFutureNextStatusCheck = now + VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS;
		const maximumFutureNextStatusCheck = now + VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS;
		return {
			tempFilename: job.tempFilename,
			status: job.status,
			statusMessage: job.statusMessage,
			progressPercent: job.progressPercent,
			createdAt: job.createdAt,
			updatedAt: job.updatedAt,
			nextStatusCheck: (job.nextStatusCheck && job.nextStatusCheck > minimumFutureNextStatusCheck)
				? job.nextStatusCheck : maximumFutureNextStatusCheck,
			videoId: job.videoId
		};
	}

	public static async updateVideoEncodeStatus(jobId: string, updateData: UpdateJobData): Promise<IJob> {
		const filter: FilterQuery<IJob> = {
			_id: new mongoose.Types.ObjectId(jobId),
			type: JobsType.ENCODE_VIDEO
		};
		const options: mongoose.QueryOptions = {
			session: null,
			new: true
		};

		const update: mongoose.UpdateQuery<IJob> = {
			$set: updateData,
			...(updateData.nextStatusCheck === undefined && { $unset: { nextStatusCheck: "" } })
		};

		const document = await JobDBModel.findOneAndUpdate(filter, update, options);
		if (!document) {
			throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Video Encode Job not found");
		}
		return document;
	}
}

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { AccountModel } from "../account/account.model";
import { InteractiveCollectionModel } from "./interactiveCollection.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IPostCollectionPayload } from "./interactiveCollection.interface";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";

export const postCollectionController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		await startDBTransaction(res.locals.session);

		const payload = req.body as IPostCollectionPayload;
		const secrets: ISecrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		if (!gpSecretKey) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to read secrets.hashkey?.key");
		}

		const accountModel = new AccountModel(res.locals.session);
		const accountDocument = await accountModel.readOneById(req.accountToken.account._id);

		const interactiveCollectionModel = new InteractiveCollectionModel(res.locals.sessionn);
		const collectionDocument = await interactiveCollectionModel.createOne({
			title: payload.title,
			shoppableVideos: payload.shoppableVideos,
			buttonBackgroundColor: payload.buttonBackgroundColor,
			buttonBackgroundBlur: payload.buttonBackgroundBlur,
			iconTextColor: payload.iconTextColor,
			displayFont: payload.displayFont,
			carouselBorderRadius: payload.carouselBorderRadius,
			carouselIsCentered: payload.carouselIsCentered,
			carouselMargin: payload.carouselMargin,
			carouselGap: payload.carouselGap,
			widgetBorderRadius: payload.widgetBorderRadius,
			widgetPosition: payload.widgetPosition,
			inlineBorderRadius: payload.inlineBorderRadius
		}, accountDocument);

		if (collectionDocument.hasError()) {
			throw collectionDocument.getError();
		}

		await completeDBTransaction(res.locals.session);
		return res.send({ shoppableCollection: collectionDocument.getData()._id });
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

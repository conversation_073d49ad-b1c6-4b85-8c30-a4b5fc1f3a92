import { SpeechToTextData } from "../speechToText/speechToText.interfaces";
import { CaptionText } from "../caption/caption.interface";

interface CaptionSegment {
	index: number;
	text: string;
	startTimeSeconds: number;
	endTimeSeconds: number;
}

interface SegmentDecisionContext {
	word: string;
	currentLine: string;
	duration: number;
}

interface BuildSegmentContextArgs {
	word: string;
	currentLine: string;
	currentStartTime: { seconds: number; nanos: number; } | null;
	currentEndTime: { seconds: number; nanos: number; } | null;
}

interface HandleSegmentCreationArgs {
	segments: CaptionSegment[];
	index: number;
	currentLine: string;
	currentStartTime: { seconds: number; nanos: number; } | null;
	currentEndTime: { seconds: number; nanos: number; } | null;
	context: SegmentDecisionContext;
}

interface FinalizeLastSegmentArgs {
	segments: CaptionSegment[];
	index: number;
	currentLine: string;
	currentStartTime: { seconds: number; nanos: number; } | null;
	words: Array<{ endTime: { seconds: number; nanos: number; } }>;
	context: SegmentDecisionContext;
}

interface PushOrMergeSegmentArgs {
	segments: CaptionSegment[];
	segment: CaptionSegment;
	context: SegmentDecisionContext;
}

const SRT_MODEL_DEFAULTS = {
	minDuration: 1.5,
	maxCharsPerSegment: 40,
	maxDuration: 4.0
};

export class SRTModel {
	private readonly minDuration: number;
	private readonly maxCharsPerSegment: number;
	private readonly maxDuration: number;

	constructor(options?: {
		minDuration?: number;
		maxCharsPerSegment?: number;
		maxDuration?: number;
	}) {
		this.minDuration = options?.minDuration ?? SRT_MODEL_DEFAULTS.minDuration;
		this.maxCharsPerSegment = options?.maxCharsPerSegment ?? SRT_MODEL_DEFAULTS.maxCharsPerSegment;
		this.maxDuration = options?.maxDuration ?? SRT_MODEL_DEFAULTS.maxDuration;
	}

	public convertFromSpeechToText(sttData: SpeechToTextData): string {
		if (!sttData.words.length) {
			return "";
		}

		const segments = this.createCaptionSegments(sttData);
		this.adjustSegmentEndTimes(segments);

		let srtContent = "";
		for (const seg of segments) {
			srtContent += this.createSRTContent(
				seg.index,
				seg.text,
				seg.startTimeSeconds,
				seg.endTimeSeconds
			);
		}

		return srtContent.trim();
	}

	private createCaptionSegments(sttData: SpeechToTextData): CaptionSegment[] {
		const segments: CaptionSegment[] = [];
		let index = 1;

		let currentLine = "";
		let currentStartTime: { seconds: number; nanos: number; } | null = null;
		let currentEndTime: { seconds: number; nanos: number; } | null = null;
		let lastWordEndTime: { seconds: number; nanos: number; } | null = null;

		for (const word of sttData.words) {
			const wordStart = word.startTime;

			// If there's a large gap, force a split
			if (
				lastWordEndTime &&
				wordStart &&
				this.getSeconds(wordStart) - this.getSeconds(lastWordEndTime) > this.minDuration
			) {
				// Finalize the previous segment
				if (currentLine && currentStartTime && currentEndTime) {
					this.handleSegmentCreation({
						segments,
						index,
						currentLine,
						currentStartTime,
						currentEndTime,
						context: this.buildSegmentContext({
							word: word.word,
							currentLine,
							currentStartTime,
							currentEndTime
						})
					});
					index++;
					({ currentLine, currentStartTime, currentEndTime } = this.resetCurrentLine());
				}
			}

			if (!currentStartTime) {
				currentStartTime = word.startTime;
			}
			currentLine += (currentLine ? " " : "") + word.word;
			currentEndTime = word.endTime;
			lastWordEndTime = word.endTime;

			const context = this.buildSegmentContext({
				word: word.word,
				currentLine,
				currentStartTime,
				currentEndTime
			});

			if (this.shouldCreateSegment(context)) {
				this.handleSegmentCreation({
					segments,
					index,
					currentLine,
					currentStartTime,
					currentEndTime,
					context
				});
				index++;
				({ currentLine, currentStartTime, currentEndTime } = this.resetCurrentLine());
			}
		}

		const lastContext = this.buildSegmentContext({
			word: currentLine.split(" ").slice(-1)[0] || "",
			currentLine,
			currentStartTime,
			currentEndTime: sttData.words.length ? sttData.words[sttData.words.length - 1].endTime : null
		});

		this.finalizeLastSegment({
			segments,
			index,
			currentLine,
			currentStartTime,
			words: sttData.words,
			context: lastContext
		});

		return segments;
	}

	private buildSegmentContext(args: BuildSegmentContextArgs): SegmentDecisionContext {
		const { word, currentLine, currentStartTime, currentEndTime } = args;
		const startTimeSeconds = currentStartTime ? this.getSeconds(currentStartTime) : 0;
		const endTimeSeconds = currentEndTime ? this.getSeconds(currentEndTime) : 0;
		const duration = endTimeSeconds - startTimeSeconds;
		return {
			word,
			currentLine,
			duration
		};
	}

	private handleSegmentCreation(args: HandleSegmentCreationArgs): void {
		const { segments, index, currentLine, currentStartTime, currentEndTime, context } = args;
		if (!currentStartTime || !currentEndTime) return;
		const startTimeSeconds = this.getSeconds(currentStartTime);
		const endTimeSeconds = this.getSeconds(currentEndTime);
		this.pushOrMergeSegment({
			segments,
			segment: {
				index,
				text: currentLine,
				startTimeSeconds,
				endTimeSeconds
			},
			context
		});
	}

	private resetCurrentLine(): { currentLine: string; currentStartTime: null; currentEndTime: null } {
		return {
			currentLine: "",
			currentStartTime: null,
			currentEndTime: null
		};
	}

	private finalizeLastSegment(args: FinalizeLastSegmentArgs): void {
		const { segments, index, currentLine, currentStartTime, words, context } = args;
		if (currentLine && currentStartTime && words.length > 0) {
			const lastWord = words[words.length - 1];
			const startTimeSeconds = this.getSeconds(currentStartTime);
			const endTimeSeconds = this.getSeconds(lastWord.endTime);

			this.pushOrMergeSegment({
				segments,
				segment: {
					index,
					text: currentLine,
					startTimeSeconds,
					endTimeSeconds
				},
				context
			});
		}
	}

	private getSeconds(time: { seconds: number; nanos: number; }): number {
		return time.seconds + time.nanos / 1e9;
	}

	private isPunctuation(word: string): boolean {
		return /[.!?]$/.test(word);
	}

	private shouldCreateSegment(context: SegmentDecisionContext): boolean {
		const isPunctuation = this.isPunctuation(context.word);
		const isMaxChars = context.currentLine.length >= this.maxCharsPerSegment;
		return isPunctuation || isMaxChars;
	}

	private pushOrMergeSegment(args: PushOrMergeSegmentArgs): void {
		const { segments, segment } = args;

		if (segments.length > 0) {
			const prev = segments[segments.length - 1];
			const nextSegmentDuration = segment.endTimeSeconds - segment.startTimeSeconds;
			const gap = segment.startTimeSeconds - prev.endTimeSeconds;

			if (
				nextSegmentDuration < this.minDuration &&
				gap < this.minDuration
			) {
				prev.text += " " + segment.text;
				prev.endTimeSeconds = segment.endTimeSeconds;
				return;
			}
		}

		segments.push(segment);
	}

	private adjustSegmentEndTimes(
		segments: Array<{ startTimeSeconds: number; endTimeSeconds: number }>
	): void {
		for (let i = 0; i < segments.length; i++) {
			const start = segments[i].startTimeSeconds;
			const naturalEnd = segments[i].endTimeSeconds;
			const maxAllowedEnd = start + this.maxDuration;

			let nextStart = Infinity;
			if (i < segments.length - 1) {
				nextStart = segments[i + 1].startTimeSeconds - 0.01;
			}

			// End at the earliest of natural end or next segment
			let end = Math.min(naturalEnd, nextStart);

			// Only clamp to maxDuration if the natural end is not longer
			if (end > maxAllowedEnd && naturalEnd <= maxAllowedEnd) {
				end = maxAllowedEnd;
			}

			segments[i].endTimeSeconds = end;
		}
	}

	private formatTimeToSRT(seconds: number): string {
		const hours = Math.floor(seconds / 3600);
		seconds %= 3600;
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		const wholeSeconds = Math.floor(remainingSeconds);
		const milliseconds = Math.floor((remainingSeconds - wholeSeconds) * 1000);

		return (
			`${hours.toString().padStart(2, "0")}:` +
            `${minutes.toString().padStart(2, "0")}:` +
            `${wholeSeconds.toString().padStart(2, "0")},` +
            `${milliseconds.toString().padStart(3, "0")}`
		);
	}

	private createSRTContent(
		index: number,
		currentLine: string,
		startTimeSeconds: number,
		endTimeSeconds: number
	): string {
		let srtContent = "";
		srtContent += `${index}\n`;
		srtContent += `${this.formatTimeToSRT(startTimeSeconds)} --> ${this.formatTimeToSRT(endTimeSeconds)}\n`;
		srtContent += `${currentLine.trim()}\n\n`;
		return srtContent;
	}

	public async parseToCaptionText(srtString: string): Promise<CaptionText[]> {
		function isNumber(str: string): boolean {
			return !isNaN(Number(str.trim()));
		}

		let captionIndex = 0;
		let captionTextIsNext = false;
		const captionText: CaptionText[] = [];

		const rawSrtSplit = srtString.split("\n");
		rawSrtSplit.forEach(text => {
			if (captionTextIsNext && text !== "" && captionText.length === captionIndex + 1) {
				captionText[captionIndex].text += (captionText[captionIndex].text === "" ? "" : "\n")
                    + text.replace(/\t/g, "");
			} else if (text.includes("-->")) {
				captionTextIsNext = true;
				const times = text.replace(/ /g, "").replace(/\t/g, "").split("-->");

				const startTimeParts = times[0].split(":");
				const startTime = {
					hours: Number(startTimeParts[0]),
					minutes: Number(startTimeParts[1]),
					seconds: Number(startTimeParts[2].split(",")[0]),
					milliseconds: Number(startTimeParts[2].split(",")[1])
				};

				startTime.seconds += startTime.hours * 3600 + startTime.minutes * 60 + (startTime.milliseconds / 1000);

				const endTimeParts = times[1].split(":");
				const endTime = {
					hours: Number(endTimeParts[0]),
					minutes: Number(endTimeParts[1]),
					seconds: Number(endTimeParts[2].split(",")[0]),
					milliseconds: Number(endTimeParts[2].split(",")[1])
				};

				endTime.seconds += endTime.hours * 3600 + endTime.minutes * 60 + (endTime.milliseconds / 1000);

				captionText[captionIndex] = { startTime: startTime.seconds, endTime: endTime.seconds, text: "" };
			} else {
				captionTextIsNext = false;
				if (isNumber(text)) captionIndex = Number(text) - 1;
			}
		});

		return captionText;
	}
}

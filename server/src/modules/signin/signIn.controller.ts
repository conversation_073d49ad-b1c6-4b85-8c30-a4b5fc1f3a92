import {
	Request,
	Response
} from "express";
import bcrypt from "bcryptjs";
import { AuthenticationModel } from "../../modules/authentication/authentication.model";
import { AuthLinkModel } from "../../modules/authentication/link/authlink.model";
import { readKey } from "../../modules/apiKey/apiKey.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	ISignInPayload,
	SignInMethod
} from "./signin.interfaces";
import {
	IAuthentication,
	ApikeyAuthenticationData
} from "../../modules/authentication/authentication.interface";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	readUnverifiedJwt,
	verifyJwt
} from "../../utils/helpers/gpJwt.helper";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { AuthMethods } from "../authentication/authentication.enums";
import { EmailPasswordAuthentication } from "../authentication/emailpassword.authentication.model";
import { UserModel } from "../user/user.model";

const testEmailPasswordSignIn = async (payload: ISignInPayload): Promise<IAuthentication> => {
	if (!payload.email) {
		throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing email");
	}

	if (!payload.password) {
		throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing email");
	}

	const authModel = new EmailPasswordAuthentication(null);
	const authDocument = await authModel.testEmailPassword(payload.email, payload.password);
	return authDocument;
};

const testAuthLinkSignIn = async (payload: ISignInPayload): Promise<IAuthentication> => {
	if (!payload.authlink) {
		throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing authlink");
	}

	const authLinkToken = await AuthLinkModel.verifyAndDecodeToken(payload.authlink);
	const authenticationId = authLinkToken.authenticationId;

	const authModel = new AuthenticationModel(null);
	const authDocument = await authModel.readOneById(authenticationId);
	return authDocument;
};

export const signInController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const secrets: ISecrets = await getSecrets();

		const gpSecretKey = secrets.hashkey.key;

		const payload = req.body as ISignInPayload;

		if (payload.method === SignInMethod.APIKEY && !payload.apikey) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing apikey");
		}

		let authRecord: IAuthentication | null = null;
		let apiKeyId: string | undefined;

		if (payload.token) {
			const decodedAuthentication = readUnverifiedJwt(payload.token);

			if (!decodedAuthentication?.authenticationId) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Failed to retrieve authId from provided token.");
			}

			const authenticationId = decodedAuthentication.authenticationId;

			const authModel = new AuthenticationModel(null);
			authRecord = await authModel.readOneById(authenticationId);

			if (!authRecord) {
				throw new APIError(APIErrorName.E_INVALID_ACCOUNT, "Failed to find authentication record");
			}

			const saltedHash = `${gpSecretKey}${authRecord.salt}`;
			const decodedJWT = verifyJwt<ISignupPayload>(payload.token, saltedHash);

			if (!decodedJWT) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Failed to verify Token.");
			}
		} else {
			if (payload.method === SignInMethod.EMAIL_PASSWORD) {
				authRecord = await testEmailPasswordSignIn(payload);
			} else if (payload.method === SignInMethod.AUTHLINK) {
				authRecord = await testAuthLinkSignIn(payload);
			} else if (payload.method === SignInMethod.APIKEY) {
				const apikeyComponents = payload.apikey?.split("-");

				if (!apikeyComponents) {
					throw new APIError(
						APIErrorName.E_INVALID_AUTHORIZATION,
						"Failed to sign in - incorrect api key format"
					);
				}

				const keyId = apikeyComponents.shift();
				const partialApiKey = apikeyComponents.join("-");

				const keyDocument = await readKey(
					{
						_id: keyId
					},
					null
				);

				if (!keyDocument) {
					throw new APIError(
						APIErrorName.E_DOCUMENT_NOT_FOUND,
						"Failed to sign in - could not find key document"
					);
				}

				apiKeyId = keyDocument._id.toString();

				const authModel = new AuthenticationModel(null);
				authRecord = await authModel.readOneById(keyDocument.authenticationId.toString());

				payload.apikey?.split("-");
				const apikeyHash = bcrypt.hashSync(partialApiKey, authRecord.salt);

				if (apikeyHash !== (authRecord.data as ApikeyAuthenticationData).apikeyHash) {
					throw new APIError(APIErrorName.E_NOT_AUTHENTICATED, "API key does not match auth record data");
				}
			}
		}

		if (!authRecord) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Failed to sign in");
		}

		if (authRecord.method === AuthMethods.OIDC) {
			throw new APIError(APIErrorName.E_AUTH_METHOD_NOT_SUPPORTED,
				"sign In is not available for this authentication method.");
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(authRecord.userId.toString());

		const accessToken = await createAccessToken(authRecord, userDocument);
		const refreshToken = await createRefreshToken(authRecord);

		const result = {
			accessToken,
			refreshToken,
			apiKeyId
		};

		return res.status(200).send(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

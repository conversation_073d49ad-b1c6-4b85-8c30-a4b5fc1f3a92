import mongoose, {
	Document,
	ObjectId
} from "mongoose";

export interface IAuthentication extends Document {
	_id: ObjectId;
	data: UserAuthenticationData | ApikeyAuthenticationData | OIDCAuthenticationData;
	method: string;
	salt: string;
	userId: mongoose.Types.ObjectId;
	verified: boolean;
	legalAgreement: boolean;
	accounts: [
		{
			_id: ObjectId;
			permissions: any;
		}
	];
	super: boolean;
}

export interface UserAuthenticationData {
	email: string;
	passwordHash: string;
}

export interface ApikeyAuthenticationData {
	apikeyHash: string;
}

export interface OIDCAuthenticationData {
	email: string,
	sub: string;
}

export interface IAuthenticationOptions {
	verified?: boolean,
	legalAgreement?: boolean,
}

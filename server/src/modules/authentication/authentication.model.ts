import mongoose, {
	ClientSession,
	FilterQuery,
	QueryOptions
} from "mongoose";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import {
	ApikeyAuthenticationData,
	IAuthentication,
	OIDCAuthenticationData,
	UserAuthenticationData
} from "./authentication.interface";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { IUser } from "../user/user.interfaces";
import { UserDBModel } from "../user/userDB.model";
import { AuthenticationDBModel } from "./authenticationDBModel";
import { AccountModel } from "../account/account.model";

export class AuthenticationModel {
	protected session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public async detachManyByAccountId (accountId: string): Promise<void> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				"accounts._id": new mongoose.Types.ObjectId(accountId)
			};
			const options: mongoose.QueryOptions = {
				session: this.session
			};
			const update: mongoose.UpdateQuery<IAuthentication> = {
				$pull: {
					accounts: {
						_id: new mongoose.Types.ObjectId(accountId)
					}
				}
			};

			await AuthenticationDBModel.updateMany(filter, update, options);
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	public async readOneByEmail (email: string): Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				"data.email": email.toLowerCase().trim()
			};

			const document: IAuthentication | null = await AuthenticationDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found");
			}

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error).suppressLogIf([APIErrorName.E_DOCUMENT_NOT_FOUND]);
		}
	}

	public async readOneById (_id: string): Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				_id: new mongoose.Types.ObjectId(_id)
			};

			const document: IAuthentication | null = await AuthenticationDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found");
			}

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error).suppressLogIf([APIErrorName.E_DOCUMENT_NOT_FOUND]);
		}
	}

	private async attachAccount(authentication: IAuthentication, accountId: string): Promise<IAuthentication> {
		if (!authentication.accounts.some(acc => acc._id.toString() === accountId)) {
			const filter: FilterQuery<IAuthentication> = {
				_id: authentication._id
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};
			const update: mongoose.UpdateQuery<IAuthentication> = {
				$push: {
					accounts: {
						_id: new mongoose.Types.ObjectId(accountId)
					}
				}
			};

			const document = await AuthenticationDBModel.findOneAndUpdate(filter, update, options);
			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication not found.");
			}

			return document;
		}

		return authentication;
	}

	private async detachAccount(authentication: IAuthentication, accountId: string): Promise<IAuthentication> {
		if (!authentication.accounts.some(acc => acc._id.toString() === accountId)) {
			throw new APIError(APIErrorName.E_ACCOUNT_EXISTS, "Account not currently attached.");
		}

		const filter: FilterQuery<IAuthentication> = {
			_id: authentication._id
		};
		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};
		const update: mongoose.UpdateQuery<IAuthentication> = {
			$pull: {
				accounts: {
					_id: new mongoose.Types.ObjectId(accountId)
				}
			}
		};

		const document = await AuthenticationDBModel.findOneAndUpdate(filter, update, options);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication not found.");
		}

		return document;
	}

	protected async createInternal (createData: Partial<IAuthentication>, accountId: string):
	Promise<IAuthentication>
	{
		try {
			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const accountModel = new AccountModel(this.session);
			const account = await accountModel.readOneById(accountId);

			const newDocument = await new AuthenticationDBModel({
				data: createData.data,
				method: createData.method,
				salt: createData.salt,
				userId: createData.userId,
				verified: createData.verified,
				legalAgreement: createData.legalAgreement,
				accounts: [],
				super: createData.super
			}).save(options);

			const updatedDocument = await this.attachAccount(newDocument, account._id.toString());

			if (updatedDocument.verified){
				await this.syncVerify(updatedDocument.userId.toString());
			}

			return updatedDocument;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async verifyOne (authentication: IAuthentication): Promise<IAuthentication> {
		try {
			if (authentication.verified) {
				throw new APIError(APIErrorName.E_ALREADY_VERIFIED, "Authentication already verified");
			}

			const filter: FilterQuery<IAuthentication> = {
				_id: authentication._id
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};
			const update: Partial<IAuthentication> = {
				verified: true
			};

			const document = await AuthenticationDBModel.findOneAndUpdate(filter, update, options);
			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication not found");
			}

			await this.syncVerify(document.userId.toString());

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async attachAccountByUserId(userId: string, accountId: string): Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				userId: new mongoose.Types.ObjectId(userId),
				"accounts._id": { $ne: new mongoose.Types.ObjectId(accountId) }
			};

			const document: IAuthentication | null = await AuthenticationDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Authenticated user already have accountId attached.");
			}

			const updatedDocument = await this.attachAccount(document, accountId);
			return updatedDocument;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async detachAccountByUserId(userId: string, accountId: string):
	Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				userId: new mongoose.Types.ObjectId(userId),
				"accounts._id": new mongoose.Types.ObjectId(accountId)
			};

			const document: IAuthentication | null = await AuthenticationDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Authenticated user does not have accountId attached.");
			}

			const updatedDocument = await this.detachAccount(document, accountId);
			return updatedDocument;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	protected async syncVerify(userId: string): Promise<ModelResponse<void>> {
		try {
			const filter: FilterQuery<IUser> = {
				_id: new mongoose.Types.ObjectId(userId)
			};

			const document: IUser | null = await UserDBModel.findOne(filter).session(this.session);

			if (!document) {
				return new ModelResponse<void>(
					null,
					new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
				);
			}

			return new ModelResponse<void>(null, null);
		} catch (error: unknown) {
			return new ModelResponse<void>(
				null,
				APIError.fromUnknownError(error)
			);
		}
	}

	/**
	 * A duplicate authentication record is defined as having the same IAuthentication.data object.
	 */
	async exists (
		authData: UserAuthenticationData | ApikeyAuthenticationData | OIDCAuthenticationData): Promise<boolean> {

		const filter: FilterQuery<IAuthentication> = {
			data: authData
		};
		const document = await AuthenticationDBModel.findOne(filter).session(
			this.session
		);

		return !!document;
	}

	public async delete(_id: string): Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				_id: new mongoose.Types.ObjectId(_id)
			};
			const options: QueryOptions<IAuthentication> = {};

			const document = await AuthenticationDBModel.findOneAndDelete(filter, options).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found");
			}

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error).suppressLogIf([APIErrorName.E_DOCUMENT_NOT_FOUND]);
		}
	}
}




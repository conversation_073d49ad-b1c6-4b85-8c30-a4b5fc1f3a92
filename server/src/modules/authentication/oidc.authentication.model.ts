import mongoose, {
	ClientSession,
	FilterQuery,
	UpdateQ<PERSON>y
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { AuthMethods } from "./authentication.enums";
import {
	IAuthentication,
	IAuthenticationOptions,
	OIDCAuthenticationData
} from "./authentication.interface";
import { AuthenticationModel } from "./authentication.model";
import bcrypt from "bcryptjs";
import { AuthenticationDBModel } from "./authenticationDBModel";
import { UserModel } from "../user/user.model";


export class OIDCAuthentication extends AuthenticationModel {
	constructor (session: ClientSession | null) {
		super(session);
	}

	public async createOne(
		userId: string,
		accountId: string,
		sub: string,
		options?: IAuthenticationOptions): Promise<IAuthentication>
	{
		const userModel = new UserModel(this.session);
		const user = await userModel.readOneById(userId);

		const authData: OIDCAuthenticationData = {
			email: user.email,
			sub: sub
		};

		if (await this.exists(authData)) {
			throw new APIError(APIErrorName.E_AUTH_EXISTS_DUPLICIATE, "authentication already exists.");
		}

		const authenticationSalt: string = bcrypt.genSaltSync(10);

		const createData: Partial<IAuthentication> = {
			data: authData,
			method: AuthMethods.OIDC,
			salt: authenticationSalt,
			userId: new mongoose.Types.ObjectId(user._id.toString()),
			verified: options?.verified ?? false,
			legalAgreement: options?.legalAgreement ?? true,
			super: false
		};

		const authenticationDocument = await super.createInternal(createData, accountId);

		return authenticationDocument;
	}

	public async readOneBySub (sub: string): Promise<IAuthentication | null> {
		const filter: FilterQuery<IAuthentication> = { "data.sub": sub };
		const document = await AuthenticationDBModel.findOne(filter).session(this.session);
		return document;
	}

	public async findAndUpdateForOIDC(email: string, sub: string): Promise<IAuthentication | null> {
		const filter: FilterQuery<IAuthentication> = { "data.email": email, method: AuthMethods.EMAIL_PASSWORD } ;

		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};
		const update: UpdateQuery<IAuthentication> = {
			$set: {
				method: AuthMethods.OIDC,
				"data.sub": sub,
				"data.email": email
			},
			$unset: {
				"data.passwordHash": ""
			}
		};

		const document = await AuthenticationDBModel.findOneAndUpdate(filter, update, options);
		return document;
	}
}

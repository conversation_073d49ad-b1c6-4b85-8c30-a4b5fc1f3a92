import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { AuthMethods } from "./authentication.enums";
import {
	ApikeyAuthenticationData,
	IAuthentication,
	IAuthenticationOptions
} from "./authentication.interface";
import { AuthenticationModel } from "./authentication.model";
import bcrypt from "bcryptjs";
import { AuthenticationDBModel } from "./authenticationDBModel";
import { UserModel } from "../user/user.model";


export class ApikeyAuthentication extends AuthenticationModel {
	constructor (session: ClientSession | null) {
		super(session);
	}

	public async createOne(
		userId: string,
		accountId: string,
		partialApiKey: string,
		options?: IAuthenticationOptions): Promise<IAuthentication>
	{
		const userModel = new UserModel(this.session);
		const user = await userModel.readOneById(userId);

		const authenticationSalt: string = bcrypt.genSaltSync(10);
		const apikeyHash = bcrypt.hashSync(partialApiKey, authenticationSalt);

		const authData: ApikeyAuthenticationData = {
			apikeyHash: apikeyHash
		};

		if (await this.exists(authData)) {
			throw new APIError(APIErrorName.E_AUTH_EXISTS_DUPLICIATE, "authentication already exists.");
		}

		const createData: Partial<IAuthentication> = {
			data: authData,
			method: AuthMethods.APIKEY,
			salt: authenticationSalt,
			userId: new mongoose.Types.ObjectId(user._id.toString()),
			verified: options?.verified ?? false,
			legalAgreement: options?.legalAgreement ?? true,
			super: false
		};

		const authenticationDocument = await super.createInternal(createData, accountId);

		return authenticationDocument;
	}

	public async readOneByApikeyHash (email: string): Promise<IAuthentication> {
		try {
			const filter: FilterQuery<IAuthentication> = {
				"data.email": email.toLowerCase().trim()
			};

			const document: IAuthentication | null = await AuthenticationDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found");
			}

			return document;

		} catch (error: unknown) {
			throw APIError.fromUnknownError(error).suppressLogIf([APIErrorName.E_DOCUMENT_NOT_FOUND]);
		}
	}

}

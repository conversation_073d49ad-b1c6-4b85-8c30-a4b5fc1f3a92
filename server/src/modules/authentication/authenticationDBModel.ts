import mongoose, { Schema } from "mongoose";
import { IAuthentication } from "../../modules/authentication/authentication.interface";
import { AuthMethods } from "../../modules/authentication/authentication.enums";

const AuthenticationSchema: Schema = new Schema({
	data: {
		type: Schema.Types.Mixed,
		required: true
	},
	method: { type: String, required: true, enum: Object.values(AuthMethods) },
	salt: { type: String, required: true },
	userId: { type: Schema.Types.ObjectId, required: true },
	verified: { type: Boolean, required: true },
	legalAgreement: { type: Boolean, required: true },
	accounts: [
		{
			_id: Schema.Types.ObjectId,
			permissions: Object
		}
	],
	super: { type: Boolean, required: true },
	createdAt: {
		type: Number,
		default: () => Date.now()
	},
	updatedAt: {
		type: Number,
		default: () => Date.now()
	}
},
{ timestamps: true }
);

export const AuthenticationDBModel = mongoose.model<IAuthentication>("Authentications", AuthenticationSchema);

import jwt, { SignOptions } from "jsonwebtoken";
import { AuthenticationModel } from "../authentication.model";
import { getSecrets } from "../../secrets/secrets.model";
import { AuthLinkToken } from "./authlink.interfaces";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";

export class AuthLinkModel {
	public static async createToken (authenticationId: string, accountId: string): Promise<string> {
		const saltedHash = await this.createSaltedHash(authenticationId);

		const options: SignOptions = {
			expiresIn: "2m",
			algorithm: "HS256"
		};

		const payload: AuthLinkToken = {
			authenticationId,
			accountId,
			type: "authlink"
		};

		const signedJWT = jwt.sign(payload, saltedHash, options);

		return signedJWT;
	}

	public static decodeToken (token: string): AuthLinkToken {
		const decodedToken = jwt.decode(token) as AuthLinkToken;
		if (!decodedToken.accountId || !decodedToken.authenticationId || !decodedToken.type) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Failed to decode authlink token");
		}

		return decodedToken;
	}

	public static async verifyAndDecodeToken (token: string): Promise<AuthLinkToken> {
		const decodedToken = this.decodeToken(token);
		const saltedHash = await this.createSaltedHash(decodedToken.authenticationId);

		try {
			const authLinkToken = jwt.verify(token, saltedHash) as AuthLinkToken;
			return authLinkToken;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, error);
		}
	}

	private static async createSaltedHash (authenticationId: string): Promise<string> {
		const authModel = new AuthenticationModel(null);
		const authDocument = await authModel.readOneById(authenticationId);

		const secrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		return `${gpSecretKey}${authDocument.salt}`;
	}
}

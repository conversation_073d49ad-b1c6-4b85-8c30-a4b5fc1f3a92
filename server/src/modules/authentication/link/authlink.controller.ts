import {
	Request,
	Response
} from "express";
import { AuthLinkPostRequest } from "./authlink.interfaces";
import { AuthLinkPostValidator } from "./authlink.joi";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { AuthLinkModel } from "./authlink.model";
import { AccessTokenModel } from "../../accessToken/accessToken.model";
import { AccountTokenModel } from "../../account/token/account.token.model";

export class AuthLinkController {
	async post (request: Request, response: Response): Promise<Response> {
		let authLinkPostRequest: AuthLinkPostRequest;

		try {
			authLinkPostRequest = await AuthLinkPostValidator.validateAsync(
				{
					apiVersion: request.headers["x-api-version"],
					accessToken: request.headers["authorization"],
					accountToken: request.headers["x-account-token"]
				}
			);
		} catch (error: unknown) {
			const apiError: APIError = new APIError(
				APIErrorName.E_INVALID_INPUT,
				error
			);

			return apiError.log().setResponse(response);
		}

		let decodedAccountToken;
		try {
			await AccessTokenModel.verifyAndDecode(authLinkPostRequest.accessToken);
			decodedAccountToken = await AccountTokenModel.verifyAndDecode(authLinkPostRequest.accountToken);
		} catch (error: unknown) {
			const apiError: APIError = new APIError(
				APIErrorName.E_INVALID_AUTHORIZATION,
				error
			);

			return apiError.log().setResponse(response);
		}

		try {
			const authLinkToken: string = await AuthLinkModel.createToken(
				decodedAccountToken.authenticationId,
				decodedAccountToken.account._id
			);

			return response.status(200).send({
				authLinkToken: authLinkToken
			});
		} catch (error: unknown) {
			const apiError: APIError = new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				error
			);

			return apiError.log().setResponse(response);
		}
	}
}

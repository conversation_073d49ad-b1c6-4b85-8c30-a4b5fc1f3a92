import express, {
	Express,
	Request,
	Response
} from "express";

import { AuthLinkController } from "./authlink.controller";

export class AuthLinkRouter {
	private controller: AuthLinkController = new AuthLinkController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[],
			(request: Request, response: Response) => {
				return this.controller.post(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/auth/link", this.router);
	}
}

import { SpeechClient } from "@google-cloud/speech/build/src/v2";
import { protos } from "@google-cloud/speech";
import { Storage } from "@google-cloud/storage";
import { APIError } from "../../utils/helpers/apiError";
import {
	Credentials,
	SpeechToTextData,
	TranscriptFile,
	WordTiming
} from "./speechToText.interfaces";
import protobuf from "protobufjs";
import * as googleProtoFiles from "google-proto-files";



export class SpeechToTextModel {
	private speechClient: SpeechClient;
	private storage: Storage;
	private projectId: string;
	private transcriptPath: string | undefined;
	private transcriptData: SpeechToTextData | undefined;
	private speechRoot: protobuf.Root | null = null;

	public constructor(
		public bucketAudioFilePath: string,
		public bucketName: string,
		apiEndpoint: string,
		credentials: Credentials
	) {
		this.projectId = credentials.project_id;
		this.speechClient = new SpeechClient({
			apiEndpoint: apiEndpoint,
			credentials
		});
		this.storage = new Storage({ credentials });
	}

	public get data(): SpeechToTextData {
		if (!this.transcriptData) {
			throw new Error("Transcript data is not available. Please call transcribe() first.");
		}

		return this.transcriptData;
	}

	public get path(): string {
		if (!this.transcriptPath) {
			throw new Error("Transcript path is not available. Please call transcribe() first.");
		}

		return this.transcriptPath;
	}

	public async transcribe(): Promise<void> {
		try {
			const request = this.createRecognitionRequest();
			const operation = await this.startRecognition(request);
			this.transcriptPath = await this.waitForTranscript(operation);
			const transcriptFile = await this.readTranscriptFile(this.transcriptPath);
			this.transcriptData = this.extractWordsFromTranscript(transcriptFile);
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	private createRecognitionRequest(): protos.google.cloud.speech.v2.IBatchRecognizeRequest {
		return {
			recognizer: `projects/${this.projectId}/locations/global/recognizers/_`,
			config: {
				autoDecodingConfig: {},
				languageCodes: ["en-US"],
				model: "long",
				features: {
					enableWordTimeOffsets: true,
					enableAutomaticPunctuation: true,
					enableWordConfidence: true,
					enableSpokenPunctuation: true,
					enableSpokenEmojis: false
				}
			},
			recognitionOutputConfig: {
				gcsOutputConfig: {
					uri: `gs://${this.bucketName}/speechToText/`
				}
			},
			files: [{
				uri: `gs://${this.bucketName}/${this.bucketAudioFilePath}`
			}]
		};
	}

	private async startRecognition(request: protos.google.cloud.speech.v2.IBatchRecognizeRequest): Promise<any> {
		const [operation, metadata] = await this.speechClient.batchRecognize(request);
		if (!metadata?.name) {
			throw new Error("Operation name not found in response");
		}
		return operation;
	}

	private async waitForTranscript(operation: any): Promise<string> {
		const response = await this.pollOperation(operation.name);
		if (!response) {
			throw new Error("No response received from operation");
		}

		const decodedResponse = this.decodeResponse(response);
		const inputUri = `gs://${this.bucketName}/${this.bucketAudioFilePath}`;
		let outputUri: string | undefined;

		if (
			decodedResponse &&
			typeof decodedResponse === "object" &&
			"results" in decodedResponse &&
			decodedResponse.results &&
			typeof decodedResponse.results === "object"
		) {
			const uriValue = (decodedResponse as protos.google.cloud.speech.v2.BatchRecognizeResponse)
				.results?.[inputUri]?.uri;
			if (uriValue !== null && uriValue !== undefined) {
				outputUri = uriValue;
			}
		}

		if (!outputUri) {
			throw new Error("Output URI not found in response");
		}
		return outputUri;
	}

	private extractWordsFromTranscript(transcriptFile: TranscriptFile): SpeechToTextData {
		if (!transcriptFile?.results) {
			return { words: [] };
		}

		const validResults = transcriptFile.results.filter(
			(result) =>
				Array.isArray(result.alternatives) &&
				result.alternatives[0]?.words?.length > 0
		);

		if (validResults.length === 0) {
			return { words: [] };
		}

		const words = validResults.flatMap((result) =>
			result.alternatives && result.alternatives[0]?.words
				? result.alternatives[0].words
				: []
		);

		const wordTimings: WordTiming[] = words.map((word) => ({
			startTime: this.parseTime(word.startOffset),
			endTime: this.parseTime(word.endOffset),
			word: word.word
		}));

		return { words: wordTimings };
	}

	private parseTime(timeString: string): { seconds: number; nanos: number } {
		const seconds = parseFloat(timeString?.replace("s", "") || "0");
		return {
			seconds: Math.floor(seconds),
			nanos: Math.round((seconds % 1) * 1e9)
		};
	}

	private async loadSpeechProto(): Promise<protobuf.Root> {
		if (!this.speechRoot) {
			const root = new protobuf.Root();

			root.resolvePath = function (origin: string, target: string): string {
				if (target.startsWith("google/")) {
					return googleProtoFiles.getProtoPath(target.replace(/^google\//, ""));
				}
				return target;
			};

			await root.load(googleProtoFiles.getProtoPath("cloud/speech/v2/cloud_speech.proto"));
			root.resolveAll();

			this.speechRoot = root;
		}

		return this.speechRoot;
	}

	private async decodeMetadata(buffer: Buffer): Promise<any> {
		function tryDecode(
			type: protobuf.Type,
			buffer: Buffer
		): protos.google.protobuf.IAny | null {
			try {
				const decoded = type.decode(buffer);
				return type.toObject(decoded, { defaults: true });
			} catch (err) {
				return null;
			}
		}

		const root = await this.loadSpeechProto();

		const typeNames = [
			"google.cloud.speech.v2.OperationMetadata",
			"google.cloud.speech.v2.BatchRecognizeMetadata",
			"google.cloud.speech.v2.BatchRecognizeTranscriptionMetadata"
		];

		for (const typeName of typeNames) {
			const MetadataType = root.lookupType(typeName);
			if (MetadataType) {
				const result = tryDecode(MetadataType, buffer);
				if (result) {
					return result;
				}
			}
		}

		return null;
	}

	private async pollOperation(
		operationName: string,
		maxAttempts = 30
	): Promise<protos.google.protobuf.IAny | null | undefined> {
		const baseDelay = 2000;
		let lastProgress: number | null = null;

		const poll = async (attempt: number): Promise<protos.google.protobuf.IAny | null | undefined> => {
			if (attempt >= maxAttempts) {
				throw new Error("Operation timed out");
			}

			const request = {
				name: operationName,
				toJSON(): any {
					return { name: this.name };
				}
			};

			const [operation] = await this.speechClient.getOperation(request);

			if (operation.metadata && typeof operation.metadata === "object") {
				const meta: any = operation.metadata;

				let buffer: Buffer | null = null;
				if (Buffer.isBuffer(meta.value)) {
					buffer = meta.value;
				} else if (meta.value && meta.value.data) {
					buffer = Buffer.from(meta.value.data);
				} else if (meta.value) {
					buffer = Buffer.from(meta.value);
				}

				if (buffer && buffer.length > 0) {
					try {
						const metadata = await this.decodeMetadata(buffer);
						if (
							metadata &&
							typeof metadata === "object" &&
							typeof metadata.progressPercent === "number"
						) {
							if (
								lastProgress === null ||
								metadata.progressPercent > lastProgress
							) {
								lastProgress = metadata.progressPercent;
								attempt = 0;
							}
						}
					} catch (err) {
						console.error("Failed to decode metadata:", err);
					}
				}
			}

			if (operation.done) {
				return operation.response;
			}

			const delay = Math.min(baseDelay * Math.pow(1.5, attempt), 10000);
			await new Promise(resolve => setTimeout(resolve, delay));
			return poll(attempt + 1);
		};

		return poll(0);
	}

	private decodeResponse(
		response: protos.google.protobuf.IAny
	): protos.google.protobuf.IAny | protos.google.cloud.speech.v2.BatchRecognizeResponse {
		if (!response) {
			throw new Error("Response is null or undefined");
		}

		if (response.type_url === "type.googleapis.com/google.cloud.speech.v2.BatchRecognizeResponse") {
			try {
				const buffer = Buffer.isBuffer(response.value)
					? response.value
					: Buffer.from(response.value || []);

				const decodedResponse = protos.google.cloud.speech.v2.BatchRecognizeResponse.decode(buffer);

				return decodedResponse;
			} catch (error: unknown) {
				throw APIError.fromUnknownError(error).setDetail({
					rawResponse: response
				});
			}
		}
		return response;
	}

	private parseGCSUri(gcsUri: string): { bucketName: string; filePath: string } {
		const match = gcsUri.match(/gs:\/\/([^/]+)\/(.+)/);
		if (!match) {
			throw new Error("Invalid GCS URI format");
		}
		const [, bucketName, filePath] = match;
		return { bucketName, filePath };
	}

	private async getGCSFileUri(gcsUri: string): Promise<string> {
		const { bucketName, filePath } = this.parseGCSUri(gcsUri);
		const bucket = this.storage.bucket(bucketName);
		const file = bucket.file(filePath);
		const [content] = await file.download();

		if (!content || content.length === 0) {
			throw new Error(`File not found or empty at GCS URI: ${gcsUri}`);
		}

		return content.toString();
	}

	private async readTranscriptFile(gcsUri: string): Promise<TranscriptFile> {
		const transcriptData = await this.getGCSFileUri(gcsUri);
		return JSON.parse(transcriptData);
	}
}

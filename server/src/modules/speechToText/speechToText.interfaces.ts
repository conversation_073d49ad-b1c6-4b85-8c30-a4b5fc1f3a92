export interface Credentials {
	client_email: string;
	private_key: string;
	project_id: string;
}

export interface WordTiming {
	startTime: {
		seconds: number;
		nanos: number;
	};
	endTime: {
		seconds: number;
		nanos: number;
	};
	word: string;
}

export interface SpeechToTextData {
	words: WordTiming[];
}

export interface TranscriptWord {
	startOffset: string;
	endOffset: string;
	word: string;
	confidence: number;
}

export interface TranscriptAlternative {
	transcript: string;
	confidence: number;
	words: TranscriptWord[];
}

export interface TranscriptResult {
	alternatives?: TranscriptAlternative[];
	resultEndOffset: string;
	languageCode: string;
}

export interface TranscriptFile {
	results: TranscriptResult[];
}

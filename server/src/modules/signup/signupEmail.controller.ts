import {
	Request,
	Response
} from "express";
import { SignUpModel } from "../../modules/signup/signup.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";

export const signupEmailController = async (
	req: Request,
	res: Response
): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}
		const signupEmailPayload = req.body as ISignupEmailPayload;

		await startDBTransaction(res.locals.session);

		const signupModel = new SignUpModel(res.locals.session);
		const signupResult = await signupModel.createFromEmail({
			email: signupEmailPayload.email,
			callbackEndpoint: signupEmailPayload.callbackEndpoint,
			locale: signupEmailPayload.locale
		});

		await completeDBTransaction(res.locals.session);

		if (apiVersion < 3) {
			return res.sendStatus(201);
		}

		const accessToken = await createAccessToken(
			signupResult.authenticationDoc,
			signupResult.userDoc
		);
		const refreshToken = await createRefreshToken(signupResult.authenticationDoc);

		const responseData = {
			accessToken: accessToken,
			refreshToken: refreshToken
		};

		const response = res.status(200).json(responseData);
		return response;
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IUploadChunks,
	IUploadChunksOut,
	uploadAssetChunksToCloud
} from "../../services/gp/bucket.service";

export const putFilesController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const location = req.headers["location"];
		const contentType = req.headers["content-type"];
		const contentLength = req.headers["content-length"] as string;
		const contentRange = req.headers["content-range"];
		const bodyChunk = req.body as Buffer;

		if (!location || !contentType || !contentLength || !contentRange) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing request Headers.");
		}

		const uploadChunksInput: IUploadChunks = {
			location: location,
			contentType: contentType,
			contentLength: parseInt(contentLength, 10),
			contentRange: contentRange,
			fileChunk: bodyChunk
		};

		const uploadChunksOut: IUploadChunksOut | undefined =
      await uploadAssetChunksToCloud(uploadChunksInput);

		if (uploadChunksOut?.status === 308) {
			return res.status(308).json({});
		}

		if (!uploadChunksOut?.url) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Failed to upload file.");
		}

		const result = {
			publicURL: uploadChunksOut.url
		};

		return res.status(200).json(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

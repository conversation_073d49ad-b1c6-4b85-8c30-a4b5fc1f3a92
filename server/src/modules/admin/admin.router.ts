import express, {
	Express,
	Request,
	Response
} from "express";
import path from "path";

export class AdminRouter {
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[],
			(request: Request, response: Response) => {
				const error = new Error("Request Not Found.");
				return response.status(404).json({
					message: error.message
				});
			}
		);

		this.router.put(
			"/",
			[],
			(request: Request, response: Response) => {
				const error = new Error("Request Not Found.");
				return response.status(404).json({
					message: error.message
				});
			}
		);

		this.router.patch(
			"/",
			[],
			(request: Request, response: Response) => {
				const error = new Error("Request Not Found.");
				return response.status(404).json({
					message: error.message
				});
			}
		);

		this.router.delete(
			"/",
			[],
			(request: Request, response: Response) => {
				const error = new Error("Request Not Found.");
				return response.status(404).json({
					message: error.message
				});
			}
		);

		this.router.get("*", (request: Request, response: Response) => {
			const options = {
				root: AdminRouter.getAdminDistributionPath()
			};

			response.sendFile("index.html", options);
		});
	}

	public use (expressServer: Express): void {
		expressServer.use(express.static(AdminRouter.getAdminDistributionPath()));
		expressServer.use("/", this.router);
	}

	static getAdminDistributionPath = (): string => {
		const defaultAdminBuildPath: string = path.resolve(process.cwd(), "..", "admin", "dist");
		const adminBuildPath: string = process.env.ADMIN_DIST_PATH || defaultAdminBuildPath;
		return adminBuildPath;
	};
}

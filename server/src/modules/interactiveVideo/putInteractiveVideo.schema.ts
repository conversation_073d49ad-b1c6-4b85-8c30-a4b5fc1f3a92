import Joi from "joi";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IPutInteractiveVideoPayload,
	VideoDisplayModeEnum
} from "./interactiveVideo.interface";
import {
	validateCaptionData,
	captionDataValidationMessages
} from "../caption/captionValidation";

export const putInteractiveVideoSchema = {
	data: Joi.object<IPutInteractiveVideoPayload>({
		title: Joi.string().required(),
		description: Joi.string().allow("").max(150).optional(),
		videoURL: Joi.string().required(),
		productTitle0: Joi.string().optional(),
		productTitle1: Joi.string().optional(),
		productTitle2: Joi.string().optional(),
		productTitle3: Joi.string().optional(),
		productTitle4: Joi.string().optional(),
		productTitle5: Joi.string().optional(),
		productTitle6: Joi.string().optional(),
		productTitle7: Joi.string().optional(),
		productTitle8: Joi.string().optional(),
		productTitle9: Joi.string().optional(),
		productURL0: Joi.string().optional(),
		productURL1: Joi.string().optional(),
		productURL2: Joi.string().optional(),
		productURL3: Joi.string().optional(),
		productURL4: Joi.string().optional(),
		productURL5: Joi.string().optional(),
		productURL6: Joi.string().optional(),
		productURL7: Joi.string().optional(),
		productURL8: Joi.string().optional(),
		productURL9: Joi.string().optional(),
		productImageURL0: Joi.string().optional(),
		productImageURL1: Joi.string().optional(),
		productImageURL2: Joi.string().optional(),
		productImageURL3: Joi.string().optional(),
		productImageURL4: Joi.string().optional(),
		productImageURL5: Joi.string().optional(),
		productImageURL6: Joi.string().optional(),
		productImageURL7: Joi.string().optional(),
		productImageURL8: Joi.string().optional(),
		productImageURL9: Joi.string().optional(),
		productDescription0: Joi.string().optional(),
		productDescription1: Joi.string().optional(),
		productDescription2: Joi.string().optional(),
		productDescription3: Joi.string().optional(),
		productDescription4: Joi.string().optional(),
		productDescription5: Joi.string().optional(),
		productDescription6: Joi.string().optional(),
		productDescription7: Joi.string().optional(),
		productDescription8: Joi.string().optional(),
		productDescription9: Joi.string().optional(),
		subTitle0: Joi.string().optional(),
		subTitle1: Joi.string().optional(),
		subTitle2: Joi.string().optional(),
		subTitle3: Joi.string().optional(),
		subTitle4: Joi.string().optional(),
		subTitle5: Joi.string().optional(),
		subTitle6: Joi.string().optional(),
		subTitle7: Joi.string().optional(),
		subTitle8: Joi.string().optional(),
		subTitle9: Joi.string().optional(),
		ctaText: Joi.string().required(),
		showTitle: Joi.string().valid("true", "false").required(),
		videoPosterImageURL: Joi.string().required(),
		videoPosterPlayEmbedURL: Joi.string().required(),
		gifURL: Joi.string().required(),
		captionData: Joi.string().custom(validateCaptionData).messages(captionDataValidationMessages).optional(),
		videoDisplayMode: Joi.string().valid(VideoDisplayModeEnum.PORTRAIT, VideoDisplayModeEnum.LANDSCAPE).optional(),
		phone: Joi.string().trim().allow("").regex(/^\+1\d{10}$/)
			.messages({
				"string.pattern.base": "Invalid phone number format. Must be in the format +1XXXXXXXXXX."
			})
			.optional(),
		email: Joi.string().email({ tlds: { allow: false } }).trim().allow("").optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

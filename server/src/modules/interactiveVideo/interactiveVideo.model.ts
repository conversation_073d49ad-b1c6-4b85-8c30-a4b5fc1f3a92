import mongoose, {
	C<PERSON>Session,
	FilterQuery,
	ObjectId,
	PipelineStage
} from "mongoose";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { InteractiveVideoDBModel } from "./interactiveVideoDB.model";
import { MetricInteractiveVideoModel } from "../metricInteractiveVideo/metricInteractiveVideo.model";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { AccountModel } from "../account/account.model";
import {
	IShoppableVideo,
	IShoppableVideoProduct,
	InteractiveVideoReadManyByAccountId,
	InteractiveVideoUpdateOne,
	InteractiveVideoCreateOne
} from "./interactiveVideo.interface";
import { APIErrorName } from "../../interfaces/apiTypes";
import { CDN_DIR } from "../../utils/helpers/gp.helper";
import { writeAssetToCloud } from "../../services/gp/bucket.service";
import {
	IVideoPlayTimeInput,
	IVideoPlayTimeOutput
} from "../metricVideoPlayTime/metricVideoPlayTime.interfaces";

export class InteractiveVideoModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments(filter: FilterQuery<IShoppableVideo>): Promise<number> {
		const count: number = await InteractiveVideoDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	private async isInteractiveVideoLimitReached(account: IAccount): Promise<boolean> {
		const filter: FilterQuery<IShoppableVideo> = {
			accountId: account._id
		};

		const documentCount = await this.countDocuments(filter);

		const limit = account.subscription.maxInteractiveVideoLimit;

		return documentCount >= limit;
	}

	private async testResourceLimits(account: IAccount, createData: any): Promise<void> {
		if (await this.isInteractiveVideoLimitReached(account)) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Interactive video limit reached").suppressLog();
		}

		const accountModel: AccountModel = new AccountModel(this.session);
		if (accountModel.isImpressionLimitReached(account)) {
			throw new APIError(APIErrorName.E_IMPRESSION_LIMIT_REACHED, "impression limit reached");
		}

		const maxVideoProductLinksLimit = account.subscription.maxVideoProductLinksLimit;

		if (createData.products.length > maxVideoProductLinksLimit) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Interactive video product links limit reached");
		}
	}

	async createOne(createData: InteractiveVideoCreateOne, account: IAccount): Promise<IShoppableVideo> {
		await this.testResourceLimits(account, createData);

		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new InteractiveVideoDBModel({
			...createData,
			accountId: account._id
		}).save(options);

		const metricInteractiveVideoModel: MetricInteractiveVideoModel =
			new MetricInteractiveVideoModel(this.session);

		const metricInteractiveVideoDocument = await metricInteractiveVideoModel.createOne(
			{
				accountId: account._id,
				interactiveVideoId: newDocument._id
			}
		);
		metricInteractiveVideoDocument.throwIfError();
		await this.updateVideoDataCache(newDocument);

		return newDocument;
	}

	async readManyByIds(_ids: ObjectId[]): Promise<IShoppableVideo[]> {
		const filter: FilterQuery<IShoppableVideo> = {
			_id: { $in: _ids }
		};
		const documents = await InteractiveVideoDBModel.find(filter).session(this.session);
		return documents;
	}

	async updateOneById(_id: string, updateData: InteractiveVideoUpdateOne): Promise<IShoppableVideo> {
		try {
			const filter: FilterQuery<IShoppableVideo> = {
				_id: new mongoose.Types.ObjectId(_id)
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};

			const document = await InteractiveVideoDBModel.findOneAndUpdate(filter, updateData, options);
			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
			}

			await this.updateRelatedCollectionDataCache(document);
			await this.updateVideoDataCache(document);

			return document;
		} catch (error: unknown) {
			if (error instanceof APIError) {
				throw error;
			}
			throw new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message);
		}
	}

	private async updateRelatedCollectionDataCache(interactiveVideo: IShoppableVideo): Promise<void> {
		const interactiveCollectionModel = new InteractiveCollectionModel(this.session);
		const interactiveCollections = await interactiveCollectionModel.readManyByVideo(interactiveVideo);
		if (interactiveCollections.length < 1) {
			throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Failed to read interactiveCollections");
		}

		const promises = interactiveCollections.map(collection =>
			interactiveCollectionModel.updateDataCache(collection)
		);
		await Promise.all(promises);
	}

	async updateVideoDataCache(interactiveVideo: IShoppableVideo): Promise<void> {
		const fileContent = JSON.stringify({
			accountId: interactiveVideo.accountId,
			interactiveVideo: interactiveVideo
		});

		const fileDestination = `${CDN_DIR.DATACACHE}videos/` +
			`${interactiveVideo._id.toString()}-video.json`;

		const cacheControl = "no-cache, max-age=0";
		await writeAssetToCloud(fileContent, fileDestination, cacheControl);
	}

	async redactData(interactiveVideos: IShoppableVideo[]): Promise<IShoppableVideo[]> {
		if (interactiveVideos.length === 0) return [];

		const accountId = interactiveVideos[0].accountId;
		const accountModel: AccountModel = new AccountModel(this.session);
		const accountDocument = await accountModel.readOneById(accountId.toString());

		const shouldRedactEngagementMetrics = !accountDocument.subscription.enableEngagementMetrics;
		const shouldRedactCTALeads = !accountDocument.subscription.allowCTALead;

		const isBasicAccount = accountDocument.subscription.type === "basic";

		interactiveVideos.forEach(video => {
			if (shouldRedactEngagementMetrics) {
				video.playPercentCount20 = 0;
				video.playPercentCount40 = 0;
				video.playPercentCount60 = 0;
				video.playPercentCount80 = 0;
				video.playPercentCount100 = 0;
				video.videoScore = 0;
			}
			if (isBasicAccount) {
				video.likes = 0;
			}
			if (shouldRedactCTALeads) {
				video.emailSubmitCount = 0;
				video.phonePressCount = 0;
			}
		});
		return interactiveVideos;
	}

	async constructPublicData(interactiveVideos: IShoppableVideo[]): Promise<Partial<IShoppableVideo>[]> {
		if (interactiveVideos.length === 0) return [];

		return interactiveVideos.map(video => ({
			_id: video._id,
			title: video.title,
			description: video.description,
			videoURL: video.videoURL,
			videoPosterURL: video.videoPosterURL,
			products: video.products,
			ctaText: video.ctaText,
			showTitle: video.showTitle,
			videoDisplayMode: video.videoDisplayMode,
			videoWidthPx: video.videoWidthPx,
			videoHeightPx: video.videoHeightPx,
			videoPosterPlayEmbedURL: video.videoPosterPlayEmbedURL,
			phone: video.phone,
			email: video.email,
			captionData: video.captionData
		}));
	}

	async incrementLikesById(_id: string): Promise<IShoppableVideo> {
		const filter: FilterQuery<IShoppableVideo> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const update = { $inc: { likes: 1 } };
		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};

		const document = await InteractiveVideoDBModel.findOneAndUpdate(filter, update, options);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
		}
		return document;
	}

	async updateVideoMetrics(videoInput: IVideoPlayTimeInput, metricInput: IVideoPlayTimeOutput):
	Promise<IShoppableVideo> {
		const updatePipeline: PipelineStage[] = [
			{
				$set: {
					videoTotalSeconds: videoInput.videoSecondsLength,
					videoPlayCount: {
						$cond: {
							if: { $eq: [metricInput.wasCreated, true] },
							then: { $add: ["$videoPlayCount", 1] },
							else: "$videoPlayCount"
						}
					},
					videoPlayDurationSeconds: {
						$cond: {
							if: { $eq: [metricInput.wasCreated, true] },
							then: {
								$add: ["$videoPlayDurationSeconds", videoInput.videoSecondsPosition]
							},
							else: {
								$add: [
									"$videoPlayDurationSeconds",
									{
										$max: [
											{
												$subtract: [
													videoInput.videoSecondsPosition,
													metricInput.oldDocPlayTimeSeconds
												]
											},
											0
										]
									}
								]
							}
						}
					}
				}
			},
			{
				$set: {
					videoUniquePlayDurationSeconds: {
						$cond: {
							if: {
								$and: [
									metricInput.newPercentToInc,
									metricInput.oldPercentToDec, {
										$ne: [
											metricInput.newPercentToInc,
											metricInput.oldPercentToDec
										]
									}
								]
							},
							then: {
								$subtract: [
									{ $add: ["$videoUniquePlayDurationSeconds", videoInput.videoSecondsPosition] },
									{ $ifNull: [metricInput.oldPlayDurationSeconds, 0] }
								]
							},
							else: {
								$cond: {
									if: {
										$and: [
											metricInput.newPercentToInc,
											{ $not: { $ifNull: [metricInput.oldPercentToDec, false] } }]
									},
									then: {
										$add: ["$videoUniquePlayDurationSeconds", videoInput.videoSecondsPosition]
									},
									else: "$videoUniquePlayDurationSeconds"
								}
							}
						}
					},
					videoUniquePlayCount: {
						$cond: {
							if: {
								$and: [
									metricInput.newPercentToInc,
									{ $not: { $ifNull: [metricInput.oldPercentToDec, false] } }
								]
							},
							then: { $add: ["$videoUniquePlayCount", 1] },
							else: "$videoUniquePlayCount"
						}
					},
					...(
						metricInput.newPercentToInc ? {
							[metricInput.newPercentToInc]: {
								$add: [`$${metricInput.newPercentToInc}`, 1]
							}
						} : {}
					),
					...(
						metricInput.oldPercentToDec ? {
							[metricInput.oldPercentToDec]: {
								$subtract: [`$${metricInput.oldPercentToDec}`, 1]
							}
						} : {}
					)
				}
			},
			{
				$set: {
					videoScore: {
						$cond: {
							if: {
								$gt: [{ $multiply: ["$videoUniquePlayCount", videoInput.videoSecondsLength] }, 0]
							},
							then: {
								$multiply: [
									{
										$divide: [
											"$videoUniquePlayDurationSeconds",
											{ $multiply: ["$videoUniquePlayCount", videoInput.videoSecondsLength] }
										]
									},
									100
								]
							},
							else: 0
						}
					}
				}
			}
		];

		const filter: FilterQuery<IShoppableVideo> = {
			_id: new mongoose.Types.ObjectId(videoInput.videoId)
		};

		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};

		const updatedDocument = await InteractiveVideoDBModel.findOneAndUpdate(filter, updatePipeline, options);
		if (!updatedDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
		}
		return updatedDocument;
	}

	async readOneById(_id: string): Promise<IShoppableVideo> {
		try {
			const filter: FilterQuery<IShoppableVideo> = {
				_id: new mongoose.Types.ObjectId(_id)
			};

			const document: IShoppableVideo | null = await InteractiveVideoDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "InteractiveVideo document not found");
			}

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	public async readManyByAccountId(
		params: InteractiveVideoReadManyByAccountId
	): Promise<IShoppableVideo[]> {
		try {
			const defaultSortKey = "createdAt";
			const finalSortKey = params.sortKey || defaultSortKey;
			const finalSortBy = params.sortBy === "dsc" ? -1 : 1;

			const pipeline: any[] = [
				{
					$match: {
						accountId: new mongoose.Types.ObjectId(params.accountId)
					}
				},
				{ $sort: { [finalSortKey]: finalSortBy } }
			];
			if (params.limit) {
				pipeline.push({ $limit: params.limit });
			}

			const documents: IShoppableVideo[] = await InteractiveVideoDBModel.aggregate(
				pipeline
			).session(this.session);
			return documents;
		} catch (error: any) {
			throw APIError.fromUnknownError(error);
		}
	}
	async incrementPhonePressCount(_id: string): Promise<IShoppableVideo> {
		const filter: FilterQuery<IShoppableVideo> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const update = { $inc: { phonePressCount: 1 } };
		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};

		const document = await InteractiveVideoDBModel.findOneAndUpdate(filter, update, options);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
		}
		return document;
	}

	async incrementEmailSubmitCount(_id: string): Promise<IShoppableVideo> {
		const filter: FilterQuery<IShoppableVideo> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const update = { $inc: { emailSubmitCount: 1 } };
		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true
		};

		const document = await InteractiveVideoDBModel.findOneAndUpdate(filter, update, options);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
		}
		return document;
	}

	public async incrementProductLinkClick(_id: string, product: IShoppableVideoProduct): Promise<IShoppableVideo> {
		const filter: FilterQuery<IShoppableVideo> = {
			_id: new mongoose.Types.ObjectId(_id)
		};

		const updatePipeline = [
			{
				$set: {
					linkClicks: {
						$let: {
							vars: {
								existingClick: {
									$filter: {
										input: "$linkClicks",
										as: "click",
										cond: {
											$and: [
												{ $eq: ["$$click.productTitle", product.title] },
												{ $eq: ["$$click.productURL", product.url] },
												{
													$eq: ["$$click.productImageURL", product?.productThumbnail]
												}
											]
										}
									}
								}
							},
							in: {
								$cond: {
									if: { $gt: [{ $size: "$$existingClick" }, 0] },
									then: {
										$map: {
											input: "$linkClicks",
											as: "click",
											in: {
												$cond: {
													if: {
														$and: [
															{ $eq: ["$$click.productTitle", product.title] },
															{ $eq: ["$$click.productURL", product.url] },
															{
																$eq: [
																	"$$click.productImageURL",
																	product?.productThumbnail
																]
															}
														]
													},
													then: {
														$mergeObjects: [
															"$$click",
															{ clickCount: { $add: ["$$click.clickCount", 1] } }
														]
													},
													else: "$$click"
												}
											}
										}
									},
									else: {
										$concatArrays: [
											"$linkClicks",
											[
												{
													productId: product._id,
													productTitle: product.title,
													productURL: product.url,
													productImageURL: product?.productThumbnail,
													clickCount: 1
												}
											]
										]
									}
								}
							}
						}
					}
				}
			}
		];

		const options: mongoose.QueryOptions = {
			session: this.session,
			new: true,
			upsert: true
		};

		const document = await InteractiveVideoDBModel.findOneAndUpdate(filter, updatePipeline, options);

		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Interactive video not found");
		}

		return document;
	}
}

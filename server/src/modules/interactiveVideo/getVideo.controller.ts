import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { AccountModel } from "../../modules/account/account.model";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { APIErrorName } from "../../interfaces/apiTypes";

export const getVideoController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		const interactiveVideoModel = new InteractiveVideoModel(null);
		const videoDoc = await interactiveVideoModel.readOneById(req.params.videoId);

		if (videoDoc.accountId.toString() != req.accountToken.account._id) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Failed to authenticate");
		}

		const accountModel = new AccountModel(null);
		const accountDocument = await accountModel.readOneById(req.accountToken.account._id);

		if (accountDocument.subscription.enableEngagementMetrics !== true) {
			videoDoc.playPercentCount20 = 0;
			videoDoc.playPercentCount40 = 0;
			videoDoc.playPercentCount60 = 0;
			videoDoc.playPercentCount80 = 0;
			videoDoc.playPercentCount100 = 0;
			videoDoc.videoScore = 0;
			videoDoc.videoUniquePlayCount = 0;
			videoDoc.videoUniquePlayDurationSeconds = 0;
		}

		if (videoDoc.captionData) {
			const tempCaption:object = JSON.parse(JSON.stringify(videoDoc.captionData));
			if ("fontSizeRem" in tempCaption) {
				videoDoc.captionData.fontSize = tempCaption.fontSizeRem as number;
			} else if (!videoDoc.captionData.fontSize) {
				videoDoc.captionData.fontSize = 1;
			}
		}

		const result = {
			shoppableVideo: (await interactiveVideoModel.redactData([videoDoc]))[0]
		};

		return res.send(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

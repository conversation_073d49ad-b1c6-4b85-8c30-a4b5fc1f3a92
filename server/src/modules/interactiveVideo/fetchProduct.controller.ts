import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import { fetchProductData } from "../../services/product/iframely.service";
import { APIError } from "../../utils/helpers/apiError";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

export const fetchProductController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version"
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version"
			);
		}

		if (!req.accountToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header"
			);
		}

		if (!req.query.url || typeof req.query.url !== "string") {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing Product URL"
			);
		}

		let productData = { title: "", imageURL: "", price: "", currency: "", description: "" };

		try {
			const resp = await fetchProductData(req.query.url as string);
			productData = {
				title: resp.meta?.title ?? "",
				imageURL: resp.links?.thumbnail?.[0]?.href ?? "",
				price: resp.meta?.price ?? "",
				currency: resp.meta?.currency ?? "",
				description: resp.meta?.description ?? ""
			};
		} catch (error: any) {
			gpLog({
				message: "Failed to fetch product info",
				objData: {
					url: req.query.url,
					error: error.message
				},
				trace: "controllers.shoppable.fetchProduct",
				scope: LogScope.INFO
			});
		}

		return res.send(productData);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import { APIError } from "../../utils/helpers/apiError";
import { ObjectId } from "mongodb";
import {
	Request,
	Response
} from "express";
import { InteractiveVideoModel } from "./interactiveVideo.model";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { VideoModel } from "../video/video.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IPostInteractiveVideoPayload,
	IShoppableVideoProduct,
	InteractiveVideoCreateOne
} from "./interactiveVideo.interface";
import { readAccount2 } from "../../services/mongodb/account.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";

export const postVideoController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.accountToken?.account?._id) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing accountId in account token");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		await startDBTransaction(res.locals.session);

		const createVideoPayload = req.body as IPostInteractiveVideoPayload;

		if (apiVersion > 3 && (!createVideoPayload.videoId || createVideoPayload.videoURL)){
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing videoId");
		}

		if (apiVersion <= 3 && !createVideoPayload.videoURL){
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing videoURL");
		}

		const accountDocument = await readAccount2(
			{
				_id: new ObjectId(req.accountToken.account._id)
			},
			res.locals.session
		);

		if (!accountDocument) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR,
				"Failed to read the account document with " +
				`_id ${req.accountToken.account._id} supplied in access token`);
		}

		const collectionId = accountDocument.defaultCollectionId;

		const collectionModel: InteractiveCollectionModel = new InteractiveCollectionModel(res.locals.session);
		const defaultCollection = await collectionModel.readOneById(collectionId.toString());

		if (defaultCollection.accountId.toString() != req.accountToken.account._id) {
			throw new APIError(APIErrorName.E_ACCESS_FORBIDDEN, "Failed to authenticate");
		}

		const productTitles: string[] = [
			createVideoPayload.productTitle0,
			createVideoPayload.productTitle1,
			createVideoPayload.productTitle2,
			createVideoPayload.productTitle3,
			createVideoPayload.productTitle4,
			createVideoPayload.productTitle5,
			createVideoPayload.productTitle6,
			createVideoPayload.productTitle7,
			createVideoPayload.productTitle8,
			createVideoPayload.productTitle9
		];

		const productURLs: string[] = [
			createVideoPayload.productURL0,
			createVideoPayload.productURL1,
			createVideoPayload.productURL2,
			createVideoPayload.productURL3,
			createVideoPayload.productURL4,
			createVideoPayload.productURL5,
			createVideoPayload.productURL6,
			createVideoPayload.productURL7,
			createVideoPayload.productURL8,
			createVideoPayload.productURL9
		];

		const productImageURLs: string[] = [
			createVideoPayload.productImageURL0,
			createVideoPayload.productImageURL1,
			createVideoPayload.productImageURL2,
			createVideoPayload.productImageURL3,
			createVideoPayload.productImageURL4,
			createVideoPayload.productImageURL5,
			createVideoPayload.productImageURL6,
			createVideoPayload.productImageURL7,
			createVideoPayload.productImageURL8,
			createVideoPayload.productImageURL9
		];

		const productDescriptions: string[] = [
			createVideoPayload.productDescription0,
			createVideoPayload.productDescription1,
			createVideoPayload.productDescription2,
			createVideoPayload.productDescription3,
			createVideoPayload.productDescription4,
			createVideoPayload.productDescription5,
			createVideoPayload.productDescription6,
			createVideoPayload.productDescription7,
			createVideoPayload.productDescription8,
			createVideoPayload.productDescription9
		];

		const productSubTitle: string[] = [
			createVideoPayload.subTitle0,
			createVideoPayload.subTitle1,
			createVideoPayload.subTitle2,
			createVideoPayload.subTitle3,
			createVideoPayload.subTitle4,
			createVideoPayload.subTitle5,
			createVideoPayload.subTitle6,
			createVideoPayload.subTitle7,
			createVideoPayload.subTitle8,
			createVideoPayload.subTitle9
		];

		const videoProducts: IShoppableVideoProduct[] = [];

		for (let i = 0; i < 10; i++) {
			if (productTitles[i] && productURLs[i]) {
				const videoProduct = <IShoppableVideoProduct> {
					title: productTitles[i],
					url: productURLs[i],
					productThumbnail: productImageURLs[i] ? productImageURLs[i] : ""
				};
				if (productDescriptions[i]){
					videoProduct.productDescription = productDescriptions[i];
				}
				if (productSubTitle[i]){
					videoProduct.subTitle = productSubTitle[i];
				}
				videoProducts.push(videoProduct);
			}
		}

		let videoUrl;
		let videoWidth;
		let videoHeight;
		let videoId;

		if (apiVersion > 3 && createVideoPayload.videoId){
			const videoModel = new VideoModel(res.locals.session);
			const video = await videoModel.readOneById(createVideoPayload.videoId);
			videoUrl = video.publicVideoURL;
			videoWidth = video.videoWidthPx;
			videoHeight = video.videoHeightPx;
			videoId = video._id;
		} else {
			videoUrl = createVideoPayload.videoURL;
			videoWidth = undefined;
			videoHeight = undefined;
			videoId = undefined;
		}

		const interactiveVideo: InteractiveVideoCreateOne = {
			title: createVideoPayload.title,
			description: createVideoPayload.description,
			videoURL: videoUrl,
			videoPosterURL: createVideoPayload.videoPosterImageURL,
			videoPosterPlayEmbedURL: createVideoPayload.videoPosterPlayEmbedURL,
			gifURL: createVideoPayload.gifURL,
			products: videoProducts,
			ctaText: createVideoPayload.ctaText,
			showTitle: createVideoPayload.showTitle === "true",
			videoDisplayMode: createVideoPayload.videoDisplayMode,
			videoWidthPx: videoWidth,
			videoHeightPx: videoHeight,
			videoId: videoId,
			phone: createVideoPayload.phone,
			email: createVideoPayload.email,
			captionData: (createVideoPayload.captionData == undefined)
				? undefined : JSON.parse(createVideoPayload.captionData)
		};

		const interactiveVideoModel: InteractiveVideoModel = new InteractiveVideoModel(res.locals.session);
		const videoDocument = await interactiveVideoModel.createOne(interactiveVideo, accountDocument);

		defaultCollection.shoppableVideos.unshift(videoDocument._id);

		await collectionModel.updateOne(defaultCollection);

		const result: any = {
			shoppableVideo: videoDocument
		};

		await completeDBTransaction(res.locals.session);
		return res.status(201).json(result);
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import mongoose, { Schema } from "mongoose";
import { IMetricVideoEmbedCreated } from "./metricVideoEmbed.interface";

const MetricVideoEmbedCreatedSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	videoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricVideoEmbedCreatedDBModel = mongoose.model<IMetricVideoEmbedCreated>(
	"video_embed_copy_thumbnail_metric",
	MetricVideoEmbedCreatedSchema,
	"video_embed_copy_thumbnail_metric"
);

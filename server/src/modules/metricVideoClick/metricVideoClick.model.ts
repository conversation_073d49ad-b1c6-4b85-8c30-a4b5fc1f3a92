import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricVideoClickDBModel } from "./metricVideoClickDB.model";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import {
	MetricVideoClickCreateOne,
	IMetricVideoClicks
} from "./metricVideoClick.interfaces";

export class MetricVideoClickModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IMetricVideoClicks>): Promise<number> {
		const count: number = await MetricVideoClickDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async isClickMetricLimitReached (account: IAccount): Promise<boolean> {
		const startOfMonth = new Date();
		startOfMonth.setDate(1);
		startOfMonth.setHours(0, 0, 0, 0);

		const startOfMonthISOString = startOfMonth.toISOString();

		const now = new Date();
		now.setHours(23, 59, 59, 999);

		const nowISOString = now.toISOString();

		const filter: FilterQuery<IMetricVideoClicks> = {
			accountId: account._id,
			createdAt: {
				$gte: startOfMonthISOString,
				$lte: nowISOString
			}
		};

		const documentCount = await this.countDocuments(filter);
		const limit = account.subscription.maxClicksMetricPerMonth;

		return documentCount >= limit;
	}

	async createOne (
		createData: MetricVideoClickCreateOne,
		account: IAccount
	): Promise<ModelResponse<IMetricVideoClicks>> {
		try {
			if (await this.isClickMetricLimitReached(account)) {
				return new ModelResponse<IMetricVideoClicks>(
					null,
					new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Metric video click limit reached")
				);
			}

			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new MetricVideoClickDBModel({
				createdAt: createData.createdAt,
				productId: createData.productId,
				videoId: createData.videoId,
				accountId: account._id
			}).save(options);

			return new ModelResponse<IMetricVideoClicks>(newDocument, null);
		} catch (error: unknown) {
			return new ModelResponse<IMetricVideoClicks>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message)
			);
		}
	}


	async readTotalPrevDayByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, number>>> {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfPreviousDay = new Date(startOfToday.getTime() - (24 * 60 * 60 * 1000));
		const endOfPreviousDay = new Date(startOfToday.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousDay, $lte: endOfPreviousDay }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalVideoCTAClicksDaily: { $sum: 1 }
				}
			}
		];

		const results = await MetricVideoClickDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, number>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_video_clicks found for the previous day")
			);
		}

		const totalVideoCTAClicksByAccountId: Map<string, number> = new Map(
			results.map(item => [item._id.toString(), item.totalVideoCTAClicksDaily])
		);

		return new ModelResponse<Map<string, number>>(totalVideoCTAClicksByAccountId, null);
	}



}

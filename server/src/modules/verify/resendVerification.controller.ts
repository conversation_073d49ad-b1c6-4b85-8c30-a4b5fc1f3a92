import {
	Request,
	Response
} from "express";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import {
	EmailInput,
	sendTransactionalEmail
} from "../../services/email/email.service";
import {
	APIErrorName,
	AdminLinkType
} from "../../interfaces/apiTypes";
import { IResendVerificationPayload } from "./verify.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { buildAdminLink } from "../../utils/helpers/gp.helper";
import { signJwt } from "../../utils/helpers/gpJwt.helper";
import { AuthenticationModel } from "../authentication/authentication.model";

export const resendVerificationController = async (
	req: Request,
	res: Response
): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const payload = req.body as IResendVerificationPayload;
		const secrets: ISecrets = await getSecrets();
		const email = payload.email.toLowerCase().trim();

		const authModel = new AuthenticationModel(null);
		const authDoc = await authModel.readOneByEmail(email);

		if (authDoc.verified) {
			throw new APIError(
				APIErrorName.E_ALREADY_VERIFIED,
				"Account is already verified"
			).setDetail({ authenticationId: authDoc._id });
		}

		const gpSecretKey = secrets.hashkey.key;

		const jwtValue = signJwt(
			{ authenticationId: authDoc._id },
			`${gpSecretKey}${authDoc.salt}`
		);

		const baseURLCallback = payload.callbackEndpoint;
		const link = buildAdminLink({
			linkType: AdminLinkType.VERIFY,
			baseUrl: baseURLCallback,
			token: jwtValue
		});

		// eslint-disable-next-line camelcase
		const emailInput: EmailInput = {
			template: "verify-email",
			to: payload.email,
			subject: "Verify Your Account",
			data: { link },
			locale: payload.locale
		};

		const sent = await sendTransactionalEmail(emailInput);

		if (!sent) {
			throw new APIError(APIErrorName.E_EMAIL_DELIVERY, "Failed to send email");
		}

		return res.status(200).send({});
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import {
	initExpressRoutes,
	initExpressServer
} from "./express";
import dotenv from "dotenv";
import {
	formatMongoURIFromSecrets,
	initMongoDB
} from "./mongo";
import {
	gpLog,
	LogScope
} from "./utils/managers/gpLog.manager";
import {
	getSecrets,
	ISecrets
} from "./modules/secrets/secrets.model";
import i18n from "i18n";
import { JobModel } from "./modules/job/job.model";
import { APIError } from "./utils/helpers/apiError";
import { WorkersModel } from "./modules/workers/workers.model";
import path from "path";

const initRuntimes = async (secrets: ISecrets): Promise<void> => {
	if (process.env.jobId) {
		const jobModel = new JobModel(null);
		await jobModel.runJobById(process.env.jobId);
		return process.exit(0);
	}

	if (process.env.ENABLE_WORKERS_RUNTIME === "true") {
		const workerFiles = [
			path.resolve(__dirname, "./modules/eventProcessor/eventProcessor.worker.js")
		];
		new WorkersModel(secrets, workerFiles);
	}

	if (process.env.ENABLE_API_RUNTIME !== "false") {
		const expressPort = Number(process.env.PORT || 3000);
		const expressServer = await initExpressServer(expressPort);

		i18n.configure({
			locales: ["en", "fr"],
			directory: __dirname + "/locales"
		});

		expressServer.use(i18n.init);
		initExpressRoutes(expressServer);
	}
};

export const main = async (): Promise<void> => {
	try {
		gpLog({
			message: "Starting main thread. Loading environment configuration...",
			scope: LogScope.INFO
		});
		dotenv.config();

		const secrets: ISecrets = await getSecrets();
		if (!secrets) {
			throw new Error("Unable to read API secrets. Exit now!");
		}

		await initMongoDB(formatMongoURIFromSecrets(secrets));
		await initRuntimes(secrets);

	} catch (error: unknown) {
		APIError.fromUnknownError(error).setDetail({
			message: "Error trapped in main thread",
			jobId: process.env.jobId || "N/A"
		}).log();
		process.exit(1);
	}
};

main();

import {
	Request,
	Response,
	NextFunction
} from "express";
import { APIErrorName } from "../interfaces/apiTypes";
import { APIError } from "../utils/helpers/apiError";

export const validateHeaders = async (
	req: Request,
	res: Response,
	next: NextFunction
): Promise<void | Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version"
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version"
			);
		}

		if (!req.accessToken?.userId) {
			throw new APIError(
				APIErrorName.E_INVALID_AUTHORIZATION,
				"Missing userId in access token"
			);
		}

		next();
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

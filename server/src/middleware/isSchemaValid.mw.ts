import Jo<PERSON> from "joi";
import {
	NextFunction,
	Request,
	Response
} from "express";
import { APIError } from "../utils/helpers/apiError";
import { APIErrorName } from "../interfaces/apiTypes";

export const isSchemaValid = (schema: Joi.Schema) => {
	return async (
		req: Request,
		res: Response,
		next: NextFunction
	): Promise<void | Response> => {
		try {
			const validationTarget =
			Object.keys(req.body).length > 0 ? req.body : req.query;
			await schema.validateAsync(validationTarget);
			next();
		} catch (error: any) {
			if (error.message === APIErrorName.E_INVALID_INPUT) {
				return new APIError(
					APIErrorName.E_SCHEMA_VALIDATION_ERROR,
					"Schema validation error")
					.log().setResponse(res);
			}

			if (Object.values(APIErrorName).includes(error.message)) {
				const returnError = new APIError(error.message, "Failed to validate Input.");
				return returnError.log().setResponse(res);
			}
			const returnError = new APIError(APIErrorName.E_INVALID_INPUT, error.message);
			return returnError.setResponse(res);
		}
	};
};

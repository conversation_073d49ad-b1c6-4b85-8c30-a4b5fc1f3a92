import Joi from "joi";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	SignInMethod,
	ISignInPayload
} from "../../modules/signin/signin.interfaces";

const emailSchema = Joi.string()
	.lowercase()
	.email({
		tlds: { allow: false }
	})
	.trim();

export const signInSchema = {
	data: Joi.object<ISignInPayload>({
		method: Joi.string()
			.valid(...Object.values(SignInMethod))
			.required(),
		email: Joi.when("method", {
			is: SignInMethod.EMAIL_PASSWORD,
			then: emailSchema.required(),
			otherwise: Joi.disallow()
		}),
		password: Joi.when("method", {
			is: SignInMethod.EMAIL_PASSWORD,
			then: Joi.string().required(),
			otherwise: Joi.disallow()
		}),
		apikey: Joi.when("method", {
			is: SignInMethod.APIKEY,
			then: Joi.string().required(),
			otherwise: Joi.disallow()
		}),
		token: Joi.when("method", {
			is: SignInMethod.REFRESH_TOKEN,
			then: Joi.string().required(),
			otherwise: Joi.disallow()
		}),
		authlink: Joi.when("method", {
			is: SignInMethod.AUTHLINK,
			then: Joi.string().required(),
			otherwise: Joi.disallow()
		})
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

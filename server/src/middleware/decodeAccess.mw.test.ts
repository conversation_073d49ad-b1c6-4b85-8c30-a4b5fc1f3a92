import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { decodeAccess } from "./decodeAccess.mw";
import express, {
	Request,
	Response
} from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import TestHelper from "../__tests__/mocks/testHelper";
import * as secretsModel from "../modules/secrets/secrets.model";
import { ISecrets } from "../modules/secrets/secrets.model";

// eslint-disable-next-line max-lines-per-function
describe("decodeAccess.mw.ts", () => {
	let signupResult: any;
	let expressApp: express.Express;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const newUserPayload = {
			firstName: "Decode",
			lastName: "Access",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "decode.access",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		signupResult = await testHelper.signup(newUserPayload);

		expect(signupResult).toBeDefined();
		expect(signupResult).toHaveProperty("accessToken");
	});

	it("should fail to decode a missing Authorization header", async () => {
		const req = {
			header: (name: string) => {
				if (name.toLowerCase() === "authorization") {
					return undefined;
				}

				return undefined;
			}
		} as Request;

		const res = {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			status: (code: number) => {
				return {
					json: (data: any): any => {
						return data;
					}
				};
			}
		} as Response;

		const next = jest.fn();

		const response = await decodeAccess(req, res, next);

		expect(response).toBeDefined();

		const result = response as any;

		expect(result).toBeDefined();
		expect(result).toBeInstanceOf(Object);
		expect(result).toHaveProperty("error");
		expect(result.error).toBe("E_MISSING_AUTHORIZATION");
		expect(result).toHaveProperty("errorRef");
		expect(result.errorRef).toHaveLength(13);
		expect(result).toHaveProperty("message");
		expect(result.message).toBe("missing bearer authorization header");
	});

	it("should fail to decode an empty bearer token", async () => {
		const req = {
			header: (name: string) => {
				if (name.toLowerCase() === "authorization") {
					return "Bearer";
				}

				return undefined;
			}
		} as Request;

		const res = {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			status: (code: number) => {
				return {
					json: (data: any): any => {
						return data;
					}
				};
			}
		} as Response;

		const next = jest.fn();

		const response = await decodeAccess(req, res, next);

		expect(response).toBeDefined();

		const result = response as any;

		expect(result).toBeDefined();
		expect(result).toBeInstanceOf(Object);
		expect(result).toHaveProperty("error");
		expect(result.error).toBe("E_INVALID_AUTHORIZATION");
		expect(result).toHaveProperty("errorRef");
		expect(result.errorRef).toHaveLength(13);
		expect(result).toHaveProperty("message");
		expect(result.message).toBe("invalid bearer authorization header");
	});

	it("should fail to decode an invalid bearer token", async () => {
		const req = {
			header: (name: string) => {
				if (name.toLowerCase() === "authorization") {
					return "Bearer invalid";
				}

				return undefined;
			}
		} as Request;

		const res = {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			status: (code: number) => {
				return {
					json: (data: any): any => {
						return data;
					}
				};
			}
		} as Response;

		const next = jest.fn();

		const response = await decodeAccess(req, res, next);

		expect(response).toBeDefined();

		const result = response as any;

		expect(result).toBeDefined();
		expect(result).toBeInstanceOf(Object);
		expect(result).toHaveProperty("error");
		expect(result.error).toBe("E_INVALID_AUTHORIZATION");
		expect(result).toHaveProperty("errorRef");
		expect(result.errorRef).toHaveLength(13);
		expect(result).toHaveProperty("message");
		expect(result.message).toBe("invalid bearer authorization header");
	});

	it("should fail for a missing hashKey.key", async () => {
		const secretsMock = jest.spyOn(secretsModel, "getSecrets").mockImplementationOnce(
			async (): Promise<ISecrets> => {
				return <ISecrets> {
				};
			}
		);

		const req = {
			header: (name: string) => {
				if (name.toLowerCase() === "authorization") {
					return `Bearer ${signupResult.accessToken}`;
				}

				return undefined;
			}
		} as Request;

		const res = {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			status: (code: number) => {
				return {
					json: (data: any): any => {
						return data;
					}
				};
			}
		} as Response;

		const next = jest.fn();

		const response = await decodeAccess(req, res, next);

		secretsMock.mockRestore();

		expect(response).toBeDefined();

		const result = response as any;

		expect(result).toBeDefined();
		expect(result).toBeInstanceOf(Object);
		expect(result).toHaveProperty("error");
		expect(result.error).toBe("E_SERVICE_FAILED");
		expect(result).toHaveProperty("errorRef");
		expect(result.errorRef).toHaveLength(13);
		expect(result).toHaveProperty("message");
		expect(result.message).toBe("failed to read the hash key");
	});

	it("should fail for an invalid token signature", async () => {
		const secretsMock = jest.spyOn(secretsModel, "getSecrets").mockImplementationOnce(
			async (): Promise<ISecrets> => {
				return <ISecrets> {
					hashkey: {
						key: "test"
					}
				};
			}
		);

		const req = {
			header: (name: string) => {
				if (name.toLowerCase() === "authorization") {
					return `Bearer ${signupResult.accessToken}`;
				}

				return undefined;
			}
		} as Request;

		const res = {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			status: (code: number) => {
				return {
					json: (data: any): any => {
						return data;
					}
				};
			}
		} as Response;

		const next = jest.fn();

		const response = await decodeAccess(req, res, next);

		secretsMock.mockRestore();

		expect(response).toBeDefined();

		const result = response as any;

		expect(result).toBeDefined();
		expect(result).toBeInstanceOf(Object);
		expect(result).toHaveProperty("error");
		expect(result.error).toBe("E_INVALID_AUTHORIZATION");
		expect(result).toHaveProperty("errorRef");
		expect(result.errorRef).toHaveLength(13);
		expect(result).toHaveProperty("message");
		expect(result.message).toBe("invalid signature");
	});
});

import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { ISignupPayload } from "src/modules/signup/signup.interfaces";

describe("POST /auth/verify", () => {
	let expressApp: express.Express;
	let accountToken: string;
	let accessToken: string;
	let accountId: string;

	const newUserPayload = {
		firstName: "Johnny",
		lastName: "Verify",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "verify.positive.mode.test Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(newUserPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();

		accountToken = await testHelper.getAccountToken(accountId, accessToken);
	});


	it("Verify and Verify again", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token: accountToken
			});

		expect(res.statusCode).toBe(200);

		const authenticationModel = new AuthenticationModel(null);
		const authentication = await authenticationModel.readOneByEmail(newUserPayload.email);
		expect(authentication.verified).toBe(true);

		// if verify is called again it must pass
		const resAgain = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token: accountToken
			});

		expect(resAgain.statusCode).toBe(200);
		const authAgain = await authenticationModel.readOneByEmail(newUserPayload.email);
		expect(authAgain.verified).toBe(true);
	});
});

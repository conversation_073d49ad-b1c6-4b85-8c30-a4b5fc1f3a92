import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";


describe("POST /auth/sign-in/email positive mode test", () => {
	let accessToken: string;
	let accountToken: string;
	let expressApp: express.Express;
	let accountId: string;

	const createUserPayload = {
		firstName: "John",
		lastName: "Signin Email",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signin.email.positive.test Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;


	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		({ accessToken, accountToken } = await testHelper.createUserAndAccount(createUserPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();

		accountToken = await testHelper.getAccountToken(accountId, accessToken);

		await testHelper.verify(accountToken);
	});

	it("Email sign in successful. Should return 200.", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				locale: "en_US",
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(200);
	});

	it("Email sign in successful without optional locale. Should return 200.", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(200);
	});
});

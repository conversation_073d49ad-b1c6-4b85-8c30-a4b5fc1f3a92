import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { APIError } from "../../utils/helpers/apiError";
import { IAccount } from "../../modules/account/account.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import TestHelper from "../mocks/testHelper";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { AccountModel } from "../../modules/account/account.model";
import * as MetricConversionService from "../../services/mongodb/metricConversion.service";


describe("GET /metrics/conversion", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;

	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "<PERSON>",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.metric.conversion.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	const queryParam = "startDate=2023-08-20T16:40:40.529Z&endDate=2023-08-30T16:45:40.529Z";

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);

		const accountModel = new AccountModel(null);
		const updateAccount = {
			"subscription.enableConversionMetrics": true
		};
		const updatedAccount = await accountModel.updateOneById(account._id.toString(), updateAccount);
		expect(updatedAccount.subscription.enableConversionMetrics).toBe(true);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version.", async () => {
		const res = await supertest(expressApp)
			.get(`/api/metrics/conversion?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] when missing account token", async () => {
		const res = await supertest(expressApp)
			.get(`/api/metrics/conversion?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 400 [E_SCHEMA_VALIDATION_ERROR] invalid query is passed.", async () => {
		const badQuery = "start=some date";

		const res = await supertest(expressApp)
			.get(`/api/metrics/conversion?${badQuery}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("Should return 500 [E_SERVICE_FAILED] failed to run aggregation in DB.", async () => {
		jest.spyOn(MetricConversionService, "aggregateMetricConversion").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "")
		);

		const res = await supertest(expressApp)
			.get(`/api/metrics/conversion?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 200 [OK].", async () => {
		jest.spyOn(MetricConversionService, "aggregateMetricConversion").mockResolvedValueOnce(
			[{
				totalOrders: 10,
				totalItemsSold: 20
			}]
		);

		jest.spyOn(MetricConversionService, "aggregateMetricConversion").mockResolvedValueOnce(
			[{
				uniqueSessions: 10
			}]
		);

		jest.spyOn(MetricConversionService, "aggregateMetricConversion").mockResolvedValueOnce(
			[{
				totalOrders: 10,
				totalItemsSold: 20
			}]
		);

		jest.spyOn(MetricConversionService, "aggregateMetricConversion").mockResolvedValueOnce(
			[{
				uniqueSessions: 10
			}]
		);

		const res = await supertest(expressApp)
			.get(`/api/metrics/conversion?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("previousMetricConversions");
		expect(res.body).toHaveProperty("currentMetricConversions");
	});
});

import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";


const createUserPayload = {
	firstName: "John",
	lastName: "get.users.failure.cases",
	email: "<EMAIL>",
	password: "Password1!",
	companyName: "get.users.failure.cases Co.",
	locale: LocaleAPI.EN_US,
	callbackEndpoint: "https://domain.tld/callback",
	legalAgreement: true
} as ISignupPayload;

describe("GET /users failure mode tests", () => {
	let accessToken: string;
	let accountToken: string;
	let expressApp: express.Express;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		({ accessToken, accountToken } = await testHelper.createUserAndAccount(createUserPayload));
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] if the request doesn't have an account token", async () => {
		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});
});

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { InteractiveCollectionModel } from "../modules/interactiveCollection/interactiveCollection.model";
import { IAccount } from "../modules/account/account.interfaces";
import {
	IPostCollectionPayload,
	IShoppableCollection
} from "../modules/interactiveCollection/interactiveCollection.interface";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { InteractiveVideoModel } from "../modules/interactiveVideo/interactiveVideo.model";
import { VideoDisplayModeEnum } from "../modules/interactiveVideo/interactiveVideo.interface";
import { AccountModel } from "../modules/account/account.model";
import { Subscription } from "../modules/subscription/subscription.interfaces";


describe("POST /shoppable-collections", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "create.shoppable.collection.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_MISSING_AUTHORIZATION]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.send({ title: "Test Collection" });

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for empty input", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for empty title input", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({ title: "" });
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[Successfully created shoppable collection]. return 200 > no videos", async () => {
		const payload: IPostCollectionPayload = {
			title: "Test Collection - empty",
			shoppableVideos: []
		};

		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		const interactiveCollectionId = res.body.shoppableCollection;
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const interactiveCollection: IShoppableCollection =
			await interactiveCollectionModel.readOneById(interactiveCollectionId);

		expect(interactiveCollection.title).toBe(payload.title);
		expect(interactiveCollection.shoppableVideos.length).toBe(0);
	});

	it("[Successfully created shoppable collection]. return 200 > couple videos", async () => {
		const payloadVideo1 = {
			title: "video1",
			description: "description1",
			videoURL: "someURL1",
			videoPosterURL: "posterURL1",
			gifURL: "http://example.tld/example1.gif",
			phone: "+***********",
			email: "<EMAIL>",
			videoPosterPlayEmbedURL: "http://example.tld/example-play1.jpg",
			ctaText: "ctaText1",
			showTitle: true,
			videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
			products: []
		};

		const payloadVideo2 = {
			title: "video2",
			description: "description2",
			videoURL: "somweURL2",
			videoPosterURL: "posterURL2",
			gifURL: "http://example.tld/example2.gif",
			videoPosterPlayEmbedURL: "http://example.tld/example-play2.jpg",
			phone: "+***********",
			email: "<EMAIL>",
			ctaText: "ctaText2",
			showTitle: false,
			videoDisplayMode: VideoDisplayModeEnum.LANDSCAPE,
			products: []
		};

		const interactiveVideoModel: InteractiveVideoModel = new InteractiveVideoModel(null);
		const videoDocument1 = await interactiveVideoModel.createOne(payloadVideo1, account);
		const videoDocument2 = await interactiveVideoModel.createOne(payloadVideo2, account);

		const payloadCollection: IPostCollectionPayload = {
			title: "Test Collection - couple videos",
			shoppableVideos: [
				videoDocument1._id.toString(),
				videoDocument2._id.toString()
			]
		};

		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payloadCollection);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		const interactiveCollectionId = res.body.shoppableCollection;
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const interactiveCollection: IShoppableCollection =
			await interactiveCollectionModel.readOneById(interactiveCollectionId);

		expect(interactiveCollection.title).toBe(payloadCollection.title);
		expect(interactiveCollection.shoppableVideos.length).toBe(2);

		const video1 = await interactiveVideoModel.readOneById(interactiveCollection.shoppableVideos[0].toString());
		const video2 = await interactiveVideoModel.readOneById(interactiveCollection.shoppableVideos[1].toString());

		const videos = [video1, video2];
		expect(videos.filter(v => v.title == payloadVideo1.title).length).toBe(1);
		expect(videos.filter(v => v.title == payloadVideo2.title).length).toBe(1);
	});

	it("[Successfully created shoppable collection]. return 200 > full IPostCollectionPayload", async () => {
		const payload: IPostCollectionPayload = {
			title: "Test Collection - couple videos",
			shoppableVideos: [],
			buttonBackgroundColor: "some colour",
			buttonBackgroundBlur: false,
			iconTextColor: "another colour",
			displayFont: "some font",
			carouselBorderRadius: 10,
			carouselIsCentered: true,
			carouselMargin: 4,
			carouselGap: 3,
			widgetBorderRadius: 5,
			widgetPosition: "left",
			inlineBorderRadius: 6
		};

		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		const interactiveCollectionId = res.body.shoppableCollection;
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const interactiveCollection: IShoppableCollection =
			await interactiveCollectionModel.readOneById(interactiveCollectionId);

		expect(interactiveCollection.title).toBe(payload.title);
		expect(interactiveCollection.buttonBackgroundColor).toBe(payload.buttonBackgroundColor);
		expect(interactiveCollection.iconTextColor).toBe(payload.iconTextColor);
		expect(interactiveCollection.displayFont).toBe(payload.displayFont);
		expect(interactiveCollection.carouselBorderRadius).toBe(payload.carouselBorderRadius);
		expect(interactiveCollection.carouselIsCentered).toBe(payload.carouselIsCentered);
		expect(interactiveCollection.carouselMargin).toBe(payload.carouselMargin);
		expect(interactiveCollection.carouselGap).toBe(payload.carouselGap);
		expect(interactiveCollection.widgetBorderRadius).toBe(payload.widgetBorderRadius);
		expect(interactiveCollection.widgetPosition).toBe(payload.widgetPosition);
		expect(interactiveCollection.inlineBorderRadius).toBe(payload.inlineBorderRadius);
	});

	it("[E_REQUEST_FORBIDDEN]. return 403 > isInteractiveCollectionLimitReached", async () => {
		const payload: IPostCollectionPayload = {
			title: "Test Collection - isInteractiveCollectionLimitReached",
			shoppableVideos: []
		};

		const accountModel = new AccountModel(null);
		const update: Partial<IAccount> = {
			subscription: {
				maxInteractiveCollectionLimit: 0
			} as Subscription
		};
		const accountUpdated = await accountModel.updateOneById(account._id.toString(), update);
		expect(accountUpdated.subscription.maxInteractiveCollectionLimit).toBe(0);

		const res = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toBe(APIErrorName.E_REQUEST_FORBIDDEN);

		const reset: Partial<IAccount> = {
			subscription: {
				maxInteractiveCollectionLimit: 500
			} as Subscription
		};
		const accountReset = await accountModel.updateOneById(account._id.toString(), reset);
		expect(accountReset.subscription.maxInteractiveCollectionLimit).toBe(500);
	});
});

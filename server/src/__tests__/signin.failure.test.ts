import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { SignInMethod } from "../modules/signin/signin.interfaces";
import {
	ISignupEmailPayload,
	ISignupPayload
} from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";


describe("POST /auth/sign-in failure mode tests", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let otherUserAccessToken: string;
	let otherAccountToken: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signin.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		await testHelper.signup(createUserPayload);

		const otherUserSignupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		({ accessToken: otherUserAccessToken } = await testHelper.signupEmail(otherUserSignupEmailPayload));
		const otherAccount = await testHelper.getAccount(otherUserAccessToken);
		otherAccountToken = await testHelper.getAccountToken(otherAccount._id.toString(), otherUserAccessToken);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 when no parameters have been passed", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "x")
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
		expect(res.body.message).toEqual("Schema validation error");
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				email: createUserPayload.email,
				password: createUserPayload.password
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
		expect(res.body.message).toEqual("Missing API version");
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				email: createUserPayload.email,
				password: createUserPayload.password
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "0")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				email: createUserPayload.email,
				password: createUserPayload.password
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});


	it("[E_SCHEMA_VALIDATION_ERROR]. return 400 when missing email password for email/password method", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				token: "4354353"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for invalid input for refresh token method", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.REFRESH_TOKEN,
				email: "test@localhost",
				password: "password"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SIGN_IN_INCORRECT]. Should return 400 for email/password invalid login credentials", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				email: createUserPayload.email,
				password: "invalidpassword"
			});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SIGN_IN_INCORRECT);
	});

	it("Apikey sign in fails with no api key. Should return 400.", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.APIKEY
			});
		expect(res.statusCode).toBe(400);
	});

	it("Apikey sign in fails with wrong api key. Should return 401.", async () => {
		const resCreateKey = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${otherUserAccessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", otherAccountToken);

		expect(resCreateKey.statusCode).toBe(200);

		const apikey = resCreateKey.body.apikey;

		const authenticationModel = new AuthenticationModel(null);
		const authenticationEmailPassword = await authenticationModel.readOneByEmail(createUserPayload.email);

		jest.spyOn(AuthenticationModel.prototype, "readOneById").mockResolvedValueOnce(
			authenticationEmailPassword
		);

		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "2")
			.send({
				method: SignInMethod.APIKEY,
				apikey: apikey
			});

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_NOT_AUTHENTICATED);
	});
});

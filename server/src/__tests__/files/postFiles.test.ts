import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import * as BucketService from "../../services/gp/bucket.service";
import TestHelper from "../mocks/testHelper";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";

describe("POST /files", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "post.files.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_INTERNAL_ERROR]. Should return 500", async () => {
		jest.spyOn(BucketService, "readBucketSignedUrl").mockResolvedValueOnce(undefined);

		const res = await supertest(expressApp)
			.post("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("content-type", "video/mp4")
			.set("x-filename", "testVideo.mp4")
			.send();

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("[E_INVALID_INPUT]. Should return 400 - missing x-Filename", async () => {
		const res = await supertest(expressApp)
			.post("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("content-type", "video/mp4")
			.set("x-api-version", "1")
			.send();

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[200]. Should return 200", async () => {
		jest.spyOn(BucketService, "readBucketSignedUrl").mockResolvedValueOnce(
			"media/63f52a208d16beab51100c15/testVideo_64203ec04ffd3cccaab09452.mp4"
		);

		const res = await supertest(expressApp)
			.post("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("content-type", "video/mp4")
			.set("x-filename", "testVideo.mp4")
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("location");
	});
});

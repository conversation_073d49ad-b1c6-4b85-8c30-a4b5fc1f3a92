import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";

import * as BucketService from "../../services/gp/bucket.service";


describe("DELETE /files/:filename", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.file.s.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version.", async () => {
		const res = await supertest(expressApp)
			.delete("/api/files/filename.mp4")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] when missing account token", async () => {
		const res = await supertest(expressApp)
			.delete("/api/files/filename.mp4")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 200 [OK] when delete Assets From Cloud fails?", async () => {
		jest.spyOn(BucketService, "deleteAssetsFromCloud").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.delete("/api/files/filename.mp4")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});
	});

	it("Should return 200 [OK] when delete Assets From Cloud succeeds", async () => {
		const res = await supertest(expressApp)
			.delete("/api/files/filename.mp4")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});
	});
});

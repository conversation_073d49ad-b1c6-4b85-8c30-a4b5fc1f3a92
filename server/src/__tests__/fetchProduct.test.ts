import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { IAccount } from "../modules/account/account.interfaces";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import * as IframelyService from "../services/product/iframely.service";

describe("GET /shoppable-videos/fetch-product", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "shoppable.videos.fetch.product.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[400. return 400 if no url passed]", async () => {
		const res = await supertest(expressApp)
			.get("/api/shoppable-videos/fetch-product")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("When fetch to iframely fails - log error and return 200", async () => {
		jest.spyOn(IframelyService, "fetchProductData").mockRejectedValueOnce(
			new Error("HTTP error!")
		);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos/fetch-product?url=https://www.site.com/product")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual(
			{
				title: "",
				imageURL: "",
				price: "",
				currency: "",
				description: ""
			}
		);
	});


	it("[200. Should return 200]", async () => {
		const fetchProductDataResp = {
			meta: {
				title: "Product title",
				price: "100",
				currency: "USD",
				description: "Product description"
			},
			links: {
				thumbnail: [
					{
						href: "https://www.site.com/product/image.jpg"
					}
				]
			}
		};

		const expectedProductData = {
			title: fetchProductDataResp.meta.title,
			imageURL: fetchProductDataResp.links.thumbnail[0].href,
			price: fetchProductDataResp.meta.price,
			currency: fetchProductDataResp.meta.currency,
			description: fetchProductDataResp.meta.description
		};

		jest.spyOn(IframelyService, "fetchProductData").mockResolvedValueOnce(
			fetchProductDataResp
		);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos/fetch-product?url=https://www.site.com/product")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual(expectedProductData);
	});
});

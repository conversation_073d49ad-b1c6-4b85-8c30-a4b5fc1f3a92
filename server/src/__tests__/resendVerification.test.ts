import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import * as EmailService from "../services/email/email.service";


describe("POST /auth/resend-verification", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "resend.verification.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		await testHelper.signup(createUserPayload);
	});

	it("E_DOCUMENT_NOT_FOUND. Should return 404", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/resend-verification")
			.set("x-api-version", "1")
			.send({
				email: "<EMAIL>",
				callbackEndpoint: "https://domain.tld/callback"
			});
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_EMAIL_DELIVERY]. Should return 500", async () => {
		jest.spyOn(EmailService, "sendTransactionalEmail").mockResolvedValueOnce(undefined);

		const res = await supertest(expressApp)
			.post("/api/auth/resend-verification")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "https://domain.tld/callback"
			});
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_EMAIL_DELIVERY);
	});

	it("[Successfully sent verification email]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/resend-verification")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "https://domain.tld/callback"
			});
		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});
	});
});

import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AccountModel } from "../modules/account/account.model";
import { UserModel } from "../modules/user/user.model";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import {
	IAuthentication,
	UserAuthenticationData
} from "../modules/authentication/authentication.interface";
import { AuthMethods } from "../modules/authentication/authentication.enums";
import { Subscription } from "../modules/subscription/subscription.interfaces";

describe("POST /auth/sign-up/email positive mode tests", () => {
	let expressApp: express.Express;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("return 200", async () => {
		const email = "<EMAIL>";

		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: email,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");

		const userModel = new UserModel(null);
		const user = await userModel.readOneByEmail(email);
		expect(user.isPasswordSet).toBe(false);

		const accountModel = new AccountModel(null);
		const account = await accountModel.readOneByOwnerId(user._id.toString());

		const authModel = new AuthenticationModel(null);
		const authDoc: IAuthentication = await authModel.readOneByEmail(email);
		expect(authDoc).toBeTruthy();
		expect(authDoc.verified).toBe(false);
		expect(authDoc.method).toBe(AuthMethods.EMAIL_PASSWORD);

		expect(authDoc.accounts).toBeTruthy();
		expect(authDoc.accounts.length).toBe(1);
		const accountId = authDoc.accounts[0]._id.toString();
		expect(accountId).toBe(account._id.toString());
		expect(account.stripeCustomerId).toBeTruthy();
		expect(account.defaultCollectionId).toBeTruthy();

		const subscription: Subscription = account.subscription;
		expect(subscription.stripePriceId).toBeTruthy();
		expect(subscription.stripeProductId).toBeTruthy();
		expect(subscription.stripeSubscriptionId).toBeTruthy();
		expect(subscription.stripeSubscriptionId).toBeTruthy();
		expect(subscription.price).toBe(0);

		expect(authDoc.data).toBeTruthy();
		const authDocData = authDoc.data as UserAuthenticationData;
		expect(authDocData.email).toBe(email);
		expect(authDocData.passwordHash).toBeTruthy();
	});
});

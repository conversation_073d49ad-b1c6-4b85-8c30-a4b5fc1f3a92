import path from "path";
import fs from "fs";

// eslint-disable-next-line no-undef, @typescript-eslint/no-unused-vars
global.fetch = jest.fn().mockImplementation(async (input: RequestInfo | URL, init?: RequestInit) => {
	let url = null;
	if (typeof input === "string") {
		url = new URL(input);
	} else if (input instanceof URL) {
		url = input;
	}

	if (!url) {
		throw new Error("Invalid URL in fetch mock");
	}

	const relativePath = path.join(
		process.cwd(),
		url.pathname.startsWith("/") ? url.pathname.substring(1) : url.pathname
	);

	let fileContent: Buffer;
	try {
		fileContent = fs.readFileSync(relativePath);
	} catch (err) {
		return Promise.resolve({
			ok: false,
			status: 404,
			statusText: "Not Found",
			body: null
		});
	}

	// eslint-disable-next-line no-undef
	const stream = new ReadableStream({
		start(controller): void {
			controller.enqueue(fileContent);
			controller.close();
		}
	});

	return Promise.resolve({
		headers: {
			get: (name: string) => {
				if (name === "content-type") {
					const ext = path.extname(relativePath).toLowerCase();
					if (ext === ".jpg" || ext === ".jpeg") return "image/jpeg";
					if (ext === ".png") return "image/png";
					if (ext === ".txt") return "text/plain";
					return "application/octet-stream";
				}
				return null;
			}
		},
		ok: true,
		status: 200,
		statusText: "OK",
		body: stream,
		arrayBuffer: async () => fileContent.buffer
	});
});

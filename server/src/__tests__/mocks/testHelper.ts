import express from "express";
import mongoose from "mongoose";
import supertest from "supertest";
import { IAccount } from "../../modules/account/account.interfaces";
import { AuthenticationModel } from "../../modules/authentication/authentication.model";
import jwt, { JwtPayload } from "jsonwebtoken";
import {
	ISignupEmailPayload,
	ISignupPayload
} from "../../modules/signup/signup.interfaces";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { IInvitation } from "../../modules/invitation/invitation.interfaces";
import { IAuthentication } from "../../modules/authentication/authentication.interface";
import { IAccessToken } from "../../modules/accessToken/accessToken.interface";
import { InvitationStatus } from "../../modules/invitation/invitation.enum";
import {
	IPostCollectionPayload,
	IShoppableCollection
} from "../../modules/interactiveCollection/interactiveCollection.interface";
import { IVideo } from "../../modules/video/video.interfaces";
import { createVideo } from "../../services/mongodb/video.service";
import { IShoppableVideo } from "../../modules/interactiveVideo/interactiveVideo.interface";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";
import { VideoProfileDBModel } from "../../modules/videoProfile/videoProfile.db.model";
import {
	VideoProfile,
	VideoProfileCreate
} from "../../modules/videoProfile/videoProfile.interface";
import {
	LevelEnum,
	pixelFormatEnum,
	CRFEnum,
	PresetEnum,
	AudioBitrateEnum
} from "../../modules/videoProfile/videoProfile.enum";
import { randomBytes } from "crypto";

class TestHelper {
	private server: express.Express;

	constructor(server: express.Express) {
		this.server = server;
	}

	async createUserAndAccount(createUserPayload: ISignupPayload): Promise<any> {
		const { accessToken } = await this.signup(createUserPayload);

		const account = await this.getAccount(accessToken);
		const accountId = account._id.toString();

		const accountToken = await this.getAccountToken(accountId, accessToken);

		return {
			"accessToken": accessToken,
			"accountToken": accountToken,
			"account": account
		};
	}

	async signup(createUserPayload: ISignupPayload): Promise<any> {
		const createUserResponse = await supertest(this.server)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send(createUserPayload);

		expect(createUserResponse.statusCode).toBe(200);
		expect(createUserResponse.body).toHaveProperty("accessToken");
		expect(createUserResponse.body).toHaveProperty("refreshToken");
		expect(createUserResponse.body.refreshToken).toBeTruthy();
		expect(createUserResponse.body.accessToken).toBeTruthy();

		return {
			"accessToken": createUserResponse.body.accessToken,
			"refreshToken": createUserResponse.body.refreshToken
		};
	}

	async signupEmail(signupEmailPayload: ISignupEmailPayload): Promise<any> {
		const createUserResponse = await supertest(this.server)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send(signupEmailPayload);

		expect(createUserResponse.statusCode).toBe(200);
		expect(createUserResponse.body).toHaveProperty("accessToken");
		expect(createUserResponse.body).toHaveProperty("refreshToken");
		expect(createUserResponse.body.refreshToken).toBeTruthy();
		expect(createUserResponse.body.accessToken).toBeTruthy();

		return {
			"accessToken": createUserResponse.body.accessToken,
			"refreshToken": createUserResponse.body.refreshToken
		};
	}

	async getAccount(accessToken: string): Promise<IAccount> {
		const getAccountsResponse = await supertest(this.server)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(getAccountsResponse.statusCode).toBe(200);
		expect(getAccountsResponse.body).toHaveProperty("accounts");
		expect(getAccountsResponse.body.accounts.length).toBe(1);

		return getAccountsResponse.body.accounts[0];
	}

	async getAccountToken(accountId: string, accessToken: string): Promise<string> {
		const accountTokenResponse = await supertest(this.server)
			.post("/api/accounts/token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json")
			.field("accountId", accountId);

		expect(accountTokenResponse.statusCode).toBe(200);

		return accountTokenResponse.body.token;
	}

	async verify(accountToken: string): Promise<void> {
		const res = await supertest(this.server)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token: accountToken
			});

		expect(res.statusCode).toBe(200);
	}

	async getAuthentication (
		accessToken: string
	): Promise<IAuthentication> {
		const decodedAuthentication = jwt.decode(accessToken) as JwtPayload;

		expect(decodedAuthentication).toBeTruthy();
		expect(decodedAuthentication.authenticationId).toBeTruthy();

		const authenticationId = decodedAuthentication.authenticationId;
		const authModel = new AuthenticationModel(null);
		const authDocument: IAuthentication = await authModel.readOneById(authenticationId);

		return authDocument;
	}

	async createInvitation (
		accountToken: string,
		invitationEmail: string,
		accessToken: string
	): Promise<IInvitation> {
		const resInvitations = await supertest(this.server)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", invitationEmail)
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(resInvitations.statusCode).toBe(200);

		return resInvitations.body.invitation;
	}

	async acceptInvitation(invitationId: string, accessToken: string): Promise<IInvitation> {
		const res = await supertest(this.server)
			.put(`/api/invitations/${invitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3")
			.field("status", InvitationStatus.ACCEPTED);

		expect(res.statusCode).toBe(200);

		return res.body.invitation as IInvitation;
	}

	getUserId(accessToken: string): string {
		const accessTokenData1 = jwt.decode(accessToken) as IAccessToken;
		if (!accessTokenData1) {
			throw new Error("Failed to retrieve IAccessToken from provided access token");
		}
		return accessTokenData1.userId;
	}

	/**
	 * Returns a new collection with a default title and no Shoppable videos.
	 * Caller can update using the model post calling createCollection.
	 */
	async createCollection(
		accountToken: string,
		accessToken: string,
		shoppableVideoIds?: string[]): Promise<IShoppableCollection> {
		const payload: IPostCollectionPayload = {
			title: "Test Collection - empty",
			shoppableVideos: shoppableVideoIds || []
		};

		const res = await supertest(this.server)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		const collectionId: string = res.body.shoppableCollection;
		const collectionModel = new InteractiveCollectionModel(null);

		return await collectionModel.readOneById(collectionId);
	}

	async createVideo(accountId: string): Promise<IVideo> {
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: "http://example.tld/example.mp4",
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);

		return video;
	}

	async createShoppableVideo(videoId: string, accountToken: string, accessToken: string): Promise<IShoppableVideo> {
		const shoppableVideoCreateData = {
			title: "This is great stuff",
			ctaText: "Shop Right Now",
			videoDisplayMode: "landscape",
			showTitle: true,
			videoPosterImageURL: "http://example.tld/example.jpg",
			videoPosterPlayEmbedURL: "http://example.tld/example-play.jpg",
			gifURL: "http://example.tld/example.gif",
			phone: "+***********",
			email: "<EMAIL>"
		};

		const productLinkData = {
			productURL: "http://example.tld/example",
			productTitle: "product title",
			productDescription: "very nice product",
			subTitle: "$100",
			productImageURL: "http://example.tld/product-img.jpg"
		};

		const resShoppableVid = await supertest(this.server)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "4")
			.set("x-account-token", accountToken)
			.field("videoId", videoId)
			.field("title", shoppableVideoCreateData.title)
			.field("videoPosterImageURL", shoppableVideoCreateData.videoPosterImageURL)
			.field("gifURL", shoppableVideoCreateData.gifURL)
			.field("phone", shoppableVideoCreateData.phone)
			.field("email", shoppableVideoCreateData.email)
			.field("videoPosterPlayEmbedURL", shoppableVideoCreateData.videoPosterPlayEmbedURL)
			.field("ctaText", shoppableVideoCreateData.ctaText)
			.field("videoDisplayMode", shoppableVideoCreateData.videoDisplayMode)
			.field("showTitle", shoppableVideoCreateData.showTitle)
			.field("productURL0", productLinkData.productURL)
			.field("productTitle0", productLinkData.productTitle)
			.field("productDescription0", productLinkData.productDescription)
			.field("subTitle0", productLinkData.subTitle)
			.field("productImageURL0", productLinkData.productImageURL);

		expect(resShoppableVid.statusCode).toBe(201);
		expect(resShoppableVid.body.shoppableVideo).toBeTruthy();
		const shoppableVideo: IShoppableVideo = resShoppableVid.body.shoppableVideo;
		return shoppableVideo;
	}

	async createAdditionalAccount(accessToken: string): Promise<IAccount> {
		const resAdditionalAccount = await supertest(this.server)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		expect(resAdditionalAccount.statusCode).toBe(200);
		const additionalAccount: IAccount = resAdditionalAccount.body.account;
		return additionalAccount;
	}

	static async createDefaultVideoProfile(): Promise<VideoProfile> {
		const profileData: VideoProfileCreate = {
			name: `baseline-${Date.now()}`,
			default: true,
			level: LevelEnum.L3_0,
			pixelFormat: pixelFormatEnum.NV12,
			constantRateFactor: CRFEnum.BALANCED,
			preset: PresetEnum.MEDIUM,
			audioBitrate: AudioBitrateEnum.A128k,
			scale: 1080
		};
		const document = await new VideoProfileDBModel(profileData).save({ session: null });
		return document;
	}

	static async initializeEssentialData(): Promise<void> {
		await this.createDefaultVideoProfile();
	}
}

export default TestHelper;

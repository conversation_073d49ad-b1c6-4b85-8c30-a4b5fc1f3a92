import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { APIError } from "../utils/helpers/apiError";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";

const expressApp = createServer();
initExpressRoutes(expressApp);

describe("POST /auth/verify negative mode tests", () => {
	it("[E_INVALID_INPUT]. Should return 400", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_INVALID_INPUT]. Should return 400", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({ token: "invalidToken" });
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[Failed to find authentication doc]. Should return 404", async () => {
		jest.spyOn(AuthenticationModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found")
		);

		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token:
          `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGlvbklkIjoiNjNkMzEzMTBjYjMzMmI
					yMDkyOGFmY2JiIiwiaWF0IjoxNjc0Nzc3MzYwfQ.TTl1FuwdkTpsNOJvHDoX83oO1SHJg1ZCoMMhQq3E8Ts`
			});
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toBe(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_DOCUMENT_NOT_FOUND | Failed to verify]. Should return 400 - cant find auth doc", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGlvbklkIjoiN" +
				"jQzODIyMzkxMDRlYTBkOGMwNWVjNjU1IiwiaWF0IjoxNjgzNzUzNzg4fQ.RPqMGyIheiPUysiATmpqHhdBffHpxMEL4a-vU6Zx07Y"
			});
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_SERVICE_FAILED | Failed to verify]. Should return 400", async () => {
		const newUserPayload = {
			firstName: "Johnny",
			lastName: "Verify",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "verify.negative.mode.test Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		const { accessToken } = await testHelper.signup(newUserPayload);

		jest.spyOn(AuthenticationModel.prototype, "verifyOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "")
		);

		const res = await supertest(expressApp)
			.post("/api/auth/verify")
			.set("x-api-version", "1")
			.send({
				token: accessToken
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});
});

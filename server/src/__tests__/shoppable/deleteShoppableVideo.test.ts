import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { randomBytes } from "crypto";

describe("DELETE /shoppable-videos/:videoId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let accountId: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.shoppable.video.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[200]. Should return 200 > only default collection", async () => {
		const newVideo = await testHelper.createVideo(accountId);
		const newInteractiveVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const collectionModel = new InteractiveCollectionModel(null);
		const defaultCollection = await collectionModel.readOneById(account.defaultCollectionId.toString());
		const isFoundBefore = defaultCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundBefore).toBe(true);

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-videos/${newInteractiveVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		const interctiveVideoModel = await new InteractiveVideoModel(null);

		// becuase it is deleted
		expect(
			interctiveVideoModel.readOneById(newInteractiveVideo._id.toString())
		).rejects.toThrow();

		// expect deleted video to be removed from the default collection
		const updatedCollection = await collectionModel.readOneById(account.defaultCollectionId.toString());
		const isFoundAfter = updatedCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundAfter).toBe(false);
	});

	it("[200]. Should return 200 > additional collection", async () => {
		const collectionModel = new InteractiveCollectionModel(null);

		const newVideo = await testHelper.createVideo(accountId);
		const newInteractiveVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const newCollection = await testHelper.createCollection(
			accountToken,
			accessToken,
			[newInteractiveVideo._id.toString()]);

		const isFoundInNewCollection = newCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundInNewCollection).toBe(true);

		const defaultCollection = await collectionModel.readOneById(account.defaultCollectionId.toString());
		const isFoundInDefaultCollection = defaultCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundInDefaultCollection).toBe(true);

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-videos/${newInteractiveVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		const interctiveVideoModel = await new InteractiveVideoModel(null);

		// becuase it is deleted
		expect(
			interctiveVideoModel.readOneById(newInteractiveVideo._id.toString())
		).rejects.toThrow();

		// expect deleted video to be removed from the default collection
		const updatedDefaultCollection = await collectionModel.readOneById(account.defaultCollectionId.toString());
		const isFoundInDefaultCollectionAfter = updatedDefaultCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundInDefaultCollectionAfter).toBe(false);

		// expect deleted video to be removed from the new collection
		const updatedNewCollection = await collectionModel.readOneById(newCollection._id.toString());
		const isFoundInNewCollectionAfter = updatedNewCollection.shoppableVideos.some(
			c => c.toString() == newInteractiveVideo._id.toString()
		);
		expect(isFoundInNewCollectionAfter).toBe(false);
	});


	it("[E_DATABASE_FAILURE]. Should return 500 failing to find any shoppable videos", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/shoppable-videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_DATABASE_FAILURE);
	});

	it("[E_SERVICE_FAILED]. Should return 500 failing to update Collection or manifest file", async () => {
		jest.spyOn(InteractiveCollectionModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "")
		);

		const newVideo = await testHelper.createVideo(accountId);
		const newInteractiveVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-videos/${newInteractiveVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("[E_SERVICE_FAILED]. Should return 500 failing due to ShoppableVideo not found", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/shoppable-videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_DATABASE_FAILURE);
	});
});

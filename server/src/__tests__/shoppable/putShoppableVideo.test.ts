import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	IShoppableVideo,
	IShoppableVideoProduct
} from "../../modules/interactiveVideo/interactiveVideo.interface";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "src/modules/account/account.interfaces";
import { ISignupPayload } from "src/modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { randomBytes } from "crypto";


// eslint-disable-next-line max-lines-per-function
describe("PUT /shoppable-videos/:videoId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.collection.s.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[OK - 201]. Should be successful and product should have meta object.", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const updateVideoPayload = {
			title: "updated2",
			videoURL: "http://example.tld/example.mp4",
			videoPosterURL: "http://example.tld/example.jpg",
			videoPosterPlayEmbedURL: "http://example.tld/embed-example.jpg",
			gifURL: "http://example.tld/example.gif",
			phone: "+***********",
			email: "<EMAIL>",
			ctaText: "updated",
			showTitle: false,
			videoDisplayMode: "landscape"
		};

		const updateProductPayload = {
			productURL0: "http://example.tld/example",
			productTitle0: "updated product title",
			productDescription0: "very nice product",
			subTitle0: "$200",
			productImageURL0: "http://example.tld/product-img.jpg"
		};

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", updateVideoPayload.title)
			.field("videoURL", updateVideoPayload.videoURL)
			.field("videoPosterImageURL", updateVideoPayload.videoPosterURL)
			.field("videoPosterPlayEmbedURL", updateVideoPayload.videoPosterPlayEmbedURL)
			.field("gifURL", updateVideoPayload.gifURL)
			.field("phone", updateVideoPayload.phone)
			.field("email", updateVideoPayload.email)
			.field("ctaText", updateVideoPayload.ctaText)
			.field("videoDisplayMode", updateVideoPayload.videoDisplayMode)
			.field("showTitle", updateVideoPayload.showTitle)
			.field("productURL0", updateProductPayload.productURL0)
			.field("productTitle0", updateProductPayload.productTitle0)
			.field("productDescription0", updateProductPayload.productDescription0)
			.field("subTitle0", updateProductPayload.subTitle0)
			.field("productImageURL0", updateProductPayload.productImageURL0);

		expect(res.statusCode).toBe(200);

		const updatedShoppableVideo: IShoppableVideo = res.body.shoppableVideo;
		expect(updatedShoppableVideo._id.toString()).toEqual(newShoppableVideo._id.toString());
		expect(updatedShoppableVideo.title).toEqual(updateVideoPayload.title);
		expect(updatedShoppableVideo.videoURL).toEqual(updateVideoPayload.videoURL);
		expect(updatedShoppableVideo.videoPosterURL).toEqual(updateVideoPayload.videoPosterURL);
		expect(updatedShoppableVideo.videoPosterPlayEmbedURL).toEqual(updateVideoPayload.videoPosterPlayEmbedURL);
		expect(updatedShoppableVideo.gifURL).toEqual(updateVideoPayload.gifURL);
		expect(updatedShoppableVideo.phone).toEqual(updateVideoPayload.phone);
		expect(updatedShoppableVideo.email).toEqual(updateVideoPayload.email);
		expect(updatedShoppableVideo.ctaText).toEqual(updateVideoPayload.ctaText);
		expect(updatedShoppableVideo.videoDisplayMode).toEqual(updateVideoPayload.videoDisplayMode);
		expect(updatedShoppableVideo.showTitle).toEqual(updateVideoPayload.showTitle);

		expect(updatedShoppableVideo.products.length).toBe(1);

		const product0: IShoppableVideoProduct = updatedShoppableVideo.products[0];
		expect(product0.url).toEqual(updateProductPayload.productURL0);
		expect(product0.title).toEqual(updateProductPayload.productTitle0);
		expect(product0.productDescription).toEqual(updateProductPayload.productDescription0);
		expect(product0.subTitle).toEqual(updateProductPayload.subTitle0);
		expect(product0.productThumbnail).toEqual(updateProductPayload.productImageURL0);
	});

	it("[OK - 201]. Should be successful .", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const updatePayload = {
			title: "updated",
			videoURL: "http://example.tld/example.mp4",
			videoPosterURL: "http://example.tld/example.jpg",
			gifURL: "http://example.tld/example.gif",
			videoPosterPlayEmbedURL: "http://example.tld/example-play.jpg",
			ctaText: "updated",
			showTitle: true,
			videoDisplayMode: "portrait"
		};

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", updatePayload.title)
			.field("videoURL", updatePayload.videoURL)
			.field("videoPosterImageURL", updatePayload.videoPosterURL)
			.field("videoPosterPlayEmbedURL", updatePayload.videoPosterPlayEmbedURL)
			.field("gifURL", updatePayload.gifURL)
			.field("ctaText", updatePayload.ctaText)
			.field("videoDisplayMode", updatePayload.videoDisplayMode)
			.field("showTitle", updatePayload.showTitle);

		expect(res.statusCode).toBe(200);
		const updatedShoppableVideo: IShoppableVideo = res.body.shoppableVideo;

		expect(updatedShoppableVideo._id.toString()).toEqual(newShoppableVideo._id.toString());
		expect(updatedShoppableVideo.title).toEqual(updatePayload.title);
		expect(updatedShoppableVideo.videoURL).toEqual(updatePayload.videoURL);
		expect(updatedShoppableVideo.videoPosterURL).toEqual(updatePayload.videoPosterURL);
		expect(updatedShoppableVideo.gifURL).toEqual(updatePayload.gifURL);
		expect(updatedShoppableVideo.videoPosterPlayEmbedURL).toEqual(updatePayload.videoPosterPlayEmbedURL);
		expect(updatedShoppableVideo.ctaText).toEqual(updatePayload.ctaText);
		expect(updatedShoppableVideo.videoDisplayMode).toEqual(updatePayload.videoDisplayMode);
		expect(updatedShoppableVideo.showTitle).toEqual(updatePayload.showTitle);
	});

	it("[E_ACCESS_FORBIDDEN]. Should fail because of mixed account ID and return 403.", async () => {
		const additionalAccount = await testHelper.createAdditionalAccount(accessToken);
		const additionalAccountToken = await testHelper.getAccountToken(additionalAccount._id.toString(), accessToken);
		const newVideo = await testHelper.createVideo(additionalAccount._id.toString());
		const newShoppableVideoCreatedWithAdditionalAccount = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			additionalAccountToken,
			accessToken);

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideoCreatedWithAdditionalAccount._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("videoPosterPlayEmbedURL", "http://example.tld/embed-example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_ACCESS_FORBIDDEN);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 401 for invalid authentication token", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${mockId}`)
			.set("Authorization", "Bearer 123")
			.set("x-account-token", "does not matter")
			.set("x-api-version", "1")
			.field("title", "abcd")
			.field("ctaText", "Shop Now")
			.field("videoDisplayMode", "portrait")
			.field("showTitle", "true");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for missing required payload", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)

			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			// this is commented out to demonstrate the missing param
			// uncomment and see res.statusCode ==  200
			// .field("ctaText", "Shop Now")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("videoPosterPlayEmbedURL", "http://example.tld/embed-example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should fail to read shoppablevideo doc and return 404.", async () => {
		const mockId = randomBytes(12).toString("hex");

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("videoPosterPlayEmbedURL", "http://example.tld/embed-example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("ctaText", "Shop Now")
			.field("videoDisplayMode", "portrait")
			.field("showTitle", "true")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_INTERNAL_ERROR]. Should fail to update shoppablevideo doc and return 500.", async () => {
		jest.spyOn(InteractiveVideoModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_INTERNAL_ERROR, "failed to update interactive video")
		);

		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("videoPosterPlayEmbedURL", "http://example.tld/embed-example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("[OK - 201]. Should nullify phone and email .", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const updatePayload = {
			title: "updated",
			videoURL: "http://example.tld/example.mp4",
			videoPosterURL: "http://example.tld/example.jpg",
			gifURL: "http://example.tld/example.gif",
			videoPosterPlayEmbedURL: "http://example.tld/example-play.jpg",
			ctaText: "updated",
			showTitle: true,
			phone: "",
			email: ""
		};

		const res = await supertest(expressApp)
			.put(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", updatePayload.title)
			.field("videoURL", updatePayload.videoURL)
			.field("videoPosterImageURL", updatePayload.videoPosterURL)
			.field("videoPosterPlayEmbedURL", updatePayload.videoPosterPlayEmbedURL)
			.field("gifURL", updatePayload.gifURL)
			.field("ctaText", updatePayload.ctaText)
			.field("showTitle", updatePayload.showTitle)
			.field("phone", updatePayload.phone)
			.field("email", updatePayload.email);

		expect(res.statusCode).toBe(200);
		const updatedShoppableVideo: IShoppableVideo = res.body.shoppableVideo;
		expect(updatedShoppableVideo.phone).toBeNull();
		expect(updatedShoppableVideo.email).toBeNull();
	});
});



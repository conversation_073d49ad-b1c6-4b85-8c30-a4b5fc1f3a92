import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import {
	ApikeyAuthenticationData,
	IAuthentication,
	UserAuthenticationData
} from "../modules/authentication/authentication.interface";
import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { AuthMethods } from "../modules/authentication/authentication.enums";


describe("POST /api/keys Positive Tests", () => {
	let accessToken: string;
	let accountToken: string;
	let expressApp: express.Express;
	let accountId: string;

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "API Keys Post Tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;


	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		const testHelper = new TestHelper(expressApp);

		({ accessToken, accountToken } = await testHelper.createUserAndAccount(
			createUserPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
	});

	it("create key. Should return 200, create new Authentication doc", async () => {
		// expect an authenticationModel should already exists
		// when the user was created via AuthMethods.EMAIL_PASSWORD
		const authenticationModel = new AuthenticationModel(null);
		const authOriginal: IAuthentication =
			await authenticationModel.readOneByEmail(createUserPayload.email);

		expect(authOriginal.method).toBe(AuthMethods.EMAIL_PASSWORD);

		const authOriginalData = authOriginal.data as UserAuthenticationData;
		expect(authOriginalData.email).toBe(createUserPayload.email);

		const res = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("key");
		expect(res.body.key.accountId).toBe(accountId);
		expect(res.body).toHaveProperty("apikey");

		const key = res.body.key;
		const apikey = res.body.apikey;

		expect(res.body.key.keyLast4).toBe(apikey.slice(-4));

		const authNew: IAuthentication =
			await authenticationModel.readOneById(key.authenticationId.toString());

		expect(authNew._id.toString()).not.toBe(authOriginal._id.toString());

		expect(authNew._id.toString()).toBe(key.authenticationId.toString());
		expect(authNew.method).toBe(AuthMethods.APIKEY);
		expect(authNew.userId.toString()).toBe(key.createdBy);
		expect(authNew.accounts[0]._id.toString()).toBe(key.accountId);

		const authNewData = authNew.data as ApikeyAuthenticationData;
		expect(authNewData.apikeyHash).toBeTruthy();
	});
});

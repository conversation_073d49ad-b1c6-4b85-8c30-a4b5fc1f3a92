import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { randomBytes } from "crypto";
import { APIError } from "../utils/helpers/apiError";
import { IInvitation } from "../modules/invitation/invitation.interfaces";

import * as InvitationService from "../services/mongodb/invitations.service";


describe("DELETE /invitations", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let accountId: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.invitation.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "0")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "x")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_MISSING_AUTHORIZATION] for missing access token", async () => {
		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 500 [E_SERVICE_FAILED] when deleting the invitation fails", async () => {
		jest.spyOn(InvitationService, "deleteInvitation").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "some error")
		);

		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the invitation does not exist", async () => {
		jest.spyOn(InvitationService, "deleteInvitation").mockResolvedValueOnce(null);

		const mockInvitationId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/invitations/${mockInvitationId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 200 [OK] when deleting the invitation is successful", async () => {
		const inviteEmail = "<EMAIL>";
		const invitaiton = await testHelper.createInvitation(accountToken, inviteEmail, accessToken);

		const res = await supertest(expressApp)
			.delete(`/api/invitations/${invitaiton._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitation");
		const invitationRes: IInvitation = res.body.invitation;

		expect(invitationRes._id.toString()).toBe(invitaiton._id.toString());
		expect(invitationRes.accountId.toString()).toBe(accountId);
	});
});

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { UserModel } from "../modules/user/user.model";
import { APIError } from "../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { AccountModel } from "../modules/account/account.model";
import { IAccount } from "../modules/account/account.interfaces";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";

// eslint-disable-next-line max-lines-per-function
describe("POST /api/accounts/users/post-signup", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "post.files.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", "Bearer 123")
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for missing companyName", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for missing team", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for missing platform", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for invalid team", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Test Team",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 for invalid platform", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Test Platform"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_INTERNAL_ERROR]. Should return 404 if user does not exist", async () => {
		jest.spyOn(UserModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_INTERNAL_ERROR]. Should return 404 if account does not exist", async () => {
		jest.spyOn(AccountModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
		);

		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[200]. Should return 200 for valid input", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				name: "John Doe",
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("user");
		expect(res.body.user).toHaveProperty("postSignupCompleted");
		expect(res.body.user).toHaveProperty("team");
		expect(res.body).toHaveProperty("account");
		expect(res.body.account).toHaveProperty("platform");

	});

	it("[200]. Should return 200 if name is only field missing in payload", async () => {
		const res = await supertest(expressApp)
			.post("/api/accounts/users/post-signup")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.accept("json")
			.send({
				companyName: "Test Company",
				team: "Marketing / Social",
				platform: "Shopify"
			});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("user");
		expect(res.body.user).toHaveProperty("postSignupCompleted");
		expect(res.body.user).toHaveProperty("team");
		expect(res.body).toHaveProperty("account");
		expect(res.body.account).toHaveProperty("platform");
	});
});

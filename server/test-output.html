<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        line-height: 1.4;
    }

    .report-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .header {
        background: white;
        padding: 30px;
        border-bottom: 2px solid #000000;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .logo {
        font-size: 32px;
        font-weight: bold;
        color: #333;
    }

    .report-period {
        text-align: right;
        color: #000000;
    }

    .report-period .label {
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 4px;
    }

    .report-period .dates {
        font-size: 14px;
        color: #4285f4;
        font-weight: 500;
    }

    .content {
        padding: 30px;
    }

    .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 30px;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        margin-top: 40px;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .metric-card {
        background: #fafafa;
        border-radius: 10px;
        padding: 20px;
        position: relative;
    }

    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .metric-label {
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #666;
    }

    .metric-change {
        font-size: 10px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
    }

    .metric-change.positive {
        color: #0f9d58;
        background: #e8f5e8;
    }

    .metric-change.negative {
        color: #d93025;
        background: #fce8e6;
    }

    .metric-value-row {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .metric-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        flex-shrink: 0;
    }

    .metric-value {
        font-size: 18px;
        font-weight: 700;
        color: #333;
        line-height: 1;
    }

    .chart-container {
        margin-top: 20px;
        margin-bottom: 40px;
        position: relative;
        height: 350px;
        width: 100%;
    }

    .chart-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }

    .chart-subtitle {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 15px;
        height: 20px;
    }

    .chart-canvas {
        height: 300px !important;
        width: 100% !important;
        max-height: 300px;
        display: block;
    }

    .chart-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #666;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .additional-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .additional-metric-card {
        background: #fafafa;
        border-radius: 10px;
        padding: 20px;
    }

    .additional-metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .additional-metric-label {
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #666;
    }

    .additional-metric-change {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
    }

    .additional-metric-change.positive {
        color: #0f9d58;
        background: #e8f5e8;
    }

    .additional-metric-change.negative {
        color: #d93025;
        background: #fce8e6;
    }

    .additional-metric-value {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        line-height: 1;
    }

    .additional-metric-description {
        font-size: 10px;
        color: #999;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 4px;
    }

    .video-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 0.7em;
        margin-bottom: 40px;
        font-size: 14px;
    }

    .video-table th {
        padding: 12px 8px;
        text-align: left;
        font-weight: 600;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #000000;
    }

    .video-table tbody tr {
        background: #fafafa;
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .video-table td {
        padding: 12px 8px;
        vertical-align: middle;
    }

    .video-position {
        font-weight: 700;
        font-size: 16px;
        color: #333;
        width: 40px;
    }

    .video-position-change {
        font-size: 10px;
        font-weight: 600;
        margin-left: 4px;
    }

    .video-position-change.up {
        color: #0f9d58;
    }

    .video-position-change.down {
        color: #d93025;
    }

    .video-position-change.same {
        color: #666;
    }

    .video-thumbnail {
        width: 40px;
        height: 30px;
        border-radius: 4px;
        background: #ddd;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .video-info {
        display: flex;
        align-items: center;
    }

    .video-title {
        font-weight: 500;
        color: #333;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ai-insights {
        background: #1a1a1a;
        color: white;
        border-radius: 12px;
        padding: 24px;
        margin-top: 40px;
    }

    .ai-insights-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
    }

    .ai-insights-icon {
        font-size: 20px;
    }

    .ai-insights-title {
        font-size: 16px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .ai-insights-content {
        font-size: 14px;
        line-height: 1.6;
        color: #e0e0e0;
    }

    .ai-insights-content p {
        margin-bottom: 12px;
    }

    .ai-insights-content p:last-child {
        margin-bottom: 0;
    }

    @media (max-width: 768px) {
        .header {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .metrics-grid {
            grid-template-columns: 1fr;
        }

        .additional-metrics {
            grid-template-columns: 1fr;
        }

        .content {
            padding: 20px;
        }

        .chart-legend {
            gap: 10px;
        }

        .video-table {
            font-size: 12px;
        }

        .video-table th,
        .video-table td {
            padding: 8px 4px;
        }

        .video-title {
            max-width: 120px;
        }
    }

    @media (max-width: 600px) {
        .video-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }
    }

    /* PDF-specific styles */
    @media print {
        body {
            background-color: white !important;
            padding: 0;
            margin: 0;
        }

        .report-container {
            box-shadow: none;
            border-radius: 0;
            max-width: none;
            width: 100%;
        }

        .header {
            display: flex !important;
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 20px 30px !important;
            background: white !important;
            text-align: left !important;
            gap: 0 !important;
        }

        .logo {
            font-size: 28px !important;
            font-weight: bold !important;
        }

        .report-period {
            text-align: right !important;
        }

        .report-period .label {
            font-size: 11px !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            margin-bottom: 4px !important;
        }

        .report-period .dates {
            font-size: 13px !important;
            color: #4285f4 !important;
            font-weight: 500 !important;
        }

        .chart-container {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        #dailyPerformanceSection,
        #metricsSection {
            page-break-before: always;
        }

        .video-table {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .ai-insights {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .metrics-grid {
            page-break-inside: avoid;
            break-inside: avoid;
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 15px !important;
        }

        .additional-metrics {
            page-break-inside: avoid;
            break-inside: avoid;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 15px !important;
        }

        .metric-card {
            min-width: auto !important;
        }

        .additional-metric-card {
            min-width: auto !important;
        }

        /* Ensure charts render properly in PDF */
        .chart-canvas {
            max-width: 100% !important;
            height: auto !important;
        }
    }
</style>

</head>
<body>
    <div class="report-container">
        <div class="header">
    <div class="logo">
        <img alt="logo" class="logo" width="150" src="http://cdn:8080/reports/logo.svg" />
    </div>
    <div class="report-period">
        <div class="label">MONTHLY REPORT</div>
        <div class="dates">June 2025</div>
    </div>
</div>


        <div class="content">
            <div class="title">Your monthly performance metrics from June 2025 are here.</div>

            <div class="section-title">Performance</div>

<div class="metrics-grid">
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">IMPRESSIONS</span>
            <span class="metric-change positive">
                ↑ 1034
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon impressions">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/impressions-icon.svg" />
            </div>
            <div class="metric-value">104,402</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYS</span>
            <span class="metric-change positive">
                ↑ 230
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon plays">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/plays-icon.svg" />
            </div>
            <div class="metric-value">3,312</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CLICKS</span>
            <span class="metric-change negative">
                ↓ 230
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon clicks">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/clicks-icon.svg" />
            </div>
            <div class="metric-value">426</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">EMAILS</span>
            <span class="metric-change positive">
                ↑ 30
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon emails">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/emails-icon.svg" />
            </div>
            <div class="metric-value">125</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CALLS</span>
            <span class="metric-change positive">
                ↑ 10
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon calls">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/phone-icon.svg" />
            </div>
            <div class="metric-value">32</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">ENGAGED SESSIONS</span>
            <span class="metric-change positive">
                ↑ 54
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon engaged">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/engaged-sessions-icon.svg" />
            </div>
            <div class="metric-value">2,045</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYTIME</span>
            <span class="metric-change positive">
                ↑ 1h 32m 10s
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon playtime">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/playtime-icon.svg" />
            </div>
            <div class="metric-value">44h 35m 16s</div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">LIKES</span>
            <span class="metric-change positive">
                ↑ 23
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon likes">
                <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/likes-icon.svg" />
            </div>
            <div class="metric-value">45</div>
        </div>
    </div>
</div>


            <div class="section-title">Daily Playtime</div>

<div class="chart-container">
    <div class="chart-subtitle">PLAYTIME (HOURS)</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="monthlyPlaytimeChart" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('monthlyPlaytimeChart').getContext('2d');

        const rawData = [15.5,23.2,18.1,27.3,31.8,12.8,25.1];

        // Calculate dynamic max value with buffer
        const maxDataValue = Math.max(...rawData, 0);
        const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 2); // At least 2 hours minimum
        const stepSize = Math.max(Math.ceil(dynamicMax / 5), 1); // Divide into ~5 steps

        const chartData = {
            labels: ["6/1","6/2","6/3","6/4","6/5","6/6","6/7"],
            datasets: [{
                data: rawData,
                borderColor: '#333',
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointBackgroundColor: '#4285f4',
                pointBorderColor: '#4285f4',
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: false
            }]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: dynamicMax,
                        ticks: {
                            stepSize: stepSize,
                            color: '#666',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return value + ':00';
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#4285f4',
                        hoverBorderColor: '#4285f4'
                    }
                }
            }
        };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>


            <div id="dailyPerformanceSection" class="section-title">Daily Performance</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>EMAILS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ea4335;"></div>
            <span>CALLS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #34a853;"></div>
            <span>CLICKS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #fbbc04;"></div>
            <span>LIKES</span>
        </div>
    </div>
    <div class="chart-subtitle">COUNT</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="monthlyPerformanceChart" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('monthlyPerformanceChart').getContext('2d');

        const emailsData = [31,29,33,35,36,28,37];
        const callsData = [24,26,28,26,25,29,29];
        const clicksData = [35,34,32,36,32,31,34];
        const likesData = [27,25,25,29,28,25,31];

        // Calculate dynamic max value with buffer
        const maxDataValue = Math.max(
            ...emailsData,
            ...callsData,
            ...clicksData,
            ...likesData,
            0
        );
        const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 5); // At least 5 minimum
        const stepSize = Math.max(Math.ceil(dynamicMax / 4), 1); // Divide into ~4 steps

        const chartData = {
            labels: ["6/1","6/2","6/3","6/4","6/5","6/6","6/7"],
            datasets: [
                {
                    label: 'EMAILS',
                    data: emailsData,
                    borderColor: '#4285f4',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#4285f4',
                    pointBorderColor: '#4285f4',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CALLS',
                    data: callsData,
                    borderColor: '#ea4335',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#ea4335',
                    pointBorderColor: '#ea4335',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CLICKS',
                    data: clicksData,
                    borderColor: '#34a853',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#34a853',
                    pointBorderColor: '#34a853',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'LIKES',
                    data: likesData,
                    borderColor: '#fbbc04',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#fbbc04',
                    pointBorderColor: '#fbbc04',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                }
            ]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: dynamicMax,
                        ticks: {
                            stepSize: stepSize,
                            color: '#666',
                            font: {
                                size: 11
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                }
            }
        };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>


            <div class="section-title">Daily Plays</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>CURRENT WEEK</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #fbbc04;"></div>
            <span>PREVIOUS WEEK</span>
        </div>
    </div>
    <div class="chart-subtitle">COUNT</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="monthlyPlaysChart" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('monthlyPlaysChart').getContext('2d');

        const currentWeekData = [15800,17200,16200,15600,14800,15400,16600];
        const previousWeekData = [14000,16400,14800,16200,13600,14000,17200];

        // Calculate dynamic max value with buffer
        const maxDataValue = Math.max(
            ...currentWeekData,
            ...previousWeekData,
            0
        );
        const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 1000); // At least 1k minimum
        const stepSize = Math.max(Math.ceil(dynamicMax / 4), 500); // Divide into ~4 steps

        const chartData = {
            labels: ["MON","TUE","WED","THU","FRI","SAT","SUN"],
            datasets: [
                {
                    label: 'CURRENT WEEK',
                    data: currentWeekData,
                    backgroundColor: '#4285f4',
                    borderColor: '#4285f4',
                    borderWidth: 1
                },
                {
                    label: 'PREVIOUS WEEK',
                    data: previousWeekData,
                    backgroundColor: '#fbbc04',
                    borderColor: '#fbbc04',
                    borderWidth: 1
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: dynamicMax,
                        ticks: {
                            stepSize: stepSize,
                            color: '#666',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return (value / 1000) + 'k';
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                },
                barPercentage: 0.8,
                categoryPercentage: 0.9
            }
        };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>


            <div id="metricsSection" class="section-title">Metrics</div>

<div class="additional-metrics">
    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">ENGAGEMENT RATE</span>
            <span class="additional-metric-change positive">
                ↑1.2%
            </span>
        </div>
        <div class="additional-metric-value">4.23%</div>
        <div class="additional-metric-description">IMPRESSIONS / ENGAGED SESSIONS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAY RATE</span>
            <span class="additional-metric-change positive">
                ↑0.31%
            </span>
        </div>
        <div class="additional-metric-value">1.21%</div>
        <div class="additional-metric-description">IMPRESSIONS / PLAYS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">CLICKTHROUGH RATE</span>
            <span class="additional-metric-change negative">
                ↓0.42%
            </span>
        </div>
        <div class="additional-metric-value">4.53%</div>
        <div class="additional-metric-description">PLAYS / CLICKS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">LEAD COUNT</span>
            <span class="additional-metric-change positive">↑125</span>
        </div>
        <div class="additional-metric-value">4,502</div>
        <div class="additional-metric-description">LEADS TO DATE</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAYS PER SESSION</span>
            <span class="additional-metric-change positive">
                ↑2
            </span>
        </div>
        <div class="additional-metric-value">10</div>
        <div class="additional-metric-description">PLAYS / ENGAGED SESSION</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">AVG PLAYTIME</span>
            <span class="additional-metric-change positive">
                ↑4:32
            </span>
        </div>
        <div class="additional-metric-value">4:02</div>
        <div class="additional-metric-description">PLAYTIME / ENGAGED SESSION</div>
    </div>
</div>


            <div class="section-title">Your Videos</div>

<table class="video-table">
    <thead>
        <tr>
            <th>POSITION</th>
            <th>TITLE</th>
            <th>LENGTH</th>
            <th>PLAYS</th>
            <th>CLICKS</th>
            <th>EMAILS</th>
            <th>CALLS</th>
            <th>PLAYTIME</th>
            <th>SCORE</th>
        </tr>
    </thead>
    <tbody>
        
        <tr>
            <td>
                <span class="video-position">1</span>
                
                    <span class="video-position-change up">↑ 1</span>
                
            </td>
            <td>
                <div class="video-info">
                    <div class="video-thumbnail"></div>
                    <div class="video-title">Video 1</div>
                </div>
            </td>
            <td>1:45</td>
            <td>202</td>
            <td>30</td>
            <td>12</td>
            <td>12</td>
            <td>2:30</td>
            <td>94</td>
        </tr>
        
    </tbody>
</table>


            <div class="ai-insights">
    <div class="ai-insights-header">
        <div class="ai-insights-icon">
            <img alt="logo" class="logo" width="30" src="http://cdn:8080/reports/ai-insights-icon.svg" />
        </div>
        <div class="ai-insights-title">AI INSIGHT SUMMARY</div>
    </div>
    <div class="ai-insights-content">
        
            
                <p>Your content performed exceptionally well in June 2025.</p>
            
                <p>The engagement rate shows strong audience interest.</p>
            
        
    </div>
</div>

        </div>
    </div>
</body>
</html>

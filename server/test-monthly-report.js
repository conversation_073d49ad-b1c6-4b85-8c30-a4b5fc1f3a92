// Simple test script to verify the monthly report endpoint works
const path = require('path');
const ejs = require('ejs');

async function testMonthlyReport() {
    try {
        // Test data similar to what the controller generates
        const reportData = {
            reportPeriod: "June 2025",
            monthRange: "June 2025",
            metrics: {
                impressions: { value: "104,402", change: 1034 },
                plays: { value: "3,312", change: 230 },
                clicks: { value: "426", change: -230 },
                emails: { value: "125", change: 30 },
                calls: { value: "32", change: 10 },
                engaged: { value: "2,045", change: 54 },
                playtime: { value: "44h 35m 16s", change: "1h 32m 10s" }
            },
            dailyPlaytimeData: {
                labels: ["6/1", "6/2", "6/3", "6/4", "6/5", "6/6", "6/7"],
                values: [15.5, 23.2, 18.1, 27.3, 31.8, 12.8, 25.1] // Values exceeding old max of 10
            },
            dailyPerformanceData: {
                labels: ["6/1", "6/2", "6/3", "6/4", "6/5", "6/6", "6/7"],
                emails: [31, 29, 33, 35, 36, 28, 37], // Values exceeding old max of 20
                calls: [24, 26, 28, 26, 25, 29, 29],
                clicks: [35, 34, 32, 36, 32, 31, 34]
            },
            dailyPlaysData: {
                currentWeek: [15800, 17200, 16200, 15600, 14800, 15400, 16600], // Values exceeding old max of 8000
                previousWeek: [14000, 16400, 14800, 16200, 13600, 14000, 17200]
            },
            metricsData: {
                engagementRate: { value: "4.23%", change: 1.20 },
                playRate: { value: "1.21%", change: 0.31 },
                clickthroughRate: { value: "4.53%", change: -0.42 },
                leadCount: { value: "4,502", change: 125 },
                playsPerSession: { value: "10", change: 2 },
                avgPlaytime: { value: "4:02", change: "4:32" }
            },
            videos: [
                {
                    position: 1,
                    change: 'up',
                    changeValue: 1,
                    title: 'Video 1',
                    length: '1:45',
                    plays: 202,
                    clicks: 30,
                    emails: 12,
                    calls: 12,
                    playtime: '2:30',
                    score: 94
                }
            ],
            insights: [
                "Your content performed exceptionally well in June 2025.",
                "The engagement rate shows strong audience interest."
            ],
            host: 'http://cdn:8080/'
        };

        const templatePath = path.resolve(__dirname, 'src/modules/reports/templates/monthly-report.ejs');
        console.log('Template path:', templatePath);

        const html = await ejs.renderFile(templatePath, reportData);
        console.log('✅ Template rendered successfully!');
        console.log('HTML length:', html.length, 'characters');

        // Write to file for inspection
        const fs = require('fs');
        fs.writeFileSync('test-output.html', html);
        console.log('📄 Output written to test-output.html');

    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error(error.stack);
    }
}

testMonthlyReport();
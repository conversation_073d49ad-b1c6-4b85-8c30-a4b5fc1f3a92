{"name": "ap-server", "version": "1.0.0", "description": "Server/API", "main": "index.js", "scripts": {"start": "node ./dist/index.js -p tsconfig.start.json", "build222": "rimraf dist && tsc && copyfiles --error --up 1 src/assets/**/*.* dist/", "build": "rimraf dist && tsc && copyfiles --error --up 4 src/modules/reports/templates/**/*.* dist/modules/reports/templates/ && copyfiles --error --up 1 src/assets/**/*.* dist/", "watch": "nodemon ./src/index.ts", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "typecheck": "tsc --noEmit", "test": "jest --config jest.config.js --coverage=false", "test:watch": "jest --config jest.config.js --watch", "test:ci": "jest --config jest.config.ci.js", "test:cov": "jest --config jest.config.js --coverage=true", "audit": "better-npm-audit audit --level=low"}, "devDependencies": {"@stylistic/eslint-plugin-ts": "^3.0.1", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/dockerode": "^3.3.19", "@types/ejs": "^3.1.1", "@types/express": "^4.17.15", "@types/fluent-ffmpeg": "^2.1.21", "@types/i18n": "^0.13.6", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.1", "@types/node": "^20.17.57", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^5.48.1", "@typescript-eslint/parser": "^5.48.1", "better-npm-audit": "^3.7.3", "copyfiles": "^2.4.1", "eslint": "^8.31.0", "eslint-plugin-import": "^2.27.0", "eslint-plugin-import-newlines": "^1.3.4", "jest": "^29.3.1", "jest-fetch-mock": "^3.0.3", "mongodb-memory-server": "^9.4.0", "nodemon": "^3.1.0", "prettier": "^2.8.2", "rimraf": "^5.0.1", "supertest": "^6.3.3", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.4", "undici-types": "^7.10.0"}, "dependencies": {"@google-cloud/logging": "^11.0.0", "@google-cloud/run": "^1.2.0", "@google-cloud/secret-manager": "^5.4.0", "@google-cloud/speech": "^7.1.0", "@google-cloud/storage": "^7.13.0", "@types/multer": "^1.4.7", "axios": "^1.3.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dockerode": "^4.0.6", "dotenv": "^16.0.3", "ejs": "^3.1.8", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "google-proto-files": "^5.0.0", "i18n": "^0.15.1", "joi": "^17.7.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.1.0", "mongoose": "^6.13.8", "multer": "^1.4.5-lts.1", "node-html-parser": "^6.1.5", "puppeteer": "^21.0.0", "protobufjs": "^7.5.3", "sharp": "^0.32.5", "stripe": "^14.15.0", "uuid": "^9.0.1"}}
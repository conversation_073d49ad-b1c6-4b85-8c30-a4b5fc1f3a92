services:
  mongodb1:
    container_name: "ap-mongodb1"
    build:
      context: .
      dockerfile: Dockerfile.mongo
    command: ["mongod", "--bind_ip_all", "--port", "27017", "--keyFile", "/mongodb.key", "--auth", "--replSet", "ap-replication-set", "--quiet"]
    ports:
      - "${MONGOHOST1_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_DATABASE=admin
      - MONGO_INITDB_ROOT_USERNAME=mongoroot
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - ${VOLUME_PATH}/mongodb.1:/data/db
    depends_on:
      - mongodb2
      - mongodb3
    healthcheck:
      test: mongosh < /initiate.js
      interval: 30s
      start_period: 30s
    networks:
      - ap-docker-framework
  
  mongodb2:
    container_name: "ap-mongodb2"
    build:
      context: .
      dockerfile: Dockerfile.mongo
    command: ["mongod", "--bind_ip_all", "--port", "27018", "--keyFile", "/mongodb.key", "--auth", "--replSet", "ap-replication-set", "--quiet"]
    ports:
      - "${MONGOHOST2_PORT:-27018}:27018"
    volumes:
      - ${VOLUME_PATH}/mongodb.2:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.runCommand('ping').ok"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ap-docker-framework
  
  mongodb3:
    container_name: "ap-mongodb3"
    build:
      context: .
      dockerfile: Dockerfile.mongo
    command: ["mongod", "--bind_ip_all", "--port", "27019", "--keyFile", "/mongodb.key", "--auth", "--replSet", "ap-replication-set", "--quiet"]
    ports:
      - "${MONGOHOST3_PORT:-27019}:27019"
    volumes:
      - ${VOLUME_PATH}/mongodb.3:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.runCommand('ping').ok"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ap-docker-framework
    
  sendgrid:
    container_name: "ap-sendgrid"
    build:
      context: .
      dockerfile: Dockerfile.sendgrid
    ports:
      - "${SENDGRID_PORT:-5005}:3000"
    environment:
      API_KEY: "1234"
    networks:
      - ap-docker-framework

  server:
    container_name: "ap-server"
    build:
      context: .
      dockerfile: Dockerfile.server
    ports:
      - "${SERVER_PORT:-5004}:8080"
    environment:
      PORT: "8080"
      GOOGLE_APPLICATION_CREDENTIALS: "./mock-gcp-key.json"
      SERVICE_TIMEOUT: 30
      NODE_ENV: 'development'
      SENDGRID_API_ENDPOINT: "sendgrid"
      SENDGRID_API_PORT: 3000
      SUPPORTS_DB_TRANSACTIONS: "TRUE"
      SERVER_PATH: ${SERVER_PATH}
      VOLUME_PATH: ${VOLUME_PATH}
      IMAGE_NAME: "docker-framework-server"
      NETWORK_NAME: "ap-docker-framework"
      ENABLE_WORKERS_RUNTIME: "true"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${VOLUME_PATH}/secrets:/secrets
      - ${SERVER_PATH}/src:/usr/src/app/server/src
      - ${SERVER_PATH}/package.json:/usr/src/app/server/package.json
      - ${SERVER_PATH}/package-lock.json:/usr/src/app/server/package-lock.json
      - ${SERVER_PATH}/tsconfig.json:/usr/src/app/server/tsconfig.json
      - ${SERVER_PATH}/nodemon.json:/usr/src/app/server/nodemon.json
      - ${VOLUME_PATH}/storage:/storage
      - ${ADMIN_PATH}:/usr/src/app/admin/dist
    depends_on:
      - mongodb1
      - mongodb2
      - mongodb3
    networks:
      - ap-docker-framework

  cdn:
    container_name: "ap-cdn"
    build:
      context: .
      dockerfile: Dockerfile.cdn
    ports:
      - "8080:8080"
    volumes:
      - ${VOLUME_PATH}/storage/${STORAGE_BUCKET_NAME:-local-cdn.autoplay.video}:/usr/nginx/storage
    networks:
      - ap-docker-framework

  storage:
    container_name: "ap-storage"
    image: fsouza/fake-gcs-server
    ports:
      - "4443:4443"
    command: -scheme http --host 0.0.0.0 -external-url http://storage:4443 -public-host storage:4443 -cors-headers content-type,x-goog-resumable
    volumes:
      - ${VOLUME_PATH}/storage:/storage
    networks:
      - ap-docker-framework
  cron:
    container_name: "ap-cron"
    build:
      context: .
      dockerfile: Dockerfile.cron
    volumes:
      - ./cron-hourly.sh:/usr/local/bin/cron-hourly.sh

    networks:
      - ap-docker-framework

networks:
  ap-docker-framework:
    name: ap-docker-framework
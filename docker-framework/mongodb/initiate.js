db=db.getSiblingDB('admin');
if (db) {
	const authResult = db.auth('mongoroot','password');
	if (authResult.ok === 1) {
		try {
			if (rs.status().ok === 1) {
				process.exit(0);
			}
		} catch (error) {
			try {
				const result = rs.initiate({
					_id:'ap-replication-set',
					members:[{
						_id:0,
						host:"mongodb1:27017",
						priority:2
					},{
						_id:1,
						host:"mongodb2:27018",
						priority:0
					},{
						_id:2,
						host:"mongodb3:27019",
						priority:0
					}]
				});

				if (result.ok === 1) {
					process.exit(0);
				}
			} catch (error) {
				if (error?.code === 23) { // MongoServerError: already initialized
					process.exit(0);
				}
			}
		}
	}
}

process.exit(1);

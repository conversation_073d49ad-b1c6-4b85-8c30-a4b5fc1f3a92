# Docker Framework

## Installation

### Prerequisites

Docker Engine and docker compose supporting version 3.8 or higher.

### Instructions

1. Create a `ap-volumes` folder in a path of your own choosing. This location will hold persistent data the survives the docker container restart(s).
    - Create a `secrets` folder inside the `ap-volumes` folder location.
    - Create a `storage` folder inside the `ap-volumes` folder location.

2. Clone the https://github.com/autoplayvideo/ap-platform repository into a path of your own choosing.
    - Change to the `server` directory.
    	- Run `npm install` in the repository.
    	- Copy the `.env.sample` file into a `.env` file in the same location.
    	- Modify the `.env` file to set the `VOLUME_SECRETS_PATH` to the `ap-volumes/secrets` location.
    	- Modify the `.env` file to set the `VOLUME_STORAGE_PATH` to the `ap-volumes/storage` location.
    	- Copy the `server/secrets.sample.json` file into the `ap-volumes/secrets` folder location as `ap.json`.
    	- Copy the assets from the `setup/cdn` folder into `ap-volumes/storage/{bucket name}`.
	- Change to the `admin` directory.
    	- Run `npm install` in the repository.
    	- Run `npm run build` in the repository.  (I got an error here "Failed to load ./.env.production")
	- Change to the `player` directory.
    	- Run `npm install` in the repository.
    	- Run `npm run build` in the repository.
    	- Copy the `.env.sample` into a `.env` file in the same folder location.
	- Change to the `docker-framework` directory.
		- Copy the `.env.sample` into a `.env` file in the same folder location.
		- Modify the `.env` file to set the `VOLUME_PATH` to the `ap-volumes` location.
		- Modify the `.env` file to set the `ADMIN_PATH` to the `ap-platform/admin` `dist` folder location.
		- Modify the `.env` file to set the `PLAYER_PATH` to the `ap-platform/player` `dist` folder location.
		- Modify the `.env` file to set the `SERVER_PATH` to the `ap-platform/server` `dist` folder location.
		- Optionally modify the additional environment variables by uncommenting if the details conflict with your environment.
		- MongoDB must be able to read the key file `mongodb/mongodb.key`. Ensure correct permissions with `chmod 600 mongodb.key`.
		- Run `docker compose build` from the root of this repository.
		- Run `docker compose up` from the root of this repository.

3. Modify your local development machine
    - If you are accessing the MongoDB replication set OR any other docker service outside of the docker containers (from the host machine), mongo and the other services will need to be added to the `hosts` file on the machine. The host needs the ability to resolve the service names for the replica set when connecting.

    The location of the `hosts` file will vary by platform.
    * For Linux/MacOS, the file is located as `/etc/hosts`. 
    * For Windows, the file is located as `c:\Windows\System32\Drivers\etc\hosts`.
    
    <br>
    Add the following to the end of the `hosts` file.
    <br>
    
    ```
    127.0.0.1 mongodb1 mongodb2 mongodb3 cdn storage
    ```

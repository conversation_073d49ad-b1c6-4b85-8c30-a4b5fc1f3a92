FROM node:18-alpine
WORKDIR /usr/src/app/server
COPY ./mock-gcp-key.json ./mock-gcp-key.json
COPY ./ffmpeg-release-amd64-static.tar.xz ./ffmpeg-release-amd64-static.tar.xz

RUN apk add --no-cache tar xz
RUN tar -xf ffmpeg-release-amd64-static.tar.xz -C /tmp
RUN mv /tmp/ffmpeg-*-static/ffmpeg /usr/local/bin/ffmpeg
RUN mv /tmp/ffmpeg-*-static/ffprobe /usr/local/bin/ffprobe
RUN rm -rf /tmp/ffmpeg-*-static
RUN apk del tar xz
RUN rm ffmpeg-release-amd64-static.tar.xz

RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    fontconfig \
    dbus \
    udev \
    libx11 \
    libxcomposite \
    libxdamage \
    libxrandr \
    libxext \
    mesa-gl \
    alsa-lib \
    && rm -rf /var/cache/apk/*

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

CMD npm install && npx nodemon

## Basic Usage

1. **Copy the environment sample file and set your values:**

   ```sh
   cp .env.sample .env
   ```

   Edit `.env` and set the values for:
   - `PORT` (the port the server will run on)
   - `API_ENDPOINT` (the endpoint for your player API, e.g., `http://localhost:5003`)
   - `COLLECTION_ID` (the collection ID to display)

2. **Install dependencies:**

   ```sh
   npm install
   ```

3. **Run the development server:**

   ```sh
   npm run dev
   ```

   The app will be available at [http://localhost:5009](http://localhost:5009) (or the port you set).
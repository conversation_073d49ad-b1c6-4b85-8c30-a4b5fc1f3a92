{"name": "my-app", "version": "1.0.0", "type": "module", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "copy:views": "copyfiles -u 2 src/views/**/* dist/views", "dev": "nodemon", "build": "npm run clean && tsc && npm run copy:views", "start": "node dist/server.js"}, "dependencies": {"dotenv": "^16.5.0", "ejs": "^3.1.9", "express": "^4.19.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.6.3", "copyfiles": "^2.4.1", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "typescript": "^5.4.5"}}
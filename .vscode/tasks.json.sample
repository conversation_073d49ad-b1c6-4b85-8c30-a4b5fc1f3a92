{"version": "2.0.0", "tasks": [{"label": "Start MongoDB", "type": "shell", "command": "cd docker-framework && docker compose up mongodb1 mongodb2 mongodb3", "problemMatcher": []}, {"label": "Start Server", "type": "shell", "command": "cd docker-framework && docker compose up server", "problemMatcher": []}, {"label": "Start Emulators", "type": "shell", "command": "cd docker-framework && docker compose up sendgrid cdn storage cron", "problemMatcher": []}, {"label": "Start Player", "type": "shell", "command": "cd player && npm install && npm start", "problemMatcher": []}, {"label": "Start Share", "type": "shell", "command": "cd player/cloudflare-worker && npm install && npm start", "problemMatcher": []}, {"label": "Start Admin", "type": "shell", "command": "cd admin && npm install && npm start", "problemMatcher": []}, {"label": "Start Stripe", "type": "shell", "command": "stripe login && stripe listen --forward-to localhost:5004/api/stripe/webhook", "problemMatcher": []}, {"label": "Start Web", "type": "shell", "command": "cd web && npm run dev", "problemMatcher": []}, {"label": "Start All Services", "dependsOn": ["Start MongoDB", "Start Server", "Start Emulators", "Start Player", "Start Share", "Start Admin", "Start Stripe", "Start Web"], "dependsOrder": "parallel"}]}
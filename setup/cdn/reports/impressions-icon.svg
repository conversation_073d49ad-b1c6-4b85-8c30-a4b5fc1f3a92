<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 200 200">
  <!-- Generator: Adobe Illustrator 29.3.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 151)  -->
  <defs>
    <style>
      .st0 {
        fill: url(#linear-gradient1);
      }

      .st1 {
        fill: url(#linear-gradient2);
        opacity: .5;
      }

      .st2 {
        fill: url(#linear-gradient);
      }

      .st3 {
        fill: #fdfdfd;
      }
    </style>
    <linearGradient id="linear-gradient" x1="14.3" y1="100" x2="185.7" y2="100" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1e1e7f"/>
      <stop offset="1" stop-color="#403ee1"/>
    </linearGradient>
    <linearGradient id="linear-gradient1" x1="61.7" y1="100" x2="137.7" y2="100" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient2" x1="71.1" y1="100" x2="128.3" y2="100" xlink:href="#linear-gradient"/>
  </defs>
  <g id="_x32_BOSa8">
    <g>
      <path class="st2" d="M95.6,33.5c36.6-2.2,67.7,22.7,84.5,53.3,1.6,2.9,5.5,9.9,5.7,12.9s-3.2,9.1-4.7,12c-26,48.1-80.6,73.6-129.1,38.9-14.3-10.2-28.9-28-35.8-44.1-2.7-6.3-2.3-8.3.5-14.5,13.9-30.3,44.9-56.6,79-58.6ZM95.6,43c-29.7,2-55.8,24.3-69,50-1.2,2.3-3.3,5.9-2.5,8.4s5.1,10,7,13c19.6,31.1,53.9,51.7,91.1,38.9,20.3-7,39.1-25.4,49.4-44,.9-1.6,4.2-7.9,4.2-9.1,0-2.1-3.9-8.9-5.2-11-15.3-26.5-42.9-48.4-75-46.2Z"/>
      <path class="st3" d="M95.6,43c32.1-2.2,59.7,19.7,75,46.2,1.2,2.2,5.2,9,5.2,11s-3.4,7.6-4.2,9.1c-10.2,18.6-29.1,37-49.4,44-37.2,12.8-71.5-7.8-91.1-38.9-1.9-3-6.1-9.8-7-13s1.3-6.1,2.5-8.4c13.3-25.7,39.3-48,69-50ZM137.7,100c0-21-17-38-38-38s-38,17-38,38,17,38,38,38,38-17,38-38Z"/>
      <g>
        <path class="st0" d="M137.7,100c0,21-17,38-38,38s-38-17-38-38,17-38,38-38,38,17,38,38ZM128.3,100c0-15.8-12.8-28.6-28.6-28.6s-28.6,12.8-28.6,28.6,12.8,28.6,28.6,28.6,28.6-12.8,28.6-28.6Z"/>
        <ellipse class="st1" cx="99.7" cy="100" rx="28.6" ry="28.6"/>
      </g>
    </g>
  </g>
</svg>
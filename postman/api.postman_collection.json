{"info": {"_postman_id": "dd78b0cf-4ecd-4a70-833f-d2ee0083fd8a", "name": "api", "description": "# Autoplay API\n\n## Getting Started\n\nThe collection requests have been set up to use environment variables to parametize the data. Avoid manually adjusting the collection request inputs and instead create variables to use in the collection requests.\n\n### Authentication\n\n- Start by choosing an environment:\n    \n        \n    - localhost:5004 (localhost)\n        \n- Set the `USER_EMAIL` and `USER_PASSWORD` variables in the environment current values field. Leave the initial values empty.\n    \n- Sign in with the `api/auth/sign-in (email / password)` POST request.\n    \n    - If successful, this will auto-fill the `ACCESS_TOKEN` and `REFRESH_TOKEN` collection variables. These will be used in subsequent requests.\n        \n- Use the `api/accounts/self` GET request to retrieve a list of accounts attached to the signed in user.\n    \n    - The `ACCOUNT_ID` collection variable will be set with the first account `_id` received in the GET response if there is at least one account.\n        \n- Use the `api/accounts/token` POST request to create an account token.\n    \n    - The `ACCOUNT_TOKEN` collection variable will be set if successful using the current `ACCOUNT_ID` and `ACCESS_TOKEN` as input.\n        \n- User and account authentication is complete. To use a different account, modify the `ACCOUNT_ID` collection variable before calling the `api/accounts/token` POST request.\n    \n\n### Account Creation\n\nIf there are no accounts created and/or attached to a user, one will need to be created to complete the authentication steps above.\n\n- Use the `api/accounts` POST request to create a new account. Fill the `COMPANY_NAME_ACCOUNT_CREATE` collection variable with a company name; this can be anything.\n    \n\n## Exporting\n\nThe Postman collection(s) and environment(s) are exported and stored in the code repository under the `/postman` directory. To export and commit any changes made, create a new git branch and export the Postman collection(s) and/or environment(s) to the `/postman` repository folder as `.json files.`\n\n```\n/postman\n    api.postman_collection.json\n    localhost.postman_environment.json\n\n ```\n\n## Importing\n\nThe Postman collections(s) and envronment(s) can be selectively imported as needed. Use the import option and select the \"folder\" option, choosing the `/postman` code repository folder.\n\n- Select/Unselect the items to import then press import.\n    \n- Choose the \"replace\" option.\n    \n    - If you want to keep the existing collections/environments choose copy instead.\n        \n\nNote that replacing will remove any set variables in either the collection(s) or environment(s).", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "3844314"}, "item": [{"name": "auth", "item": [{"name": "sign-in (email / password)", "event": [{"listen": "test", "script": {"exec": ["const CryptoJS = require(\"crypto-js\");", "", "var jsonData = JSON.parse(pm.response.text());", "", "if (jsonData.error) {", "    throw new Error(jsonData.error);", "}", "", "pm.collectionVariables.set(\"ACCESS_TOKEN\", jsonData.accessToken);", "pm.collectionVariables.set(\"REFRESH_TOKEN\", jsonData.refreshToken);", "", "function parseJwt (token,part) {", "   var base64Url = token.split('.')[part];", "   var words = CryptoJS.enc.Base64.parse(base64Url);", "   var jsonPayload = CryptoJS.enc.Utf8.stringify(words);", "   return  JSON.parse(jsonPayload);", "};", "", "var jwtInfo ={};", "jwtInfo.size = jsonData.accessToken.length;", "jwtInfo.header = parseJwt(jsonData.accessToken,0);", "jwtInfo.payload = parseJwt(jsonData.accessToken,1);", "jwtInfo.signature = jsonData.accessToken.split('.')[2];", "jwtInfo.expires = ((jwtInfo.payload.exp-Date.now().valueOf()/1000)/60).toFixed(1);", "", "pm.collectionVariables.set(\"AUTHENTICATION_ID\", jwtInfo.payload.authenticationId);", "pm.collectionVariables.set(\"ACCOUNT_ID\", jwtInfo.payload.accountId);", "pm.collectionVariables.set(\"USER_ID\", jwtInfo.payload.userId);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-api-version", "value": "2", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"method\": \"email/password\",\n    \"email\": \"{{USER_EMAIL}}\",\n    \"password\": \"{{USER_PASSWORD}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_ENDPOINT}}/api/auth/sign-in", "host": ["{{API_ENDPOINT}}"], "path": ["api", "auth", "sign-in"]}}, "response": []}]}, {"name": "accounts", "item": [{"name": "token", "event": [{"listen": "test", "script": {"exec": ["var atob = require('atob');", "", "var jsonData = JSON.parse(responseBody);", "pm.collectionVariables.set(\"ACCOUNT_TOKEN\", jsonData.token);", "", "var tokenDataSplit = jsonData.token.split(\".\");", "var tokenDataBase64 = tokenDataSplit[1];", "var tokenData = JSON.parse(atob(tokenDataBase64));", "console.log(tokenData);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-api-version", "value": "2", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "accountId", "value": "{{ACCOUNT_ID}}", "type": "text"}]}, "url": {"raw": "{{API_ENDPOINT}}/api/accounts/token", "host": ["{{API_ENDPOINT}}"], "path": ["api", "accounts", "token"]}}, "response": []}, {"name": "self", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(pm.response.text());", "", "if (jsonData.accounts?.length) {", "    const firstAccount = jsonData.accounts[0];", "    const id = firstAccount._id;", "    pm.collectionVariables.set(\"ACCOUNT_ID\", id);", "} else {", "    console.error(\"no accounts available\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-api-version", "value": "2", "type": "text"}], "url": {"raw": "{{API_ENDPOINT}}/api/accounts/self", "host": ["{{API_ENDPOINT}}"], "path": ["api", "accounts", "self"]}}, "response": []}, {"name": "(create account)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-api-version", "value": "2", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "companyName", "value": "{{COMPANY_NAME_ACCOUNT_CREATE}}", "type": "text"}]}, "url": {"raw": "{{API_ENDPOINT}}/api/accounts", "host": ["{{API_ENDPOINT}}"], "path": ["api", "accounts"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "ACCESS_TOKEN", "value": ""}, {"key": "REFRESH_TOKEN", "value": ""}, {"key": "AUTHENTICATION_ID", "value": ""}, {"key": "ACCOUNT_ID", "value": ""}, {"key": "USER_ID", "value": ""}, {"key": "ACCOUNT_TOKEN", "value": ""}, {"key": "COMPANY_NAME_ACCOUNT_CREATE", "value": "", "type": "string"}]}
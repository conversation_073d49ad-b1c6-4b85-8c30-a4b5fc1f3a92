name: Integration Tests

on:
  pull_request:
    branches: [ main ]
  workflow_dispatch:

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  filter:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: read

    outputs:
      server_changed: ${{ steps.filter.outputs.server }}
      admin_changed: ${{ steps.filter.outputs.admin }}
      player_changed: ${{ steps.filter.outputs.player }}
      player_worker_changed: ${{ steps.filter.outputs.player_worker }}

    steps:
      - uses: actions/checkout@v4
      - name: Filter paths
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            server:
              - 'server/**'
            admin:
              - 'admin/**'
            player:
              - 'player/**'
            player_worker:
              - 'player/cloudflare-worker/**'

  server-tests:
    needs: filter
    if: needs.filter.outputs.server_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: Install Dependencies
        run: npm ci
        working-directory: ./server
      - name: Run Vulnerability Audit
        run: npm run audit
        working-directory: ./server
      - name: Lint
        run: npm run lint
        working-directory: ./server
      - name: Typecheck
        run: npm run typecheck
        working-directory: ./server
      - name: Run Tests
        run: npm run test:ci
        working-directory: ./server

  admin-tests:
    needs: filter
    if: needs.filter.outputs.admin_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: Install Dependencies
        run: npm ci
        working-directory: ./admin
      - name: Run Vulnerability Audit
        run: npm run audit
        working-directory: ./admin
      - name: Lint
        run: npm run lint
        working-directory: ./admin
      - name: Typecheck
        run: npm run typecheck
        working-directory: ./admin
      - name: Run Tests
        run: npm test
        working-directory: ./admin

  player-tests:
    needs: filter
    if: needs.filter.outputs.player_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: Install Dependencies
        run: npm ci
        working-directory: ./player
      - name: Run Vulnerability Audit
        run: npm run audit
        working-directory: ./player
      - name: Lint
        run: npm run lint
        working-directory: ./player
      - name: Typecheck
        run: npm run typecheck
        working-directory: ./player
      - name: Run Tests
        run: npm test
        working-directory: ./player

  player-worker-tests:
    needs: filter
    if: needs.filter.outputs.player_worker_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
      - name: Install Dependencies
        run: npm ci
        working-directory: ./player/cloudflare-worker
      - name: Run Vulnerability Audit
        run: npm run audit
        working-directory: ./player/cloudflare-worker
      - name: Copy Wrangler config
        run: cp wrangler.toml.sample wrangler.toml
        working-directory: ./player/cloudflare-worker
      - name: Generate runtime types
        run: npm run types
        working-directory: ./player/cloudflare-worker
      - name: Typecheck
        run: npm run typecheck
        working-directory: ./player/cloudflare-worker
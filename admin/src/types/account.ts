export interface Account {
	_id: string;
	companyName: string;
	companyLogo: string;
	companyURL: string;
	platform: string;
	defaultCollectionId: string;
	ownerUserId: string;
	snippetImpressionDates: {
		latest: number | null;
		lastSubmitted?: number | null;
	};
	dailyMetricsUpdatedAt: number | null;
	totalVideosCreatedDaily: number | null;
	totalVideoImpressionsDaily: number | null;
	totalCollectionsCreatedDaily: number | null;
	totalVideoPlaysDaily: number | null;
	totalPlaytimeDaily: number | null;
	totalVideoCTAClicksDaily: number | null;
	totalConversionDaily: number | null;
	weeklyMetricsUpdatedAt: number | null;
	totalVideosCreatedWeekly: number | null;
	totalCollectionsCreatedWeekly: number | null;
	totalVideoPlaysWeekly: number | null;
	totalPlaytimeWeekly: number | null;
	totalConversionWeekly: number | null;
	dailySyncUpdatedAt: number | null;
	createdAt: number;
	stripeCustomerId: string;
	invoices: AccountInvoices[];
	subscription: AccountSubscription;
	totalImpressionsCurrentCycle: number;
}

export interface AccountSubscription {
	allowLandscape: boolean;
	allowThemes: boolean;
	allowSharing: boolean;
	stripeSubscriptionId: string;
	stripeProductId: string;
	stripePriceId: string;
	hideVanityBranding: boolean;
	type: string;
	enableConversionMetrics: boolean;
	enableEngagementMetrics: boolean;
	maxClicksMetricPerMonth: number;
	maxImpressionsPerCycle: number;
	maxInteractiveCollectionLimit: number;
	maxInteractiveVideoLimit: number;
	maxPlaysMetricPerMonth: number;
	maxVideoProductLinksLimit: number;
	hasPaymentMethod: boolean;
	firstPaymentDate: number | null;
	lastPaymentDate: number | null;
	trialActive: boolean;
	trialAvailable: boolean;
	trialStartDate: number | null;
	trialEndDate: number | null;
	trialDaysTotal: number;
	pendingChangeDate: number | null;
	pendingChangePriceId: string | null;
	pendingChangeProductId: string | null;
	nextBillingDate: number | null;
	clockTime: number | null;
	price: number;
	allowCTALead?: boolean;
}

export interface AccountInvoices {
	id: string;
	amountDue: number;
	created: number;
	currency: string;
	dueDate: number;
	periodEnd: number;
	periodStart: number;
	status: string;
	pdfInvoiceURL: string;
	hostedInvoiceURL: string;
}

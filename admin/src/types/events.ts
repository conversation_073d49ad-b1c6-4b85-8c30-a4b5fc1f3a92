export enum EventNameEnum {
	CREATE_ACCOUNT_IMPRESSION = "create_account_impression",
	ACCEPT_TERMS_PRESS = "accept_terms_press",
	CREATE_ACCOUNT_PRESS = "create_account_press",
	VERIFY_ACCOUNT_IMPRESSION = "verify_account_impression",
	COPY_SCRIPT_PRESS = "copy_script_press",
	CONTINUE_SCRIPT_PRESS = "continue_script_press",
	INSTALLATION_CHECK = "installation_check",
	SKIP_SCRIPT_PRESS = "skip_script_press",
	CREATE_VIDEO_PRESS = "create_video_press",
	CREATE_VIDEO_MAIN_PRESS = "create_video_main_press",
	GENERATE_EMBED_PRESS = "generate_embed_press",
	COPY_EMBED_PRESS = "copy_embed_press",
	VIDEO_SHARE_LINK_COPY = "video_share_link_copy",
	COLLECTION_SHARE_LINK_COPY = "collection_share_link_copy",
	VIDEO_EMBED_COPY_THUMBNAIL = "video_embed_copy_thumbnail"
}

interface IUser {
	sessionId?: string;
	fingerprint?: string;
	dbUserId?: string;
}

export interface IDevice {
	type?: "Macintosh" | "PC" | "iPhone" | "Android";
	osName?: "MacOS" | "iOS" | "iPadOS" | "Windows" | "Linux" | "Android" | undefined;
	osVersion?: string;
}

export interface IEventDimensions {
	browserCurrentURL?: string;
	browserReferrerURL?: string;
	browserName?: string;
	browserVersion?: string;
	browserLocale?: string;
	browserPixelWidth?: number;
	browserPixelHeight?: number;
	browserUserAgent?: string;
	collectionId?: string;
	videoId?: string;
	videoSecondsLength?: number;
	videoSecondsPosition?: number;
	productActionedURL?: string;
}

export interface IInstallation {
	url: string;
	success: boolean;
	error: boolean;
}

export interface IEventPayload {
	eventName: EventNameEnum;
	appId: "gp-admin";
	accountId?: string;
	user: IUser;
	device: IDevice;
	installation?: IInstallation;
	eventDimensions: IEventDimensions;
}

export interface IEvent {
	eventName: EventNameEnum;
	videoId?: string;
	collectionId?: string;
	installationUrl?: string;
	installationSuccess?: boolean;
	installationError?: boolean;
}

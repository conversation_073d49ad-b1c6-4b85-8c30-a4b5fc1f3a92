import { ProductTypeEnum } from "./product";
import { CaptionData } from "./caption";

export enum TabMode {
	FROM_VIDEO = "1",
	UPLOAD = "2",
	FROM_LINK = "3"
}

export enum SettingsTab {
	GENERAL = "General",
	INSTALLATION = "Installation",
	BILLING = "Billing"
}

export enum DisplayPreferencesTab {
	CLOSEALL = "Close All",
	GENERAL = "General",
	CAROUSEL = "Carousel",
	WIDGET = "Widget",
	INLINE = "Inline"
}

export interface shoppableVideoData {
	shoppableVideo: shoppableVideo;
}

export interface shoppableVideo {
	_id: string;
	accountId?: string;
	collectionId?: string;
	createdAt: number;
	title?: string;
	description?: string;
	videoURL?: string;
	videoPosterURL?: string;
	gifURL?: string;
	videoPosterPlayEmbedURL?: string;
	products?: Products[];
	updatedAt: number;
	ctaText?: string;
	showTitle: boolean;
	linkClicks?: LinkClicks[];
	playPercentCount100?: number;
	playPercentCount80?: number;
	playPercentCount60?: number;
	playPercentCount40?: number;
	playPercentCount20?: number;
	videoDisplayMode?: string;
	videoPlayCount?: number;
	videoPlayDurationSeconds?: number;
	videoScore?: number;
	videoTotalSeconds?: number;
	likes?: number;
	phonePressCount?: number;
	emailSubmitCount?: number;
	captionData?: CaptionData | null;
}

export interface LinkClicks {
	productId: string;
	productImageURL: string;
	productTitle: string;
	productURL: string;
	clickCount: number;
}

export interface Products {
	_id?: string;
	title?: string;
	url?: string;
	productThumbnail?: string;
	subTitle?: string;
}

export interface FileInput {
	_id?: string;
	productTitle: string;
	productImageFile?: File;
	productURL: string;
	imagePreview?: string;
	imageURL?: string;
	fetchingProduct?: boolean;
	showProduct?: boolean;
	hovering?: boolean;
	subTitle?: string;
}

export interface accountToken {
	account: {
		_id: string;
		companyName?: string;
		companyLogo?: string;
	};
	authenticationId?: string;
	userId?: string;
	passwordRequiredToWrite?: boolean;
	tier?: {
		tierId?: string;
		name?: string;
		type?: ProductTypeEnum;
	};
}

export interface accessToken {
	type: string;
	authenticationId: string;
	userId: string;
	super: boolean;
	iat: number;
	exp: number;
	firstName: string;
}

export interface refreshToken {
	type: string;
	authenticationId: string;
	iat: number;
	exp: number;
}

export interface companyDetails {
	email?: string;
	firstName?: string;
	lastName?: string;
	companyName?: string;
	companyLogo?: string;
	companyURL?: string;
	type?: ProductTypeEnum;
	vanityBranding?: boolean;
	isAccountOwner?: boolean;
	isPasswordSet?: boolean;
}

export interface companyPage {
	email?: string;
	firstName?: string;
	lastName?: string;
	accounts?: Accounts[];
	postSignupCompleted?: boolean;
}

export interface accountsList {
	accounts?: Accounts[];
}

export interface Accounts {
	_id: string;
	companyName?: string;
	createdAt?: number;
	updatedAt?: number;
	defaultCollectionId?: string;
	companyURL?: string;
	companyLogo?: string;
	ownerUserId?: string;
}

export enum SearchType {
	SEARCH_PREFIX = "SEARCH_PREFIX",
	SEARCH_ALL_NUMBER = "SEARCH_ALL_NUMBER",
	SEARCH_ALL_SPECIAL = "SEARCH_ALL_SPECIAL"
}

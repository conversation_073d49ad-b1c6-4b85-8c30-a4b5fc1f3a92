export interface Invitation {
	_id: string;
	accountId?: string;
	createdAt: number;
	email?: string;
	status?: string;
	updatedAt: number;
	account?: Account[];
}

export enum InvitationStatus {
	OWNER = "owner",
	ACTIVE = "active",
	PENDING = "pending",
	DECLINED = "declined"
}

export interface Account {
	_id: string;
	companyLogo?: string;
	companyName?: string;
	createdAt?: number;
	defaultCollectionId?: string;
	ownerUserId?: string;
	updatedAt?: number;
	__v: number;
}

export interface InviteToken {
	invitationId: string;
	accountId: string;
	email: string;
	companyName: string;
	userExists: boolean;
	iat: number;
}

import { FileInput } from "@src/types/videos";
import { useTranslation } from "@src/components/hooks/translations";

export const validateVideoFields = (
	videoTitleValue: string,
	uploadFileURL: string,
	translation: ReturnType<typeof useTranslation>,
	productsInputs?: FileInput[]
): string | null => {

	if (!videoTitleValue) {
		return translation.errors.addVideoTitle;
	}
	if (!uploadFileURL) {
		return translation.errors.uploadVideo;
	}
	if (productsInputs?.some((i) => i.productURL && !i.productTitle)) {
		return translation.errors.addLinkTitle;
	}

	return null;
};

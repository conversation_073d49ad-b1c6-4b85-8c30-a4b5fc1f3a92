export function formatDate(date: Date) {
	const day = String(date.getDate()).padStart(2, "0");
	const month = String(date.getMonth() + 1).padStart(2, "0");
	const year = String(date.getFullYear()).substr(2);
	return `${month}/${day}/${year}`;
}

export function formatTimestamp(timestamp: number) {
	const months = [
		"January",
		"February",
		"March",
		"April",
		"May",
		"June",
		"July",
		"August",
		"September",
		"October",
		"November",
		"December"
	];
	const date = new Date(timestamp);
	const year = date.getFullYear();
	const month = months[date.getMonth()];
	const day = date.getDate();
	return `${month} ${day}, ${year}`;
}

const requiredEnvVariables = {
	API_ENDPOINT: process.env.API_ENDPOINT,
	CMS_ENDPOINT: process.env.CMS_ENDPOINT,
	APP_ENDPOINT: process.env.APP_ENDPOINT,
	SHARE_ENDPOINT: process.env.SHARE_ENDPOINT,
	API_VERSION: process.env.API_VERSION,
	STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
	OIDC_INTUIT_AUTH_URL: process.env.OIDC_INTUIT_AUTH_URL,
	OIDC_INTUIT_CLIENT_ID: process.env.OIDC_INTUIT_CLIENT_ID,
	OIDC_INTUIT_REDIRECT_URI: process.env.OIDC_INTUIT_REDIRECT_URI,
	OIDC_INTUIT_PROMPT: process.env.OIDC_INTUIT_PROMPT,
	OIDC_INTUIT_PROVIDER: process.env.OIDC_INTUIT_PROVIDER,
	OIDC_INTUIT_EMAIL_DOMAIN: process.env.OIDC_INTUIT_EMAIL_DOMAIN,
	GP_CDN_ENDPOINT: process.env.GP_CDN_ENDPOINT
};

export const validateEnvVariables = (): void => {
	const missingVariables = Object.entries(requiredEnvVariables).filter(([, value]) => {
		return value === undefined || value === "";
	});

	if (missingVariables.length > 0) {
		const missingKeys = missingVariables.map(([key]) => key);
		console.error(`Missing the following environment variables: ${missingKeys.join(", ")}`);
		throw new Error("Required environment variables are missing. Please check your .env file.");
	}
};

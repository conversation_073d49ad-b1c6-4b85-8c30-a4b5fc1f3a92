import React, { useState } from "react";
import { renderHook } from "@testing-library/react";
import { validateVideoFields } from "@src/utils/validation";
import { FileInput } from "@src/types/videos";
import { useTranslation } from "@src/components/hooks/translations";
import { LanguageContext } from "@src/components/contexts/LanguageContext";

const LanguageProviderWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [language, changeLanguage] = useState("en");
	return (
		<LanguageContext.Provider value={{ language, changeLanguage }}>
			{children}
		</LanguageContext.Provider>
	);
};

describe("validateVideoFields", () => {
	let translation: ReturnType<typeof useTranslation>;

	beforeEach(() => {
		const { result } = renderHook(() => useTranslation(), { wrapper: LanguageProviderWrapper });
		translation = result.current;
	});

	it("should return an error if videoTitleValue is empty", () => {
		const result = validateVideoFields("", "some-url.mp4", translation);
		expect(result).toBe(translation.errors.addVideoTitle);
	});

	it("should return an error if uploadFileURL is empty", () => {
		const result = validateVideoFields("Sample Title", "", translation);
		expect(result).toBe(translation.errors.uploadVideo);
	});

	it("should return an error if a product has a URL but no title", () => {
		const products: FileInput[] = [{ productURL: "https://example.com", productTitle: "" }];
		const result = validateVideoFields("Sample Title", "some-url.mp4", translation, products);
		expect(result).toBe(translation.errors.addLinkTitle);
	});

	it("should return null if all fields are valid", () => {
		const products: FileInput[] = [{ productURL: "https://example.com", productTitle: "Product 1" }];
		const result = validateVideoFields("Sample Title", "some-url.mp4", translation, products);
		expect(result).toBeNull();
	});
});

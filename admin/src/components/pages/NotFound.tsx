import React from "react";
import { useTranslation } from "../hooks/translations";
import { <PERSON><PERSON>utton, HttpError<PERSON><PERSON>r, HttpError<PERSON>ode, HttpErrorTitle, HttpErrorMessage } from "@src/styles/components";
import { useNavigate } from "react-router-dom";

const NotFound: React.FC = () => {
	const translation = useTranslation();
	const navigate = useNavigate();

	return (
		<HttpErrorContainer>
			<HttpErrorCode>{translation.notFoundPage.errorCode}</HttpErrorCode>
			<HttpErrorTitle>{translation.notFoundPage.errorTitle}</HttpErrorTitle>
			<HttpErrorMessage className="mt-2 mb-2">{translation.notFoundPage.errorMessage}</HttpErrorMessage>
			<MainButton type="button" className="mt-3" data-testid="signInRedirect" onClick={() => navigate("/sign-in/password")}>
				{translation.general.signIn}
			</MainButton>
		</HttpErrorContainer>
	);
};

export default NotFound;

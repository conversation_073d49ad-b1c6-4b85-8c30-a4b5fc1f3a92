import React, { useEffect } from "react";
import { Container, Col } from "react-bootstrap";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Footer from "./Footer";
import ImageContentPanel from "@src/components/authentication/ImageContentPanel";
import { verifyEmailBackgroundImage } from "@src/assets";
import { FlexCol, FullHeightRow } from "@src/styles/components";
import ConfirmEmailSignIn from "../authentication/ConfirmEmailSignIn";

const VerifyEmail: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.createAccountPage.verifyEmail;
	}, [translation.createAccountPage.verifyEmail]);

	return (
		<Container fluid>
			<FullHeightRow pl="0" pr="0">
				<Col sm="12" md={{ span: 8 }} style={{ paddingLeft: 0, paddingRight: 0 }}>
					<ImageContentPanel title={translation.createAccountPage.emailConfirmedTitle} copy={translation.createAccountPage.emailConfirmedCopy} backgroundUrl={verifyEmailBackgroundImage} />
				</Col>
				<FlexCol sm="12" md={{ span: 4 }} style={{ position: "relative" }}>
					<Header auth={false} />
					<ConfirmEmailSignIn />
					<Footer />
				</FlexCol>
			</FullHeightRow>
		</Container>
	);
};

export default VerifyEmail;

import React, { createContext, useState } from "react";

export const LanguageContext = createContext({
	language: "en",
	// Remove below comment after changeLanguage is implemented
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	changeLanguage: (_value: string) => {
		/* TO BE IMPLEMENTED */
	}
});

interface Props {
	children?: React.ReactNode;
}

export const LanguageContextProvider = ({ children }: Props) => {
	const [language, changeLanguage] = useState("en");

	return <LanguageContext.Provider value={{ language, changeLanguage }}>{children}</LanguageContext.Provider>;
};

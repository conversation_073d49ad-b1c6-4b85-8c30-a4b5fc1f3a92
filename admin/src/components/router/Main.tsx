import React from "react";
import { BrowserRouter, Route, Routes, Navigate } from "react-router-dom";
import AuthLink from "../authentication/AuthLink";
import { AppRoute, routes } from "./AppRoute";

export const Main: React.FC = () => {
	return (
		<AuthLink>
			<BrowserRouter>
				<Routes>
					{/* Redirect from /sign-in to /sign-in/email */}
					<Route path="/sign-in" element={<Navigate to="/sign-in/email" replace />} />

					{routes.map(({ isPublic = false, path, element, displayName }) => (
						<Route
							key={path}
							path={`/${path}`}
							element={<AppRoute Component={element} displayName={displayName} isPublic={isPublic} />}
						/>
					))}
				</Routes>
			</BrowserRouter>
		</AuthLink>
	);
};

import React, { useRef, useEffect, RefObject, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { BodyText, XmarkIcon, CloneIconDiv, CloneIcon } from "@src/styles/forms";
import { Row, Col } from "react-bootstrap";
import {
	Flex,
	TabTitle,
	TabTitleImg,
	MainButton,
	LinkButton,
	ButtonIcon,
	PreviewGif,
	LinkSectionModal,
	ThirdButton,
	CustomHighlight,
	HighlightCodeModal,
	CodeSnippet,
	TitleText,
	SpanColor
} from "@src/styles/components";
import { LinkIconBlack, VideoLibraryIcon, AddToSiteIcon, ShareIcon, EmailIcon, LockIconBlack } from "@src/assets";
import { ShareObject, ShareTabMode, ShareType } from "@src/types/share";
import { useMediaQuery } from "react-responsive";
import { copyHtmlToClipboard, copylinkURL } from "@src/utils/clipboard";

interface Props {
	visible: boolean;
	allowSharing: boolean;
	onCancel: () => void;
	shareObject: ShareObject;
	saveChanges?: boolean;
	setModalStatus?: (value: boolean) => void;
	setNavigationUrl?: (value: string) => void;
}

const useOutsideClick = (ref: RefObject<HTMLElement>, callback: () => void) => {
	const handleClickOutside = useCallback(
		(event: MouseEvent) => {
			if (ref.current && !ref.current.contains(event.target as Node)) {
				callback();
			}
		},
		[ref, callback]
	);

	useEffect(() => {
		document.addEventListener("mouseup", handleClickOutside);
		return () => {
			document.removeEventListener("mouseup", handleClickOutside);
		};
	}, [handleClickOutside]);
};

export const ShareModal: React.FC<Props> = ({ visible, allowSharing, onCancel, shareObject, saveChanges, setModalStatus, setNavigationUrl }) => {
	const SHARE_ENDPOINT = process.env.SHARE_ENDPOINT;
	const translation = useTranslation();
	const [selectedOption, setSelectedOption] = useState(ShareTabMode.ADD_TO_SITE);
	const [embedScriptString, setEmbedScriptString] = useState("");
	const [linkURL, setLinkURL] = useState("");
	const wrapperRef = useRef(null);
	const navigate = useNavigate();
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const embedScript = `<script src='${process.env.APP_ENDPOINT}/core.js' defer></script>`;

	// Close the dropdown if clicked outside
	useOutsideClick(wrapperRef, onCancel);

	const closeModal = () => {
		onCancel();
	};

	const handleClick = (shareTab: ShareTabMode) => {
		if (allowSharing === true) {
			setSelectedOption(shareTab);
		} else {
			if (saveChanges && setNavigationUrl && setModalStatus) {
				closeModal();
				setNavigationUrl("/plans-pricing");
				setModalStatus(true);
			} else {
				navigate("/plans-pricing");
			}
		}
	};

	useEffect(() => {
		if (shareObject.type === ShareType.SHARE_COLLECTION) {
			setLinkURL(SHARE_ENDPOINT + "/c/" + shareObject.collectionId);
			setSelectedOption(ShareTabMode.SHARE);
		} else {
			setLinkURL(SHARE_ENDPOINT + "/v/" + shareObject.videoId);
			setSelectedOption(ShareTabMode.ADD_TO_SITE);
			const scriptStr =
				"<div \n data-gp-video='" +
				shareObject.videoId +
				"' \n data-gp-type='player' \n data-video-width='338' \n data-video-height='600' \n></div>";
			setEmbedScriptString(scriptStr);
		}
	}, [SHARE_ENDPOINT, shareObject]);

	return (
		<BaseModal
			modalStyle={{ maxHeight: "42rem", width: "100%" }}
			visible={visible}
			header={
				shareObject.type === ShareType.SHARE_COLLECTION
					? translation.modals.shareYourCollection
					: translation.modals.shareYourVideo
			}
			wrapperRef={wrapperRef}
		>
			{!isScreenSmall ? (
				<LinkButton
					data-testid="closeButton"
					onClick={closeModal}
					style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
				>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={closeModal}>
					<XmarkIcon style={{ marginTop: "-80px" }} />
				</LinkButton>
			)}
			<Flex>
				{shareObject.type !== ShareType.SHARE_COLLECTION && (
					<TabTitle
						show={selectedOption === ShareTabMode.ADD_TO_SITE ? true : false}
						onClick={() => setSelectedOption(ShareTabMode.ADD_TO_SITE)}
						style={{ marginRight: "10px" }}
					>
						<TabTitleImg
							show={selectedOption === ShareTabMode.ADD_TO_SITE ? true : false}
							src={AddToSiteIcon}
							alt={translation.modals.addToSite}
							data-testid="addToSite"
						/>
						{translation.modals.addToSite}
					</TabTitle>
				)}
				<TabTitle
					show={selectedOption === ShareTabMode.SHARE ? true : false}
					onClick={() => handleClick(ShareTabMode.SHARE)}
					style={{ marginRight: "10px" }}
				>
					<TabTitleImg
						show={selectedOption === ShareTabMode.SHARE ? true : false}
						src={allowSharing === true ? ShareIcon : LockIconBlack}
						alt={translation.modals.share}
						data-testid="shareLink"
					/>
					{translation.modals.share}
				</TabTitle>
				<TabTitle
					show={selectedOption === ShareTabMode.EMAIL_EMBED ? true : false}
					onClick={() => handleClick(ShareTabMode.EMAIL_EMBED)}
				>
					<TabTitleImg
						show={selectedOption === ShareTabMode.EMAIL_EMBED ? true : false}
						src={allowSharing === true ? EmailIcon : LockIconBlack}
						alt={translation.modals.emailEmbed}
						data-testid="emailEmbedLink"
					/>
					{translation.modals.emailEmbed}
				</TabTitle>
			</Flex>

			{selectedOption === ShareTabMode.ADD_TO_SITE && shareObject.type !== ShareType.SHARE_COLLECTION && (
				<div className="p-3 mt-3">
					<TitleText>
						<SpanColor>1. </SpanColor>
						{translation.modals.addStep1}
					</TitleText>
					<BodyText>{translation.modals.addStep1Text}</BodyText>
					<div style={{ position: "relative" }}>
						<CloneIconDiv style={{ width: "100%", position: "absolute", pointerEvents: "none" }}>
							<CloneIcon
								data-testid="CloneIcon"
								style={{ float: "right", marginTop: "9px", marginRight: "9px", pointerEvents: "auto" }}
								onClick={() => {
									navigator.clipboard.writeText(embedScript);
								}}
							/>
						</CloneIconDiv>
						<CodeSnippet>
							<CustomHighlight data-testid="highlightBox">
								<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
									{embedScript}
								</HighlightCodeModal>
							</CustomHighlight>
						</CodeSnippet>
					</div>
					<TitleText className="mt-3 mb-3">
						<SpanColor>2. </SpanColor>
						{translation.modals.embedVideo}
					</TitleText>
					<div style={{ position: "relative" }}>
						<CloneIconDiv style={{ width: "100%", position: "absolute" }}>
							<CloneIcon
								data-testid="CloneIcon"
								style={{ float: "right" }}
								onClick={() => {
									navigator.clipboard.writeText(embedScriptString);
								}}
							/>
						</CloneIconDiv>
						<CodeSnippet>
							<CustomHighlight data-testid="highlightBox">
								<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
									{embedScriptString}
								</HighlightCodeModal>
							</CustomHighlight>
						</CodeSnippet>
					</div>
					<BodyText className="mt-3">{translation.modals.embedVideoSize}</BodyText>
				</div>
			)}

			{selectedOption === ShareTabMode.SHARE && (
				<LinkSectionModal className="pt-2 pb-2">
					<BodyText className="text-center">{translation.modals.copyLinkText}</BodyText>
					<div style={{ position: "relative" }}>
						<CloneIconDiv style={{ width: "100%", position: "absolute", pointerEvents: "none" }}>
							<CloneIcon
								data-testid="CloneIcon"
								style={{ float: "right", marginTop: "9px", marginRight: "9px", pointerEvents: "auto" }}
								onClick={() => copylinkURL(shareObject, linkURL)}
							/>
						</CloneIconDiv>
						<CodeSnippet>
							<CustomHighlight data-testid="highlightBox" style={{ height: "22px" }}>
								<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
									{linkURL.replace("https://", "")}
								</HighlightCodeModal>
							</CustomHighlight>
						</CodeSnippet>
					</div>
					<ThirdButton
						type="button"
						className="mx-auto mt-3"
						greyColor={true}
						onClick={() => copylinkURL(shareObject, linkURL)}
					>
						<TabTitleImg src={LinkIconBlack} alt={translation.modals.copyLink} />
						{translation.modals.copyLink}
					</ThirdButton>
				</LinkSectionModal>
			)}

			{selectedOption === ShareTabMode.EMAIL_EMBED && (
				<LinkSectionModal className="pt-2 pb-2">
					<Row pl="0" pr="0">
						<Col sm="12" md="6" className="mb-4">
							<PreviewGif src={shareObject.gifURL} alt="GIF" />
						</Col>
						<Col sm="12" md="6" className="mb-4">
							<BodyText className="text-center">{translation.modals.copyThumbnailText}</BodyText>
							<MainButton
								type="button"
								data-testid="copyThumbnailText"
								className="mt-4"
								onClick={() => copyHtmlToClipboard(shareObject)}
								greyColor={true}
							>
								<ButtonIcon src={VideoLibraryIcon} alt={translation.modals.copyThumbnail} />
								{translation.modals.copyThumbnail}
							</MainButton>
						</Col>
					</Row>
				</LinkSectionModal>
			)}
		</BaseModal>
	);
};

import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	onCancel: () => void;
}

export const CheckEmailModal: React.FC<Props> = ({ visible, onCancel }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header={translation.modals.checkEmailTitle} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.checkEmailText}</ModalText>
			<>
				<MainButton type="button" onClick={onCancel} className="mx-auto mt-3" data-testid="ModalGoBack">
					{translation.general.close}
				</MainButton>
			</>
		</BaseModal>
	);
};

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>rapper, <PERSON><PERSON><PERSON><PERSON><PERSON>rapper, <PERSON><PERSON><PERSON>eader, ModalContent } from "@src/styles/modals";

export type BaseModalProps = {
	visible: boolean;
	modalStyle?: React.CSSProperties;
	contentStyle?: React.CSSProperties;
	children?: React.ReactNode;
	header?: React.ReactNode;
	className?: string;
	wrapperRef?: ((instance: HTMLDivElement | null) => void) | React.RefObject<HTMLDivElement> | null;
	leftAlignedHeader?: boolean;
	hideHeader?: boolean;
	wide?: boolean;
};

export const BaseModal: React.FC<BaseModalProps> = ({ visible, modalStyle, contentStyle, children, header, className, wrapperRef, leftAlignedHeader, hideHeader = false, wide = false }) => {
	if (!visible) return null;
	return (
		<ModalContainer>
			<ModalOuterWrapper>
				<ModalInnerWrapper wide={wide} data-testid="modal" style={modalStyle} className={className} ref={wrapperRef}>
					<ModalHeader data-testid="ModalHeaderText" style={{ textAlign: leftAlignedHeader ? "left" : "center" }} hidden={hideHeader}>
						{header}
					</ModalHeader>
					<ModalContent style={contentStyle}>{children}</ModalContent>
				</ModalInnerWrapper>
			</ModalOuterWrapper>
		</ModalContainer>
	);
};

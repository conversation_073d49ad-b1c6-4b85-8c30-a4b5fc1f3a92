import React, { useRef, useEffect, RefObject } from "react";
import { useRecoilState } from "recoil";
import { useTranslation } from "../hooks/translations";
import { isLoggedInState } from "../authentication/state";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	onCancel: () => void;
}

export const SignOutModal: React.FC<Props> = ({ visible, onCancel }) => {
	const translation = useTranslation();
	const [, setIsLoggedIn] = useRecoilState(isLoggedInState);
	const handleLogout = () => {
		localStorage.removeItem("accessToken");
		localStorage.removeItem("refreshToken");
		localStorage.removeItem("userHash");
		localStorage.removeItem("postSignup");
		localStorage.removeItem("oidcClientId");
		sessionStorage.removeItem("token");
		setIsLoggedIn(false);
	};

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header={translation.general.signOut + "?"} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.signOutModalText}</ModalText>
			<>
				<MainButton type="button" onClick={() => handleLogout()} className="mx-auto mt-3" data-testid="SignOutButton">
					{translation.general.signOut}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink">
					{translation.general.cancel}
				</ThirdButton>
			</>
		</BaseModal>
	);
};

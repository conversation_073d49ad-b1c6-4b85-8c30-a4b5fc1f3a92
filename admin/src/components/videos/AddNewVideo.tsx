/* eslint-disable max-lines */
import React, {
	useRef,
	useState,
	useEffect
} from "react";
import { Form, Row, Col } from "react-bootstrap";
import { useDropzone } from "react-dropzone";
import { useNavigate } from "react-router-dom";
import ProgressBar from "react-bootstrap/ProgressBar";
import Spinner from "react-bootstrap/Spinner";
import {
	FlexSpace,
	CustomSwitch,
	TooltipWrapper,
	PageBody,
	PageRow,
	MainButton,
	VideoPageTitleText,
	FileDropBox,
	NoFileBox,
	TableBox,
	VideoPlayerText,
	CoverImageBox,
	NoImageFileBox,
	UploadImage,
	ProductsBox,
	ProductImage,
	UploadProductImage,
	CustomInputBox,
	FetchingProductText,
	SortIcon,
	VideoPerformance,
	VideoWrapper,
	FlexSwitchRow,
	FlexSwitchCol,
	PortraitBox,
	LandscapeBox,
	Flex,
	ImageButton,
	VideoCaptionsText
} from "@src/styles/components";
import {
	HeadingText,
	ConfirmationBoxWrapper,
	BodyText,
	CustomInput,
	CustomTextarea,
	CircleXmarkIcon,
	ImageIcon
} from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../hooks/getErrorString";
import addVideo from "../hooks/addVideo";
import { useTokenCheck } from "../hooks/useTokenCheck";
import {
	FileInput,
	companyDetails
} from "@src/types/videos";
import { CaptionData } from "@src/types/caption";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import parseProductUrl from "../hooks/parseProductUrl";
import {
	UploadIcon,
	OpenLockIcon,
	LinkIconBlack,
	EmailIconBlack,
	PhoneIconBlack,
	CCFillIcon
} from "@src/assets";
import {
	CustomSelectItem,
	CustomSelectOption,
	CustomSelectStyles
} from "../inputs/CustomSelect";
import Select, {
	components,
	OnChangeValue
} from "react-select";
import getCollections from "../hooks/getCollections";
import { ShoppableCollection } from "@src/types/collections";
import updateCollection from "../hooks/updateCollection";
import { ConfirmLeavingModal } from "../modals/ConfirmLeavingModal";
import Lottie from "lottie-react";
import { LoadingScreenAnimation } from "../../assets/lottie_animations/loading-screen";
import { CoverImageModal } from "../modals/CoverImageModal";
import { VideoUploadModal } from "../modals/VideoUploadModal";
import { EnterPasswordModal } from "../modals/EnterPasswordModal";
import { usePasswordRequired } from "../hooks/getPasswordRequired";
import addCollection from "../hooks/addCollection";
import optimizeImage from "../hooks/optimizeImage";
import uploadFile from "../hooks/uploadFile";
import {
	Video,
	JobVideoStatus,
	VideoEncodeResponse
} from "@src/types/files";
import uploadVideoFile from "../hooks/uploadVideoFile";
import getVideoFile from "../hooks/getVideoFile";
import getVideoJobStatus from "../hooks/getVideoJobStatus";
import uploadVideoURL from "../hooks/uploadVideoURL";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import VideoPreview from "./VideoPreview";
import {
	DisplayFormatOptions,
	VideoPropsProduct
} from "@src/types/snippetOptions";
import { getImageSize } from "@src/utils/compare";
import { AccountSubscription } from "@src/types/account";
import { useMask } from "@react-input/mask";
import { waitForNextStatusCheck } from "@src/utils/waitForNextStatusCheck";
import { validateVideoFields } from "@src/utils/validation";
import { CaptionsModal } from "../modals/CaptionsModal";

interface Props {
	userData?: companyDetails | undefined;
	subscription?: AccountSubscription | undefined;
	saveChanges: boolean;
	setSaveChanges: (value: boolean) => void;
	navigationUrl: string;
	setNavigationUrl: (value: string) => void;
	modalStatus: boolean;
	setModalStatus: (value: boolean) => void;
}

// eslint-disable-next-line max-lines-per-function
const AddNewVideo: React.FC<Props> = ({
	userData,
	subscription,
	saveChanges,
	setSaveChanges,
	navigationUrl,
	setNavigationUrl,
	modalStatus,
	setModalStatus
}) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [editVideoError, setEditVideoError] = useState("");
	const [uploadingStatus, setUploadingStatus] = useState(false);
	const [encodingVideo, setEncodingVideo] = useState(false);
	const [loading, setLoading] = useState(false);
	const [generateButton, setGenerateButton] = useState(true);
	const [uploadFileURL, setUploadFileURL] = useState("");
	const [videoTitleValue, setVideoTitleValue] = useState("");
	const [videoDescValue, setVideoDescValue] = useState("");
	const [videoDescriptionCount, setVideoDescriptionCount] = useState(0);
	const [videoButtonTextValue] = useState(translation.createVideoPage.videoButtonText);
	const [coverImage, setCoverImage] = useState<File>();
	const [coverImageURL, setCoverImageURL] = useState("");
	const [gifURL, setGifURL] = useState("");
	const [videoPosterPlayEmbedURL, setVideoPosterPlayEmbedURL] = useState("");
	const [imagePreview, setImagePreview] = useState("");
	const [coverImageStatus, setCoverImageStatus] = useState(false);
	const [productsInputs, setProductsInputs] = useState<FileInput[]>([]);
	const [emailAddress, setEmailAddress] = useState("");
	const [showEmailAddress, setShowEmailAddress] = useState(false);
	const [phoneNumber, setPhoneNumber] = useState("");
	const [showPhoneNumber, setShowPhoneNumber] = useState(false);
	const emailRef = useRef<HTMLInputElement>(null);
	const phoneRef = useMask({ mask: "(___) ___-____", replacement: { _: /\d/ } });
	const [now, setNow] = useState(0);
	const [allCollections, setAllCollections] = useState<ShoppableCollection[]>([]);
	const [defaultCollection, setDefaultCollection] = useState<ShoppableCollection>();
	const [unselectedCollections, setUnselectedCollections] = useState<CustomSelectOption[]>([]);
	const [selectedCollections, setSelectedCollections] = useState<ShoppableCollection[]>([]);
	const [newCollectionSelected, setNewCollectionSelected] = useState(false);
	const [displayTitle, setDisplayTitle] = useState(true);
	const [videoStatusText, setVideoStatusText] = useState("");
	const [showProgressBar, setShowProgressBar] = useState(true);
	const [videoModalStatus, setVideoModalStatus] = useState(false);
	const [goToPrevPage, setGoToPrevPage] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const [displayFormat, setDisplayFormat] = useState(DisplayFormatOptions.PORTRAIT);
	const [uploadFileID, setUploadFileID] = useState("");
	const [videoWidthPx, setVideoWidthPx] = useState(500);
	const [videoHeightPx, setVideoHeightPx] = useState(750);
	const [captionData, setCaptionData] = useState<CaptionData | undefined>();
	const [captionsModalStatus, setCaptionsModalStatus] = useState(false);

	const handleRemoveClick = (index: number) => {
		const list = [...productsInputs];
		list.splice(index, 1);
		setProductsInputs(list);
	};

	const handleAddClick = () => {
		const newInput = {
			productImageFile: undefined,
			imagePreview: "",
			productTitle: "",
			productURL: "",
			subTitle: undefined,
			hovering: false
		} as FileInput;
		const list = [...productsInputs];
		list.push(newInput);
		setProductsInputs(list);
	};

	const handleInputChange = (value: string, typeInput: string, index: number) => {
		const list = [...productsInputs];
		if (typeInput === "productTitle") list[index].productTitle = value;
		if (typeInput === "subTitle") list[index].subTitle = value;
		if (typeInput === "productURL") {
			list[index].productURL = value;
			if (!value) list[index].showProduct = false;
		}
		setProductsInputs(list);
	};

	const handleMouseOver = (index: number) => {
		const list = [...productsInputs];
		list[index].hovering = true;
		setProductsInputs(list);
	};

	const handleMouseLeave = (index: number) => {
		const list = [...productsInputs];
		list[index].hovering = false;
		setProductsInputs(list);
	};

	const handleDescChange = (value: string) => {
		setVideoDescValue(value);
		setVideoDescriptionCount(value.length);
	};

	const handleParseProductUrl = async (value: string, index: number) => {
		const list = [...productsInputs];
		const productURL = list[index].productURL;
		if (productURL) {
			if (!productURL.includes("https://")) list[index].productURL = `https://${productURL}`;
			list[index].fetchingProduct = true;
			list[index].showProduct = true;
			setProductsInputs(list);

			const { data } = await apiRetryHandler(async () => await parseProductUrl(value));
			if (data) {
				const list = [...productsInputs];
				list[index].productTitle = data.title;
				list[index].subTitle = data.price ?? undefined;
				const { data: optimizedImage, error: jobError } = await apiRetryHandler(
					async () => await optimizeImage(data.imageURL, 500)
				);
				if (jobError) {
					list[index].imagePreview = "";
					list[index].imageURL = "";
				} else {
					list[index].imagePreview = optimizedImage;
					list[index].imageURL = optimizedImage;
				}
				list[index].fetchingProduct = false;
				setProductsInputs(list);
			}
		} else {
			list[index].productTitle = "";
			list[index].imagePreview = "";
			list[index].imageURL = "";
			list[index].subTitle = undefined;
			setProductsInputs(list);
		}
	};

	const addShoppableVideo = async (password?: string) => {
		if (showEmailAddress && emailRef.current) {
			const emailInput = emailRef.current;
			const emailPattern = /[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,20}$/i;

			if (!emailPattern.test(emailInput.value)) {
				emailInput.classList.add("is-invalid");
				setEditVideoError(translation.errors.emailFormat);
				return;
			}

			emailInput.classList.remove("is-invalid");
		}

		if (showPhoneNumber && phoneRef.current) {
			const phoneInput = phoneRef.current;
			const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;

			if (!phonePattern.test(phoneInput.value)) {
				phoneInput.classList.add("is-invalid");
				setEditVideoError(translation.errors.phoneFormat);
				return;
			}

			phoneInput.classList.remove("is-invalid");
		}

		setGenerateButton(true);
		setLoading(true);
		let coverImageLink = coverImageURL;
		if (coverImage) {
			const { data: uploadData, error: uploadError } = await apiRetryHandler(
				async () => await uploadFile(coverImage as File)
			);
			if (!uploadError) {
				const { data: optimizedImage, error: jobError } = await apiRetryHandler(
					async () => await optimizeImage(uploadData.publicURL, videoWidthPx, videoHeightPx)
				);
				if (!jobError) {
					coverImageLink = optimizedImage;
				} else {
					setGenerateButton(false);
					setLoading(false);
					const errorText = getErrorString(translation, jobError?.message);
					setEditVideoError(errorText);
					return;
				}
			}
		}

		const { data, error } = await apiRetryHandler(
			async () =>
				await addVideo({
					videoTitleValue,
					videoDescValue,
					uploadFileID,
					videoButtonTextValue,
					displayFormat,
					coverImageLink,
					gifURL,
					videoPosterPlayEmbedURL,
					productsInputs,
					displayTitle,
					emailAddress,
					phoneNumber,
					password,
					captionData
				})
		);
		setGenerateButton(false);
		setLoading(false);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
		} else {
			registerEvent({
				eventName: EventNameEnum.GENERATE_EMBED_PRESS,
				videoId: data.shoppableVideo._id
			});

			if (data.shoppableVideo._id) {
				for (const selectedCollection of selectedCollections) {
					selectedCollection.shoppableVideos?.push(data.shoppableVideo._id);
					const { error: updateCollectionError } = await apiRetryHandler(
						async () =>
							await updateCollection(selectedCollection._id, selectedCollection.shoppableVideos ?? [], password)
					);
					if (updateCollectionError) {
						setGenerateButton(false);
						setLoading(false);
						const errorText = getErrorString(translation, updateCollectionError?.response?.data?.error);
						setEditVideoError(errorText);
						return;
					}
				}

				if (newCollectionSelected) {
					const { error: createCollectionError } = await apiRetryHandler(
						async () =>
							await addCollection({ collectionTitle: data.shoppableVideo.title, videos: [data.shoppableVideo], password })
					);
					if (createCollectionError) {
						setGenerateButton(false);
						setLoading(false);
						const errorText = getErrorString(translation, createCollectionError?.response?.data?.error);
						setEditVideoError(errorText);
						return;
					}
				}
			}

			navigate("/edit-video/" + data.shoppableVideo._id);
		}
	};

	const validateFields = () => {
		const validationError = validateVideoFields(
			videoTitleValue,
			uploadFileURL,
			translation,
			productsInputs
		);

		if (validationError) {
			setEditVideoError(validationError);
		}
	};

	const handleSave = () => {
		if (passwordRequired) {
			setEnterPassword(true);
		} else {
			addShoppableVideo();
		}
	};

	const setProgressBar = (val: number) => {
		setNow(val);
	};

	const submitVideoURL = async (url: string) => {
		const { data: videoData, error: getVideoError } = await apiRetryHandler(async () => await uploadVideoURL(url));
		if (getVideoError) {
			setEditVideoError(translation.errors.videoUrlError);
		} else {
			await checkVideoStatus(videoData);
		}
	};

	const checkVideoStatus = async (initialJobStatus: VideoEncodeResponse) => {
		setEncodingVideo(true);
		setVideoStatusText(translation.createVideoPage.encodingPrepStatus1);
		setShowProgressBar(false);
		let completed = false;
		let encodingStarted = false;
		const startEncodeTime = Date.now();

		while (!completed) {
			const { data: videoData, error: getVideoError } = await apiRetryHandler(
				async () => await getVideoJobStatus(initialJobStatus.tempFilename)
			);
			if (getVideoError) {
				const errorText = getErrorString(translation, getVideoError?.response?.data?.error);
				setEditVideoError(errorText);
				setEncodingVideo(false);
				break;
			} else {
				if (videoData?.status === JobVideoStatus.FAILED) {
					setEditVideoError("Video could not be encoded");
				}
				let progressPercent = Math.round(videoData?.progressPercent ?? 0);
				if (progressPercent < 0) progressPercent = 0;
				else if (progressPercent > 100) progressPercent = 100;
				setProgressBar(progressPercent);

				if (progressPercent > 0 && !encodingStarted) {
					setVideoStatusText(translation.createVideoPage.encodingStatus);
					encodingStarted = true;
					setShowProgressBar(true);
				} else if (progressPercent === 0) {
					if (Date.now() - startEncodeTime < 10 * 1000)
						setVideoStatusText(translation.createVideoPage.encodingPrepStatus1);
					else if (Date.now() - startEncodeTime < 20 * 1000)
						setVideoStatusText(translation.createVideoPage.encodingPrepStatus2);
					else setVideoStatusText(translation.createVideoPage.encodingPrepStatus3);
				}

				if (videoData?.status === JobVideoStatus.COMPLETE && videoData.videoId) {
					await encodeUploadVideo(videoData.videoId);
					setProgressBar(100);
					completed = true;
				} else {
					await waitForNextStatusCheck(videoData.nextStatusCheck);
				}
			}
		}
	};

	const encodeUploadVideo = async (videoId: string) => {
		let video: Video | null = null;

		const { data: videoData, error: getVideoError } = await apiRetryHandler(async () => await getVideoFile(videoId));
		if (getVideoError) {
			const errorText = getErrorString(translation, getVideoError?.response?.data?.error);
			setEditVideoError(errorText);
			setEncodingVideo(false);
		} else {
			video = videoData.video;
		}

		if (video !== null) {
			const publicPosterURL = video.publicPosterURL;
			setUploadFileURL(video.publicVideoURL);
			setGifURL(video.publicGifURL);
			setVideoPosterPlayEmbedURL(video.publicPosterPlayEmbedURL);
			setUploadFileID(video._id);
			setCaptionData(video.captionData);
			const { imageWidthPx, imageHeightPx } = getImageSize(video?.videoWidthPx, video?.videoHeightPx);
			setVideoWidthPx(imageWidthPx);
			setVideoHeightPx(imageHeightPx);
			const { data: optimizedImage, error: jobError } = await apiRetryHandler(
				async () => await optimizeImage(publicPosterURL, imageWidthPx, imageHeightPx)
			);
			if (jobError) {
				const errorText = getErrorString(translation, jobError?.message);
				setEditVideoError(errorText);
			} else {
				setImagePreview(optimizedImage);
				setCoverImageURL(optimizedImage);
			}
			setNow(0);
		}
		setEncodingVideo(false);
	};

	const setVideoFromLibrary = async (video: Video) => {
		setUploadFileURL(video.publicVideoURL);
		setGifURL(video.publicGifURL);
		setVideoPosterPlayEmbedURL(video.publicPosterPlayEmbedURL);
		setUploadFileID(video._id);
		const { imageWidthPx, imageHeightPx } = getImageSize(video?.videoWidthPx, video?.videoHeightPx);
		setVideoWidthPx(imageWidthPx);
		setVideoHeightPx(imageHeightPx);
		setImagePreview(video.publicPosterURL);
		setCoverImageURL(video.publicPosterURL);
		setCaptionData(video.captionData);
	};

	// video file upload
	const { getRootProps: getVideoProps, getInputProps: getVideoFileProps } = useDropzone({
		maxFiles: 1,
		accept: {
			"video/*": []
		},
		onDrop: (acceptedFiles) => {
			setVideoModalStatus(false);
			if (acceptedFiles.length && acceptedFiles[0]?.size < 512 * 1024 * 1024) {
				setEditVideoError("");
				(async () => {
					setUploadingStatus(true);
					setImagePreview("");
					setCoverImageURL("");
					setGifURL("");
					setVideoPosterPlayEmbedURL("");
					setVideoStatusText(translation.createVideoPage.uploadingStatus);
					const { data, error } = await apiRetryHandler(
						async () => await uploadVideoFile(acceptedFiles[0], setProgressBar)
					);

					if (error) {
						const errorText = getErrorString(translation, error?.response?.data?.error);
						setEditVideoError(errorText);
					} else {
						setNow(0);
					}
					setUploadingStatus(false);

					await checkVideoStatus(data);
				})();
			} else {
				if (!acceptedFiles.length) {
					setEditVideoError(translation.errors.videoDetailWrongFormat);
				} else {
					setEditVideoError(translation.errors.videoDetailFileSize);
				}
			}
		}
	});

	// Product images upload
	const onDrop = async (event: React.FormEvent<HTMLLabelElement>, index: number) => {
		const reader = new FileReader();
		const file = (event.target as HTMLInputElement)?.files?.[0];
		if (file) {
			const list = [...productsInputs];
			reader.onloadend = async () => {
				let imageLink = "";
				const { data: uploadData, error: uploadError } = await apiRetryHandler(
					async () => await uploadFile(file as File)
				);
				if (!uploadError) {
					const { data: optimizedImage, error: jobError } = await apiRetryHandler(
						async () => await optimizeImage(uploadData.publicURL, 500)
					);
					if (!jobError) {
						imageLink = optimizedImage;
					}
				}
				list[index].imageURL = imageLink;
				list[index].imagePreview = imageLink;
				setProductsInputs(list);
			};
			reader.readAsDataURL(file);
		}
	};

	useEffect(() => {
		const invalidLink = productsInputs.some((i) => i.productURL && !i.productTitle);
		if (
			!uploadingStatus &&
			!encodingVideo &&
			videoTitleValue &&
			uploadFileURL &&
			(coverImage || coverImageURL) &&
			!invalidLink &&
			videoButtonTextValue
		) {
			setGenerateButton(false);
		} else {
			setGenerateButton(true);
		}

		if (
			uploadingStatus ||
			encodingVideo ||
			!displayTitle ||
			videoTitleValue ||
			uploadFileURL ||
			coverImage ||
			coverImageURL ||
			invalidLink ||
			emailAddress ||
			phoneNumber ||
			videoButtonTextValue !== translation.createVideoPage.videoButtonText
		) {
			setSaveChanges(true);
		} else {
			setSaveChanges(false);
		}
	}, [
		displayTitle,
		videoTitleValue,
		uploadFileURL,
		coverImage,
		coverImageURL,
		productsInputs,
		videoButtonTextValue,
		setSaveChanges,
		translation,
		uploadingStatus,
		emailAddress,
		phoneNumber,
		encodingVideo
	]);

	useEffect(() => {
		(async () => {
			if (!allCollections.length) {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const { data: collectionsData, error: collectionsError }: {data: ShoppableCollection[]; error: any} =
					await apiRetryHandler(async () => await getCollections());
				if (collectionsError) {
					const errorText = getErrorString(translation, collectionsError?.response?.data?.error);
					setEditVideoError(errorText);
				} else if (collectionsData.length) {
					setAllCollections(collectionsData);
					setDefaultCollection(collectionsData[0]);
					setUnselectedCollections([
						...collectionsData
							.filter(
								(collection) =>
									collection._id !== collectionsData[0]._id &&
									!selectedCollections.some((col) => col._id === collection._id)
							)
							.map((collection) => {
								return { value: collection._id, label: collection.title };
							}),
						{ value: "new_collection", label: translation.createVideoPage.createCollectionText }
					]);
				}
			}
		})();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [apiRetryHandler, translation]);

	const handleCollectionSelect = (selectedOption: OnChangeValue<CustomSelectOption, false>) => {
		const selectedCollection = allCollections.find((collection) => collection._id === selectedOption?.value);
		if (selectedCollection) {
			const insertIndex = selectedCollections.findIndex(
				(collection) =>
					collection.title !== undefined &&
					selectedCollection.title !== undefined &&
					collection.title.localeCompare(selectedCollection.title) > 0
			);

			let newSelectedCollections;
			if (insertIndex !== -1) {
				newSelectedCollections = [
					...selectedCollections.slice(0, insertIndex),
					selectedCollection,
					...selectedCollections.slice(insertIndex)
				];
			} else {
				newSelectedCollections = [...selectedCollections, selectedCollection];
			}
			setSelectedCollections(newSelectedCollections);
			setUnselectedCollections(
				unselectedCollections.filter((collection: CustomSelectOption) => collection.value !== selectedCollection._id)
			);
		} else if (selectedOption?.value === "new_collection") {
			setUnselectedCollections(
				unselectedCollections.filter((collection: CustomSelectOption) => collection.value !== "new_collection")
			);
			setNewCollectionSelected(true);
		}
	};

	const handleCollectionRemove = (_id: string) => {
		const matchingCollection = allCollections.find((collection) => collection._id === _id);
		if (matchingCollection) {
			setSelectedCollections(selectedCollections.filter((collection) => collection._id !== _id));
			setUnselectedCollections([
				...unselectedCollections,
				{ value: matchingCollection._id, label: matchingCollection.title }
			]);
		}
	};

	const handleNewCollectionRemove = () => {
		setNewCollectionSelected(false);
		setUnselectedCollections([
			...unselectedCollections,
			{ value: "new_collection", label: translation.createVideoPage.createCollectionText }
		]);
	};

	const handlePlanClick = () => {
		if (saveChanges) {
			setNavigationUrl("/plans-pricing");
			setModalStatus(true);
		} else {
			navigate("/plans-pricing");
		}
	};

	const toggleCaptionsEnabled = () => {
		if (!captionData) return;

		const updated = { ...captionData, enabled: !captionData.enabled };
		setCaptionData(updated);
	};

	useEffect(() => {
		// Prevent navigation if changes are not saved
		if (saveChanges) window.history.pushState(null, "", window.location.pathname);

		// prompt before refresh page
		const handleBeforeUnload = (event: {preventDefault: () => void; returnValue: string}) => {
			if (saveChanges) {
				event.preventDefault();
				// Required for some browsers
				event.returnValue = "";
			}
		};

		const handlePopState = () => {
			if (saveChanges) {
				window.history.pushState(null, "", window.location.pathname);
				setModalStatus(true);
				setGoToPrevPage(true);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		window.addEventListener("popstate", handlePopState);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			window.removeEventListener("popstate", handlePopState);
		};
	}, [saveChanges, setModalStatus]);

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const CustomSingleValue: React.FC<any> = ({ ...props }) => (
		<components.SingleValue {...props}>
			{translation.createVideoPage.addToCollectionText}
			<SortIcon />
		</components.SingleValue>
	);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!editVideoError && (
					<ErrorMessage error={editVideoError} setError={setEditVideoError} displayCloseIcon={true} />
				)}
			</ConfirmationBoxWrapper>
			<PageBody>
				<PageRow className="mb-4" noJustify={true}>
					<HeadingText data-testid="createVideoPage">{translation.createVideoPage.shoppableVideo}</HeadingText>
					<MainButton
						type="button"
						data-testid="generateButton"
						inactive={generateButton}
						onClick={generateButton ? validateFields : handleSave}
						className="ms-auto"
					>
						{translation.createVideoPage.generate}
						{loading ? (
							<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
						) : (
							""
						)}
					</MainButton>
				</PageRow>

				<Row pl="0" pr="0">
					<Col sm="12" md="12" className="mb-4 mt-2">
						{uploadingStatus || encodingVideo ? (
							<FileDropBox>
								<TableBox>
									<NoFileBox data-testid="NoFileBox">
										<VideoPageTitleText data-testid="VideoPageTitleText" className="mb-3">
											{videoStatusText.split("\n").map((item, key) => {
												return (
													<span key={key}>
														{item}
														<br />
													</span>
												);
											})}
											{showProgressBar ? (
												<>
													<div className="mt-1">{`${now}%`}</div>
													<ProgressBar animated now={now} style={{ margin: ".5rem 5% 0 5%" }} />
												</>
											) : (
												<Lottie
													animationData={LoadingScreenAnimation}
													loop={true}
													style={{ width: "100px", margin: "auto" }}
												/>
											)}
										</VideoPageTitleText>
									</NoFileBox>
								</TableBox>
							</FileDropBox>
						) : (
							<>
								<FileDropBox {...getVideoProps()} data-testid="fileDropDiv" onClick={() => setVideoModalStatus(true)}>
									<input id="videoFile0" data-testid="hiddenFileInput" {...getVideoFileProps()} />
									{uploadFileURL ? (
										<>
											<VideoWrapper>
												<VideoPreview
													subscription={subscription}
													appEndpoint={process.env.APP_ENDPOINT as string}
													title={videoTitleValue}
													videoURL={uploadFileURL}
													videoPosterURL={imagePreview}
													showTitle={displayTitle}
													emailAddress={emailAddress}
													phoneNumber={phoneNumber}
													products={productsInputs as VideoPropsProduct[]}
													videoDisplayMode={displayFormat}
													captionData={captionData}
												/>
											</VideoWrapper>
											<VideoPlayerText data-testid="replaceTextDiv">{translation.general.replace}</VideoPlayerText>
										</>
									) : (
										<TableBox>
											<NoFileBox data-testid="NoFileBox">
												<VideoPageTitleText data-testid="VideoPageTitleText" className="mb-3" resize={true}>
													{translation.createVideoPage.uploadVideoText}
												</VideoPageTitleText>
												<BodyText className="mt-0" resize={true}>
													{translation.createVideoPage.uploadVideoTextLine1} <br />{" "}
													{translation.createVideoPage.uploadVideoTextLine2}
												</BodyText>
												<MainButton type="button" data-testid="uploadFileButton" resize={true}>
													{translation.createVideoPage.uploadVideoText}
												</MainButton>
											</NoFileBox>
										</TableBox>
									)}
								</FileDropBox>
								{uploadFileURL && captionData && captionData.captionText?.length ? (
									<ImageButton
										onClick={() => setCaptionsModalStatus(true)}
										style={{ margin: "auto", padding: "0.5rem", width: "max-content" }}
										className="mt-3"
									>
										<VideoCaptionsText><img src={CCFillIcon} style={{ marginRight: "0.5rem" }} /> {translation.general.editCaptions}</VideoCaptionsText>
									</ImageButton>
								) : ("")}
							</>
						)}
					</Col>
				</Row>
				<Row pl="0" pr="0" className="mt-4">
					<Col sm="12" md="6" className="mb-4">
						<FlexSpace>
							<VideoPageTitleText data-testid="videoTitleDiv" className="mb-3">
								{`${translation.createVideoPage.videoTitle}*`}{" "}
							</VideoPageTitleText>
							<div>
								<CustomSwitch
									type="switch"
									defaultChecked="checked"
									onChange={() => setDisplayTitle((state) => !state)}
									label={translation.createVideoPage.displayTitle}
									id="displayTitleSwitch"
								/>
							</div>
						</FlexSpace>
						<Form.Group className="mb-3">
							<CustomInput
								className="form-control"
								type="text"
								required
								autoFocus
								placeholder={translation.createVideoPage.enterTitle}
								id="videoTitle"
								data-testid="videoTitle"
								onChange={(value) => setVideoTitleValue(value.target.value)}
								value={videoTitleValue}
							/>
						</Form.Group>
						<FlexSpace>
							<VideoPageTitleText className="mb-3">{translation.editVideoPage.videoDescription}</VideoPageTitleText>
							<div>{videoDescriptionCount}/150</div>
						</FlexSpace>
						<Form.Group className="mb-3">
							<CustomTextarea
								className="form-control"
								maxLength={150}
								placeholder={translation.editVideoPage.videoDescPlaceholder}
								id="videoDescription"
								data-testid="videoDescription"
								onChange={(value) => handleDescChange(value.target.value)}
								value={videoDescValue}
							/>
						</Form.Group>

						<FlexSpace>
							<VideoPageTitleText data-testid="coverImageTitle" className="mb-2">
								{translation.createVideoPage.displayFormat}
							</VideoPageTitleText>
							<TooltipWrapper
								title={!captionData || !captionData.captionText?.length ? translation.appCustomization.closedCaptionsNotAvailable : ""}
								disabled={!captionData || !captionData.captionText?.length}
							>
								<CustomSwitch
									type="switch"
									checked={captionData?.enabled && captionData.captionText?.length || false}
									onChange={toggleCaptionsEnabled}
									label={translation.createVideoPage.displayCaptions}
									id="displayCaptions"
									disabled={!captionData || !captionData.captionText?.length}
								/>
							</TooltipWrapper>
						</FlexSpace>

						{subscription?.allowLandscape ? (
							<FlexSwitchRow>
								<FlexSwitchCol
									active={displayFormat === DisplayFormatOptions.PORTRAIT}
									onClick={() => setDisplayFormat(DisplayFormatOptions.PORTRAIT)}
								>
									<PortraitBox />
									{translation.editVideoPage.portrait}
								</FlexSwitchCol>
								<FlexSwitchCol
									active={displayFormat === DisplayFormatOptions.LANDSCAPE}
									onClick={() => setDisplayFormat(DisplayFormatOptions.LANDSCAPE)}
								>
									<LandscapeBox />
									{translation.editVideoPage.landscape}
								</FlexSwitchCol>
							</FlexSwitchRow>
						) : (
							<FlexSwitchRow>
								<FlexSwitchCol active={true}>
									<PortraitBox />
									{translation.editVideoPage.portrait}
								</FlexSwitchCol>
								<FlexSwitchCol onClick={() => handlePlanClick()}>
									<LandscapeBox />
									{translation.editVideoPage.landscape}
									<img src={OpenLockIcon} style={{ marginLeft: "10px" }} />
								</FlexSwitchCol>
							</FlexSwitchRow>
						)}
						<VideoPageTitleText
							data-testid="coverImageTitle"
							className="mb-3 mt-4"
						>{`${translation.createVideoPage.coverImageText}*`}</VideoPageTitleText>
						<Form.Group className="mb-3">
							<div
								data-testid="coverImageDiv"
								onClick={() => {
									uploadingStatus || encodingVideo || !imagePreview ? false : setCoverImageStatus(true);
								}}
							>
								<NoImageFileBox data-testid="coverImage">
									<div style={{ width: "75px" }}>
										<CoverImageBox
											landscape={displayFormat === DisplayFormatOptions.LANDSCAPE}
											data-testid="CoverImageBox"
										>
											{imagePreview ? (
												<UploadImage data-testid="uploadedCoverImage" src={imagePreview} />
											) : (
												<ImageIcon
													data-testid="CoverImageIcon"
													landscape={displayFormat === DisplayFormatOptions.LANDSCAPE || undefined}
												/>
											)}
										</CoverImageBox>
									</div>
									<div>
										<BodyText
											data-testid="uploadCoverImage"
											className="mt-3"
											disabled={uploadingStatus || encodingVideo || !imagePreview ? true : false}
										>
											{uploadingStatus || encodingVideo
												? translation.createVideoPage.generatingCoverImage
												: translation.createVideoPage.uploadCoverImage}
										</BodyText>
									</div>
								</NoImageFileBox>
							</div>
						</Form.Group>
						<div>&nbsp;</div>
						<VideoPageTitleText className="mb-2">{`${translation.createVideoPage.addInteraction}`}</VideoPageTitleText>

						{productsInputs.map((x, index) => {
							return (
								<Form.Group className="mb-3" key={index}>
									<ProductsBox>
										<CustomInputBox style={{ marginBottom: `${productsInputs[index].productURL ? "0.5rem" : "0"}` }}>
											<CustomInput
												className="form-control"
												type="text"
												required
												style={{ backgroundColor: "#ffffff" }}
												onChange={(e) => handleInputChange(e.target.value, "productURL", index)}
												onBlur={(e) => {
													if (!productsInputs[index].showProduct && !productsInputs[index].fetchingProduct) {
														handleParseProductUrl(e.target.value, index);
													}
												}}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														handleParseProductUrl((e.target as HTMLInputElement).value, index);
													}
												}}
												placeholder={translation.createVideoPage.productURL}
												id={`productURL${index}`}
												data-testid={`productURL${index}`}
												value={x.productURL}
											/>
											<CircleXmarkIcon
												style={{ position: "relative", top: "-4.3rem" }}
												data-testid={`deleteProductIcon${index}`}
												onClick={() => {
													handleRemoveClick(index);
												}}
											/>
										</CustomInputBox>
										{productsInputs[index].showProduct && productsInputs[index].fetchingProduct ? (
											<FetchingProductText>{translation.createVideoPage.fetchingProductText}</FetchingProductText>
										) : (
											productsInputs[index].showProduct &&
											!productsInputs[index].fetchingProduct && (
												<CustomInputBox style={{ display: "flex" }}>
													<ProductImage
														key={index}
														data-testid={`prImage${index}`}
														htmlFor={`prImage${index}`}
														onChange={(e: React.FormEvent<HTMLLabelElement>) => onDrop(e, index)}
														onMouseOver={() => handleMouseOver(index)}
														onMouseLeave={() => handleMouseLeave(index)}
													>
														<input
															type="file"
															id={`prImage${index}`}
															accept="image/png, image/jpeg, image/jpg"
															hidden
														/>
														{!x.hovering && x.imagePreview ? (
															<UploadProductImage key={"UploadProductImage" + index} src={x.imagePreview} />
														) : (
															<>
																<img
																	src={UploadIcon}
																	alt={translation.createVideoPage.uploadImageAltText}
																	data-testid={`addImageIcon${index}`}
																	style={{ margin: "16px", width: "25px", height: "25px" }}
																/>
															</>
														)}
													</ProductImage>
													<CustomInput
														className="form-control"
														type="text"
														required
														style={{ backgroundColor: "#ffffff", marginLeft: "0.5rem" }}
														onChange={(value) => handleInputChange(value.target.value, "productTitle", index)}
														placeholder={translation.general.title}
														id={`productTitle${index}`}
														data-testid={`productTitle${index}`}
														value={x.productTitle}
													/>
													<CustomInput
														className="form-control"
														type="text"
														style={{ backgroundColor: "#ffffff", marginLeft: "0.5rem" }}
														onChange={(value) => handleInputChange(value.target.value, "subTitle", index)}
														placeholder={translation.createVideoPage.subTitle}
														id={`subTitle${index}`}
														data-testid={`subTitle${index}`}
														value={x.subTitle}
													/>
												</CustomInputBox>
											)
										)}
									</ProductsBox>
								</Form.Group>
							);
						})}

						{showEmailAddress && subscription?.allowCTALead && (
							<Form.Group className="mb-3">
								<ProductsBox>
									<CustomInputBox>
										<CustomInput
											type="email"
											ref={emailRef}
											className="form-control"
											style={{ backgroundColor: "#ffffff" }}
											onChange={(e) => {
												setEmailAddress(e.target.value);
												if (emailRef.current?.classList.contains("is-invalid")) {
													emailRef.current.classList.remove("is-invalid");
												}
											}}
											placeholder={translation.createVideoPage.emailAddress}
											id="productEmailAddress"
											data-testid="productEmailAddress"
											value={emailAddress}
										/>
										<CircleXmarkIcon
											style={{ position: "relative", top: "-4.3rem" }}
											data-testid="deleteproductEmailAddress"
											onClick={() => {
												setEmailAddress("");
												setShowEmailAddress(false);
											}}
										/>
									</CustomInputBox>
								</ProductsBox>
							</Form.Group>
						)}

						{showPhoneNumber && subscription?.allowCTALead && (
							<Form.Group className="mb-3">
								<ProductsBox>
									<CustomInputBox>
										<CustomInput
											ref={phoneRef}
											className="form-control"
											type="text"
											style={{ backgroundColor: "#ffffff" }}
											onChange={(e) => setPhoneNumber(e.target.value)}
											placeholder={translation.createVideoPage.phoneNumber}
											id="productPhoneNumber"
											data-testid="productPhoneNumber"
											value={phoneNumber}
										/>
										<CircleXmarkIcon
											style={{ position: "relative", top: "-4.3rem" }}
											data-testid="deleteProductPhoneNumber"
											onClick={() => {
												setPhoneNumber("");
												setShowPhoneNumber(false);
											}}
										/>
									</CustomInputBox>
								</ProductsBox>
							</Form.Group>
						)}

						<Flex>
							{productsInputs.length < 10 && (
								<ImageButton onClick={() => handleAddClick()}>
									<img src={LinkIconBlack} />
								</ImageButton>
							)}
							{!showEmailAddress && (
								<ImageButton
									disabled={!subscription?.allowCTALead}
									onClick={() => {
										if (subscription?.allowCTALead) {
											if (!emailAddress && userData?.email) {
												setEmailAddress(userData.email);
											}
											setShowEmailAddress(true);
										} else {
											handlePlanClick();
										}
									}}
								>
									<img src={EmailIconBlack} />
									{!subscription?.allowCTALead && <img src={OpenLockIcon} className="lockImage" />}
								</ImageButton>
							)}
							{!showPhoneNumber && (
								<ImageButton
									disabled={!subscription?.allowCTALead}
									onClick={() => (subscription?.allowCTALead ? setShowPhoneNumber(true) : handlePlanClick())}
								>
									<img src={PhoneIconBlack} />
									{!subscription?.allowCTALead && <img src={OpenLockIcon} className="lockImage" />}
								</ImageButton>
							)}
						</Flex>
					</Col>
					<Col sm="12" md="6" className="mb-4">
						<>
							<VideoPageTitleText data-testid="performance" className="mb-3">
								{translation.createVideoPage.performanceSubheading}
							</VideoPageTitleText>
							<VideoPerformance>
								<div style={{ textAlign: "center", fontWeight: "bold", paddingTop: "5rem", paddingBottom: "5rem" }}>
									{translation.createVideoPage.performanceNoData}
								</div>
							</VideoPerformance>
							<div>&nbsp;</div>
						</>
						<VideoPageTitleText data-testid="addToCollection" className="mb-3">
							{translation.createVideoPage.collectionsSubheading}
						</VideoPageTitleText>
						<ProductsBox>
							<CustomInputBox>
								<Select
									styles={CustomSelectStyles}
									options={unselectedCollections}
									onChange={handleCollectionSelect}
									isSearchable={true}
									components={{
										SingleValue: CustomSingleValue,
										Placeholder: CustomSingleValue,
										DropdownIndicator: () => null
									}}
								/>
							</CustomInputBox>
						</ProductsBox>
						<CustomSelectItem
							_id={defaultCollection?._id ?? ""}
							readonly={true}
							title={`${translation.createVideoPage.allVideosText} (${defaultCollection?.title})`}
							subtext={`${defaultCollection?.shoppableVideos?.length} ${translation.createVideoPage.videosText}`}
						/>
						{selectedCollections.map((collection) => {
							return (
								<CustomSelectItem
									key={collection._id}
									onRemove={handleCollectionRemove}
									_id={collection._id}
									title={`${collection?.title}`}
									subtext={`${collection?.shoppableVideos?.length} ${translation.createVideoPage.videosText}`}
								/>
							);
						})}
						{newCollectionSelected && (
							<CustomSelectItem
								onRemove={handleNewCollectionRemove}
								_id="new_collection"
								title={translation.createVideoPage.newCollectionText}
								subtext={`0 ${translation.createVideoPage.videosText}`}
							/>
						)}
					</Col>
				</Row>
			</PageBody>
			<VideoUploadModal
				visible={videoModalStatus}
				onCancel={() => setVideoModalStatus(false)}
				getVideoProps={getVideoProps}
				getVideoFileProps={getVideoFileProps}
				submitVideoURL={submitVideoURL}
				setVideoFromLibrary={setVideoFromLibrary}
				setEditVideoError={setEditVideoError}
			/>
			<CoverImageModal
				displayFormat={displayFormat}
				visible={coverImageStatus}
				uploadFileURL={uploadFileURL}
				imagePreview={imagePreview}
				setCoverImage={setCoverImage}
				setImagePreview={setImagePreview}
				setEditVideoError={setEditVideoError}
				onCancel={() => setCoverImageStatus(false)}
			/>
			<CaptionsModal
				displayFormat={displayFormat}
				visible={captionsModalStatus}
				uploadFileURL={uploadFileURL}
				captionData={captionData}
				setCaptionData={setCaptionData}
				setEditVideoError={setEditVideoError}
				onCancel={() => setCaptionsModalStatus(false)}
			/>
			<ConfirmLeavingModal
				visible={modalStatus}
				onCancel={() => {
					setModalStatus(false);
					setGoToPrevPage(false);
				}}
				onContinue={() => {
					if (goToPrevPage) window.history.go(-2);
					else navigate(navigationUrl);
				}}
			/>
			<EnterPasswordModal
				visible={enterPassword}
				onCancel={() => setEnterPassword(false)}
				onContinue={addShoppableVideo}
			/>
		</>
	);
};

export default AddNewVideo;

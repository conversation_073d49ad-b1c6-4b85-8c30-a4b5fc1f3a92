import React, { useState } from "react";
import { useTranslation } from "../hooks/translations";
import CollectionListItem from "./CollectionListItem";
import { Flex, ListBox, HeaderListTitle } from "@src/styles/components";
import { shoppableVideo } from "@src/types/videos";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";

interface Props {
	videos: shoppableVideo[];
	setVideos: (value: shoppableVideo[]) => void;
}

const CollectionVideos: React.FC<Props> = ({ videos, setVideos }) => {
	const translation = useTranslation();
	const [isDropDisabled, setIsDropDisabled] = useState(false);

	const handleOnDragEnd = (result: DropResult) => {
		if (!result.destination) {
			return;
		}

		const videosCopy = [...videos];
		const [reorderedItem] = videosCopy.splice(result.source.index, 1);
		videosCopy.splice(result.destination.index, 0, reorderedItem);

		setVideos(videosCopy);
	};

	return (
		<DragDropContext onDragEnd={handleOnDragEnd}>
			<Flex className="mt-4">
				<ListBox style={{ maxWidth: "2rem" }}>
					<HeaderListTitle data-testid="videoListGridBox" />
				</ListBox>
				<ListBox style={{ width: "50%" }}>
					<HeaderListTitle data-testid="videoListTitle">{translation.dashboardPage.title}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "40%" }}>
					<HeaderListTitle data-testid="videoListProducts">{translation.dashboardPage.products}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ minWidth: "6rem" }} last={true}>
					<HeaderListTitle data-testid="videoListActions">{translation.dashboardPage.actions}</HeaderListTitle>
				</ListBox>
			</Flex>
			<Droppable droppableId="videos">
				{(provided) => (
					<div {...provided.droppableProps} ref={provided.innerRef}>
						{videos.map((video: shoppableVideo, index: number) => (
							<Draggable key={video._id} draggableId={video._id} isDragDisabled={isDropDisabled} index={index}>
								{(provided) => (
									<div {...provided.draggableProps} {...provided.dragHandleProps} ref={provided.innerRef} className="mb-4">
										<CollectionListItem numberId={index} key={video._id} videoItem={video} videos={videos} setVideos={setVideos} setIsDropDisabled={setIsDropDisabled} />
									</div>
								)}
							</Draggable>
						))}
						{provided.placeholder}
					</div>
				)}
			</Droppable>
		</DragDropContext>
	);
};

export default CollectionVideos;

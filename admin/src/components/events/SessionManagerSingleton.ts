import { SessionManager } from "./SessionManager";

/*
This singleton class ensures that we have a single,
shared instance of the SessionManager class available
for use, avoiding the need to create multiple instances
and pass them around explicitly.
*/
class SessionManagerSingleton {
	private static instance: SessionManager;

	private constructor() {
		// Private constructor to prevent direct instantiation
	}

	// Returns the singleton instance of the SessionManager class.
	// Ensures that the app can only access the shared instance of the class
	// through this static method.
	public static getInstance = (): SessionManager => {
		if (!SessionManagerSingleton.instance) {
			SessionManagerSingleton.instance = new SessionManager();
		}
		return SessionManagerSingleton.instance;
	};
}

export default SessionManagerSingleton;

import ObjectID from "bson-objectid";

interface ISession {
	id: string;
	createdAt: string;
	updatedAt: string;
}

export class SessionManager {
	private sessionCookieKey = "gpAdminSession";
	private sessionExpiration = 365 * 24 * 60 * 60 * 1000;

	public initSession = async (): Promise<void> => {
		const existingSession = this.getSession();
		if (existingSession) {
			if (this.isSessionExpired(existingSession.updatedAt)) {
				this.createNewSession();
			} else {
				this.updateSession(existingSession);
			}
		} else {
			this.createNewSession();
		}
	};

	public getSession = (): ISession | null => {
		const sessionStr = this.getCookie(this.sessionCookieKey);
		return sessionStr ? JSON.parse(sessionStr) : null;
	};

	public createNewSession = async (): Promise<void> => {
		const now = new Date().toISOString();
		const newSession: ISession = {
			id: this.generateUniqueId(),
			createdAt: now,
			updatedAt: now
		};
		this.setSessionCookie(newSession);
	};

	public updateSession = (session: ISession): void => {
		session.updatedAt = new Date().toISOString();
		this.setSessionCookie(session);
	};

	public isSessionExpired = (lastUpdatedAt: string): boolean => {
		const now = new Date();
		const lastUpdated = new Date(lastUpdatedAt);
		return now.getTime() - lastUpdated.getTime() > this.sessionExpiration;
	};

	private generateUniqueId(): string {
		return ObjectID().toHexString();
	}

	private setSessionCookie = (session: ISession): void => {
		const sessionStr = JSON.stringify(session);
		document.cookie = `${this.sessionCookieKey}=${sessionStr}; path=/;`;
	};

	private getCookie = (name: string): string | null | undefined => {
		const value = "; " + document.cookie;
		const parts = value.split("; " + name + "=");
		if (parts.length === 2) {
			return parts.pop()?.split(";").shift();
		}
		return null;
	};
}

import { IDevice, IEventDimensions } from "@src/types/events";

export const validateBackslash = (url: string | undefined): string | undefined => {
	if (!url) {
		return undefined;
	}
	if (url[url.length - 1] === "/") {
		return url;
	}

	return url + "/";
};

export const isIOSChrome = (): boolean => {
	const userAgent = window.navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !("MSStream" in window);
	const isChrome = /CriOS/.test(userAgent);

	return isIOS && isChrome;
};

const getBrowserInfo = (): {browserName: string; browserVersion: string} => {
	const userAgent = navigator.userAgent;
	const browsers = [
		{ name: "Chrome", regex: /Chrome\/([\d.]+)/ },
		{ name: "Firefox", regex: /Firefox\/([\d.]+)/ },
		{ name: "Safari", regex: /Version\/([\d.]+).*Safari/ },
		{ name: "Internet Explorer", regex: /MSIE ([\d.]+)/ },
		{ name: "Internet Explorer", regex: /rv:([\d.]+).*Trident/ },
		{ name: "Edge", regex: /Edge\/([\d.]+)/ }
	];

	for (const browser of browsers) {
		const match = userAgent.match(browser.regex);
		if (match) {
			return { browserName: browser.name, browserVersion: match[1] };
		}
	}

	return { browserName: "Unknown", browserVersion: "Unknown" };
};

export const getDevice = (): IDevice => {
	const userAgent = navigator.userAgent;

	let type: IDevice["type"];
	let osName: IDevice["osName"];

	if (/Macintosh/.test(userAgent)) {
		type = "Macintosh";
		osName = "MacOS";
	} else if (/Windows/.test(userAgent)) {
		type = "PC";
		osName = "Windows";
	} else if (/iPhone/.test(userAgent)) {
		type = "iPhone";
		osName = "iOS";
	} else if (/Android/.test(userAgent)) {
		type = "Android";
		osName = "Android";
	} else {
		type = undefined;
		osName = undefined;
	}

	const osVersion = userAgent.match(/(Mac OS X|Windows NT|Android|CPU( iPhone)? OS) ([\d._]+)/);

	const version = osVersion && osVersion[3] ? osVersion[3]?.replace(/_/g, ".") : "";

	return {
		type,
		osName,
		osVersion: version.length > 0 ? version : undefined
	};
};

const isValidURI = (uri: string): boolean => {
	try {
		new URL(uri);
		return true;
	} catch {
		return false;
	}
};

export const getEventDimensions = (): IEventDimensions => {
	const browserCurrentURL = isValidURI(window.location.href) ? window.location.href : undefined;

	const browserReferrerURL = isValidURI(document.referrer) ? document.referrer : undefined;
	const { browserName, browserVersion } = getBrowserInfo();
	const browserLocale = navigator.language;
	const browserPixelWidth = window.innerWidth;
	const browserPixelHeight = window.innerHeight;
	const browserUserAgent = navigator.userAgent;

	const eventDimensions: IEventDimensions = {
		browserName,
		browserVersion,
		browserLocale,
		browserPixelWidth,
		browserPixelHeight,
		browserUserAgent
	};

	if (browserCurrentURL) {
		eventDimensions.browserCurrentURL = browserCurrentURL;
	}
	if (browserReferrerURL) {
		eventDimensions.browserReferrerURL = browserReferrerURL;
	}

	return eventDimensions;
};

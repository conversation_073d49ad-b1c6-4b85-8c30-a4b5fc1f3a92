import React, { useRef, useState, useEffect, RefObject } from "react";
import { Form, Row, Col } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import Spinner from "react-bootstrap/Spinner";
import {
	PageBody,
	MainButton,
	MainSuccessButton,
	VideosSection,
	VideoPageTitleText,
	CustomHighlight,
	HighlightCodeModal,
	CustomSwitch,
	CodeSnippet,
	FlexRow,
	FlexSwitchRow,
	FlexSwitchCol,
	ColorBoxContainer,
	BasicAccountSection,
	ListTabHeading,
	ListTabRow,
	ColorBox,
	ColorInput,
	ProLinkButton,
	ProIconHeader,
	DesktopRow,
	MobileRow
} from "@src/styles/components";
import {
	HeadingText,
	HeadingTextH2,
	ConfirmationBoxWrapper,
	ReadOnlyInput,
	CustomInput,
	CloneIconDiv,
	CloneIcon
} from "@src/styles/forms";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { getErrorString } from "../hooks/getErrorString";
import editCollection from "../hooks/editCollection";
import { VideosModal } from "../modals/VideosModal";
import { ShareModal } from "../modals/ShareModal";
import CollectionVideos from "@src/components/dashboard/CollectionVideos";
import getCollectionById from "../hooks/getCollectionById";
import { ConfirmLeavingModal } from "../modals/ConfirmLeavingModal";
import getVideos from "../hooks/getVideos";
import { EnterPasswordModal } from "../modals/EnterPasswordModal";
import { usePasswordRequired } from "../hooks/getPasswordRequired";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { shoppableVideo, DisplayPreferencesTab } from "@src/types/videos";
import { HexColorPicker } from "react-colorful";
import { ArrowUp, ArrowDown, StarIcon, ArrowUpGrey, ArrowDownGrey, OpenLockIcon } from "@src/assets";
import { fontOptions } from "@src/types/snippetOptions";
import { Account } from "../../types/account";
import { ShareObject, ShareType } from "@src/types/share";
import deepEqual from "deep-equal";
import DetailShareButton from "../share/DetailShareButton";

interface Props {
	saveChanges: boolean;
	setSaveChanges: (value: boolean) => void;
	navigationUrl: string;
	setNavigationUrl: (value: string) => void;
	modalStatus: boolean;
	setModalStatus: (value: boolean) => void;
	setModalSnippet: (value: boolean) => void;
	hideCreateVideo: boolean;
	accountData: Account | undefined;
}

// eslint-disable-next-line max-lines-per-function
const EditCollectionPage: React.FC<Props> = ({
	saveChanges,
	setSaveChanges,
	navigationUrl,
	setNavigationUrl,
	modalStatus,
	setModalStatus,
	setModalSnippet,
	hideCreateVideo,
	accountData
}) => {
	const { id } = useParams<{id: string}>();
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const navigate = useNavigate();
	const [collectionError, setCollectionError] = useState("");
	const [refetch, setRefetch] = useState(true);
	const [generateButton, setGenerateButton] = useState(false);
	const [loading, setLoading] = useState(false);
	const [collectionTitle, setCollectionTitle] = useState("");
	const [collectionTitleCopy, setCollectionTitleCopy] = useState("");
	const [shareObject, setShareObject] = useState<ShareObject>();
	const [videos, setVideos] = useState<shoppableVideo[]>([]);
	const [videoDataCopy, setVideoDataCopy] = useState<shoppableVideo[]>([]);
	const [videosModalStatus, setVideosModalStatus] = useState(false);
	const [shareModalStatus, setShareModalStatus] = useState(false);
	const [goToPrevPage, setGoToPrevPage] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const [selectedTab, setSelectedTab] = useState<DisplayPreferencesTab>(DisplayPreferencesTab.GENERAL);
	const [isPickerVisible, setPickerVisible] = useState(false);
	const [isTextColorVisible, setIsTextColorVisible] = useState(false);
	const [buttonBackgroundColor, setButtonBackgroundColor] = useState("#061714");
	const [iconTextColor, setIconTextColor] = useState("#FFFFFF");
	const [buttonBackgroundBlur, setButtonBackgroundBlur] = useState(true);
	const [selectedFont, setSelectedFont] = useState(fontOptions[0].value);
	const [buttonBackgroundColorCopy, setButtonBackgroundColorCopy] = useState("#061714");
	const [iconTextColorCopy, setIconTextColorCopy] = useState("#FFFFFF");
	const [buttonBackgroundBlurCopy, setButtonBackgroundBlurCopy] = useState(true);
	const [selectedFontCopy, setSelectedFontCopy] = useState(fontOptions[0].value);
	const [borderRadius, setBorderRadius] = useState(10);
	const [borderRadiusCopy, setBorderRadiusCopy] = useState(10);
	const [centerOnPage, setCenterOnPage] = useState(false);
	const [centerOnPageCopy, setCenterOnPageCopy] = useState(false);
	const [margin, setMargin] = useState(0);
	const [marginCopy, setMarginCopy] = useState(0);
	const [paddingBetween, setPaddingBetween] = useState(24);
	const [paddingBetweenCopy, setPaddingBetweenCopy] = useState(24);
	const [widgetBorderRadius, setWidgetBorderRadius] = useState(10);
	const [widgetBorderRadiusCopy, setWidgetBorderRadiusCopy] = useState(10);
	const [widgetPosition, setWidgetPosition] = useState("right");
	const [widgetPositionCopy, setWidgetPositionCopy] = useState("right");
	const [inlineBorderRadius, setInlineBorderRadius] = useState(10);
	const [inlineBorderRadiusCopy, setInlineBorderRadiusCopy] = useState(10);

	const updateCollection = async (password?: string) => {
		if (id) {
			setGenerateButton(true);
			setLoading(true);
			const { error } = await apiRetryHandler(
				async () =>
					await editCollection({
						id,
						collectionTitle,
						buttonBackgroundColor,
						buttonBackgroundBlur,
						iconTextColor,
						selectedFont,
						borderRadius,
						centerOnPage,
						margin,
						paddingBetween,
						widgetBorderRadius,
						widgetPosition,
						inlineBorderRadius,
						videos,
						password
					})
			);
			setGenerateButton(false);
			setLoading(false);
			if (error) {
				console.error(error);
				const errorText = getErrorString(translation, error?.response?.data?.error);
				setCollectionError(errorText);
			} else {
				setRefetch(true);
			}
		}
	};

	const handleSave = () => {
		if (passwordRequired) {
			setEnterPassword(true);
		} else {
			updateCollection();
		}
	};

	const handlePlanClick = () => {
		if (saveChanges) {
			setNavigationUrl("/plans-pricing");
			setModalStatus(true);
		} else {
			navigate("/plans-pricing");
		}
	};

	useEffect(() => {
		// Prevent navigation if changes are not saved
		if (saveChanges) window.history.pushState(null, "", window.location.pathname);

		// prompt before refresh page
		const handleBeforeUnload = (event: {preventDefault: () => void; returnValue: string}) => {
			if (saveChanges) {
				event.preventDefault();
				// Required for some browsers
				event.returnValue = "";
			}
		};

		const handlePopState = () => {
			if (saveChanges) {
				window.history.pushState(null, "", window.location.pathname);
				setModalStatus(true);
				setGoToPrevPage(true);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		window.addEventListener("popstate", handlePopState);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			window.removeEventListener("popstate", handlePopState);
		};
	}, [saveChanges, setModalStatus]);

	useEffect(() => {
		const go = async () => {
			if (id) {
				const { data: collection, error: collectionError } = await apiRetryHandler(
					async () => await getCollectionById(id)
				);

				if (collectionError) {
					setCollectionError(translation.errors.internalError);
				} else {
					setCollectionTitle(collection?.title);
					setCollectionTitleCopy(collection?.title);

					if (collection?.buttonBackgroundColor) {
						setButtonBackgroundColor(collection?.buttonBackgroundColor);
						setButtonBackgroundColorCopy(collection?.buttonBackgroundColor);
					}

					if (collection?.iconTextColor) {
						setIconTextColor(collection?.iconTextColor);
						setIconTextColorCopy(collection?.iconTextColor);
					}

					if (collection?.buttonBackgroundBlur === true || collection?.buttonBackgroundBlur === false) {
						setButtonBackgroundBlur(collection?.buttonBackgroundBlur);
						setButtonBackgroundBlurCopy(collection?.buttonBackgroundBlur);
					}

					if (collection?.displayFont) {
						setSelectedFont(collection?.displayFont);
						setSelectedFontCopy(collection?.displayFont);
					}

					if (collection?.carouselBorderRadius !== undefined) {
						setBorderRadius(collection?.carouselBorderRadius);
						setBorderRadiusCopy(collection?.carouselBorderRadius);
					}

					if (collection?.carouselIsCentered === true || collection?.carouselIsCentered === false) {
						setCenterOnPage(collection?.carouselIsCentered);
						setCenterOnPageCopy(collection?.carouselIsCentered);
					}

					if (collection?.carouselMargin !== undefined) {
						setMargin(collection?.carouselMargin);
						setMarginCopy(collection?.carouselMargin);
					}

					if (collection?.carouselGap !== undefined) {
						setPaddingBetween(collection?.carouselGap);
						setPaddingBetweenCopy(collection?.carouselGap);
					}

					if (collection?.widgetBorderRadius !== undefined) {
						setWidgetBorderRadius(collection?.widgetBorderRadius);
						setWidgetBorderRadiusCopy(collection?.widgetBorderRadius);
					}

					if (collection?.widgetPosition) {
						setWidgetPosition(collection?.widgetPosition);
						setWidgetPositionCopy(collection?.widgetPosition);
					}

					if (collection?.inlineBorderRadius !== undefined) {
						setInlineBorderRadius(collection?.inlineBorderRadius);
						setInlineBorderRadiusCopy(collection?.inlineBorderRadius);
					}

					if (collection?.shoppableVideos?.length) {
						const { data } = await apiRetryHandler(async () => await getVideos());
						if (data?.length) {
							const videoIds: shoppableVideo[] = [];

							collection?.shoppableVideos.map((x: string) => {
								const obj = data.find((item: shoppableVideo) => item._id === x);
								videoIds.push(obj);
							});
							setVideos(videoIds);
							setVideoDataCopy(videoIds);

							if (videoIds[0].title && videoIds[0].gifURL) {
								setShareObject({
									videoId: videoIds[0]._id,
									collectionId: id,
									type: ShareType.SHARE_COLLECTION,
									videoTitle: videoIds[0].title,
									gifURL: videoIds[0].gifURL
								});
							} else {
								setShareObject(undefined);
							}
						}
					} else {
						setShareObject(undefined);
					}
					setRefetch(false);
				}
			} else {
				navigate("/");
			}
		};

		if (refetch) {
			go();
		}
	}, [refetch, apiRetryHandler, id, navigate, translation]);

	const ThemeChanged = !deepEqual(videos, videoDataCopy);

	useEffect(() => {
		if (
			ThemeChanged ||
			collectionTitle !== collectionTitleCopy ||
			buttonBackgroundColor !== buttonBackgroundColorCopy ||
			iconTextColor !== iconTextColorCopy ||
			buttonBackgroundBlur !== buttonBackgroundBlurCopy ||
			selectedFont !== selectedFontCopy ||
			borderRadius !== borderRadiusCopy ||
			centerOnPage !== centerOnPageCopy ||
			margin !== marginCopy ||
			paddingBetween !== paddingBetweenCopy ||
			widgetBorderRadius !== widgetBorderRadiusCopy ||
			widgetPosition !== widgetPositionCopy ||
			inlineBorderRadius !== inlineBorderRadiusCopy
		) {
			setSaveChanges(true);
			setGenerateButton(false);
		} else {
			setSaveChanges(false);
			setGenerateButton(true);
		}
	}, [
		ThemeChanged,
		collectionTitle,
		collectionTitleCopy,
		buttonBackgroundColor,
		iconTextColor,
		buttonBackgroundBlur,
		selectedFont,
		buttonBackgroundColorCopy,
		iconTextColorCopy,
		buttonBackgroundBlurCopy,
		selectedFontCopy,
		borderRadius,
		borderRadiusCopy,
		centerOnPage,
		centerOnPageCopy,
		margin,
		marginCopy,
		paddingBetween,
		paddingBetweenCopy,
		widgetBorderRadius,
		widgetBorderRadiusCopy,
		widgetPosition,
		widgetPositionCopy,
		inlineBorderRadius,
		inlineBorderRadiusCopy,
		setSaveChanges
	]);

	const handleTabClick = (tab: DisplayPreferencesTab) => {
		setSelectedTab((prevTab) => (prevTab === tab ? DisplayPreferencesTab.CLOSEALL : tab));
	};

	const useOutsideClick = (ref: RefObject<HTMLDivElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					setPickerVisible(false);
					setIsTextColorVisible(false);
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef<HTMLDivElement>(null);
	useOutsideClick(wrapperRef);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!collectionError && (
					<ErrorMessage error={collectionError} setError={setCollectionError} displayCloseIcon={true} />
				)}
			</ConfirmationBoxWrapper>
			<PageBody>
				{/* desktop view */}
				<DesktopRow>
					<Col sm="12" md="12" lg="12" xl="4" className="mb-4">
						<HeadingText data-testid="editVideoPage">{collectionTitle ? collectionTitle : ""}</HeadingText>
					</Col>
					<Col sm="12" md="12" lg="12" xl="8" className="mb-4 text-end">
						<DetailShareButton
							allowSharing={accountData?.subscription?.allowSharing === true}
							disabled={!shareObject}
							setShareModalStatus={setShareModalStatus}
							saveChanges={saveChanges}
							setModalStatus={setModalStatus}
							setNavigationUrl={setNavigationUrl}
							style={{ marginRight: "1rem", marginLeft: "auto" }}
						/>

						<MainSuccessButton
							type="button"
							data-testid="addToSite"
							style={{ marginRight: "1rem", marginLeft: "auto" }}
							onClick={() => {
								setModalSnippet(true);
							}}
						>
							{translation.modals.addToSite}
						</MainSuccessButton>
						<MainButton type="button" data-testid="updateButton" disabled={generateButton} onClick={handleSave}>
							{translation.profilePage.saveChanges}
							{loading ? (
								<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
							) : (
								""
							)}
						</MainButton>
					</Col>
				</DesktopRow>

				{/* mobile view */}
				<MobileRow>
					<Col sm="12" md="12" className="mb-4">
						<HeadingText data-testid="editVideoPage">{collectionTitle ? collectionTitle : ""}</HeadingText>
					</Col>
					<Col sm="12" md="12" className="mb-4">
						<MainSuccessButton
							type="button"
							data-testid="addToSite"
							className="text-center"
							style={{ width: "100%", marginBottom: "1rem" }}
							onClick={() => {
								setModalSnippet(true);
							}}
						>
							{translation.modals.addToSite}
						</MainSuccessButton>

						<Row>
							<Col style={{ width: "50%" }} className="text-start">
								<DetailShareButton
									allowSharing={accountData?.subscription?.allowSharing === true}
									disabled={!shareObject}
									setShareModalStatus={setShareModalStatus}
									style={{ width: "100%", marginRight: "1rem", marginLeft: "auto" }}
								/>
							</Col>
							<Col style={{ width: "50%" }} className="text-center">
								<MainButton
									type="button"
									data-testid="updateButton"
									disabled={generateButton}
									onClick={handleSave}
									style={{ width: "100%" }}
								>
									{translation.profilePage.saveChanges}
									{loading ? (
										<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
									) : (
										""
									)}
								</MainButton>
							</Col>
						</Row>
					</Col>
				</MobileRow>

				{refetch ? (
					<Row pl="0" pr="0">
						<Col sm="12" md="4" className="mb-4">
							<Skeleton count={10} height={50} />
						</Col>
						<Col sm="12" md="8" className="mb-4">
							<Skeleton height={535} />
						</Col>
					</Row>
				) : (
					<>
						<Row pl="0" pr="0">
							<Col sm="12" md="4" className="mb-4">
								<VideoPageTitleText className="mb-3">{translation.general.title}</VideoPageTitleText>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={translation.editCollection.placeholder}
										id="collectionTitle"
										data-testid="collectionTitle"
										onChange={(value) => {
											setCollectionTitle(value.target.value);
										}}
										value={collectionTitle}
									/>
								</Form.Group>
								<VideoPageTitleText className="mb-3">{translation.editCollection.collectionId}</VideoPageTitleText>
								<Form.Group className="mb-3">
									<div style={{ position: "relative" }}>
										<CloneIconDiv style={{ width: "100%", position: "absolute", pointerEvents: "none" }}>
											<CloneIcon
												data-testid="CloneIcon"
												style={{ float: "right", marginTop: "9px", marginRight: "9px", pointerEvents: "auto" }}
												onClick={() => {
													if (id) navigator.clipboard.writeText(id);
												}}
											/>
										</CloneIconDiv>
										<CodeSnippet>
											<CustomHighlight data-testid="highlightBox" style={{ height: "22px" }}>
												<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
													{id}
												</HighlightCodeModal>
											</CustomHighlight>
										</CodeSnippet>
									</div>
								</Form.Group>

								<FlexRow className="mt-3 mb-2" style={{ alignItems: "start" }}>
									<VideoPageTitleText className="mb-3">
										{translation.appCustomization.displayPreferences}
									</VideoPageTitleText>
									{!accountData?.subscription.allowThemes && (
										<ProLinkButton basic={true} onClick={() => handlePlanClick()}>
											<ProIconHeader data-testid="StarIcon" src={StarIcon} />
											{translation.general.unlock}
										</ProLinkButton>
									)}
								</FlexRow>

								{!accountData?.subscription.allowThemes ? (
									<>
										<ListTabRow className="mb-3">
											<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.GENERAL)}>
												<FlexRow>
													<img src={OpenLockIcon} style={{ marginRight: "10px" }} />
													{translation.appCustomization.generalTab}
												</FlexRow>
												{selectedTab === DisplayPreferencesTab.GENERAL ? (
													<img src={ArrowUpGrey} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDownGrey} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.GENERAL && (
												<BasicAccountSection>
													<FlexRow className="mt-3 mb-3" style={{ position: "relative" }}>
														{translation.appCustomization.buttonBackgroundColor}
														<ColorBoxContainer>
															<ColorBox color={"#061714"}></ColorBox>
															<ColorInput type="text" readOnly value={"#061714"} maxLength={7} />
														</ColorBoxContainer>
													</FlexRow>
													<FlexRow className="mb-4">
														{translation.appCustomization.buttonBackgroundBlur}
														<CustomSwitch style={{ fontSize: "1rem" }} type="switch" checked={true} readOnly />
													</FlexRow>
													<FlexRow className="mb-3" style={{ position: "relative" }}>
														{translation.appCustomization.iconTextColor}
														<ColorBoxContainer>
															<ColorBox color={"#FFFFFF"}></ColorBox>
															<ColorInput type="text" readOnly value={"#FFFFFF"} maxLength={7} />
														</ColorBoxContainer>
													</FlexRow>
													<FlexRow className="mb-1">
														{translation.appCustomization.font}
														<Form.Select
															id="font-select"
															disabled
															value={selectedFont}
															style={{ border: "none", width: "200px" }}
														>
															<option key={fontOptions[0].value} value={fontOptions[0].value}>
																{fontOptions[0].label}
															</option>
														</Form.Select>
													</FlexRow>
												</BasicAccountSection>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.CAROUSEL)}>
												<FlexRow>
													<img src={OpenLockIcon} style={{ marginRight: "10px" }} />
													{translation.appCustomization.carouselTab}
												</FlexRow>
												{selectedTab === DisplayPreferencesTab.CAROUSEL ? (
													<img src={ArrowUpGrey} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDownGrey} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.CAROUSEL && (
												<BasicAccountSection>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range value={borderRadius} min="0" max="35" step="1" readOnly />
														<ReadOnlyInput type="text" readOnly value={borderRadius} style={{ marginLeft: "10px" }} />
													</FlexRow>

													<FlexRow className="mb-4">
														<FlexRow>{translation.appCustomization.centerOnPage}</FlexRow>
														<CustomSwitch type="switch" style={{ fontSize: "1rem" }} checked={centerOnPage} readOnly />
													</FlexRow>

													<FlexRow>{translation.appCustomization.MarginLeftRight}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range value={margin} min="0" max="200" step="1" readOnly />
														<ReadOnlyInput type="text" readOnly value={margin + "px"} style={{ marginLeft: "10px" }} />
													</FlexRow>
													<FlexRow>{translation.appCustomization.paddingBetween}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range value={paddingBetween} min="0" max="100" step="1" readOnly />
														<ReadOnlyInput
															type="text"
															readOnly
															value={paddingBetween + "px"}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
												</BasicAccountSection>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.WIDGET)}>
												<FlexRow>
													<img src={OpenLockIcon} style={{ marginRight: "10px" }} />
													{translation.appCustomization.widgetTab}
												</FlexRow>
												{selectedTab === DisplayPreferencesTab.WIDGET ? (
													<img src={ArrowUpGrey} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDownGrey} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.WIDGET && (
												<BasicAccountSection>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range value={widgetBorderRadius} min="0" max="25" step="1" readOnly />
														<ReadOnlyInput
															type="text"
															readOnly
															value={widgetBorderRadius}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
													<FlexRow className="mb-3">{translation.appCustomization.position}</FlexRow>
													<FlexSwitchRow>
														<FlexSwitchCol active={widgetPosition === "left"}>
															{translation.appCustomization.leftCorner}
														</FlexSwitchCol>
														<FlexSwitchCol active={widgetPosition === "right"}>
															{translation.appCustomization.rightCorner}
														</FlexSwitchCol>
													</FlexSwitchRow>
												</BasicAccountSection>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.INLINE)}>
												<FlexRow>
													<img src={OpenLockIcon} style={{ marginRight: "10px" }} />
													{translation.appCustomization.inlineTab}
												</FlexRow>
												{selectedTab === DisplayPreferencesTab.INLINE ? (
													<img src={ArrowUpGrey} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDownGrey} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.INLINE && (
												<BasicAccountSection>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range value={inlineBorderRadius} min="0" max="40" step="1" readOnly />
														<ReadOnlyInput
															type="text"
															readOnly
															value={inlineBorderRadius}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
												</BasicAccountSection>
											)}
										</ListTabRow>
									</>
								) : (
									<>
										<ListTabRow className="mb-3">
											<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.GENERAL)}>
												{translation.appCustomization.generalTab}
												{selectedTab === DisplayPreferencesTab.GENERAL ? (
													<img src={ArrowUp} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDown} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.GENERAL && (
												<>
													<FlexRow className="mt-3 mb-3" style={{ position: "relative" }}>
														{translation.appCustomization.buttonBackgroundColor}
														<ColorBoxContainer>
															<ColorBox
																color={buttonBackgroundColor}
																onClick={() => {
																	setPickerVisible(!isPickerVisible);
																}}
															></ColorBox>
															<ColorInput
																type="text"
																value={buttonBackgroundColor}
																onFocus={() => {
																	setPickerVisible(!isPickerVisible);
																}}
																onChange={(value) => {
																	setButtonBackgroundColor(value.target.value);
																}}
																maxLength={7}
															/>
														</ColorBoxContainer>
														{isPickerVisible && (
															<div ref={wrapperRef} style={{ position: "absolute", right: "0", top: "40px", zIndex: 1 }}>
																<HexColorPicker
																	color={buttonBackgroundColor}
																	onChange={setButtonBackgroundColor}
																	style={{ cursor: "pointer" }}
																/>
															</div>
														)}
													</FlexRow>
													<FlexRow className="mb-4">
														{translation.appCustomization.buttonBackgroundBlur}
														<CustomSwitch
															style={{ fontSize: "1rem" }}
															type="switch"
															checked={buttonBackgroundBlur}
															onChange={() => setButtonBackgroundBlur((state) => !state)}
														/>
													</FlexRow>
													<FlexRow className="mb-3" style={{ position: "relative" }}>
														{translation.appCustomization.iconTextColor}
														<ColorBoxContainer>
															<ColorBox
																color={iconTextColor}
																onClick={() => {
																	setIsTextColorVisible(!isTextColorVisible);
																}}
															></ColorBox>
															<ColorInput
																type="text"
																value={iconTextColor}
																onFocus={() => {
																	setIsTextColorVisible(!isTextColorVisible);
																}}
																onChange={(value) => {
																	setIconTextColor(value.target.value);
																}}
																maxLength={7}
															/>
														</ColorBoxContainer>
														{isTextColorVisible && (
															<div ref={wrapperRef} style={{ position: "absolute", right: "0", top: "40px", zIndex: 1 }}>
																<HexColorPicker
																	color={iconTextColor}
																	onChange={setIconTextColor}
																	style={{ cursor: "pointer" }}
																/>
															</div>
														)}
													</FlexRow>
													<FlexRow className="mb-1">
														{translation.appCustomization.font}
														<Form.Select
															id="font-select"
															value={selectedFont}
															onChange={(e) => setSelectedFont(e.target.value)}
															style={{ border: "none", width: "200px" }}
														>
															{fontOptions.map((font) => (
																<option key={font.value} value={font.value}>
																	{font.label}
																</option>
															))}
														</Form.Select>
													</FlexRow>
												</>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.CAROUSEL)}>
												{translation.appCustomization.carouselTab}
												{selectedTab === DisplayPreferencesTab.CAROUSEL ? (
													<img src={ArrowUp} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDown} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.CAROUSEL && (
												<>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range
															value={borderRadius}
															onChange={(e) => setBorderRadius(parseFloat(e.target.value))}
															min="0"
															max="35"
															step="1"
														/>
														<ReadOnlyInput type="text" readOnly value={borderRadius} style={{ marginLeft: "10px" }} />
													</FlexRow>

													<FlexRow className="mb-4">
														<FlexRow>{translation.appCustomization.centerOnPage}</FlexRow>
														<CustomSwitch
															type="switch"
															style={{ fontSize: "1rem" }}
															checked={centerOnPage}
															onChange={() => setCenterOnPage((state) => !state)}
														/>
													</FlexRow>

													<FlexRow>{translation.appCustomization.MarginLeftRight}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range
															value={margin}
															onChange={(e) => setMargin(parseInt(e.target.value))}
															min="0"
															max="200"
															step="1"
														/>
														<ReadOnlyInput type="text" readOnly value={margin + "px"} style={{ marginLeft: "10px" }} />
													</FlexRow>
													<FlexRow>{translation.appCustomization.paddingBetween}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range
															value={paddingBetween}
															onChange={(e) => setPaddingBetween(parseInt(e.target.value))}
															min="0"
															max="100"
															step="1"
														/>
														<ReadOnlyInput
															type="text"
															readOnly
															value={paddingBetween + "px"}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
												</>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.WIDGET)}>
												{translation.appCustomization.widgetTab}
												{selectedTab === DisplayPreferencesTab.WIDGET ? (
													<img src={ArrowUp} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDown} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.WIDGET && (
												<>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range
															value={widgetBorderRadius}
															onChange={(e) => setWidgetBorderRadius(parseFloat(e.target.value))}
															min="0"
															max="25"
															step="1"
														/>
														<ReadOnlyInput
															type="text"
															readOnly
															value={widgetBorderRadius}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
													<FlexRow className="mb-3">{translation.appCustomization.position}</FlexRow>
													<FlexSwitchRow>
														<FlexSwitchCol active={widgetPosition === "left"} onClick={() => setWidgetPosition("left")}>
															{translation.appCustomization.leftCorner}
														</FlexSwitchCol>
														<FlexSwitchCol
															active={widgetPosition === "right"}
															onClick={() => setWidgetPosition("right")}
														>
															{translation.appCustomization.rightCorner}
														</FlexSwitchCol>
													</FlexSwitchRow>
												</>
											)}
										</ListTabRow>

										<ListTabRow className="mb-3">
											<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.INLINE)}>
												{translation.appCustomization.inlineTab}
												{selectedTab === DisplayPreferencesTab.INLINE ? (
													<img src={ArrowUp} style={{ width: "15px" }} />
												) : (
													<img src={ArrowDown} style={{ width: "15px" }} />
												)}
											</ListTabHeading>
											{selectedTab === DisplayPreferencesTab.INLINE && (
												<>
													<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
													<FlexRow className="mb-3">
														<Form.Range
															value={inlineBorderRadius}
															onChange={(e) => setInlineBorderRadius(parseFloat(e.target.value))}
															min="0"
															max="40"
															step="1"
														/>
														<ReadOnlyInput
															type="text"
															readOnly
															value={inlineBorderRadius}
															style={{ marginLeft: "10px" }}
														/>
													</FlexRow>
												</>
											)}
										</ListTabRow>
									</>
								)}
							</Col>
							<Col sm="12" md="8" className="mb-4">
								<VideoPageTitleText className="mb-3">{translation.editCollection.videos}</VideoPageTitleText>

								<VideosSection style={videos.length === 0 ? { padding: "15%" } : { padding: "5% 15%" }}>
									<HeadingTextH2 className="mb-3">
										{videos.length === 0 ? translation.editCollection.addVideo : translation.editCollection.addVideos}
									</HeadingTextH2>
									<MainButton
										className="mt-3"
										type="button"
										data-testid="selectVideos"
										onClick={() => {
											setVideosModalStatus(true);
										}}
									>
										{translation.editCollection.selectVideos}
									</MainButton>
								</VideosSection>

								{videos?.length > 0 && <CollectionVideos videos={videos} setVideos={setVideos} />}
							</Col>
						</Row>
						<VideosModal
							visible={videosModalStatus}
							videosList={videos}
							setVideosList={setVideos}
							onCancel={() => setVideosModalStatus(false)}
							hideCreateVideo={hideCreateVideo}
						/>
						<ConfirmLeavingModal
							visible={modalStatus}
							onCancel={() => {
								setModalStatus(false);
								setGoToPrevPage(false);
							}}
							onContinue={() => {
								if (goToPrevPage) window.history.go(-2);
								else navigate(navigationUrl);
							}}
						/>
						<EnterPasswordModal
							visible={enterPassword}
							onCancel={() => setEnterPassword(false)}
							onContinue={updateCollection}
						/>
						{shareObject && (
							<ShareModal
								allowSharing={accountData?.subscription?.allowSharing === true}
								visible={shareModalStatus}
								onCancel={() => setShareModalStatus(false)}
								shareObject={shareObject}
							/>
						)}
					</>
				)}
			</PageBody>
		</>
	);
};

export default EditCollectionPage;

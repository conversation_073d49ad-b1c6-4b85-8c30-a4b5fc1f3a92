import React, { useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import { useTranslation } from "../hooks/translations";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput } from "@src/styles/forms";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const AddCompanyForm: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [validated, setValidated] = useState(false);
	const [error, setError] = useState("");
	const [loading, setLoading] = useState(false);
	const [companyName, setCompanyName] = useState("");

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			const API_ENDPOINT = process.env.API_ENDPOINT;
			const accessToken = localStorage.getItem("accessToken");
			const API_VERSION = process.env.API_VERSION;

			const bodyFormData = new FormData();
			const companyName = (form.elements.namedItem("companyName") as HTMLInputElement).value;
			bodyFormData.append("companyName", companyName);

			const returnAccount = await axios.request({
				url: `${API_ENDPOINT}/api/accounts`,
				method: "POST",
				data: bodyFormData,
				headers: {
					Authorization: "Bearer " + accessToken,
					"content-type": "multipart/form-data",
					"x-api-version": API_VERSION
				}
			});

			const bodyFormData2 = new FormData();
			bodyFormData2.append("accountId", returnAccount?.data?.account._id);

			const returnToken = await axios.request({
				url: `${API_ENDPOINT}/api/accounts/token`,
				method: "POST",
				data: bodyFormData2,
				headers: {
					Authorization: "Bearer " + accessToken,
					"content-type": "multipart/form-data",
					"x-api-version": API_VERSION
				}
			});

			sessionStorage.setItem("token", returnToken?.data?.token);
			setLoading(false);
			setError("");
			navigate("/");
		}
	};

	return (
		<FormBox>
			<HeadingText className="mb-3" data-testid="coreScriptText">
				{translation.AddCompanyPage.addCompany}
			</HeadingText>
			<BodyText data-testid="coreScriptSubText">{translation.AddCompanyPage.subText}</BodyText>
			{!!error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} />}

			<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated}>
				<Form.Group className="mb-3">
					<CustomInput
						className="form-control"
						type="text"
						required
						autoFocus
						placeholder={translation.AddCompanyPage.companyName}
						onChange={(value) => {
							setCompanyName(value.target.value);
						}}
						value={companyName}
						id="companyName"
						data-testid="companyName"
					/>
				</Form.Group>

				<MainButton type="submit" className="mt-3 mb-3" data-testid="coreScriptSubmit" disabled={loading}>
					{translation.AddCompanyPage.createCompany}
				</MainButton>

				<BodyText>
					<LinkButton
						data-testid="skipAddCompanyPage"
						onClick={() => {
							navigate("/");
						}}
					>
						{translation.AddCompanyPage.skip}
					</LinkButton>
				</BodyText>
			</Form>
		</FormBox>
	);
};

export default AddCompanyForm;

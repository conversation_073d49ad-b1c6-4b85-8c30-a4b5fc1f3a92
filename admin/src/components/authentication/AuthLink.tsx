import axios from "axios";
import React, { useEffect, useState } from "react";
import jwt_decode from "jwt-decode";
import { isLoggedInState } from "../authentication/state";
import { useSetRecoilState } from "recoil";

interface AuthLinkProps {
	children?: React.ReactNode;
}

interface AuthLinkToken {
	authenticationId: string;
	accountId: string;
	type: "authlink";
}

interface SignInResponse {
	accessToken: string;
	refreshToken: string;
}

interface AccountAuthResponse {
	token: string;
}

const authLinkSignIn = async (authlink: string): Promise<SignInResponse> => {
	const requestData = {
		method: "authlink",
		authlink: authlink
	};

	const responseData = await axios.request({
		url: `${process.env.API_ENDPOINT}/api/auth/sign-in`,
		method: "POST",
		data: JSON.stringify(requestData),
		headers: {
			"content-type": "application/json",
			"x-api-version": "1"
		}
	});

	return {
		accessToken: responseData.data.accessToken,
		refreshToken: responseData.data.refreshToken
	};
};

const getAccountToken = async (accountId: string, accessToken: string): Promise<AccountAuthResponse> => {
	const bodyFormData = new FormData();
	bodyFormData.append("accountId", accountId);

	const responseData = await axios.request({
		url: `${process.env.API_ENDPOINT}/api/accounts/token`,
		method: "POST",
		data: bodyFormData,
		headers: {
			Authorization: `Bearer ${accessToken}`,
			"content-type": "multipart/form-data",
			"x-api-version": "1"
		}
	});

	return {
		token: responseData.data.token
	};
};

const decodeAuthLink = (authlink: string): AuthLinkToken => {
	const decoded = jwt_decode(authlink) as AuthLinkToken;
	if (decoded.type !== "authlink") {
		throw new Error("Invalid authlink token");
	}

	return decoded;
};

const setBrowserSignInTokens = (tokens: SignInResponse) => {
	localStorage.setItem("accessToken", tokens.accessToken);
	localStorage.setItem("refreshToken", tokens.refreshToken);
};

const setBrowserAccountToken = (token: string) => {
	sessionStorage.setItem("token", token);
};

const processAuthLink = async (authlink: string) => {
	const authLinkData = decodeAuthLink(authlink);

	const signInResponse = await authLinkSignIn(authlink);
	setBrowserSignInTokens(signInResponse);

	const accountTokenResponse = await getAccountToken(authLinkData.accountId, signInResponse.accessToken);

	setBrowserAccountToken(accountTokenResponse.token);
};

const getAuthLinkParam = (): string | null => {
	const params = new URLSearchParams(window.location.search);
	return params.get("authlink");
};

const removeAuthLinkParam = () => {
	const params = new URLSearchParams(window.location.search);
	if (params.has("authlink")) {
		params.delete("authlink");
		const paramString = params.toString();
		const newUrl = paramString ? `${window.location.pathname}?${paramString}` : window.location.pathname;
		window.history.replaceState({}, "", newUrl);
	}
};

const AuthLink: React.FC<AuthLinkProps> = (props: AuthLinkProps) => {
	const [ready, setReady] = useState(false);
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);

	useEffect(() => {
		const authlink = getAuthLinkParam();
		if (authlink) {
			processAuthLink(authlink)
				.then(() => {
					setIsLoggedIn(true);
				})
				.catch((error: unknown) => {
					console.error("authlink error", error);
				})
				.finally(() => {
					removeAuthLinkParam();
					setReady(true);
				});
		} else {
			setReady(true);
		}
	}, [setIsLoggedIn, setReady]);

	return <>{ready ? props.children : null}</>;
};

export default AuthLink;

import { useEffect } from "react";
import LogRocket from "logrocket";
import jwt_decode from "jwt-decode";
import { accessToken } from "@src/types/videos";
import getAccount from "@src/components/hooks/getAccount";
import { useTokenCheck } from "@src/components/hooks/useTokenCheck";
import SessionManagerSingleton from "@src/components/events/SessionManagerSingleton";

export const useLogRocket = () => {
	const { apiRetryHandler } = useTokenCheck();

	useEffect(() => {
		const semanticReleaseVersion = "1.0.0";
		const rootHostname = process.env.CMS_ENDPOINT;
		const logRocketToken = process.env.LOGROCKET_TOKEN as string;

		if (!logRocketToken) {
			console.info("LogRocket token is missing.");
			return;
		}

		LogRocket.init(logRocketToken, {
			release: semanticReleaseVersion,
			rootHostname: rootHostname,
			console: {
				// Enable console log capturing
				isEnabled: true
			},
			shouldDebugLog: false,
			network: {
				// Disable network data recording
				isEnabled: false
			}
		});

		(async () => {
			const accessToken = localStorage.getItem("accessToken");
			const accountToken = sessionStorage.getItem("token");
			if (accessToken && accountToken) {
				const decoded = jwt_decode(accessToken) as accessToken;
				const dbUserId = decoded?.userId;
				const sessionManager = SessionManagerSingleton.getInstance();
				const session = sessionManager.getSession();
				const { data, error } = await apiRetryHandler(async () => await getAccount());

				if (!error && dbUserId) {
					LogRocket.identify(dbUserId, {
						name: data?.firstName + " " + data?.lastName,
						email: data?.email,
						sessionId: session?.id ? session?.id : "",
						companyName: data?.companyName
					});
				}
			}
		})();
	});
};

/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import axios from "axios";
import { useSetRecoilState } from "recoil";
import { isLoggedInState } from "../authentication/state";
import jwt_decode from "jwt-decode";
import { accessToken, refreshToken } from "@src/types/videos";

export const useTokenCheck = () => {
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;

	const handleLogout = useCallback(() => {
		localStorage.removeItem("accessToken");
		localStorage.removeItem("refreshToken");
		localStorage.removeItem("userHash");
		localStorage.removeItem("oidcClientId");
		sessionStorage.removeItem("token");
		setIsLoggedIn(false);
	}, [setIsLoggedIn]);

	const apiRetryHandler = useCallback(
		async (callBack: () => any, retryCounter = 0): Promise<any> => {
			try {
				//check locally if accessToken is expired
				const accessToken = localStorage.getItem("accessToken");
				if (accessToken) {
					const decoded = jwt_decode(accessToken) as accessToken;
					const timestamp = Math.floor(Date.now() / 1000);
					if (!decoded.exp || decoded.exp < timestamp) {
						throw { response: { data: { error: "E_INVALID_AUTHORIZATION" } } };
					}
				} else {
					throw { response: { data: { error: "E_INVALID_AUTHORIZATION" } } };
				}

				const { data, error } = await callBack();
				return { data, error };
			} catch (error: any) {
				if (error?.response?.data?.error === "E_INVALID_AUTHORIZATION") {
					if (retryCounter < 1) {
						try {
							const oidcClientId = localStorage.getItem("oidcClientId");
							const refreshToken = localStorage.getItem("refreshToken");

							if (refreshToken && oidcClientId) {
								const requestOidcObject = new FormData();
								requestOidcObject.append("oidcRefreshToken", refreshToken);
								requestOidcObject.append("oidcClientId", oidcClientId);

								const returnOidcObject = await axios.request({
									url: `${API_ENDPOINT}/api/oauth/oidc/token`,
									method: "PUT",
									data: requestOidcObject,
									headers: {
										"content-type": "multipart/form-data",
										"x-api-version": API_VERSION
									}
								});

								if (
									returnOidcObject?.data?.oidcAccessToken &&
									returnOidcObject?.data?.oidcIdToken &&
									returnOidcObject?.data?.oidcRefreshToken
								) {
									const requestObject = new FormData();
									requestObject.append("oidcIdToken", returnOidcObject?.data?.oidcIdToken);
									requestObject.append("oidcAccessToken", returnOidcObject?.data?.oidcAccessToken);
									requestObject.append("oidcClientId", oidcClientId);

									const returnObject = await axios.request({
										url: `${API_ENDPOINT}/api/oauth/oidc`,
										method: "POST",
										data: requestObject,
										headers: {
											"content-type": "multipart/form-data",
											"x-api-version": API_VERSION
										}
									});

									if (returnObject?.data?.accessToken) {
										localStorage.setItem("accessToken", returnObject.data.accessToken);
										localStorage.setItem("refreshToken", returnOidcObject?.data?.oidcRefreshToken);
										localStorage.setItem("oidcClientId", oidcClientId);
										if (returnObject.data.userHash) localStorage.setItem("userHash", returnObject.data.userHash);

										return await apiRetryHandler(callBack, 1);
									}

									throw new Error("Missing Access Token");
								} else {
									throw new Error("Missing OIDC Access Token");
								}
							} else {
								if (refreshToken) {
									//check locally if refreshToken is expired
									const decoded = jwt_decode(refreshToken) as refreshToken;
									const timestamp = Math.floor(Date.now() / 1000);
									if (!decoded.exp || decoded.exp < timestamp) {
										throw new Error("Refresh Token expired");
									}

									const requestObject = {
										method: "refreshtoken",
										token: refreshToken
									};

									const returnObject = await axios.request({
										url: `${API_ENDPOINT}/api/auth/sign-in`,
										method: "POST",
										data: JSON.stringify(requestObject),
										headers: {
											"content-type": "application/json",
											"x-api-version": API_VERSION
										}
									});

									if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
										localStorage.setItem("accessToken", returnObject.data.accessToken);
										localStorage.setItem("refreshToken", returnObject.data.refreshToken);
										if (returnObject.data.userHash) localStorage.setItem("userHash", returnObject.data.userHash);

										return await apiRetryHandler(callBack, 1);
									}

									throw new Error("Missing Access Token");
								} else {
									throw new Error("Missing Refresh Token");
								}
							}
						} catch (error: any) {
							handleLogout();
							return { error };
						}
					} else {
						handleLogout();
						return { error };
					}
				} else {
					return { error };
				}
			}
		},
		[API_ENDPOINT, API_VERSION, handleLogout]
	);

	return { apiRetryHandler };
};

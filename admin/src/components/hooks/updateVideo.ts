import axios from "axios";
import { FileInput } from "@src/types/videos";
import { CaptionData } from "@src/types/caption";

interface EditCollectionProps {
	id: string;
	videoTitleValue: string;
	videoDescValue: string;
	uploadFileURL: string;
	coverImageLink: string;
	gifURL: string;
	videoPosterPlayEmbedURL: string;
	videoButtonTextValue: string;
	displayFormat: string;
	productsInputs?: FileInput[];
	displayTitle?: boolean;
	emailAddress: string;
	phoneNumber: string;
	password?: string;
	captionData?: CaptionData;
}

const updateVideo = async ({
	id,
	videoTitleValue,
	videoDescValue,
	uploadFileURL,
	coverImageLink,
	gifURL,
	videoPosterPlayEmbedURL,
	videoButtonTextValue,
	displayFormat,
	productsInputs,
	displayTitle,
	emailAddress,
	phoneNumber,
	password,
	captionData
}: EditCollectionProps) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");

	const bodyFormData = new FormData();
	bodyFormData.append("title", videoTitleValue);
	bodyFormData.append("description", videoDescValue);
	bodyFormData.append("videoURL", uploadFileURL);
	bodyFormData.append("ctaText", videoButtonTextValue);
	bodyFormData.append("showTitle", displayTitle ? displayTitle.toString() : "false");
	bodyFormData.append("videoPosterImageURL", coverImageLink);
	bodyFormData.append("gifURL", gifURL);
	bodyFormData.append("videoPosterPlayEmbedURL", videoPosterPlayEmbedURL);
	bodyFormData.append("videoDisplayMode", displayFormat);
	bodyFormData.append("email", emailAddress ? emailAddress : "");

	if (captionData) {
		bodyFormData.append("captionData", JSON.stringify(captionData));
	}

	if (phoneNumber) {
		const cleanedPhoneNumber = phoneNumber.replace(/\D/g, "");
		bodyFormData.append("phone", `+1${cleanedPhoneNumber}`);
	} else {
		bodyFormData.append("phone", "");
	}

	productsInputs?.map((x, i) => {
		if (x.productTitle != "" && x.productURL != "") {
			bodyFormData.append("productTitle" + i, x.productTitle);
			bodyFormData.append("productURL" + i, x.productURL);
			if (x.subTitle) bodyFormData.append("subTitle" + i, x.subTitle);
			if (x.productImageFile) {
				bodyFormData.append("productImageFile" + i, x.productImageFile as File);
			} else {
				if (x?.imagePreview) bodyFormData.append("productImageURL" + i, x.imagePreview);
			}
		}
	});

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "multipart/form-data",
		"Authorization": "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	if (password) queryHeaders["x-super-password"] = password;

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-videos/${id}`,
		method: "PUT",
		data: bodyFormData,
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data };
};

export default updateVideo;

import axios from "axios";

const uploadFile = async (file: File) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const accessToken = localStorage.getItem("accessToken");
	let data:
		| {
			id: string;
			name: string;
			url: string;
		}
		| undefined;

	if (file.size === 0) {
		throw Error("error");
	}

	const fileType = file.type;

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/files`,
		method: "POST",
		data: [],
		headers: {
			"Authorization": "Bearer " + accessToken,
			"Content-Type": fileType,
			"X-Filename": file.name,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const location = queryResp?.data?.location;

	let chunkSize = 256 * 1024;
	const minChunkSizeBytes = chunkSize;
	const maxChunkSizeBytes = 4096 * 1024;
	const minChunkTimeMs = 2000;
	const maxChunkTimeMs = 4000;
	let startPointer = 0;

	while (startPointer < file.size) {
		const chunkStartTime = Date.now();
		let endPointer = startPointer + chunkSize;
		if (endPointer > file.size) {
			endPointer = file.size;
		}

		const chunk = file.slice(startPointer, endPointer);
		const contentRange = `bytes ${startPointer}-${endPointer - 1}/${file.size}`;
		const requestHeaders: HeadersInit = new Headers();
		const newVal = endPointer - startPointer;

		requestHeaders.set("Authorization", "Bearer " + accessToken);
		requestHeaders.set("location", location);
		requestHeaders.set("Content-Type", fileType);
		requestHeaders.set("Content-Length", newVal as unknown as string);
		requestHeaders.set("Content-Range", contentRange);
		requestHeaders.set("x-api-version", API_VERSION as string);
		requestHeaders.set("x-account-token", accountToken as string);

		const queryResponse = await fetch(`${API_ENDPOINT}/api/files`, {
			method: "PUT",
			headers: requestHeaders,
			body: chunk
		});

		if (queryResponse.status !== 308 && queryResponse.status !== 200) {
			throw Error("error");
		}

		startPointer += chunkSize;
		const chunkTime = Date.now() - chunkStartTime;
		if (chunkTime < minChunkTimeMs && chunkSize < maxChunkSizeBytes) {
			// increasing chunk size
			chunkSize *= 2;
		} else if (chunkTime > maxChunkTimeMs && chunkSize > minChunkSizeBytes) {
			// decreasing chunk size
			chunkSize /= 2;
		}

		if (queryResponse.status === 200) {
			data = await queryResponse.json();
		}
	}

	return { data };
};

export default uploadFile;

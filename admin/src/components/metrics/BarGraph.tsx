import React from "react";
import { BarGraphContainer, BarGraphItem, BarGraphItemContainer, BarGraphLabel } from "@src/styles/components";

const BarGraph = ({ percent20, percent40, percent60, percent80, percent100, blur }: {percent20: number; percent40: number; percent60: number; percent80: number; percent100: number; blur?: boolean}) => {
	const maxValue = Math.max(percent20, percent40, percent60, percent80, percent100);
	const maxBarHeightPx = 85;

	return (
		<>
			<BarGraphContainer style={{ filter: blur ? "blur(7px)" : "none" }}>
				<BarGraphItemContainer>
					<BarGraphItem value={(percent20 / maxValue) * maxBarHeightPx} num={1} />
					<BarGraphLabel>20%</BarGraphLabel>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem value={(percent40 / maxValue) * maxBarHeightPx} num={2} />
					<BarGraphLabel>40%</BarGraphLabel>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem value={(percent60 / maxValue) * maxBarHeightPx} num={3} />
					<BarGraphLabel>60%</BarGraphLabel>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem value={(percent80 / maxValue) * maxBarHeightPx} num={4} />
					<BarGraphLabel>80%</BarGraphLabel>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem value={(percent100 / maxValue) * maxBarHeightPx} num={5} />
					<BarGraphLabel>100%</BarGraphLabel>
				</BarGraphItemContainer>
			</BarGraphContainer>
		</>
	);
};

export default BarGraph;

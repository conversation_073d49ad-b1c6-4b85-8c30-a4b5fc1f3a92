import { Score, ScoreBar, ScorebarContainer } from "@src/styles/components";
import React from "react";

const Scorebar = ({ score, blur }: {score: number; blur?: boolean}) => {
	return (
		<ScorebarContainer style={{ filter: blur ? "blur(7px)" : "none" }}>
			<ScoreBar score={score}>{score >= 5 && <Score>{score}</Score>}</ScoreBar>
			{score < 5 && <Score displayOnSide={true}>{score}</Score>}
		</ScorebarContainer>
	);
};

export default Scorebar;

import React, { useState, useEffect } from "react";
import { Form, Row, Col } from "react-bootstrap";
import Spinner from "react-bootstrap/Spinner";
import { useDropzone } from "react-dropzone";
import Skeleton from "react-loading-skeleton";
import {
	HeadingText,
	ConfirmationBox,
	ConfirmationBoxWrapper,
	BodyText,
	CloneIconDiv,
	CloneIcon,
	CustomInput
} from "@src/styles/forms";
import {
	PageBody,
	PageRow,
	MainButton,
	VideoPageTitleText,
	LogoImageBox,
	LogoBox,
	CompanyLogo,
	Flex,
	ListBox,
	HeaderListTitle,
	TabHeading,
	CodeSnippet,
	CodeSnippetDiv,
	CustomHighlight,
	HighlightCodeModal,
	TextLink,
	ListRowItem,
	ListItemContainer
} from "@src/styles/components";
import { User } from "@src/types/users";
import getUsersWithStatus from "../hooks/getUsers";
import jwt_decode from "jwt-decode";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../hooks/getErrorString";
import getAccount from "../hooks/getAccount";
import updateAccount from "../hooks/updateAccount";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { accessToken, accountToken, companyDetails, SettingsTab } from "@src/types/videos";
import UserListItem from "./UserListItem";
import { InviteUserModal } from "../modals/InviteUserModal";
import { EnterPasswordModal } from "../modals/EnterPasswordModal";
import { ApiKeyModal } from "../modals/ApiKeyModal";
import { usePasswordRequired } from "../hooks/getPasswordRequired";
import inviteUser from "../hooks/inviteUser";
import saveAPIKey from "../hooks/saveAPIKey";
import { Key } from "@src/types/keys";
import getAPIKeys from "../hooks/getAPIKeys";
import KeyListItem from "./KeyListItem";
import { DisplayApiKeyModal } from "../modals/DisplayApiKeyModal";
import { DeletePaymentMethodModal } from "../modals/DeletePaymentMethodModal";
import getAccessToken from "../hooks/getAccessToken";
import { ChangePlanModal } from "../modals/ChangePlanModal";
import { StripeInvoice } from "@src/types/invoices";
import { formatDate } from "@src/utils/dates";
import { Account } from "@src/types/account";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { PaymentMethod } from "@src/types/payment";
import getPaymentMethods from "../hooks/getPaymentMethods";
import { EditSettingsPlan } from "./EditSettingsPlan";
import deepEqual from "deep-equal";

interface Props {
	accountData: Account | undefined;
	refetch: boolean;
	setRefetch: (val: boolean) => void;
	clockTime?: number | undefined;
}

// eslint-disable-next-line max-lines-per-function
const EditSettings: React.FC<Props> = ({ accountData, refetch, setRefetch, clockTime }) => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [settingsError, setSettingsError] = useState("");
	const [confirmationText, setConfirmationText] = useState("");
	const [settingsData, setSettingsData] = useState<companyDetails>();
	const [settingsDataCopy, setSettingsDataCopy] = useState<companyDetails>();
	const [loading, setLoading] = useState(false);
	const [generateButton, setGenerateButton] = useState(true);
	const [companyLogo, setCompanyLogo] = useState<File>();
	const [logoPreview, setLogoPreview] = useState("");
	const [companyName, setCompanyName] = useState("");
	const [companyURL, setCompanyURL] = useState("");
	const [email, setEmail] = useState("");
	const [firstName, setFirstName] = useState("");
	const [lastName, setLastName] = useState("");
	const [users, setUsers] = useState<User[]>([]);
	const [modalStatus, setModalStatus] = useState(false);
	const [deleteModalStatus, setDeleteModalStatus] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const [enterPasswordInv, setEnterPasswordInv] = useState(false);
	const [enterPasswordAPIKey, setEnterPasswordAPIKey] = useState(false);
	const [apiModalStatus, setApiModalStatus] = useState(false);
	const [displayApiModalStatus, setDisplayApiModalStatus] = useState(false);
	const [validated, setValidated] = useState(false);
	const passwordRequired = usePasswordRequired();
	const [invEmail, setInvEmail] = useState<string>("");
	const [apiKeys, setApiKeys] = useState<Key[]>([]);
	const [keyLimitReached, setKeyLimitReached] = useState(false);
	const [lastGeneratedKey, setLastGeneratedKey] = useState("");
	const [selectedTab, setSelectedTab] = useState<SettingsTab>(SettingsTab.GENERAL);
	const embedScript = `<script type='text/javascript' src='${process.env.APP_ENDPOINT}/gp-conversion.js'></script>`;
	const [displayApPro, setDisplayApPro] = useState(false);
	const [invoices, setInvoices] = useState<StripeInvoice[]>([]);
	const [subscriptionPrice, setSubscriptionPrice] = useState(1);
	const [paymentMethods, setPaymentMethods] = useState<PaymentMethod | null>(null);
	const showHelpCentre = process.env.SHOW_HELP_CENTRE === "true";

	const showConfirmationText = (value: string) => {
		setConfirmationText(value);
		setTimeout(() => {
			setConfirmationText("");
		}, 3000);
	};

	const updateCompany = async (password?: string) => {
		setGenerateButton(true);
		setLoading(true);
		const { error } = await apiRetryHandler(
			async () => await updateAccount({ logo: companyLogo as File, firstName, lastName, companyURL, companyName, password })
		);

		if (error) {
			setGenerateButton(false);
			setLoading(false);
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setSettingsError(errorText);
		} else {
			setSettingsError("");
			setConfirmationText(translation.accountSettingsPage.dataUpdated);

			// Get account token from same account to set new Company Name from token
			if (companyName !== settingsData?.companyName) {
				const decodedAccountToken = jwt_decode(sessionStorage.getItem("token") as string) as accountToken;
				await apiRetryHandler(async () => await getAccessToken(decodedAccountToken.account._id));
			}
			setTimeout(() => {
				setConfirmationText("");
				setRefetch(true);
				setLoading(false);
				setEnterPassword(false);
			}, 1000);
		}
	};

	const handleSave = () => {
		if (passwordRequired) {
			setEnterPassword(true);
		} else {
			updateCompany();
		}
	};

	const saveKey = async (password?: string) => {
		setGenerateButton(true);
		setLoading(true);
		const { data, error } = await apiRetryHandler(async () => await saveAPIKey(password));

		if (error) {
			setGenerateButton(false);
			setLoading(false);
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setSettingsError(errorText);
		} else {
			setSettingsError("");
			setConfirmationText(translation.accountSettingsPage.dataUpdated);
			setTimeout(() => {
				setConfirmationText("");
				setLoading(false);
				setEnterPasswordAPIKey(false);
				setApiModalStatus(false);
				setLastGeneratedKey(data?.apikey);
				setDisplayApiModalStatus(true);
			}, 1000);
		}
	};

	const handleSaveAPIKey = () => {
		if (passwordRequired) {
			setEnterPasswordAPIKey(true);
		} else {
			saveKey();
		}
	};

	const handleDisplayApiKeyClose = () => {
		setDisplayApiModalStatus(false);
		setLastGeneratedKey("");
		setRefetch(true);
	};

	const handleInvite = async (password?: string) => {
		if (invEmail) {
			setLoading(true);
			const { error } = await apiRetryHandler(async () => await inviteUser(invEmail, translation.locale, password));
			if (error) {
				const errorText = getErrorString(translation, error?.response?.data?.error);
				setSettingsError(errorText);
				setValidated(false);
				setLoading(false);
			} else {
				setSettingsError("");
				setRefetch(true);
				setModalStatus(false);
				setEnterPasswordInv(false);
				showConfirmationText(translation.modals.inviteUserConfirmation);
				setLoading(false);
			}
		}
	};

	// company logo image upload
	const { getRootProps: getImageProps, getInputProps: getImageFileProps } = useDropzone({
		maxFiles: 1,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/jpg": []
		},
		onDrop: (acceptedFiles) => {
			setSettingsError("");
			const reader = new FileReader();
			reader.readAsDataURL(acceptedFiles[0]);
			reader.onload = () => {
				if (reader?.result) {
					const image = new Image();
					image.src = reader.result as string;
					image.onload = () => {
						setCompanyLogo(acceptedFiles[0]);
						setLogoPreview(reader.result as string);
						setSettingsDataCopy({
							...settingsDataCopy,
							companyLogo: reader.result as string
						});
					};
				} else {
					setSettingsError(translation.errors.imageFormat);
				}
			};
		}
	});

	const dataChanged = !deepEqual(settingsData, settingsDataCopy);

	useEffect(() => {
		const hash = window.location.hash;
		if (hash === `#${SettingsTab.INSTALLATION.toLowerCase()}`) {
			setSelectedTab(SettingsTab.INSTALLATION);
		} else if (hash === `#${SettingsTab.BILLING.toLowerCase()}`) {
			setSelectedTab(SettingsTab.BILLING);
		}
	}, []);

	useEffect(() => {
		if (dataChanged && companyName !== "") {
			setGenerateButton(false);
		} else {
			setGenerateButton(true);
		}
	}, [dataChanged, companyName]);

	useEffect(() => {
		const go = async () => {
			const { data, error } = await apiRetryHandler(async () => await getAccount());

			if (error) {
				setSettingsError(translation.accountSettingsPage.loadingIssue);
			} else {
				setSettingsData(data as companyDetails);
				setSettingsDataCopy(data as companyDetails);

				setLogoPreview(data?.companyLogo);
				setCompanyName(data?.companyName);
				setCompanyURL(data?.companyURL);
				setEmail(data?.email);
				setFirstName(data?.firstName);
				setLastName(data?.lastName);
			}

			const accessToken = localStorage.getItem("accessToken");
			const decoded = accessToken ? (jwt_decode(accessToken) as accessToken) : { userId: "" };

			const { data: usersData, error: usersError } = await apiRetryHandler(
				async () => await getUsersWithStatus(decoded.userId)
			);
			if (usersError) {
				setSettingsError(translation.accountSettingsPage.loadingIssue);
			} else {
				setUsers(usersData);
			}

			const { data: keysData, error: keysError } = await apiRetryHandler(async () => await getAPIKeys());
			if (keysError) {
				setSettingsError(translation.accountSettingsPage.loadingIssue);
			} else {
				setApiKeys(keysData);
				if (keysData.length >= 5) setKeyLimitReached(true);
				else setKeyLimitReached(false);
			}

			if (accountData) {
				setSubscriptionPrice(accountData.subscription?.price);
				setInvoices(accountData.invoices);

				const { data: paymentMethodData } = await apiRetryHandler(async () => await getPaymentMethods());
				if (paymentMethodData?.paymentMethods[0]) {
					setPaymentMethods(paymentMethodData.paymentMethods[0] ?? null);
				} else {
					setPaymentMethods(null);
				}
			}

			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [refetch, setRefetch, apiRetryHandler, translation.accountSettingsPage.loadingIssue, accountData]);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!settingsError && <ErrorMessage error={settingsError} setError={setSettingsError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			<PageBody>
				<PageRow className="mb-4">
					<HeadingText data-testid="editVideoPage">{translation.accountSettingsPage.pageTitle}</HeadingText>
					<MainButton type="button" data-testid="updateButton" disabled={generateButton} onClick={handleSave}>
						{translation.accountSettingsPage.saveChanges}
						{loading ? (
							<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
						) : (
							""
						)}
					</MainButton>
				</PageRow>
				{!refetch && (
					<Flex style={{ gap: "2rem", margin: "2rem 0", fontSize: "1.2rem", fontWeight: "bold" }}>
						<TabHeading
							selected={selectedTab === SettingsTab.GENERAL}
							onClick={() => setSelectedTab(SettingsTab.GENERAL)}
						>
							{translation.accountSettingsPage.generalTabHeading}
						</TabHeading>
						<TabHeading
							selected={selectedTab === SettingsTab.INSTALLATION}
							onClick={() => setSelectedTab(SettingsTab.INSTALLATION)}
						>
							{translation.accountSettingsPage.installationTabHeading}
						</TabHeading>
						<TabHeading
							selected={selectedTab === SettingsTab.BILLING}
							onClick={() => setSelectedTab(SettingsTab.BILLING)}
						>
							{translation.accountSettingsPage.billingTabHeading}
						</TabHeading>
					</Flex>
				)}
				{refetch ? (
					<Row pl="0" pr="0">
						<Col sm="12" md="4" className="mb-4">
							<Skeleton count={10} height={50} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<Skeleton height={535} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<Skeleton height={535} />
						</Col>
					</Row>
				) : (
					(selectedTab === SettingsTab.GENERAL && (
						<Row pl="0" pr="0">
							<Col sm="12" md="4" className="mb-4">
								<VideoPageTitleText className="mb-3">
									{translation.accountSettingsPage.companyDetails}
								</VideoPageTitleText>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={translation.accountSettingsPage.company}
										id="companyName"
										data-testid="companyName"
										onChange={(value) => {
											setCompanyName(value.target.value);
											setSettingsDataCopy({
												...settingsDataCopy,
												companyName: value.target.value
											});
										}}
										value={companyName}
									/>
								</Form.Group>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={translation.accountSettingsPage.companyURL}
										id="companyURL"
										data-testid="companyURL"
										onChange={(value) => {
											setCompanyURL(value.target.value);
											setSettingsDataCopy({
												...settingsDataCopy,
												companyURL: value.target.value
											});
										}}
										value={companyURL}
									/>
								</Form.Group>
							</Col>
							<Col sm="12" md="4" className="mb-4">
								<VideoPageTitleText className="mb-3">{translation.accountSettingsPage.companyLogo}</VideoPageTitleText>

								<div {...getImageProps()} data-testid="logoDiv">
									<input id="companyLogo" data-testid="companyLogo" {...getImageFileProps()} />
									<LogoBox>
										{logoPreview ? (
											<>
												<LogoImageBox data-testid="LogoBox">
													<CompanyLogo data-testid="uploadedLogo" src={logoPreview} />
												</LogoImageBox>
											</>
										) : (
											<>
												<BodyText data-testid="uploadLogoImage" className="mt-3">
													{translation.accountSettingsPage.uploadText}
												</BodyText>
											</>
										)}
										<MainButton type="button" data-testid="updateButton">
											{translation.accountSettingsPage.upload}
										</MainButton>
									</LogoBox>
								</div>
							</Col>
							<Col sm="12" md="4" className="mb-4"></Col>
						</Row>
					)) ||
					(selectedTab === SettingsTab.INSTALLATION && (
						<Row pl="0" pr="0">
							<Col sm="12" md="4" className="mb-4">
								<VideoPageTitleText className="mb-3">{translation.accountSettingsPage.apiKeys}</VideoPageTitleText>
								<div style={{ marginTop: "1rem" }}>
									{apiKeys &&
										apiKeys.map((key: Key, index: number) => (
											<KeyListItem
												numberId={index}
												key={key._id}
												keyItem={key}
												refreshList={() => setRefetch(true)}
												showConfirmationText={showConfirmationText}
												showDeleteError={setSettingsError}
											/>
										))}
								</div>
								<MainButton
									type="button"
									data-testid="generateKey"
									onClick={() => setApiModalStatus(true)}
									disabled={keyLimitReached}
								>
									{translation.accountSettingsPage.generateKey}
								</MainButton>
							</Col>
							{accountData?.subscription.enableConversionMetrics && (
								<Col sm="12" md="4" className="mb-4">
									<VideoPageTitleText className="mb-3">
										{translation.accountSettingsPage.shopifyConversion}
									</VideoPageTitleText>

									<BodyText className="mb-3 p-2">{translation.accountSettingsPage.trackConversions2}</BodyText>
									<div style={{ position: "relative" }}>
										<CloneIconDiv style={{ width: "100%", position: "absolute", pointerEvents: "none" }}>
											<CloneIcon
												data-testid="CloneIcon"
												style={{ float: "right", marginTop: "9px", marginRight: "9px", pointerEvents: "auto" }}
												onClick={() => {
													navigator.clipboard.writeText(embedScript);
												}}
											/>
										</CloneIconDiv>
										<CodeSnippet>
											<CustomHighlight data-testid="highlightBox" style={{ height: "initial" }}>
												<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
													{embedScript}
												</HighlightCodeModal>
											</CustomHighlight>
										</CodeSnippet>
									</div>
									<BodyText className="mt-3 p-2">{translation.accountSettingsPage.trackConversions1}</BodyText>
								</Col>
							)}
							<Col sm="12" md="4" className="mb-4">
								{showHelpCentre && (
									<CodeSnippetDiv>
										<CustomHighlight data-testid="highlightBox" style={{ height: "initial" }}>
											<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
												<VideoPageTitleText className="mb-3">
													{translation.accountSettingsPage.needHelp}
												</VideoPageTitleText>
												<BodyText className="mb-3 p-2">{translation.accountSettingsPage.resourcesText}</BodyText>
												<MainButton
													type="button"
													onClick={() => window.open(translation.modals.helpCentreLink, "_blank")}
													className="mx-auto mt-3"
												>
													{translation.accountSettingsPage.installationGuide}
												</MainButton>
											</HighlightCodeModal>
										</CustomHighlight>
									</CodeSnippetDiv>
								)}
							</Col>
						</Row>
					)) ||
					(selectedTab === SettingsTab.BILLING && (
						<Row pl="0" pr="0">
							<Col sm="12" md="3" className="mb-4">
								<VideoPageTitleText className="mb-3">{translation.accountSettingsPage.plan}</VideoPageTitleText>
								<EditSettingsPlan
									accountData={accountData}
									clockTime={clockTime}
									paymentMethod={paymentMethods}
									setDisplayApPro={setDisplayApPro}
								/>
							</Col>
							<Col sm="12" md="3" className="mb-4">
								<VideoPageTitleText className="mb-3">
									{translation.accountSettingsPage.billingDetails}
								</VideoPageTitleText>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={companyName}
										id="companyName"
										disabled={true}
									/>
									<ListRowItem disabled={true} className="mb-3 p-2">
										{translation.accountSettingsPage.invoiceCompanyName}
									</ListRowItem>
								</Form.Group>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={firstName + " " + lastName}
										id="billingContact"
										disabled={true}
									/>
								</Form.Group>
								<Form.Group className="mb-3">
									<CustomInput
										className="form-control"
										type="text"
										required
										placeholder={email}
										id="email"
										disabled={true}
									/>
									<ListRowItem disabled={true} className="mb-3 p-2">
										{translation.accountSettingsPage.invoiceEmail}
									</ListRowItem>
								</Form.Group>
							</Col>
							<Col sm="12" md="3" className="mb-4">
								<VideoPageTitleText className="mb-3">
									{translation.accountSettingsPage.paymentMethod}
								</VideoPageTitleText>
								{paymentMethods?.id ? (
									<>
										<Form.Group className="mb-3">
											<CustomInput
												className="form-control"
												type="text"
												id="last4digits"
												disabled={true}
												value={"************" + paymentMethods.last4}
											/>
										</Form.Group>
										<BodyText className="mb-3 p-2">
											<TextLink data-testid="paymentMethodChange" onClick={() => setDisplayApPro(true)}>
												{translation.accountSettingsPage.paymentMethodChange}
											</TextLink>
										</BodyText>
										{subscriptionPrice === 0 && (
											<BodyText className="mb-3 p-2">
												<TextLink data-testid="deletePaymentMethod" onClick={() => setDeleteModalStatus(true)}>
													{translation.modals.deletePaymentMethod}
												</TextLink>
											</BodyText>
										)}
									</>
								) : (
									<TextLink style={{ marginLeft: "0.5rem" }} onClick={() => setDisplayApPro(true)}>
										{translation.accountSettingsPage.addPaymentMethod}
									</TextLink>
								)}
								<VideoPageTitleText className="mb-3 mt-5">
									{translation.accountSettingsPage.invoices}
								</VideoPageTitleText>
								{invoices?.length > 0 ? (
									<>
										{invoices.map((invoice) => (
											<ListItemContainer key={invoice.id}>
												<Flex>
													<ListBox style={{ width: "5rem" }} clickable={false}>
														<ListRowItem disabled={true}>{formatDate(new Date(invoice.created * 1000))}</ListRowItem>
													</ListBox>
													<ListBox style={{ width: "calc(100% - 5rem)" }} clickable={true}>
														<ListRowItem style={{ width: "100%" }}>
															<TextLink
																href={invoice.pdfInvoiceURL}
																style={{
																	textDecoration: "none",
																	backgroundColor: "initial",
																	textAlign: "right",
																	display: "block",
																	overflow: "hidden",
																	textOverflow: "ellipsis"
																}}
															>
																{invoice.id}
															</TextLink>
														</ListRowItem>
													</ListBox>
												</Flex>
											</ListItemContainer>
										))}
									</>
								) : (
									<BodyText className="mt-3 p-2">{translation.accountSettingsPage.invoicesText}</BodyText>
								)}
							</Col>
							<Col sm="12" md="3" className="mb-4">
								{showHelpCentre && (
									<CodeSnippetDiv>
										<CustomHighlight data-testid="highlightBox" style={{ height: "initial" }}>
											<HighlightCodeModal className="language-html" style={{ backgroundColor: "initial" }}>
												<VideoPageTitleText className="mb-3">
													{translation.accountSettingsPage.needHelp}
												</VideoPageTitleText>
												<BodyText className="mb-3 p-2">{translation.accountSettingsPage.resourcesText}</BodyText>
												<MainButton
													type="button"
													onClick={() => window.open(translation.modals.helpCentreLink, "_blank")}
													className="mx-auto mt-3"
												>
													{translation.accountSettingsPage.resources}
												</MainButton>
											</HighlightCodeModal>
										</CustomHighlight>
									</CodeSnippetDiv>
								)}
							</Col>
						</Row>
					))
				)}
				{selectedTab === SettingsTab.GENERAL && (
					<>
						<hr />
						<div>&nbsp;</div>
						<PageRow className="mb-4">
							<HeadingText data-testid="usersTitle">{translation.accountSettingsPage.usersTitle}</HeadingText>
							<MainButton
								type="button"
								data-testid="inviteButton"
								onClick={() => {
									setInvEmail("");
									setModalStatus(true);
								}}
							>
								{translation.accountSettingsPage.inviteUser}
							</MainButton>
						</PageRow>
					</>
				)}

				{refetch ? (
					<Row pl="0" pr="0">
						<Skeleton className="mt-2" count={8} height={50} />
					</Row>
				) : (
					selectedTab === SettingsTab.GENERAL && (
						<Row pl="0" pr="0">
							<Flex className="mt-4">
								<ListBox style={{ width: "30%" }}>
									<HeaderListTitle data-testid="usersListEmail">
										{translation.accountSettingsPage.usersName}
									</HeaderListTitle>
								</ListBox>
								<ListBox style={{ width: "30%" }}>
									<HeaderListTitle data-testid="usersListStatus">
										{translation.accountSettingsPage.usersEmail}
									</HeaderListTitle>
								</ListBox>
								<ListBox style={{ width: "30%" }}>
									<HeaderListTitle data-testid="usersListStatus">
										{translation.accountSettingsPage.usersStatus}
									</HeaderListTitle>
								</ListBox>
								<ListBox style={{ width: "10" }} last={true}></ListBox>
							</Flex>

							{users &&
								users.map((user: User, index: number) => (
									<UserListItem
										numberId={index}
										key={user._id}
										userItem={user}
										refreshList={() => setRefetch(true)}
										showConfirmationText={showConfirmationText}
										showDeleteError={setSettingsError}
									/>
								))}
						</Row>
					)
				)}
				<InviteUserModal
					invEmail={invEmail}
					setInvEmail={setInvEmail}
					visible={modalStatus}
					setVisible={setModalStatus}
					validated={validated}
					handleInvite={handleInvite}
					setEnterPassword={setEnterPasswordInv}
					passwordRequired={passwordRequired ?? false}
					loading={loading}
					onCancel={() => {
						setModalStatus(false);
					}}
				/>
				<EnterPasswordModal
					visible={enterPassword}
					onCancel={() => setEnterPassword(false)}
					onContinue={updateCompany}
				/>
				<EnterPasswordModal
					visible={enterPasswordInv}
					onCancel={() => setEnterPasswordInv(false)}
					onContinue={handleInvite}
				/>
				<EnterPasswordModal
					visible={enterPasswordAPIKey}
					onCancel={() => setEnterPasswordAPIKey(false)}
					onContinue={saveKey}
				/>
				<ApiKeyModal
					visible={apiModalStatus}
					setVisible={setApiModalStatus}
					onCancel={() => setApiModalStatus(false)}
					setEnterPassword={setEnterPasswordAPIKey}
					handleSaveAPIKey={handleSaveAPIKey}
					passwordRequired={passwordRequired ?? false}
					loading={loading}
				/>
				<DisplayApiKeyModal
					apiKey={lastGeneratedKey}
					visible={displayApiModalStatus}
					setVisible={setDisplayApiModalStatus}
					onClose={handleDisplayApiKeyClose}
				/>
				<DeletePaymentMethodModal
					visible={deleteModalStatus}
					onCancel={() => setDeleteModalStatus(false)}
					paymentMethodId={paymentMethods?.id}
					setRefetch={setRefetch}
				/>
				<ChangePlanModal
					setRefetch={setRefetch}
					upgradeToPro={false}
					accountData={accountData}
					visible={displayApPro}
					onClose={() => {
						setDisplayApPro(false);
					}}
				/>
			</PageBody>
		</>
	);
};

export default EditSettings;

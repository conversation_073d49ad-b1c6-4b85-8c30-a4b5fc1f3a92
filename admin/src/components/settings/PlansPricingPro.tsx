import React, { useEffect, useState } from "react";
import {
	Flex,
	H2,
	<PERSON>Button,
	OriginalPrice,
	PlanOption,
	PlansPageDescription,
	PlansPageUl,
	SelectionInfo,
	SelectPlanContainer
} from "../../styles/components";
import { useTranslation } from "../hooks/translations";
import { Account } from "../../types/account";
import { Product, ProductTypeEnum } from "../../types/product";
import usePlansPricing from "../hooks/usePlansPricing";

interface PlansPricingProProps {
	accountData: Account | undefined;
	planID: string;
	priceId: string;
	setPlanID: (value: string) => void;
	price: string;
	setPrice: (value: string) => void;
	pendingChangeDate: number | null | undefined;
	trialAvailable: boolean;
	clockTime: number | undefined;
	changePlan: (
		priceId: string,
		trialAvailable: boolean,
		productType: ProductTypeEnum,
		subscriptionId?: string
	) => Promise<void>;
	product: Product | null;
	disableTrialButton: boolean;
}

export const PlansPricingPro: React.FC<PlansPricingProProps> = (props: PlansPricingProProps) => {
	const translation = useTranslation();
	const { formatPrice, sortPrices } = usePlansPricing();

	const [current, setCurrent] = useState(false);

	useEffect(() => {
		if (props.planID === props.priceId) {
			setCurrent(true);
		} else {
			setCurrent(false);
		}
	}, [props.planID, props.priceId]);

	return (
		<>
			<PlanOption upgrade={true}>
				<Flex style={{ justifyContent: "space-between", marginBottom: "0.8rem" }}>
					<H2>{translation.general.pro}</H2>
					{current ? (
						<SelectionInfo selected={true}>{translation.plansPage.currentPlan}</SelectionInfo>
					) : (
						props.trialAvailable && <SelectionInfo>{translation.plansPage.free14Days}</SelectionInfo>
					)}
				</Flex>
				<PlansPageDescription>{translation.plansPage.proPlanDescription}</PlansPageDescription>
				<SelectPlanContainer className="mt-4">
					<select
						id="proPlan"
						onChange={(e) => {
							props.setPlanID(e.target.value);
							const selectedPrice = props.product?.prices.find((price) => price.id === e.target.value);
							if (selectedPrice) {
								props.setPrice(formatPrice(selectedPrice));
							}
						}}
						value={props.planID}
					>
						{props.product &&
							sortPrices(props.product.prices).map((price, index) => (
								<option key={index} value={price.id}>
									{price.name}
								</option>
							))}
					</select>
				</SelectPlanContainer>
				<div className="mt-4" style={{ position: "relative" }}>
					<OriginalPrice strikethrough={false}>{props.price}</OriginalPrice>
				</div>
				<div className="mt-4">
					<b>{translation.plansPage.apProIncludes}</b>
				</div>
				<PlansPageUl>
					<li>{translation.plansPage.everythingInBasicPlus}</li>
					<li>{translation.plansPage.removeLogo}</li>
					<li>{translation.plansPage.socialEngagementFeatures}</li>
					<li>{translation.plansPage.landscapeVideoDisplay}</li>
					<li>{translation.plansPage.videoPlayerCustomization}</li>
					<li>{translation.plansPage.additionalDisplayOptions}</li>
					<li>{translation.plansPage.advancedPerformanceMetrics}</li>
					<li>{translation.plansPage.conversionTrackingShopifyOnly}</li>
					<li>{translation.plansPage.prioritySupport}</li>
					<li>{translation.plansPage.shareableVideoLinks}</li>
					<li>{translation.plansPage.emailEmbedWithGif}</li>
				</PlansPageUl>
				<div style={{ flexGrow: 1 }}></div>
				{props.planID === props.priceId ? (
					<MainButton style={{ width: "100%", marginTop: "auto" }} disabled={true}>
						{translation.plansPage.currentPlan}
					</MainButton>
				) : (
					<MainButton
						style={{ width: "100%", marginTop: "auto" }}
						disabled={props.disableTrialButton}
						onClick={() => {
							props.changePlan(
								props.planID,
								props.trialAvailable,
								ProductTypeEnum.PRO,
								props.accountData?.subscription?.stripeSubscriptionId
							);
						}}
					>
						{props.trialAvailable ? translation.plansPage.startFreeTrial : translation.general.select}
					</MainButton>
				)}
			</PlanOption>
		</>
	);
};

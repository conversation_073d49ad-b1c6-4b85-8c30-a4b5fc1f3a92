import React from "react";
import {
	<PERSON>lex,
	H2,
	<PERSON>Button,
	OriginalPrice,
	PlanOption,
	PlansPageDescription,
	PlansPageUl,
	SelectionInfo,
	SelectPlanContainer
} from "../../styles/components";
import { useTranslation } from "../hooks/translations";
import { Account } from "../../types/account";
import { Product, ProductTypeEnum } from "../../types/product";
import usePlansPricing from "../hooks/usePlansPricing";
import useSendEnterpriseEmail from "../hooks/useSendEnterpriseEmail";

interface PlansPricingEnterpriseProps {
	accountData: Account | undefined;
	planID: string;
	priceId: string;
	setPlanID: (value: string) => void;
	price: string;
	setPrice: (value: string) => void;
	pendingChangeDate: number | null | undefined;
	trialAvailable: boolean;
	clockTime: number | undefined;
	changePlan: (
		priceId: string,
		trialAvailable: boolean,
		productType: ProductTypeEnum,
		subscriptionId?: string
	) => Promise<void>;
	product: Product | null;
	disableTrialButton: boolean;
}

export const PlansPricingEnterprise: React.FC<PlansPricingEnterpriseProps> = (props: PlansPricingEnterpriseProps) => {
	const translation = useTranslation();
	const { formatPrice, sortPrices } = usePlansPricing();
	const sendEmail = useSendEnterpriseEmail(process.env.ENTERPRISE_PRICING_EMAIL ?? "", "Enterprise");

	return (
		<>
			<PlanOption upgrade={true}>
				<Flex style={{ justifyContent: "space-between", marginBottom: "0.8rem" }}>
					<H2>{translation.general.enterprise}</H2>
					{props.accountData?.subscription.type === ProductTypeEnum.ENTERPRISE && (
						<SelectionInfo selected={true}>{translation.plansPage.currentPlan}</SelectionInfo>
					)}
				</Flex>
				<PlansPageDescription>{translation.plansPage.enterprisePlanDescription}</PlansPageDescription>
				<SelectPlanContainer className="mt-4">
					{props.product?.prices && props.product.prices.length < 1 ? (
						<span>{translation.plansPage.customImpressionLimit}</span>
					) : (
						<select
							id="enterprisePlan"
							onChange={(e) => {
								props.setPlanID(e.target.value);
								const selectedPrice = props.product?.prices.find((price) => price.id === e.target.value);
								if (selectedPrice) {
									props.setPrice(formatPrice(selectedPrice));
								}
							}}
							value={props.planID}
						>
							{props.product &&
								sortPrices(props.product.prices).map((price, index) => (
									<option key={index} value={price.id}>
										{price.name}
									</option>
								))}
						</select>
					)}
				</SelectPlanContainer>
				<div className="mt-4">
					<OriginalPrice strikethrough={false}>Custom</OriginalPrice>
				</div>
				<div className="mt-4">
					<b>{translation.plansPage.apProIncludes}</b>
				</div>
				<PlansPageUl>
					<li>{translation.plansPage.everythingInProPlus}</li>
					<li>{translation.plansPage.dedicatedCustomerSuccessManager}</li>
					<li>{translation.plansPage.contentPlusGoToMarketStrategySessions}</li>
					<li>{translation.plansPage.apSupportedImplementation}</li>
					<li>{translation.plansPage.ongoingOptimizationSupport}</li>
					<li>{translation.plansPage.customLimitOnVideoImpressions}</li>
				</PlansPageUl>
				<div style={{ flexGrow: 1 }}></div>
				<MainButton style={{ width: "100%", marginTop: "auto" }} onClick={sendEmail}>
					{translation.plansPage.contactUs}
				</MainButton>
			</PlanOption>
		</>
	);
};

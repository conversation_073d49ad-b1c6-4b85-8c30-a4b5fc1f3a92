import React from "react";
import { ImageSelectContainer, ImageText, MockImage } from "@src/styles/components";
import { SnippetDisplayMode } from "@src/types/snippetOptions";

interface ImageSelectProps {
	imageText?: string;
	selected: boolean;
	handleSelection: () => void;
	imageURL: string;
	displayTypeValue: SnippetDisplayMode;
}

const ImageSelect: React.FC<ImageSelectProps> = ({ imageText, selected, handleSelection, imageURL, displayTypeValue }) => {
	return (
		<ImageSelectContainer selected={selected} onClick={handleSelection}>
			<MockImage imageURL={imageURL} displayTypeValue={displayTypeValue} />
			<ImageText selected={selected}>{imageText}</ImageText>
		</ImageSelectContainer>
	);
};

export default ImageSelect;

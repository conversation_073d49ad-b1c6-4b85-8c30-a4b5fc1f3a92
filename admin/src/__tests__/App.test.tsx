import React from "react";
import { App } from "../App";
import { ThemeProvider } from "styled-components";
import theme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";

afterEach(cleanup);

jest.mock("logrocket", () => ({
	default: {
		init: jest.fn()
	}
}));

it("renders without crashing", async () => {
	await act(async () => {
		// Use the async variant of act()

		render(
			<ThemeProvider theme={theme}>
				<App />
			</ThemeProvider>
		);
	});
});

import styled from "styled-components";
import { Col } from "react-bootstrap";

const ModalContainer = styled.div`
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 99999999;
	height: 100%;
	font-family: ${(props) => props.theme.fonts.family};
	background: ${({ theme }) => theme.colors.apModalBackground};
`;

const ModalOuterWrapper = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	margin: 0 5%;
`;

const ModalInnerWrapper = styled.div<{wide?: boolean}>`
	display: flex;
	flex-flow: column;
	justify-content: center;
	background-color: ${({ theme }) => theme.colors.apWhite};
	box-shadow: 0px 0px 9px ${({ theme }) => theme.colors.apButtonShadow};
	max-width: 44rem;
	position: relative;
	padding: 2.8rem;
	border-radius: 1.25rem;

	${(props) =>
		props.wide &&
		`
			max-width: 100%;
		`}
`;

const ModalHeader = styled.h1<{hidden?: boolean}>`
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 2.2rem;
	font-weight: bold;
	text-align: center;

	${(props) =>
		props.hidden &&
		`
      display: none;
    `}
`;

const ModalText = styled.span`
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	text-align: center;
	margin-top: 1rem;
	margin-bottom: 1rem;
`;

const ModalContent = styled.div`
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	&::-webkit-scrollbar {
		width: 0;
		height: 0;
		display: none;
	}
	scrollbar-width: none;
	-ms-overflow-style: none;
	overflow-y: scroll;
	overflow-x: hidden;
`;

const ColWithBorder = styled(Col)`
	border-left: 1px solid ${(props) => props.theme.colors.modalBorderColor};

	@media only screen and (max-width: 767px) {
		border-top: 1px solid ${(props) => props.theme.colors.modalBorderColor};
		padding-top: 1rem;
		margin-top: 1rem;
	}
`;

export { ModalContainer, ModalOuterWrapper, ModalInnerWrapper, ModalHeader, ModalContent, ModalText, ColWithBorder };

const theme = {
	fonts: {
		family: "'Readex Pro', sans-serif"
	},
	vendorName: "Retail",
	colors: {
		apWhite: "#ffffff",
		apOffBlack: "#A1A1A1",
		apLowMedGrey: "#DEDDDD",
		apFormText: "#343434",
		apSpanColor: "#0033FF",
		apSuccess: "#6DCE49",
		apError: "#E01111",
		apMaxImpressions: "#DC3545",
		apButton: "#0033FF",
		apButtonColor: "#FFFFFF",
		apButtonHover: "#0025B7",
		apButtonActive: "#0025B7",
		logoBackground: "#0033FF",
		apButtonShadow: "#00000029",
		apThirdButton: "#FFFFFF",
		apThirdButtonColor: "#343434",
		apThirdButtonHover: "#000000",
		apThirdButtonActive: "#969595",
		apGreyButton: "#E0E0E0",
		apGreyButtonColor: "#343434",
		apGreyButtonHover: "#B3B3B3",
		apGreyButtonActive: "#E0E0E0",
		apInputColor: "#343434",
		apTextColor: "#343434",
		disabledTextColor: "#9F9F9F",
		apSectionBackground: "#F8F8F8",
		apInputBackground: "#F8F8F8",
		apModalBackground: "#00000060",
		snippetBackground: "#343434",
		snippetBorder: "#707070",
		snippetTextColor: "#E0E0E0",
		snippetModalTextColor: "#969595",
		cloneIconColor: "#E0E0E0",
		snippetModalBackground: "#FFFFFF",
		videoSectionBackground: "#F8F8F8",
		imageSectionBackground: "#F8F8F8",
		apCopyIconHover: "#969595",
		apCopyIconActive: "#E0E0E0",
		apInputBorderColorDark: "#343434",
		apFooterTextColor: "#969595",
		apImageIcon: "#343434",
		disabledInput: "#9F9F9F",
		modalBorderColor: "#000000",
		inputBorderColor: "#86b7fe",
		inputBorderBoxShadow: "#0d6efd40",
		scoreBarLevel1: "#DC3545",
		scoreBarLevel2: "#F88F00",
		scoreBarLevel3: "#FFBB00",
		scoreBarLevel4: "#98C16F",
		scoreBarLevel5: "#6DCE49",
		apMedGrey: "#D1D1D1",
		apYellow: "#FFBE2B",
		apBlue: "#00AAF8",
		apGreen: "#6DCE49",
		apBlack: "#000000",
		trialColor: "#0A6C28",
		trialBackground: "#C9FFD6",
		labelBackground: "#C9E0FF",
		labelBorder: "#0033FF",
		proLabelBackground: "#C9FFD6",
		proLabelBorder: "#0A6C28"
	}
};

export type ThemeColors = keyof (typeof theme)["colors"];
export default theme;

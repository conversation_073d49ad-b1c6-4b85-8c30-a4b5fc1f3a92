/* eslint-disable @typescript-eslint/explicit-function-return-type */
/* eslint-disable @typescript-eslint/no-var-requires */
const webpack = require("webpack");
const Dotenv = require('dotenv-webpack');
const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const dotenv = require('dotenv');

module.exports = (env, options) => {
  const isDevelopment = options.mode !== "production";

  //Establishing the env file early to retrieve favicon
  const envFile = isDevelopment ? './.env.development' : './.env.production';
  dotenv.config({ path: envFile });
  const cdnEndpoint = `${process.env.GP_CDN_ENDPOINT}/app/favicon.png`;

  return {
    mode: isDevelopment ? "development" : "production",
    entry: "./src/index.tsx",
    output: {
      filename: "[name].js",
      path: path.resolve("./dist"),
      publicPath: "/"
    },
    resolve: {
      extensions: [".ts", ".tsx", ".js", ".css"],
      fallback: {
        assert: false,
        path: false,
        fs: false
      },
      alias: {
        "@src": path.resolve("./src")
      }
    },
    module: {
      rules: [
        {
          test: /\.ts(x)?$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
            options: {
              cacheDirectory: true
            }
          }
        },
        {
          test: /\.(jpe?g|png|gif|woff|woff2|eot|ttf|svg)(\?[a-z0-9=.]+)?$/,
          type: "asset/resource"
        },
        { 
          test: /\.css$/, 
          use:['style-loader','css-loader']
        }
      ]
    },
    devtool: isDevelopment
      ? "eval-cheap-module-source-map"
      : "nosources-source-map",
    optimization: {
      minimize: !isDevelopment
    },
    performance: {
      hints: false
    },
    plugins: [
      new Dotenv({
        path: envFile,
      }),
      new HtmlWebpackPlugin({
        minify: isDevelopment
          ? false
          : {
              collapseWhitespace: true,
              keepClosingSlash: true,
              minifyCSS: true,
              minifyJS: true,
              minifyURLs: true,
              removeComments: true,
              removeEmptyAttributes: true,
              removeRedundantAttributes: true,
              removeScriptTypeAttributes: true,
              removeStyleLinkTypeAttributes: true,
              useShortDoctype: true
            },
        template: "./src/index.html",
        templateParameters: {
          faviconURL: cdnEndpoint
        }
      }),
      ...(isDevelopment ? [new webpack.HotModuleReplacementPlugin()] : [])
    ],
    stats: {
      assetsSort: "!size",
      colors: true,
      entrypoints: false,
      errors: true,
      errorDetails: true,
      groupAssetsByChunk: false,
      groupAssetsByExtension: false,
      groupAssetsByInfo: false,
      groupAssetsByPath: false,
      modules: false,
      relatedAssets: true,
      timings: false,
      version: false
    },
    devServer: {
      static: path.join(__dirname, "/dist"),
      historyApiFallback: true,
      host: "0.0.0.0",
      hot: true,
      port: 5002
    }
  };
};

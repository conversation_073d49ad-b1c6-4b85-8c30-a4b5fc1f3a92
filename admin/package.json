{"name": "gp-admin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "webpack serve --mode=development", "build": "rimraf dist && webpack --mode=production", "format": "prettier --write --check src", "qa": "npm i && webpack serve --mode=development", "lint": "eslint --ext .ts,.tsx src", "lint:fix": "eslint --ext .ts,.tsx src --fix", "typecheck": "tsc --noEmit", "test": "jest", "audit": "better-npm-audit audit --level=low"}, "repository": {"type": "git", "url": "git+https://github.com/autoplayvideo/ap-platform"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/autoplayvideo/ap-platform"}, "homepage": "https://github.com/autoplayvideo/ap-platform", "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@stylistic/eslint-plugin-ts": "^3.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/country-list": "^2.1.4", "@types/deep-equal": "^1.0.1", "@types/dotenv-webpack": "^7.0.3", "@types/jest": "^29.2.6", "@types/react": "^18.0.26", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-datepicker": "^4.15.0", "@types/react-dom": "^18.0.10", "@types/react-gtm-module": "^2.0.1", "@types/styled-components": "^5.1.26", "@types/styled-system": "^5.1.16", "@types/uuid": "^9.0.4", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "@typescript-eslint/parser": "^5.48.2", "babel-loader": "^9.1.2", "better-npm-audit": "^3.7.3", "css-loader": "^6.7.3", "dotenv-webpack": "^8.0.1", "eslint": "^8.32.0", "eslint-plugin-react": "^7.32.1", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.4.0", "jest-transform-stub": "^2.0.0", "prettier": "^2.8.3", "rimraf": "^5.0.5", "style-loader": "^3.3.1", "ts-jest": "^29.0.5", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@react-input/mask": "^1.2.10", "@stripe/react-stripe-js": "^2.5.0", "@stripe/stripe-js": "^3.0.5", "axios": "^1.3.4", "bootstrap": "^5.2.3", "bson-objectid": "^2.0.4", "country-list": "^2.3.0", "date-fns": "^2.30.0", "deep-equal": "^2.2.0", "highlight.js": "^11.7.0", "jwt-decode": "^3.1.2", "logrocket": "^10.0.0", "lottie-react": "^2.4.0", "moment": "^2.29.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.7.0", "react-colorful": "^5.6.1", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.42.1", "react-loading-skeleton": "^3.1.1", "react-localization": "^1.0.19", "react-responsive": "^9.0.2", "react-router-dom": "^6.6.2", "react-select": "^5.7.3", "recoil": "^0.7.6", "styled-components": "^5.3.6"}}
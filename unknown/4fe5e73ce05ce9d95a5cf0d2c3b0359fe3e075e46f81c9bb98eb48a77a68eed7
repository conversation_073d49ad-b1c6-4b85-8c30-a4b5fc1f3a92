import mongoose, { Schema } from "mongoose";
import { VideoProfile } from "./videoProfile.interface";

const VideoProfileSchema: Schema<VideoProfile> = new Schema(
	{
		name: { type: String, required: true },
		default: { type: Boolean, required: true },
		level: { type: String, required: true },
		pixelFormat: { type: String, required: true },
		constantRateFactor: { type: Number, required: true },
		preset: { type: String, required: true },
		audioBitrate: { type: String, required: true },
		scale: { type: Number, required: true },
		createdAt: { type: Date, default: new Date() },
		updatedAt: { type: Date, default: new Date() }
	},
	{ timestamps: true }
);

export const VideoProfileDBModel = mongoose.model<VideoProfile>("videoProfiles", VideoProfileSchema);

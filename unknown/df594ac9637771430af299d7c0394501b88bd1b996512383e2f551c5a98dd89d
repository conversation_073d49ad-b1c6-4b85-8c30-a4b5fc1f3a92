import Jo<PERSON> from "joi";
import { BaseAccessJoi } from "../base/base.joi";
import { BaseAccessRequest } from "../base/base.interfaces";

export interface SignupEnterprisePayload extends BaseAccessRequest {
	email: string;
	password: string;
}
export const SignupEnterprisePayloadSchema = BaseAccessJoi.append<SignupEnterprisePayload>({
	email: Joi.string().email({ tlds: { allow: false } }).trim().required(),
	password: Joi.string().min(8).required()
});

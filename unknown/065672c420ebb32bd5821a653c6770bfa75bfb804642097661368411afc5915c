import styled from "styled-components/macro";
import {
	space,
	color,
	layout,
	position,
	typography,
	flexbox,
	border,
	SpaceProps,
	ColorProps,
	LayoutProps,
	PositionProps,
	TypographyProps,
	BorderProps,
	FlexboxProps
} from "styled-system";

interface IBox
	extends SpaceProps,
	ColorProps,
	LayoutProps,
	PositionProps,
	TypographyProps,
	FlexboxProps,
	BorderProps {
	key?: string | number;
}

export const Box = styled.div<IBox>(
	{
		boxSizing: "border-box",
		minWidth: 0,
		padding: 0,
		userSelect: "none",
		WebkitUserSelect: "none",
		MozUserSelect: "none",
		msUserSelect: "none"
	},
	space,
	color,
	layout,
	border,
	position,
	typography,
	flexbox
);

export const BlurBox = styled(Box).attrs<{ radius: string}>(({ radius }) => ({
	style: {
		position: "absolute",
		display: "block",
		borderRadius: radius
	}
}))<{ radius: string }>`
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
	z-index: 0; 
    border-radius: ${(props): string => props.radius};
	backdrop-filter: ${(props):string => props.theme.buttons.backgroundBlur};
	background: ${(props): string =>
		getBlurBoxColor(props.theme.buttons.backgroundBlur, props.theme.buttons.backgroundColor)};
`;

const getBlurBoxColor = (backgroundBlur:string, backgroundColor:string):string => {
	const hexColorRegex = /^#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$/;

	if (!hexColorRegex.test(backgroundColor)) {
		return "rgba(0, 0, 0, 0.5)";
	}

	if (backgroundBlur === "unset") {
		return backgroundColor;
	}

	let r, g, b;

	if (backgroundColor.length === 7) {
		r = parseInt(backgroundColor.slice(1, 3), 16);
		g = parseInt(backgroundColor.slice(3, 5), 16);
		b = parseInt(backgroundColor.slice(5, 7), 16);
	} else if (backgroundColor.length === 4) {
		r = parseInt(backgroundColor.slice(1, 2).repeat(2), 16);
		g = parseInt(backgroundColor.slice(2, 3).repeat(2), 16);
		b = parseInt(backgroundColor.slice(3, 4).repeat(2), 16);
	}

	return `rgba(${r}, ${g}, ${b}, 0.5)`;
};

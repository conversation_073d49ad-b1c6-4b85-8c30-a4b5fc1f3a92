import {
	useEffect,
	useRef
} from "react";
import { useRecoilValue } from "recoil";
import {
	sliderVideoPlayerAtom,
	sliderConfigAtom
} from "@player/slider/slider.state";
import { buildEventPayload } from "@player/event/event.service";
import { IEventInput } from "@player/event/event.interface";
import { EventNameEnum } from "@player/event/event.enum";
import { validateBackslash } from "@player/app/app.util";
import {
	appSessionIdAtom,
	isAppInPreviewModeAtom
} from "@player/app/app.state";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";

export const useVideoFrameVisibility =
	(isViewable: boolean, playingVideo: boolean, lastReportedPlayedSeconds: number): void => {
		const sliderConfig = useRecoilValue(sliderConfigAtom);
		const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
		const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
		const appSessionId = useRecoilValue(appSessionIdAtom);
		const sendBeaconEventRef = useRef<() => void>();

		useEffect(() => {
			sendBeaconEventRef.current = (): void => {
				const API_ENDPOINT = process.env.GP_SERVER_API_ENDPOINT;
				if (isAppInPreviewMode || !sliderConfig || !sliderVideoPlayer || !API_ENDPOINT) {
					return;
				}
				if (sliderVideoPlayer.playedSeconds <= lastReportedPlayedSeconds) {
					return;
				}

				const eventInput: IEventInput = {
					collectionId: sliderConfig.collectionId,
					accountId: sliderConfig.accountId,
					appSessionId: appSessionId,
					eventName: EventNameEnum.VIDEO_PLAY_POSITION,
					sliderVideoPlayer: sliderVideoPlayer,
					videoPlayStatus: "stopped",
					isShareMode: sliderConfig.isShareMode
				};
				const eventPayload = buildEventPayload(eventInput);
				const blobData = { ...eventPayload, "x-api-version": "1" };
				const url = validateBackslash(API_ENDPOINT) + "api/event/buffer/text";
				appLog({
					message: `Sending blob event: ${eventPayload.eventName}`,
					data: blobData,
					scope: LogScope.INFO
				});
				const blob = new Blob([JSON.stringify(blobData)], { type: "text/plain; charset=UTF-8" });
				navigator.sendBeacon(url, blob);
			};
		}, [sliderVideoPlayer, sliderConfig, isAppInPreviewMode, appSessionId, isViewable, playingVideo,
			lastReportedPlayedSeconds]);


		useEffect(() => {
			if (!isViewable || isAppInPreviewMode) {
				return;
			}
			const handleBeforeUnload = (): void => {
				if (sendBeaconEventRef.current) {
					sendBeaconEventRef.current();
				}
			};

			const handleVisibilityChange = (): void => {
				if (document.visibilityState === "hidden") {
					if (sendBeaconEventRef.current) {
						sendBeaconEventRef.current();
					}
				}
			};

			document.addEventListener("visibilitychange", handleVisibilityChange);
			window.addEventListener("beforeunload", handleBeforeUnload);

			return () => {
				document.removeEventListener("visibilitychange", handleVisibilityChange);
				window.removeEventListener("beforeunload", handleBeforeUnload);
			};
		}, [isViewable, isAppInPreviewMode]);
	};

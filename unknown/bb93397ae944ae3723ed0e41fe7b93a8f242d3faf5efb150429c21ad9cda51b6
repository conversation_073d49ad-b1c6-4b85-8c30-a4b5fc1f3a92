import React from "react";
import { ImgContentPanel, ImgContentPanelText, ImgContentPanelTitle, ImgContentPanelCopy, ImageBox, ApLogo } from "@src/styles/components";

const CreateAccountForm: React.FC<{title: string; copy: string; backgroundUrl: string}> = ({ title, copy, backgroundUrl }) => {
	return (
		<ImgContentPanel className="position-relative">
			<ApLogo />
			<ImageBox backgroundUrl={backgroundUrl} />
			<ImgContentPanelText className="position-absolute b-3" data-testid="imgContentPanelText">
				<ImgContentPanelTitle className="mb-3" data-testid="imgContentPanelTitle">
					{title}
				</ImgContentPanelTitle>
				<ImgContentPanelCopy data-testid="imgContentPanelCopy">{copy}</ImgContentPanelCopy>
			</ImgContentPanelText>
		</ImgContentPanel>
	);
};

export default CreateAccountForm;

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { <PERSON><PERSON><PERSON> } from "../modules/apiKey/apiKey.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { IAccount } from "../modules/account/account.interfaces";

describe("GET /api/keys", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let accountId: string;

	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.keys.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		accountId = account._id.toString();
	});

	it("[E_NOT_AUTHENTICATED]. Should return 401 - missing accessToken", async () => {
		const res = await supertest(expressApp)
			.get("/api/keys")
			.send();
		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("[Successfully reads keys]. Should return 200", async () => {
		const resCreateKey1 = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(resCreateKey1.statusCode).toBe(200);
		expect(resCreateKey1.body).toHaveProperty("key");
		expect(resCreateKey1.body.key.accountId).toBe(accountId);
		expect(resCreateKey1.body).toHaveProperty("apikey");

		const newKey1: IKey = resCreateKey1.body.key;

		const resCreateKey2 = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(resCreateKey2.statusCode).toBe(200);
		expect(resCreateKey2.body).toHaveProperty("key");
		expect(resCreateKey2.body.key.accountId).toBe(accountId);
		expect(resCreateKey2.body).toHaveProperty("apikey");

		const newKey2: IKey = resCreateKey2.body.key;

		const res = await supertest(expressApp)
			.get("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);

		const keys: IKey[] = res.body;
		expect(keys.length).toBe(2);
		expect(keys.some(k => k._id.toString() == newKey1._id.toString())).toBe(true);
		expect(keys.some(k => k._id.toString() == newKey2._id.toString())).toBe(true);
	});
});

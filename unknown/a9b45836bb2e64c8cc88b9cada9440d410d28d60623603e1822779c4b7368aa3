import {
	ClientSession,
	QueryOptions
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import path from "path";
import { IVideo } from "../../modules/video/video.interfaces";
import { VideoDBModel } from "../../modules/video/videoDB.model";

const logTrace = path.basename(__filename);

export const readVideo = async (
	query: any,
	session: ClientSession | null
): Promise<IVideo | null> => {
	try {
		const document: IVideo | null = await VideoDBModel.findOne(query).session(
			session
		);
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > readVideo | Failed to read video. | ${error.name} | ${error.message}`
		);
	}
};

export const readVideos = async (
	query: any,
	session: ClientSession | null
): Promise<IVideo[] | null> => {
	try {
		const documents: IVideo[] | null = await VideoDBModel.find(query).session(
			session
		);
		return documents;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > readVideos | Failed to read videos. | ${error.name} | ${error.message}`
		);
	}
};

export const createVideo = async (
	insert: any,
	session: ClientSession | null
): Promise<IVideo> => {
	try {
		const model = new VideoDBModel(insert);
		const document = await model.save({ session });
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > createVideo | Failed to create video. | ${error.name} | ${error.message}`
		);
	}
};

export const updateVideo = async (
	query: any,
	update: any,
	session: ClientSession | null
): Promise<IVideo | null> => {
	try {
		const options: QueryOptions<IVideo> = {
			new: true,
			upsert: false
		};

		const document: IVideo | null = await VideoDBModel.findOneAndUpdate(
			query,
			update,
			options
		).session(session);
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > updateVideo | Failed to update video. | ${error.name} | ${error.message}`
		);
	}
};

export const deleteVideo = async (
	query: any,
	session: ClientSession | null
): Promise<IVideo | null> => {
	try {
		const options: QueryOptions<IVideo> = {};

		const document: IVideo | null = await VideoDBModel.findOneAndDelete(
			query,
			options
		).session(session);
		return document;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > deleteVideo | Failed to delete video. | ${error.name} | ${error.message}`
		);
	}
};

export const aggregateVideos = async (
	query: any[],
	session: ClientSession | null
): Promise<IVideo[]> => {
	try {
		const documents: IVideo[] = await VideoDBModel.aggregate(
			query
		).session(session);
		return documents;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > aggregateVideos | Failed to aggregate videos. | ${error.name} | ${error.message}`
		);
	}
};

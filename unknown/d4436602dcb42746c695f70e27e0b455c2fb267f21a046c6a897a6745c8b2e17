import { LocaleAPI } from "../../interfaces/apiTypes";
import { EventBufferModel } from "../eventBuffer/eventBuffer.model";
import { EventBufferDBModel } from "../eventBuffer/eventBufferDB.model";
import {
	EventAppIds,
	EventNames
} from "../events/events.enums";
import { IShoppableCollection } from "../interactiveCollection/interactiveCollection.interface";
import { InteractiveCollectionDBModel } from "../interactiveCollection/interactiveCollectionDB.model";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { SignUpModel } from "../signup/signup.model";
import { EventProcessorModel } from "./eventProcessor.model";

describe("EventProcessorModel", () => {
	let accountId: string;
	let collectionId: string;

	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld"
		};

		const signupModel = new SignUpModel(null);
		const signupResult = await signupModel.createFromEmail({
			email: signupEmailPayload.email,
			callbackEndpoint: signupEmailPayload.callbackEndpoint,
			locale: signupEmailPayload.locale
		});

		expect(signupResult).toHaveProperty("authenticationDoc");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts.0");

		const account = signupResult.authenticationDoc.accounts[0];
		accountId = account._id.toString();

		const collection: IShoppableCollection[] = await InteractiveCollectionDBModel.find({ accountId: accountId });
		collectionId = collection[0]._id.toString();
	});

	it("should process the buffer", async () => {
		const eventBufferModel = new EventBufferModel();
		await eventBufferModel.createOne({
			accountId: accountId,
			eventName: EventNames.SNIPPET_IMPRESSION,
			appId: EventAppIds.GP_PLAYER,
			eventDimensions: {
				collectionId: collectionId
			}
		});

		const eventBufferLengthBefore = await EventBufferDBModel.countDocuments();

		const session = await EventBufferDBModel.startSession();
		const eventProcessorModel = new EventProcessorModel();
		expect(eventProcessorModel.processBuffer(session, eventBufferLengthBefore, 3)).resolves.toBe(0);
		await session.endSession();
	});

	it("should process the buffer when the attempts property is missing", async () => {
		const eventBufferDBModel = new EventBufferDBModel({
			buffer: Buffer.from(JSON.stringify({
				accountId: accountId,
				eventName: EventNames.SNIPPET_IMPRESSION,
				appId: EventAppIds.GP_PLAYER,
				eventDimensions: {
					collectionId: collectionId
				}
			}))
		});
		const eventBufferDocument = await eventBufferDBModel.save({});

		const result = await eventBufferDBModel.collection.updateOne(
			{ _id: eventBufferDocument._id },
			{ $unset: { attempts: 1 } }
		);

		expect(result.modifiedCount).toBe(1);

		const eventDocumentAfter = await EventBufferDBModel.collection.findOne({ _id: eventBufferDocument._id });
		expect(eventDocumentAfter).not.toHaveProperty("attempts");

		const eventBufferLengthBefore = await EventBufferDBModel.countDocuments();

		const session = await EventBufferDBModel.startSession();
		const eventProcessorModel = new EventProcessorModel();
		expect(eventProcessorModel.processBuffer(session, eventBufferLengthBefore, 3)).resolves.toBe(0);
		await session.endSession();
	});

	it("should fail to process an item in the buffer with an invalid event name", async () => {
		const eventBufferModel = new EventBufferModel();
		const eventBuffer = await eventBufferModel.createOne({
			accountId: accountId,
			eventName: "invalid-event-name",
			appId: EventAppIds.GP_PLAYER,
			eventDimensions: {
				collectionId: collectionId
			}
		});

		const eventBufferLengthBefore = await EventBufferDBModel.countDocuments();

		const session = await EventBufferDBModel.startSession();
		const eventProcessorModel = new EventProcessorModel();
		await eventProcessorModel.processBuffer(session, eventBufferLengthBefore, 3);
		await session.endSession();

		const eventBuffers = await EventBufferDBModel.find({ _id: eventBuffer._id });
		expect(eventBuffers.length).toBe(1);

		const session2 = await EventBufferDBModel.startSession();
		const eventProcessorModel2 = new EventProcessorModel();
		await eventProcessorModel2.processBuffer(session, eventBufferLengthBefore, 2);
		await session2.endSession();

		const eventBuffers2 = await EventBufferDBModel.find({ _id: eventBuffer._id });
		expect(eventBuffers2.length).toBe(0);
	});
});

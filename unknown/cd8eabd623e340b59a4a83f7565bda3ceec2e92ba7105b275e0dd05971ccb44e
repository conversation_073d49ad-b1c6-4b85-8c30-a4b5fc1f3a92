import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import <PERSON><PERSON> from "joi";
import { BaseJoi } from "../../base/base.joi";
import { BaseRequest } from "../../base/base.interfaces";
import { AccountModel } from "../../account/account.model";
import { VideoModel } from "../video.model";
import { UserModel } from "../../user/user.model";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../../services/mongodb/transaction.service";


interface VideoEventUploadedPayload extends BaseRequest {
	tempFilename: string;
}

const VideoEventUploadedPayloadSchema = BaseJoi.append<VideoEventUploadedPayload>({
	tempFilename: Joi.string().min(1).required()
});


export class VideoEventUploadedController extends Controller {
	constructor() {
		super();
		this.router.post("/", [express.json()], this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoEventUploadedPayload;
		try {
			validPayload = await VideoEventUploadedPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization,
				tempFilename: request.body.tempFilename
			}
			);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await startDBTransaction(response.locals.session);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			const accountToken = await this.verifyAccountToken(validPayload.accountToken);

			const accountModel: AccountModel = new AccountModel(response.locals.session);
			const account = await accountModel.readOneById(accountToken.account._id);

			const userModel = new UserModel(response.locals.session);
			const user = await userModel.readOneById(accessToken.userId);

			const videoModel: VideoModel = new VideoModel(response.locals.session);
			const result = await videoModel.startEncodeJob(
				account._id.toString(), user._id.toString(), validPayload.tempFilename);

			await completeDBTransaction(response.locals.session);
			return response.status(200).json(result);
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}


}

import mongoose, { Schema } from "mongoose";
import { MetricVideoEngagement } from "./metricVideoEngagement.interface";

const MetricVideoEngagementSchema: Schema = new Schema({
	videoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: { type: Schema.Types.Date, required: true },
	updatedAt: { type: Schema.Types.Date, required: true },
	playPercentCount20: { type: Schema.Types.Number, required: true },
	playPercentCount40: { type: Schema.Types.Number, required: true },
	playPercentCount60: { type: Schema.Types.Number, required: true },
	playPercentCount80: { type: Schema.Types.Number, required: true },
	playPercentCount100: { type: Schema.Types.Number, required: true },
	videoScore: { type: Schema.Types.Number, required: true }
});

export const MetricVideoEngagementDBModel = mongoose.model<MetricVideoEngagement>(
	"metric_video_engagement",
	MetricVideoEngagementSchema
);

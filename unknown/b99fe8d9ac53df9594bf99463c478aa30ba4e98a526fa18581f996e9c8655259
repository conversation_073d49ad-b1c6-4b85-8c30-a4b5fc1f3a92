import supertest from "supertest";
import TestHelper from "./mocks/testHelper";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { APIErrorName } from "../interfaces/apiTypes";

import {
	getSecrets,
	ISecrets
} from "../modules/secrets/secrets.model";
import { signJwt } from "../utils/helpers/gpJwt.helper";
import { IAuthentication } from "../modules/authentication/authentication.interface";


const expressApp = createServer();
initExpressRoutes(expressApp);

describe("POST /auth/reset-password failure mode tests", () => {
	let linkToken: string;
	let accessToken: string;
	let originalAuthentication: IAuthentication;

	beforeAll(async () => {
		const email = "<EMAIL>";
		const signupRes1 = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: email,
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (signupRes1.statusCode !== 200) {
			console.error(signupRes1.body);
		}

		expect(signupRes1.statusCode).toBe(200);
		expect(signupRes1.body).toHaveProperty("accessToken");
		accessToken = signupRes1.body.accessToken;

		const testHelper = new TestHelper(expressApp);
		originalAuthentication = await testHelper.getAuthentication(accessToken);

		// generate the same token that is sent to the user via email
		// with the /api/auth/forgot-password call
		const secrets: ISecrets = await getSecrets();
		linkToken = signJwt(
			{ authenticationId: originalAuthentication._id },
			`${secrets.hashkey.key}${originalAuthentication.salt}`
		);

		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "3")
			.send({
				email: email,
				method: "email/password",
				callbackEndpoint: "https://domain.tld/reset-password"
			});
		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});
		expect(linkToken).toBeDefined();
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/reset-password")
			.set("x-api-version", "3")
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_INVALID_INPUT]. Should return 400 for short password supplied", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/reset-password")
			.set("x-api-version", "3")
			.send({ token: linkToken, password: "weakpas" });
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_PASSWORD_COMPLEXITY);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 400 for invalid token supplied", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/reset-password")
			.set("x-api-version", "3")
			.send({ token: "invalid", password: "APretail1!" });
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/reset-password")
			.set("x-api-version", "3")
			.send({
				token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6I" +
				"kpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
				password: "APretail1!"
			});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	});
});

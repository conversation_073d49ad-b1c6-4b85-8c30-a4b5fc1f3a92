import React from "react";
import { Modal } from "./Modal";
import { useRecoilValue } from "recoil";
import { notifyCoreToCloseApp } from "@player/app/app.message";
import {
	appSessionIdAtom,
	reloadApp
} from "@player/app";

export const ErrorModal: React.FC = () => {
	const appSessionId = useRecoilValue(appSessionIdAtom);

	return <Modal
		style={{
			width: "100%",
			height: "100%",
			borderRadius: "0rem",
			backgroundColor: "none"
		}}
		title="We ran into an issue."
		titleStyle={{ color: "white" }}
		body="Please refresh and try again."
		bodyStyle={{ color: "white" }}
		visible={true}
		confirmText="Refresh"
		confirmVariant={"primary"}
		onConfirm={reloadApp}
		cancelText="Close"
		cancelVariant={"secondary"}
		disableCloseIcon={true}
		onCancel={(): void =>{
			notifyCoreToCloseApp({
				sliderConfig: null,
				videoPlayer: null,
				appSessionId: appSessionId
			});
		}
		}
	/>;
};

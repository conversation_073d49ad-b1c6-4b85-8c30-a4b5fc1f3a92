import mongoose, { ClientSession } from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { ModelResponse } from "../modelResponse/modelResponse.model";

import { IMetricInteractiveCollectionCreated } from "./metricInteractiveCollection.interface";
import { MetricInteractiveCollectionCreatedDBModel } from "./metricInteractiveCollectionDB.model";
export class MetricInteractiveCollectionModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public createOne = async (createData: Partial<IMetricInteractiveCollectionCreated>):
	Promise<ModelResponse<IMetricInteractiveCollectionCreated>> => {
		try {
			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new MetricInteractiveCollectionCreatedDBModel({
				accountId: createData.accountId,
				interactiveCollectionId: createData.interactiveCollectionId
			}).save(options);

			return new ModelResponse<IMetricInteractiveCollectionCreated>(newDocument, null);
		} catch (error: unknown) {
			return new ModelResponse<IMetricInteractiveCollectionCreated>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message)
			);
		}
	};

	async readTotalPrevDayByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, number>>> {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfPreviousDay = new Date(startOfToday.getTime() - (24 * 60 * 60 * 1000));
		const endOfPreviousDay = new Date(startOfToday.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousDay, $lte: endOfPreviousDay }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalCollectionsCreatedDaily: { $sum: 1 }
				}
			}
		];

		const results = await MetricInteractiveCollectionCreatedDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, number>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_interactive_collection_created found for the previous day")
			);
		}

		const totalCollectionsByAccountId: Map<string, number> = new Map(
			results.map(item => [item._id.toString(), item.totalCollectionsCreatedDaily])
		);

		return new ModelResponse<Map<string, number>>(totalCollectionsByAccountId, null);
	}

	async readTotalPrevWeekByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, number>>> {
		const now = new Date();
		const startOfThisWeek = new Date(Date.UTC(
			now.getUTCFullYear(),
			now.getUTCMonth(),
			now.getUTCDate() - now.getUTCDay() + 1
		));
		const startOfPreviousWeek = new Date(
			startOfThisWeek.getTime() - (7 * 24 * 60 * 60 * 1000)
		);
		const endOfPreviousWeek = new Date(startOfThisWeek.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousWeek, $lte: endOfPreviousWeek }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalCollectionsCreatedWeekly: { $sum: 1 }
				}
			}
		];

		const results = await MetricInteractiveCollectionCreatedDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, number>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_interactive_collection_created found for the previous week")
			);
		}

		const totalCollectionsByAccountId: Map<string, number> = new Map(
			results.map(item => [item._id.toString(), item.totalCollectionsCreatedWeekly])
		);

		return new ModelResponse<Map<string, number>>(totalCollectionsByAccountId, null);
	}


}

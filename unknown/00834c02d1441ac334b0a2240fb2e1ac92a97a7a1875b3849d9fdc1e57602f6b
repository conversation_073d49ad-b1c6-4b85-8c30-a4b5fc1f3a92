/* eslint-disable @typescript-eslint/no-var-requires */
const {pathsToModuleNameMapper} = require("ts-jest");
const {compilerOptions} = require("./tsconfig");

module.exports = {
  moduleNameMapper: {
    ...pathsToModuleNameMapper(compilerOptions.paths, {prefix: "<rootDir>/"}),
    "\\.(svg)$": "<rootDir>/__mocks__/fileMock.js"
  },
  preset: "ts-jest",
  testEnvironment: "jsdom",
  bail: 1,
  collectCoverage: true,
  collectCoverageFrom: ["**/*.ts", "!**/*.generated.*", "!**/node_modules/**"],
  coverageDirectory: "<rootDir>/coverage/",
  coveragePathIgnorePatterns: ["(test/.*.mock).(jsx?|tsx?)$"],
  testPathIgnorePatterns: ["/node_modules/", "/dist/"],
  testRegex: "(/__tests__/.+.test.[jt]sx?)$",
  verbose: true,
  transform: {
    ".+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2)$": "jest-transform-stub"
  },
  setupFilesAfterEnv: ["./jest.setup.ts"]
};

import React from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";

const OverlayWrapper = styled(Flex)`
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
`;

interface Props {
	onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
}

export const Overlay: React.FC<Props> = ({ onClick }) => (
	<OverlayWrapper onClick={onClick} />
);

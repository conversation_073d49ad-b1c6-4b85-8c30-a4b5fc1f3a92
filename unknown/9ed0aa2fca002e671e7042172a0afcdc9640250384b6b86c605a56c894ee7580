import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IInvitation } from "../../modules/invitation/invitation.interfaces";
import { deleteInvitation } from "../../services/mongodb/invitations.service";

export const deleteInvitationController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const invitationDocument: IInvitation | null = await deleteInvitation({
			_id: req.params.invitationId
		}, null);

		if (!invitationDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Invitation not found");
		}

		return res.status(200).send({
			invitation: <IInvitation>{
				_id: invitationDocument._id,
				accountId: invitationDocument.accountId,
				email: invitationDocument.email,
				status: invitationDocument.status,
				// salt: invitationDocument.salt, // redacted
				createdAt: invitationDocument.createdAt,
				userId: invitationDocument.userId
			}
		});
	} catch (error: any) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

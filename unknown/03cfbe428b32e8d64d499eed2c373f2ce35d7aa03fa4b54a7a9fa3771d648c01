import React, { useState, useEffect } from "react";
import { PenIcon, TrashIcon } from "@src/styles/forms";
import { useNavigate } from "react-router-dom";
import { DeleteCollectionVideo } from "../modals/DeleteCollectionVideo";
import {
	Flex,
	ListBox,
	ListGridBox,
	ListItemContainer,
	ListRowItem,
	ListRowGridItem,
	ListRowGridItemBox,
	CoverImageBox,
	UploadImage
} from "@src/styles/components";
import { shoppableVideo } from "@src/types/videos";
import { DisplayFormatOptions } from "@src/types/snippetOptions";

type Props = {
	videoItem: shoppableVideo;
	setIsDropDisabled: (value: boolean) => void;
	numberId: number;
	videos: shoppableVideo[];
	setVideos: (value: shoppableVideo[]) => void;
};

const CollectionListItem: React.FC<Props> = ({ videoItem, numberId, setIsDropDisabled, videos, setVideos }) => {
	const navigate = useNavigate();
	const [modalStatus, setModalStatus] = useState(false);

	useEffect(() => {
		if (modalStatus) {
			setIsDropDisabled(true);
		} else {
			setIsDropDisabled(false);
		}
	}, [modalStatus, setIsDropDisabled]);

	return (
		<>
			<ListItemContainer data-testid={`videoListRow${numberId}`}>
				<Flex style={{ alignItems: "center" }}>
					<ListGridBox onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowGridItem data-testid={`videoListBoxes${numberId}`}>
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
						</ListRowGridItem>
					</ListGridBox>
					<ListBox style={{ width: "80px", alignItems: "center" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<CoverImageBox landscape={videoItem.videoDisplayMode === DisplayFormatOptions.LANDSCAPE}>
							<UploadImage src={videoItem.videoPosterURL} />
						</CoverImageBox>
					</ListBox>
					<ListBox style={{ width: "50%", alignItems: "center" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowItem data-testid={`videoListTitle${numberId}`}>{videoItem.title}</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "40%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowItem data-testid={`videoListProducts${numberId}`}>{videoItem.products?.length}</ListRowItem>
					</ListBox>
					<ListBox style={{ minWidth: "6rem" }} last={true}>
						<ListRowItem>
							<PenIcon
								data-testid={`penIcon${numberId}`}
								onClick={() => navigate("/edit-video/" + videoItem._id)}
								style={{ marginRight: "15px" }}
							/>
							<TrashIcon data-testid={`trashIcon${numberId}`} onClick={() => setModalStatus(true)} />
						</ListRowItem>
					</ListBox>
				</Flex>
			</ListItemContainer>
			<DeleteCollectionVideo
				video={videoItem}
				videos={videos}
				setVideos={setVideos}
				visible={modalStatus}
				onCancel={() => setModalStatus(false)}
			/>
		</>
	);
};

export default CollectionListItem;

import {
	Document,
	ObjectId
} from "mongoose";

export interface IKey extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	authenticationId: ObjectId;
	keyLast4: string;
	createdAt: Date;
	createdBy: ObjectId;
	meta: Record<string, string>;
}

export interface APIKeyPostRequest {
	meta: Record<string, string>;
}

export interface APIKeyCreateData {
	accountId: string,
	authenticationId: string,
	keyLast4: string,
	createdBy: string,
	meta: Record<string, string>
}

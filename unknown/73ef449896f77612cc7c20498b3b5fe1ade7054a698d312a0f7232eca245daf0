import {
	type Request,
	type Response
} from "express";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import <PERSON><PERSON> from "joi";
import multer from "multer";
import { VersionJoi } from "../base/base.joi";
import { EmailModel } from "./email.model";
import {
	EmailInput,
	EmailTemplateEnum
} from "./email.interface";

interface EmailPostPayload {
	apiVersion: number;
	fromEmail: string;
	toEmail: string;
	subject: string;
	message: string;
	template: EmailTemplateEnum;
	locale: LocaleAPI;
}

const EmailPostPayloadSchema = Joi.object<EmailPostPayload>({
	apiVersion: VersionJoi.required(),
	fromEmail: Joi.string().email({ tlds: { allow: false } }).trim().required(),
	toEmail: Joi.string().email({ tlds: { allow: false } }).trim().required(),
	subject: Joi.string().trim().min(5).required(),
	message: Joi.string().trim().min(10).required(),
	template: Joi.string().valid(...Object.values(EmailTemplateEnum)).required(),
	locale: Joi.string().valid(...Object.values(LocaleAPI)).required()
});

export class EmailController extends Controller {
	constructor() {
		super();
		this.router.post("/", [multer().none()], this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: EmailPostPayload;
		try {
			validPayload = await EmailPostPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				fromEmail: request.body.fromEmail,
				toEmail: request.body.toEmail,
				subject: request.body.subject,
				message: request.body.message,
				template: request.body.template,
				locale: request.body.locale
			});
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);

			const emailInput: EmailInput = {
				template: validPayload.template,
				to: validPayload.toEmail,
				subject: validPayload.subject,
				data: {
					visitorEmail: validPayload.fromEmail,
					visitorMessage: validPayload.message
				},
				locale: validPayload.locale
			};

			const emailModel = new EmailModel();
			await emailModel.sendTransactionalEmail(emailInput);

			return response.status(200).json({});

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

}

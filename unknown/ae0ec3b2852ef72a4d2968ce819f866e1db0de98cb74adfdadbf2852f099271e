import Jo<PERSON> from "joi";
import { APIErrorName } from "../interfaces/apiTypes";
import { IGetShoppableVideoPayload } from "../modules/interactiveVideo/interactiveVideo.interface";

export const getShoppableVideoSchema = {
	data: Joi.object<IGetShoppableVideoPayload>({
		limit: Joi.number().integer().positive(),
		sortKey: Joi.string(),
		SortBy: Joi.string().valid("asc", "dsc")
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"number.base": APIErrorName.E_INVALID_INPUT,
		"number.integer": APIErrorName.E_INVALID_INPUT,
		"number.positive": APIErrorName.E_INVALID_INPUT,
		"string.valid": APIErrorName.E_INVALID_INPUT
	})
};



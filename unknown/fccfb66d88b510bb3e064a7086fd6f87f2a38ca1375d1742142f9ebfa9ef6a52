/* eslint-disable camelcase */
/* eslint-disable max-lines-per-function */
import TestHelper from "../../../__tests__/mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { IAccount } from "../../../modules/account/account.interfaces";
import { AccountModel } from "../../../modules/account/account.model";
import { StripeSubscriptionModel } from "../subscription/subscription.model";
import { StripePriceModel } from "../price/price.model";
import Stripe from "../../../__mocks__/stripe";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../signup/signup.interfaces";

describe("/api/stripe/webhook", () => {
	let expressApp: express.Express;
	let accessToken: string;
	let accountId: string;
	let stripeCustomerId: string;
	const accountModel = new AccountModel(null);
	const stripe = new Stripe("");

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const userPayload = {
			firstName: "Johnny",
			lastName: "webhook",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "webhook Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(userPayload));


		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		stripeCustomerId = account.stripeCustomerId;
	});

	it("Event customer.subscription.created", async () => {
		const currentPeriodStart = Date.now();

		const payload = {
			type: "customer.subscription.created",
			data: {
				object: {
					customer: stripeCustomerId,
					current_period_start: currentPeriodStart,
					metadata: {
						gp: "true"
					}
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(payload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const customer: any = await stripe.customers.retrieve(stripeCustomerId);
		expect(customer.metadata.billingCycleStart).toBe(currentPeriodStart.toString());
	});

	it("Event customer.subscription.deleted", async () => {
		await setAccountToAnyPaid();
		const a = await accountModel.readOneById(accountId);

		expect(a.subscription.price).toBeGreaterThan(0);
		expect(a.subscription.type).toBe("pro");

		const webhookPayload = {
			type: "customer.subscription.deleted",
			data: {
				object: {
					customer: stripeCustomerId,
					metadata: {
						gp: "true"
					}
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const b: IAccount = await accountModel.readOneById(accountId);

		expect(b.subscription.price).toBe(0);
		expect(b.subscription.type).toBe("basic");
	});

	it("Event customer.subscription.updated", async () => {
		await setAccountToFree();
		const a: IAccount = await accountModel.readOneById(accountId);

		expect(a.subscription.price).toBe(0);

		const millisecondsInOneYear = 365.25 * 24 * 60 * 60 * 1000;
		const timeOneYearInThePastMS = Date.now() - millisecondsInOneYear;

		// create a new subscripton that is Paid
		const pricePaid = await getAnyPaidPrice();
		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const subscriptionPaid = await stripeSubscriptionModel.createSubscription(stripeCustomerId, pricePaid as any);

		const webhookPayload = {
			type: "customer.subscription.updated",
			data: {
				object: subscriptionPaid,
				previous_attributes: {
					current_period_start: timeOneYearInThePastMS
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.price).toBeGreaterThan(0);

		const customer = await stripe.customers.retrieve(stripeCustomerId);
		const tenMinInMS = 60000 * 10;
		expect(customer.metadata.billingCycleStart / tenMinInMS).toBeCloseTo(Date.now() / tenMinInMS, 0);
	});

	it("Event customer.created", async () => {
		// set usage to something other than 0 prior to test start
		await accountModel.setUsage(accountId, 99);

		const a :IAccount = await accountModel.readOneById(accountId);

		expect(a.totalImpressionsCurrentCycle).toBe(99);

		const webhookPayload = {
			type: "customer.created",
			data: {
				object: {
					id: stripeCustomerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const b :IAccount = await accountModel.readOneById(accountId);
		expect(b.totalImpressionsCurrentCycle).toBe(0);
	});

	it("Event customer.updated --> resetUsage", async () => {
		// set usage to something other than 0 prior to test start
		await accountModel.setUsage(accountId, 7);

		const a :IAccount = await accountModel.readOneById(accountId);

		expect(a.totalImpressionsCurrentCycle).toBe(7);

		// This value being different than what is in the DB will cause
		// usage to be reset
		const changeToBillingCycleStart = Date.now();

		const webhookPayload = {
			type: "customer.updated",
			data: {
				object: {
					id: stripeCustomerId
				},
				previous_attributes: {
					metadata: {
						billingCycleStart: changeToBillingCycleStart
					}
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const b :IAccount = await accountModel.readOneById(accountId);
		expect(b.totalImpressionsCurrentCycle).toBe(0);
	});

	it("Event customer.updated --> do not resetUsage", async () => {
		await accountModel.setUsage(accountId, 65);

		const a :IAccount = await accountModel.readOneById(accountId);

		expect(a.totalImpressionsCurrentCycle).toBe(65);

		// undefined metaData will results in AccountModel.resetUsage not being called
		const undefinedMetaData = undefined;

		const webhookPayload = {
			type: "customer.updated",
			data: {
				object: {
					id: stripeCustomerId
				},
				previous_attributes: {
					metadata: undefinedMetaData
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const b :IAccount = await accountModel.readOneById(accountId);
		expect(b.totalImpressionsCurrentCycle).toBe(65);
	});

	it("Event invoice.created", async () => {
		// ensure the user does not have any invoices to begin the test
		await accountModel.setInvoices(accountId, []);

		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.invoices.length).toBe(0);

		// create an invoice to send with the event and setup the Stripe mock
		const stripeSubscriptionId = a.subscription.stripeSubscriptionId;

		const expected_amount_due = 10;
		const expected_created = Date.now();
		const invoice = {
			amount_due: expected_amount_due,
			created: expected_created,
			currency: "CAD",
			status: "paid",
			customer: stripeCustomerId,
			subscription: stripeSubscriptionId,
			amount_paid: 1000000,
			attempt_count: 0
		};

		const webhookPayload = {
			type: "invoice.created",
			data: {
				object: invoice
			}
		};

		await stripe.invoices.create(invoice);
		const i = await stripe.invoices.list({
			customer: stripeCustomerId,
			status: "paid"
		});

		expect(i.data.length).toBe(1);
		expect(i.data[0].customer).toBe(stripeCustomerId);
		expect(i.data[0].subscription).toBe(stripeSubscriptionId);

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const updatedAccount: IAccount = await accountModel.readOneById(accountId);

		expect(updatedAccount.invoices.length).toBe(1);
		const actualInvoice = updatedAccount.invoices[0];
		expect(actualInvoice.amountDue).toBe(expected_amount_due);
		expect(actualInvoice.created).toBe(expected_created);
		expect(actualInvoice.currency).toBe("CAD");
		expect(actualInvoice.status).toBe("paid");

		const invoices = await stripe.invoices.list({
			customer: stripeCustomerId,
			status: "paid"
		});

		expect(invoices.data.length).toBe(1);
		expect(invoices.data[0].customer).toBe(stripeCustomerId);
		expect(invoices.data[0].subscription).toBe(stripeSubscriptionId);
	});

	it("Event invoice.deleted", async () => {
		// ensure the user HAS 2 and only 2 invoices to begin the test
		await accountModel.setInvoices(accountId, [] as any);
		stripe.invoices.deleteAll({
			customer: stripeCustomerId,
			status: "paid"
		});

		const a = await accountModel.readOneById(accountId);
		const stripeSubscriptionId = a.subscription.stripeSubscriptionId;

		const invoice1 = {
			amount_due: 33.78,
			created: Date.now(),
			currency: "CAD",
			due_date: undefined,
			period_end: undefined,
			period_start: undefined,
			status: "paid",
			customer: stripeCustomerId,
			subscription: stripeSubscriptionId,
			amount_paid: 99,
			attempt_count: 0
		};

		const invoice2 = {
			amount_due: 99,
			created: Date.now(),
			currency: "CAD",
			due_date: undefined,
			period_end: undefined,
			period_start: undefined,
			status: "paid",
			customer: stripeCustomerId,
			subscription: stripeSubscriptionId,
			amount_paid: 99,
			attempt_count: 1
		};

		await accountModel.setInvoices(accountId, [invoice1, invoice2] as any);
		const b = await accountModel.readOneById(accountId);
		expect(b.invoices.length).toBe(2);

		await stripe.invoices.create(invoice1);
		await stripe.invoices.create(invoice2);

		const stripeInvoices = await stripe.invoices.list({
			customer: stripeCustomerId,
			status: "paid"
		});
		expect(stripeInvoices.data.length).toBe(2);
		const invocieRemaining = stripeInvoices.data[0];
		const invocie2Delete = stripeInvoices.data[1];

		// lets pretend the this change in the stripe backend triggered the event
		stripe.invoices.delete(invocie2Delete.id);

		const webhookPayload = {
			type: "invoice.deleted",
			data: {
				object: {
					customer: stripeCustomerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);


		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const acccountActual = await accountModel.readOneById(accountId);
		expect(acccountActual.invoices.length).toBe(1);
		expect(acccountActual.invoices[0].id).toBe(invocieRemaining.id);

		const stripeInvoices2 = await stripe.invoices.list({
			customer: stripeCustomerId,
			status: "paid"
		});
		expect(stripeInvoices2.data.length).toBe(1);
		expect(stripeInvoices2.data[0].id).toBe(invocieRemaining.id);
	});

	it("Event payment_method.attached", async () => {
		// ensure there are no payment methods before starting
		await deleteAllPaymentMethods(stripeCustomerId);

		// create a payment method in stripe mock
		const paymentParams = {
			type: "card",
			card: {
				brand: "Mastercard",
				last4: "3856"
			}
		};
		const payment = await stripe.paymentMethods.create(paymentParams);

		// mimic a stripe attach in the Stripe backend
		const paymentAttached = await stripe.paymentMethods.attach(
			payment.id,
			{
				customer: stripeCustomerId
			}
		);
		expect(paymentAttached.customer).toBe(stripeCustomerId);

		const webhookPayload = {
			type: "payment_method.attached",
			data: {
				object: {
					id: payment.id,
					customer: stripeCustomerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const account: IAccount = await accountModel.readOneById(accountId);
		expect(account.subscription.hasPaymentMethod).toBe(true);
	});

	it("Event payment_method.detached", async () => {
		// ensure there are no payment methods before starting
		await deleteAllPaymentMethods(stripeCustomerId);

		const paymentParams = {
			type: "card",
			card: {
				brand: "AmericanExpress",
				last4: "1426"
			}
		};

		const payment = await createPaymentMethodAndAttach(stripeCustomerId, paymentParams);

		// remove payment method from stripe
		await stripe.paymentMethods.detach(payment.id);

		const webhookPayload = {
			type: "payment_method.detached",
			data: {
				previous_attributes: {
					customer: stripeCustomerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const a:IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.hasPaymentMethod).toBe(false);
	});


	const createPaymentMethodAndAttach = async (customerId: string, paymentParams: any): Promise<any> => {
		// create a payment method in stripe mock
		const payment = await stripe.paymentMethods.create(paymentParams);

		// send event so server updates DB with updated payment info
		const webhookPayload = {
			type: "payment_method.attached",
			data: {
				object: {
					id: payment.id,
					customer: customerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const a: IAccount = await accountModel.readOneByStripeCustomer(customerId);
		expect(a.subscription.hasPaymentMethod).toBe(true);

		return payment;
	};

	const deleteAllPaymentMethods = async (customerId: string): Promise<void> => {
		const payments = await stripe.paymentMethods.list({
			customer: customerId
		});

		payments.data.forEach(async (payment: { id: any; }) => await stripe.paymentMethods.delete(payment.id));

		const webhookPayload = {
			type: "payment_method.detached",
			data: {
				previous_attributes: {
					customer: stripeCustomerId
				}
			}
		};

		const res = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.hasPaymentMethod).toBe(false);
	};

	const setAccountToFree = async (): Promise<void> => {
		const stripePriceModel = new StripePriceModel();
		const priceFree = await stripePriceModel.getFreePrice();
		setAccountTo(priceFree);
	};

	const setAccountToAnyPaid = async (): Promise<void> => {
		const pricePaid = await getAnyPaidPrice();
		await setAccountTo(pricePaid);
	};

	const getAnyPaidPrice = async (): Promise<any> => {
		const prices = await stripe.prices.list();
		const pricePaid = prices.data.find((price: { unit_amount: number; metadata: { gp: string; }; }) =>
			price.unit_amount > 0 && price.metadata.gp?.toLocaleLowerCase().trim() === "true"
		);

		return pricePaid;
	};

	const setAccountTo = async (price: any): Promise<void> => {
		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const subscriptionFree = await stripeSubscriptionModel.createSubscription(stripeCustomerId, price);

		await accountModel.setSubscription(accountId, subscriptionFree.id);
	};
});



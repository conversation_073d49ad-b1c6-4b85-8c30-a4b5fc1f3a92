import mongoose, { Schema } from "mongoose";
import { IMetricVideoClicks } from "../../modules/metricVideoClick/metricVideoClick.interfaces";

const MetricVideoClicksSchema: Schema = new Schema({
	accountId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	videoId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	productId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricVideoClickDBModel = mongoose.model<IMetricVideoClicks>(
	"metric_video_clicks",
	MetricVideoClicksSchema
);

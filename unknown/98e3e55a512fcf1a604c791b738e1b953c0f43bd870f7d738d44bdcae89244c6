import { BucketModel } from "./bucket.model";
import mongoose from "mongoose";

describe("BucketModel", () => {
	const objectIdPattern = /-[a-f\d]{24}\./;
	const objectId = new mongoose.Types.ObjectId().toString();

	beforeEach(() => {
		jest.spyOn(mongoose.Types.ObjectId.prototype, "toString").mockReturnValue(objectId);
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	it("removes invalid characters and retains URL-safe characters", () => {
		const fileName = "user-video@!$%*&^file_with:<>?{}[]|`^&=;.mp4";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(fileName, suffix);
		expect(result).toMatch(objectIdPattern);
		expect(result).toContain(`user-videofile_with-${objectId}.mp4`);
	});

	it("truncates filenames exceeding the allowed length", () => {
		const longFileName = "a".repeat(250) + ".txt";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(longFileName, suffix);

		const maxFileNameLength = BucketModel.getMaxFileNameLength();
		const allowedBaseNameLength = maxFileNameLength - objectId.length - 1 - ".txt".length;
		const sanitizedBaseFileName = result.split("-")[0];

		expect(result).toMatch(new RegExp(`^[a-zA-Z0-9\\-_.~]+-${objectId}\\.txt$`));
		expect(result.length).toBeLessThanOrEqual(maxFileNameLength);
		expect(sanitizedBaseFileName.length).toBeLessThanOrEqual(allowedBaseNameLength);
	});

	it("handles filenames with no extension", () => {
		const fileName = "filenameWithoutExtension";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(fileName, suffix);
		expect(result).toBe(`filenameWithoutExtension-${objectId}`);
	});

	it("handles filenames with only invalid characters", () => {
		const fileName = "!@#$%^&*()";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(fileName, suffix);
		expect(result).toBe(`-${objectId}`);
	});

	it("preserves valid extensions and handles multiple dots and hyphens", () => {
		const fileName = "user..video---file.mp4";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(fileName, suffix);
		expect(result).toMatch(objectIdPattern);
		expect(result).toContain(`user..video---file-${objectId}.mp4`);
	});

	it("handles edge cases with empty filenames", () => {
		const fileName = "";
		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const result = BucketModel.sanitizeFileName(fileName, suffix);
		expect(result).toBe(`-${objectId}`);
	});
});

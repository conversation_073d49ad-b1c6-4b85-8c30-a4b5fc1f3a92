import {
	EmailInput,
	sendTransactionalEmail
} from "../../services/email/email.service";
import { APIError } from "./apiError";
import { buildAdminLink } from "./gp.helper";
import jwt from "jsonwebtoken";
import { IAccount } from "../../modules/account/account.interfaces";
import { AccountModel } from "../../modules/account/account.model";
import { IUser } from "../../modules/user/user.interfaces";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";
import { IInvitationToken } from "../../modules/invitation/invitation.interfaces";
import {
	AdminLinkType,
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { readAccount2 } from "../../services/mongodb/account.service";
import { readInvitation } from "../../services/mongodb/invitations.service";
import { ClientSession } from "mongoose";

interface EmailData {
	email: string;
	callbackEndpoint: string;
	locale: LocaleAPI;
	linkType: AdminLinkType;
	template: string;
	subject: string;
}

export async function processInvitationToken(
	inviteToken: string,
	email: string,
	hashkey: string,
	dbTransactionSession: ClientSession | null
): Promise<{ accountDocument: IAccount | null; verified: boolean; invitationId: string }> {
	const invitationToken: IInvitationToken = jwt.decode(
		inviteToken
	) as IInvitationToken;

	if (!invitationToken) {
		throw new APIError(
			APIErrorName.E_INVALID_INPUT,
			"Invalid invitation token"
		);
	}

	const invitationDocument = await readInvitation(
		{
			_id: invitationToken.invitationId
		},
		dbTransactionSession
	);

	if (!invitationDocument) {
		throw new APIError(
			APIErrorName.E_DOCUMENT_NOT_FOUND,
			"invitation document not found"
		);
	}

	jwt.verify(inviteToken, `${hashkey}${invitationDocument.salt}`);

	if (email !== invitationToken.email) {
		throw new APIError(
			APIErrorName.E_INVALID_INPUT,
			"email does not match invitation"
		);
	}

	const accountDocument = await readAccount2(
		{
			_id: invitationDocument.accountId
		},
		dbTransactionSession
	);

	if (!accountDocument) {
		throw new APIError(
			APIErrorName.E_DOCUMENT_NOT_FOUND,
			"account document not found"
		);
	}

	return { accountDocument, verified: true, invitationId: invitationToken.invitationId };
}

export function generateRandomPassword(length = 8): string {
	const characters =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
	const charactersLength = characters.length;
	let result = "";

	for (let i = 0; i < length; i++) {
		result += characters.charAt(Math.floor(Math.random() * charactersLength));
	}

	return result;
}

export async function handleAccountCreation(
	userDocument: IUser,
	dbTransactionSession: ClientSession | null,
	companyName?: string
): Promise<IAccount> {

	const accountModel = new AccountModel(dbTransactionSession);
	let accountDocument = await accountModel.create({
		companyName: companyName ?? "",
		companyURL: "",
		user: userDocument
	});

	const interactiveCollectionModel = new InteractiveCollectionModel(dbTransactionSession);
	const shoppableCollectionDocument = await interactiveCollectionModel.createOne({
		title: "Default",
		shoppableVideos: []
	}, accountDocument);

	shoppableCollectionDocument.throwIfError();

	accountDocument.defaultCollectionId = shoppableCollectionDocument.getData()._id;

	accountDocument = await accountModel.updateOneById(accountDocument._id.toString(), {
		defaultCollectionId: shoppableCollectionDocument.getData()._id
	});

	return accountDocument;
}

export async function sendEmailLink(
	emailData: EmailData,
	authenticationDocument: any,
	hashkey: string
): Promise<void> {
	const jwtValue = jwt.sign(
		{ authenticationId: authenticationDocument._id },
		`${hashkey}${authenticationDocument.salt}`
	);

	const link = buildAdminLink({
		linkType: emailData.linkType,
		baseUrl: emailData.callbackEndpoint,
		token: jwtValue
	});

	// eslint-disable-next-line camelcase
	const email_input: EmailInput = {
		template: emailData.template,
		to: emailData.email,
		subject: emailData.subject,
		data: { link },
		locale: emailData.locale ?? LocaleAPI.EN_US
	};

	const emailSent = await sendTransactionalEmail(email_input);

	if (!emailSent) {
		const err = new Error();
		err.message = `Failed to send email to: ${emailData.email}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
}

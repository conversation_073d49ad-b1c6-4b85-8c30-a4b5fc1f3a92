import React, {
	useEffect,
	useState
} from "react";
import { Flex } from "@player/components/Flex";
import { IconButton } from "@player/components/button/IconButton";
import {
	appSessionStorageAtom,
	isAppInPreviewModeAtom,
	isPortraitAtom
} from "@player/app/app.state";
import {
	useRecoilState,
	useRecoilValue
} from "recoil";
import {
	postLikeVideo,
	notifyCoreToStoreLikedVideo
} from "@player/like/like.service";
import { default as RedHeartFillIcon } from "@player/assets/icon-heart-fill-red.svg";
import { default as HeartOutlineIcon } from "@player/assets/icon-heart-outline.svg";

interface Props {
	videoId: string;
}

export const LikeVideo: React.FC<Props> = ({ videoId }) => {
	const [appSessionStorage, setAppSessionStorage] = useRecoilState(appSessionStorageAtom);
	const [liked, setLiked] = useState<boolean>(false);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const isPortrait = useRecoilValue(isPortraitAtom);

	useEffect(() => {
		setLiked(appSessionStorage?.likedVideos?.includes(videoId) ?? false);
	}, [appSessionStorage, videoId]);

	const handleLikeClick = (): void => {
		if (liked) return;
		setLiked(true);
		const appStorage = appSessionStorage || { likedVideos: [] };
		setAppSessionStorage({
			...appStorage,
			likedVideos: [...appStorage.likedVideos, videoId]
		});
		if (!isAppInPreviewMode){
			notifyCoreToStoreLikedVideo(videoId);
			postLikeVideo(videoId);
		}

	};


	return (
		<Flex alignSelf={"flex-end"}>
			<IconButton svgIcon = {liked ? RedHeartFillIcon : HeartOutlineIcon}
				isPortrait={isPortrait}
				onClick={(e): void => {
					e.stopPropagation();
					handleLikeClick();}}/>
		</Flex>
	);
};

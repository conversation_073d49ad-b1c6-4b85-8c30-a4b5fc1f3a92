import {
	Request,
	Response,
	NextFunction
} from "express";
import mongoose from "mongoose";

export const allRoutes = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
	try {
		const session = await mongoose.startSession();
		res.locals.session = session;

		res.on("close", async () => {
			const session = res.locals.session as mongoose.ClientSession | null | undefined;
			if (session && !session.hasEnded) {
				try {
					await new Promise(resolve => setTimeout(resolve, 50));
					await session.endSession();
				} catch (error) {
					console.error("Error ending session:", error);
				}
			}
		});
	} catch (error) {
		console.error("Error starting session:", error);
	}

	next();
};

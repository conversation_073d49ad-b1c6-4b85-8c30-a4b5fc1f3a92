import mongoose, { ClientSession } from "mongoose";
import { MetricVisibleImpressionDBModel } from "./metricVisibleImpression.db.model";
import { IAccount } from "../account/account.interfaces";
import {
	MetricVisibleImpression,
	MetricVisibleImpressionCreateOne
} from "./metricVisibleImpression.interface";

export class MetricVisibleImpressionModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async createOne (account: IAccount, createData: MetricVisibleImpressionCreateOne):
	Promise<MetricVisibleImpression> {
		const options: mongoose.SaveOptions = { session: this.session };
		const newDocument = await new MetricVisibleImpressionDBModel({
			createdAt: createData.createdAt,
			accountId: account._id,
			collectionId: createData.collectionId,
			videoId: createData.videoId
		}).save(options);
		return newDocument;
	}
}

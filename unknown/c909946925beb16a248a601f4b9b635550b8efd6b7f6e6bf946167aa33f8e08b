import Joi from "joi";
import { UserTeam } from "../user/user.enums";
import { AccountPlatform } from "./account.enum";
import {
	DeleteAccountRequest,
	IPatchAccountPayload,
	IPostAccountPayload,
	IPostAccountsTokenPayload,
	GetSearchPayload
} from "./account.interfaces";
import {
	BaseJoi,
	BaseAccessJoi
} from "../base/base.joi";
import {
	APIErrorName,
	SearchType
} from "../../interfaces/apiTypes";
import { IPostSignupPayload } from "../../modules/signup/signup.interfaces";

export const postAccountsTokenSchema = {
	data: Joi.object<IPostAccountsTokenPayload>({
		accountId: Joi.string().hex().length(24)
	})
};

export const patchAccountSchema = {
	data: Joi.object<IPatchAccountPayload>({
		companyName: Joi.string().min(1),
		companyURL: Joi.string().uri(),
		platform: Joi.string().valid(...Object.values(AccountPlatform)).optional(),
		videoProfile: Joi.string().hex().length(24).optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"any.only": APIErrorName.E_INVALID_INPUT
	})
};

export const postAccountSchema = {
	data: Joi.object<IPostAccountPayload>({
		companyName: Joi.string().min(1),
		companyURL: Joi.string().uri()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

export const postSignupSchema = {
	data: Joi.object<IPostSignupPayload>({
		name: Joi.string().min(1).optional(),
		team: Joi.string().valid(...Object.values(UserTeam)).required(),
		companyName: Joi.string().min(1).required(),
		platform: Joi.string().valid(...Object.values(AccountPlatform)).required()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"any.only": APIErrorName.E_INVALID_INPUT
	})
};

export const DeleteAccountJoi = BaseJoi.append<DeleteAccountRequest>({
	accountId: Joi.string().hex().length(24)
});

export const getSearchSchema = BaseAccessJoi.append<GetSearchPayload>({
	search: Joi.string()
		.when("searchType", {
			is: SearchType.SEARCH_PREFIX,
			then: Joi.string().required(),
			otherwise: Joi.string().optional().allow("")
		}),
	searchType: Joi.string()
		.valid(...Object.values(SearchType))
		.required()
});

export interface PaymentListRequest {
	apiVersion: string;
	accessToken: string;
	accountToken: string;
	accountId: string;
}

export interface PaymentPostRequest {
	apiVersion: string;
	accessToken: string;
	accountToken: string;
	paymentMethodId: string;
}

export interface PaymentListRequest {
	apiVersion: string;
	accountId: string;
}

export interface PaymentDeleteRequest {
	apiVersion: string;
	accessToken: string;
	accountToken: string;
	paymentMethodId: string;
}

export interface CardPaymentMethod {
	id: string;
	brand: string;
	last4: string;
	default: boolean;
}

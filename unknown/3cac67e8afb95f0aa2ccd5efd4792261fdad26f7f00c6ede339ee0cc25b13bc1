import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { OIDCConfig } from "../secrets/secrets.model";
import { OIDCUserInfo } from "./oidc.interfaces";



export class AuthorizationServer {
	constructor(private providerConfig: OIDCConfig) {}

	public async fetchUserInfo(accessToken: string): Promise<OIDCUserInfo> {
		const headers = {
			Authorization: `Bearer ${accessToken}`
		};

		const response = await fetch(this.providerConfig.userInfoEndpoint, {
			method: "GET",
			headers: headers
		});

		if (!response.ok) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, `Failed to fetch user info: ${response.statusText}`);
		}

		const json = await response.json();
		return json as OIDCUserInfo;
	}

	public async fetchTokens(body: URLSearchParams): Promise<any> {
		const tokenEndpoint = this.stripTrailingSlash(this.providerConfig.tokenEndpoint);
		const response = await fetch(tokenEndpoint, {
			method: "POST",
			headers: {
				"Content-Type": "application/x-www-form-urlencoded"
			},
			body: body
		});

		if (!response.ok) {
			const errorBody = await response.text();
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED,
				`Failed to exchange OIDC tokens: ${response.statusText}. Error details: ${errorBody}`
			);
		}

		return await response.json();
	}

	private stripTrailingSlash(url: string): string {
		return url.replace(/\/$/, "");
	}
}

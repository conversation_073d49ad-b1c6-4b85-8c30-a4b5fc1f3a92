import Joi from "joi";
import {
	BasePublicRequest,
	BaseRequest,
	BaseAccessRequest
} from "./base.interfaces";

export const AccessTokenJoi = Joi.string().custom((value, helpers) => {
	if (!value) {
		return helpers.error("any.invalid");
	}

	const split: string[] = value.split(" ");
	if (split.length !== 2) {
		return helpers.error("any.invalid");
	}

	const bearer: string = split[0];

	if (bearer.toLowerCase() !== "bearer") {
		return helpers.error("any.invalid");
	}

	const token: string = split[1];

	if (!token || token.length < 1) {
		return helpers.error("any.invalid");
	}

	return token;
});

export const VersionJoi = Joi.string().custom((value, helpers) => {
	if (!value) {
		return helpers.error("any.invalid");
	}

	if (isNaN(value)) {
		return helpers.error("any.invalid");
	}

	const version = Number(value);

	if (version < 1) {
		return helpers.error("any.invalid");
	}

	return version;
});

export const BasePublicJoi = Joi.object<BasePublicRequest>({
	"apiVersion": VersionJoi.required()
});

export const BaseJoi = Joi.object<BaseRequest>({
	"apiVersion": VersionJoi.required(),
	"accessToken": AccessTokenJoi.required(),
	"accountToken": Joi.string().required()
});

export const BaseAccessJoi = Joi.object<BaseAccessRequest>({
	"apiVersion": VersionJoi.required(),
	"accessToken": AccessTokenJoi.required()
});

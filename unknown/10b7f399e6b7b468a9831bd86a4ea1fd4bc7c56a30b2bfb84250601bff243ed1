import { ClientSession } from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";

export const startDBTransaction = async (session: ClientSession | null): Promise<void> => {
	try {
		if (process.env.SUPPORTS_DB_TRANSACTIONS === "FALSE") {
			return;
		}

		session?.startTransaction();
	} catch (error: any) {
		let abortMessage;
		try {
			await session?.abortTransaction();
		} catch (e: any) {
			abortMessage = e.message;
		}
		const err = new Error();
		err.message = "<startDBTransaction.service> DB Transaction aborted. | " +
		`abortTransactionError:${error.name} | ${error.message} | ${abortMessage}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const cancelDBTransaction = async (session: ClientSession | null | undefined): Promise<void> => {
	try {
		if (session?.transaction.isActive) {
			await session.abortTransaction();
		}
	} catch (error: any) {
		const err = new Error();
		err.message = "<cancelDBTransaction.service> DB Transaction cancel failed. | " +
		`abortTransactionError:${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const completeDBTransaction = async (session: ClientSession | null | undefined): Promise<void> => {
	try {
		if (session?.transaction.isActive) {
			await session?.commitTransaction();
		}
	} catch (error: any) {
		const err = new Error();
		err.message = "<completeDBTransaction.service> DB Transaction complete failed. | " +
		`abortTransactionError:${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

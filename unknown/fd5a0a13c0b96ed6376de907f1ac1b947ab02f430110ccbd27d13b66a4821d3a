import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import {
	ISignupEmailPayload,
	ISignupPayload
} from "../modules/signup/signup.interfaces";
import { IAccount } from "../modules/account/account.interfaces";

describe("GET /invitations/self", () => {
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;

	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	const inviterSignupPayload: ISignupPayload = {
		email: "<EMAIL>",
		password: "Password1!",
		firstName: "John",
		lastName: "Doe",
		companyName: "invitations.self.positive.mode",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld",
		legalAgreement: true
	};

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(inviterSignupPayload));

		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});


	it("Should return 200 [OK] with no invitation documents", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitations");
		expect(res.body.invitations).toEqual([]);
	});

	it("Should return 200 [OK] with invitation documents", async () => {
		const emailFriendOne = "<EMAIL>";
		const resInvitation1 = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", emailFriendOne)
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(resInvitation1.statusCode).toBe(200);

		const signupEmailPayload: ISignupEmailPayload = {
			email: emailFriendOne,
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));

		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitations");
		expect(res.body.invitations.length).toEqual(1);

		const invite = res.body.invitations[0];
		expect(invite.email).toBe(emailFriendOne);
		expect(invite.account[0]._id.toString()).toBe(account._id.toString());
		expect(invite.account[0].companyName).toBe(inviterSignupPayload.companyName);
	});
});

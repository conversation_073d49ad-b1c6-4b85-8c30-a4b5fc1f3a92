import Jo<PERSON> from "joi";
import { UserTeam } from "../../modules/user/user.enums";
import { IPatchUserPayload } from "../../modules/user/user.interfaces";
import { APIErrorName } from "../../interfaces/apiTypes";

export const patchUserSchema = {
	data: Joi.object<IPatchUserPayload>({
		firstName: Joi.string().min(1),
		lastName: Joi.string().min(1),
		team: Joi.string().valid(...Object.values(UserTeam)).optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"any.only": APIErrorName.E_INVALID_INPUT
	})
};

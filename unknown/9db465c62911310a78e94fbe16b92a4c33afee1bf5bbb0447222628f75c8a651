import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { InvitationStatus } from "../modules/invitation/invitation.enum";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { APIError } from "../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { UserModel } from "../modules/user/user.model";
import TestHelper from "./mocks/testHelper";
import { ISignupEmailPayload } from "../modules/signup/signup.interfaces";
import { IInvitation } from "../modules/invitation/invitation.interfaces";

import * as InvitationService from "../services/mongodb/invitations.service";


describe("PUT /invitations", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);
	const testHelper = new TestHelper(expressApp);

	let accessToken: string;
	let accountToken: string;

	const signupFriendEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] for missing status", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] for invalid status", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("status", "incorrect");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for incorrect status", async () => {
		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("status", InvitationStatus.PENDING);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 404 [E_DOCUMENT_NOT_FOUND] missing user document", async () => {
		jest.spyOn(UserModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("status", InvitationStatus.ACCEPTED);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("return 404 [E_DOCUMENT_NOT_FOUND] when failing to update the invitation", async () => {
		jest.spyOn(InvitationService, "updateInvitation").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.put("/api/invitations/000000000000000000000000")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("status", InvitationStatus.ACCEPTED);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("return 500 [E_SERVICE_FAILED] when failing to update the authentication", async () => {
		jest.spyOn(AuthenticationModel.prototype, "attachAccountByUserId").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create auth document.")
		);

		const invite: IInvitation = await testHelper.createInvitation(
			accountToken,
			signupFriendEmailPayload.email,
			accessToken);

		const { accessToken: accessTokenFriend } = await testHelper.signupEmail(signupFriendEmailPayload);

		const res = await supertest(expressApp)
			.put(`/api/invitations/${invite._id.toString()}`)
			.set("Authorization", `Bearer ${accessTokenFriend}`)
			.set("x-api-version", "2")
			.field("status", InvitationStatus.ACCEPTED);

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});
});

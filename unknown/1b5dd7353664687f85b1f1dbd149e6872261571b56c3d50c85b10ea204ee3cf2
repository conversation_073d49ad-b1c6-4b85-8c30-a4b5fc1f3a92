import React, {
	useRef,
	useEffect,
	useState,
	useCallback
} from "react";
import styled from "styled-components/macro";
import {
	IVideoData,
	SnippetTypeEnum
} from "@player/app";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";

const OnsiteContainer = styled.iframe`
    width: 370px;
    aspect-ratio: 9 / 16;
    border: 4px solid #e6e6e6;
    border-radius: ${(props):string => props.theme.inlineBorderRadius + "px"};
    margin: 10px;
`;

interface Props {
	appUrl: string;
	videoData: IVideoData[];
	collectionId: string;
}

export const Onsite: React.FC<Props> = ({ appUrl, videoData, collectionId }) => {
	const [wasViewable, setWasViewable] = useState<boolean>(false);
	const onsiteContainerRef = useRef<HTMLIFrameElement | null>(null);

	const sendViewableEvent = useCallback(() => {
		if (wasViewable) return;
		sendAppEvent({
			eventName: EventNameEnum.SNIPPET_VIEWABLE_IMPRESSION,
			accountId: videoData[0].accountId,
			collectionId,
			snippet: { type: SnippetTypeEnum.ONSITE }
		});
		setWasViewable(true);
	}, [wasViewable, videoData, collectionId]);

	const checkVisibility = useCallback(
		(entries: IntersectionObserverEntry[]) => {
			entries.forEach((entry) => {
				if (
					entry.target === onsiteContainerRef.current &&
                    entry.isIntersecting
				) {
					sendViewableEvent();
				}
			});
		},
		[sendViewableEvent]
	);

	useEffect(() => {
		const observer = new IntersectionObserver(checkVisibility, {
			root: null,
			threshold: 0.6
		});
		const currentOnsiteContainer = onsiteContainerRef.current;
		if (currentOnsiteContainer) {
			observer.observe(currentOnsiteContainer);
		}
		return () => {
			if (currentOnsiteContainer) {
				observer.unobserve(currentOnsiteContainer);
			}
		};
	}, [checkVisibility]);

	return (
		<OnsiteContainer
			ref={onsiteContainerRef}
			data-testid={"onsite-iframe"}
			src={appUrl}
		/>
	);
};

import express, {
	Express,
	Request,
	Response
} from "express";
import multer from "multer";
import { AccountTokenController } from "./account.token.controller";
import { postAccountsTokenSchema } from "../account.validator";
import { decodeAccess } from "../../../middleware/decodeAccess.mw";
import { isSchemaValid } from "../../../middleware/isSchemaValid.mw";

export class AccountTokenRouter {
	private controller: AccountTokenController = new AccountTokenController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[
				multer().any(),
				decodeAccess,
				isSchemaValid(postAccountsTokenSchema.data)
			],
			(request: Request, response: Response) => {
				return this.controller.post(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/accounts/token", this.router);
	}
}

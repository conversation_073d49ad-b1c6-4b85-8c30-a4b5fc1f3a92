import {
	Document,
	ObjectId
} from "mongoose";

export interface IShoppableCollection extends Document {
	_id: ObjectId;
	title: string;
	createdAt: Date;
	accountId: ObjectId;
	shoppableVideos: ObjectId[];
	orderCount: number;
	userSessionCount: number;
	userCVR: number;
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: "left" | "right";
	inlineBorderRadius?: number;
}

export interface IPostCollectionPayload {
	title: string;
	shoppableVideos: string[];
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: "left" | "right";
	inlineBorderRadius?: number;
}

export interface IPutCollectionPayload {
	title: string;
	shoppableVideos: string[];
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: "left" | "right";
	inlineBorderRadius?: number;
}

export interface IGetShoppableCollectionPayload {
	limit: string;
	sortKey: string;
	SortBy: "asc" | "dsc";
}

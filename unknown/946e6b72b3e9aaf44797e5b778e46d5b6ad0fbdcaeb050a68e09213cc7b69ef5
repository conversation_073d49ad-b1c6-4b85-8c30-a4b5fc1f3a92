import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	LocaleAPI,
	APIErrorName
} from "../interfaces/apiTypes";
import { IAccount } from "../modules/account/account.interfaces";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { AccountDBModel } from "../modules/account/accountDB.model";
import { InvitationDBModel } from "../modules/invitation/invitationDB.model";
import { AccountUserModel } from "../modules/account/user/account.user.model";
import { InvitationStatus } from "../modules/invitation/invitation.enum";


const expressApp = createServer();
initExpressRoutes(expressApp);

const emailAddress = "<EMAIL>";

// eslint-disable-next-line max-lines-per-function
describe("POST /invitations", () => {
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;

	beforeAll(async () => {
		const signupPayload: ISignupPayload = {
			email: emailAddress,
			password: "Password1!",
			firstName: "<PERSON>",
			lastName: "Doe",
			companyName: "<PERSON> Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		expect(signUpResponse.statusCode).toBe(200);

		accessToken = signUpResponse.body.accessToken;

		const accountsResponse = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json");

		expect(accountsResponse.statusCode).toBe(200);
		expect(accountsResponse.body.accounts.length).toBe(1);

		account = accountsResponse.body.accounts[0];

		const accountTokenResponse = await supertest(expressApp)
			.post("/api/accounts/token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json")
			.field("accountId", account._id.toString());

		expect(accountTokenResponse.statusCode).toBe(200);
		expect(accountTokenResponse.body).toHaveProperty("token");
		accountToken = accountTokenResponse.body.token;
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "0")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "x")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] for missing account token", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing email", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing locale", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "nobody5.domain.tld")
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing callbackEndpoint", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should fail when inviting a duplicate invitation email", async () => {
		const res1 = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res1.statusCode).toBe(200);

		const res2 = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res2.statusCode).toBe(409);
		expect(res2.body).toHaveProperty("error");
		expect(res2.body.error).toEqual(APIErrorName.E_EMAIL_ALREADY_INVITED);
	});

	it("Should return 500 [E_SERVICE_FAILED] if creating the invitation fails", async () => {
		const spy = jest.spyOn(InvitationDBModel.prototype, "save").mockImplementationOnce(() => {
			return Promise.reject(new Error("failed to save invitation."));
		});

		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		if (res.statusCode !== 500) {
			console.error(res.body);
		}

		spy.mockRestore();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_SERVICE_FAILED] if the account document fails to read", async () => {
		const spy = jest.spyOn(AccountDBModel, "findOne").mockImplementationOnce((): any => {
			return {
				exec: jest.fn().mockRejectedValue(new Error("failed to read account document.")),
				session: jest.fn().mockRejectedValue(new Error("failed to read account document."))
			};
		});

		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		spy.mockRestore();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] if the account document does not exist", async () => {
		const spy = jest.spyOn(AccountDBModel, "findOne").mockImplementationOnce((): any => {
			return {
				exec: jest.fn().mockResolvedValue(null),
				session: jest.fn().mockResolvedValue(null)
			};
		});
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		spy.mockRestore();
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 200 [OK] if creating the invitation succeeds", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", "<EMAIL>")
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitation");
		expect(res.body.invitation).toHaveProperty("_id");
		expect(res.body.invitation).toHaveProperty("accountId");
		expect(res.body.invitation).toHaveProperty("createdAt");
		expect(res.body.invitation).toHaveProperty("email");
		expect(res.body.invitation).toHaveProperty("status");
		expect(res.body.invitation).not.toHaveProperty("salt");
	});

	it("Should fail to create an invitation for an already registered user", async () => {
		const res = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", emailAddress)
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(res.statusCode).toBe(409);
		expect(res.body).toHaveProperty("error");
		expect(res.body.error).toEqual(APIErrorName.E_EMAIL_ALREADY_EXISTS);
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body).toHaveProperty("message");
		expect(res.body.message).toEqual("Email already exists");
		expect(res.body).toHaveProperty("detail");

		const detail = JSON.parse(res.body.detail);
		expect(detail).toHaveProperty("email");
		expect(detail.email).toEqual(emailAddress);
	});

	it("Should fail to create an invitation greater than the remaining user limit", async () => {
		const accountUserModel = new AccountUserModel(null);
		const remainingUsers = await accountUserModel.countRemainingUsers(account._id.toString());

		if (remainingUsers < 1) {
			throw new Error("No remaining users available for this account to test.");
		}

		const invitationUsers: string[] = [];
		for (let i = 0; i < remainingUsers + 1; i++) {
			invitationUsers.push(`post.invitation.multiple.${i}@domain.tld`);
		}

		await invitationUsers.reduce<Promise<void>>(async (previousPromise, email, index) => {
			await previousPromise;

			const response = await supertest(expressApp)
				.post("/api/invitations")
				.set("Authorization", `Bearer ${accessToken}`)
				.set("x-account-token", accountToken)
				.set("x-api-version", "2")
				.field("email", email)
				.field("locale", LocaleAPI.EN_US)
				.field("callbackEndpoint", "https://domain.tld/invitations/accept");

			if (index < remainingUsers) {
				expect(response.status).toBe(200);
				expect(response.body).toHaveProperty("invitation");
				expect(response.body.invitation).toHaveProperty("_id");
				expect(response.body.invitation).toHaveProperty("accountId");
				expect(response.body.invitation).toHaveProperty("createdAt");
				expect(response.body.invitation).toHaveProperty("email");
				expect(response.body.invitation).toHaveProperty("status");
				expect(response.body.invitation).not.toHaveProperty("salt");
				expect(response.body.invitation.email).toEqual(email);
				expect(response.body.invitation.status).toEqual(InvitationStatus.PENDING);
			} else {
				expect(response.status).toBe(403);
				expect(response.body).toHaveProperty("error");
				expect(response.body.error).toEqual(APIErrorName.E_USER_LIMIT_REACHED);
				expect(response.body).toHaveProperty("errorRef");
				expect(response.body).toHaveProperty("message");
				expect(response.body.message).toEqual("User limit reached");
			}

			return Promise.resolve();
		}, Promise.resolve());
	});
});

import Stripe from "stripe";
import {
	Price,
	Product
} from "./product.interfaces";
import { StripePricesModel } from "../stripe/price/prices.model";

export class ProductsModel {
	constructor(public products: Product[] = []) {}

	public async fromStripeProducts(stripeProducts: Stripe.Product[]): Promise<Product[]> {
		const products: Product[] = stripeProducts.map((product) => {
			return <Product>{
				id: product.id,
				name: product.name,
				description: product.description ?? "",
				type: product.metadata.type,
				prices: []
			};
		});

		this.products = await this.fetchAndMapProductPrices(products);

		return this.products;
	}

	private async fetchAndMapProductPrices(products: Product[]): Promise<Product[]> {
		const stripePricesModel = new StripePricesModel();
		const promises = products.map(async (product) => {
			const stripePrices = await stripePricesModel.getPricesByProductId(product.id);
			product.prices = this.fromStripePrices(stripePrices);
		});

		await Promise.all(promises);

		return products;
	}

	private fromStripePrices(stripePrices: Stripe.Price[]): Price[] {
		const prices: Price[] = stripePrices.map((price) => {
			const decimalPlaces: { [key: string]: number } = {
				USD: 2,
				EUR: 2,
				GBP: 2,
				JPY: 0,
				KRW: 0
			};

			const fractionDigits = decimalPlaces[price.currency] ?? 2;
			const divisor = Math.pow(10, fractionDigits);
			const unitAmount = price.unit_amount ?? 0;

			return {
				id: price.id,
				name: price.nickname ?? price.id,
				// eslint-disable-next-line camelcase
				unitAmount: unitAmount,
				currency: price.currency,
				unitDevisor: divisor
			};
		});

		return prices;
	}
}

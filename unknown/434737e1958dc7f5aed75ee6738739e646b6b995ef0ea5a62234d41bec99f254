import {
	Request,
	Response
} from "express";
import { AuthenticationModel } from "../authentication/authentication.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IVerifyPayload } from "./verify.interfaces";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	readUnverifiedJwt,
	verifyJwt
} from "../../utils/helpers/gpJwt.helper";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";
import { UserModel } from "../user/user.model";

export const verifyController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const secrets: ISecrets = await getSecrets();
		const verifyPayload = req.body as IVerifyPayload;

		const decodedAuthentication = readUnverifiedJwt(verifyPayload.token);

		const authenticationId = decodedAuthentication?.authenticationId;

		if (!authenticationId) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing authenticationId.");
		}

		const authModel = new AuthenticationModel(null);
		const authDoc = await authModel.readOneById(authenticationId);

		const previouslyVerified = authDoc.verified;

		// Verify the JWT
		if (!secrets.hashkey.key) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "Missing hashKey");
		}
		const saltedHash = `${secrets.hashkey.key}${authDoc.salt}`;

		const decodedJWT = verifyJwt<IVerifyPayload>(verifyPayload.token, saltedHash);

		if (!decodedJWT) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Failed to verify Token.");
		}
		let verifiedAuthentication = authDoc;
		if (!previouslyVerified) {
			const authenticationModel = new AuthenticationModel(null);
			verifiedAuthentication = await authenticationModel.verifyOne(authDoc);
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(verifiedAuthentication.userId.toString());

		const accessToken = await createAccessToken(verifiedAuthentication, userDocument);
		const refreshToken = await createRefreshToken(verifiedAuthentication);

		return res.status(200).send({ accessToken, refreshToken });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import express, {
	Request,
	Response
} from "express";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { getCheckInstallController } from "../controllers/getCheckInstall.controller";

const router = express.Router();

router.get("/", (req: Request, res: Response) => {
	res.setLocale("en");
	res.status(200).send({ data: res.__("HELLO") });
});

router.get("/check-install", [decodeAccess], getCheckInstallController);

export default router;

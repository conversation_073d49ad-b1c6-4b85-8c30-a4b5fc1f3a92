import {
	closePlayerApp,
	storeCTAEngagementData,
	startPlayerApp,
	ICTAEngagementData
} from "@player/core/core.util";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";

import {
	replyToAppWithSessionStorage,
	addLikedVideoToSessionStorage
} from "@player/core/core.storage";
import {
	ICloseAppData,
	sendCloseAppEvent
} from "@player/app/app.message";

export const CoreMessageSecret = "!gpcore:";

export enum CoreMessageTypeEnum {
	CLOSE_APPLICATION = "close_application",
	OPEN_PARENT_APP = "open_parent_app",
	STORE_CTA_ENGAGEMENT_DATA = "store_cta_engagement_data",
	REQUEST_SESSION_STORAGE = "request_session_storage",
	STORE_LIKED_VIDEO = "store_liked_video"
}
export const onReceiveMessageAtCore = (event: MessageEvent): void => {
	try {
		if (!event?.data?.toString().startsWith(CoreMessageSecret)) {
			return;
		}

		const data = event.data.substring(CoreMessageSecret.length);
		const parsedData = JSON.parse(data);
		processCoreMessage(parsedData.type as CoreMessageTypeEnum, parsedData.data, event.source as Window);
	} catch (error) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n onReceiveMessageAtCore",
			scope: LogScope.ERROR
		});
	}
};


const processCoreMessage = (messageType: CoreMessageTypeEnum, messageData: unknown, messageSource: Window): void => {
	const message: Record<CoreMessageTypeEnum, () => void> = {
		[CoreMessageTypeEnum.REQUEST_SESSION_STORAGE]: () => {
			replyToAppWithSessionStorage(messageSource);
		},
		[CoreMessageTypeEnum.CLOSE_APPLICATION]: () => {
			const data = messageData as ICloseAppData;
			closePlayerApp();
			if (data.videoPlayer && data.sliderConfig){
				sendCloseAppEvent(data.videoPlayer, data.sliderConfig, data.appSessionId);
			}
		},
		[CoreMessageTypeEnum.STORE_CTA_ENGAGEMENT_DATA]: () => {
			const data = messageData as ICTAEngagementData;
			storeCTAEngagementData(data);
		},
		[CoreMessageTypeEnum.STORE_LIKED_VIDEO]: () => {
			const data = messageData as {videoId: string};
			addLikedVideoToSessionStorage(data.videoId);
		},
		[CoreMessageTypeEnum.OPEN_PARENT_APP]: () => {
			const data = messageData as {url: string};
			startPlayerApp(data.url);

		}
	};

	if (message[messageType]) {
		message[messageType]();
	} else {
		appLog({
			message: `undefined messageType ${messageType}`,
			trace: "processCoreMessage",
			scope: LogScope.ERROR
		});
	}

};


export const sendMessageFromCoreToParent = (messageType: CoreMessageTypeEnum, data: unknown): void => {
	try {
		const message = {
			referrer: window.location.href,
			type: messageType,
			data
		};
		const postEvent = CoreMessageSecret + JSON.stringify(message);
		window?.parent?.postMessage(postEvent, "*");
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n sendMessageFromCoreToParent",
			scope: LogScope.ERROR
		});
		throw error;
	}
};

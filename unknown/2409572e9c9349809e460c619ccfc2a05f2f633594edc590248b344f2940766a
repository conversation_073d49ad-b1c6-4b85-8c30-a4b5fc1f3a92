import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { AuthenticationModel } from "../../../modules/authentication/authentication.model";
import {
	APIErrorName,
	LocaleAPI
} from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";
import { ISignupPayload } from "../../../modules/signup/signup.interfaces";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { IAccount } from "src/modules/account/account.interfaces";

import * as AccountService from "../../../services/mongodb/account.service";


describe("POST /accounts", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.accounts.self.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
	});


	it("Should return 401 [E_MISSING_AUTHORIZATION] when no authentication is supplied", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when failing to read the authentication record", async () => {
		jest.spyOn(AuthenticationModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found")
		);

		const res = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 200 [OK] for no accounts returned", async () => {
		const empty: IAccount[] = [];
		jest.spyOn(AccountService, "readAccounts").mockResolvedValueOnce(empty);

		const res = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accounts");
		expect(res.body.accounts).toEqual([]);
	});

	it("Should return 200 [OK] - return at least one account", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accounts");

		const accounts: IAccount[] = res.body.accounts;
		expect(accounts.length).toBeGreaterThan(0);
	});
});

import jwt from "jsonwebtoken";
import {
	gpLog,
	LogScope
} from "../managers/gpLog.manager";

export const verifyJwt = <T>(token: string, secretKey = ""): T | undefined => {
	try {
		const decoded = jwt.verify(token, secretKey) as T;
		if (decoded) return decoded;
		return undefined;
	} catch (error) {
		gpLog({
			message: "Failed to decode JWT token from inviteToken param.",
			trace: "utils.helpers.verifyJwt",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

export function signJwt(
	obj: any,
	privateKey: string,
	options?: jwt.SignOptions | undefined
): string {
	return jwt.sign(obj, privateKey, {
		...(options && options),
		algorithm: "HS256"
	});
}

export const readUnverifiedJwt = (token: string): any => {
	try {
		return JSON.parse(Buffer.from(token.split(".")[1], "base64").toString());
	} catch (e: any) {
		gpLog({
			message: "Failed to readUnverifiedJwt",
			objData: { token },
			trace: "gpJwt.helper | readUnverifiedJwt",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

import mongoose, { Schema } from "mongoose";
import { MetricVisibleImpression } from "./metricVisibleImpression.interface";

const MetricVisibleImpressionSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	collectionId: { type: Schema.Types.ObjectId, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	createdAt: { type: Schema.Types.Date, required: true }
});

export const MetricVisibleImpressionDBModel = mongoose.model<MetricVisibleImpression>(
	"metric_visible_impressions",
	MetricVisibleImpressionSchema
);

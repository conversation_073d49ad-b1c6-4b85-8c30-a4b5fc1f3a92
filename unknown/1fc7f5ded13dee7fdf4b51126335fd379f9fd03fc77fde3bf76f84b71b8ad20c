import mongoose, { ClientSession } from "mongoose";
import {
	MetricPhonePressDBModel,
	MetricPhonePress
} from "./metricPhonePressDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";

export class MetricPhonePressModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async createOne (videoId: string, accountId: string, createdAt: Date): Promise<MetricPhonePress> {
		try {
			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const document = await new MetricPhonePressDBModel({
				createdAt: createdAt,
				videoId: new mongoose.Types.ObjectId(videoId),
				accountId: new mongoose.Types.ObjectId(accountId)
			}).save(options);

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async registerPhonePress (videoId: string, accountId: string, createdAt: Date): Promise<void> {
		try {
			const interactiveVideoModel: InteractiveVideoModel = new InteractiveVideoModel(null);
			const videoDocument = await interactiveVideoModel.incrementPhonePressCount(videoId);
			await this.createOne(videoDocument._id.toString(), accountId, createdAt);
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

}

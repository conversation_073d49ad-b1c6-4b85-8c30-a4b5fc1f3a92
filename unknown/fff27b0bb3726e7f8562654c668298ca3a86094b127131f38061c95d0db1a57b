import { validateBackslash } from "@player/app";

export const hasSameOrigin = (): boolean => {
	try {
		if (window === window.parent) {
			return true;
		}
		const parentOrigin = window.parent.location.origin;
		const iframeOrigin = window.location.origin;
		return parentOrigin === iframeOrigin;
	} catch (error: unknown) {
		console.error("Error comparing origins:", (error as Error).message);
	}
	return false;
};


export const isCoreScriptInParent = (src: string): boolean => {
	try {
		const parsedUrl = new URL(src);
		const baseUrl = parsedUrl.origin;
		const coreSrc = validateBackslash(baseUrl) + "core.js";
		const parentScripts = window.parent.document.getElementsByTagName("script");
		for (let i = 0; i < parentScripts?.length; i++) {
			if (parentScripts[i].src === coreSrc) {
				return true;
			}
		}
		return false;
	} catch (error: unknown) {
		console.error(
			"isCoreScriptInParent | unable to process parent frame. error: ",
			(error as Error).message
		);
		return false;
	}
};

export const buildIframeApp = (url: string): void => {
	const style = document.createElement("style");
	style.id = "player-host-style";
	style.innerHTML = `
      html, body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        width: 100%;
      }
    `;
	document.head.appendChild(style);

	const iframe = document.createElement("iframe");
	iframe.id = "player-app";
	iframe.src = url;
	iframe.style.position = "fixed";
	iframe.style.top = "0";
	iframe.style.left = "0";
	iframe.style.width = "100%";
	iframe.style.height = "100%";
	iframe.style.border = "none";
	iframe.style.zIndex = "2147483647";
	iframe.setAttribute("scrolling", "no");
	document.body.appendChild(iframe);
};

export const loadGoogleFont = (fontName: string): void => {
	const fontUrlMap: { [key: string]: string } = {
		"Readex Pro": "https://fonts.googleapis.com/css2?family=Readex+Pro:wght@300;500;700&display=swap",
		"Roboto": "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap",
		"Oswald": "https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&display=swap",
		"Playfair Display": "https://fonts.googleapis.com/css2" +
		"?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap",
		"Merriweather": "https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700;900&display=swap",
		"Bebas neue": "https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap",
		"Garamond": "https://fonts.googleapis.com/css2?family=EB+Garamond:wght@400;500;600;700&display=swap",
		"Fira Sans Condensed": "https://fonts.googleapis.com/css2?" +
		"family=Fira+Sans+Condensed:wght@100;300;400;500;600;700;800;900&display=swap",
		"Arvo": "https://fonts.googleapis.com/css2?family=Arvo:wght@400;700&display=swap",
		"IBM Plex Mono": "https://fonts.googleapis.com/css2?" +
		"family=IBM+Plex+Mono:wght@100;200;300;400;500;600;700&display=swap",
		"Varela Round": "https://fonts.googleapis.com/css2?family=Varela+Round&display=swap",
		"Alfa Slab One": "https://fonts.googleapis.com/css2?family=Alfa+Slab+One&display=swap"
	};

	const fontUrl = fontUrlMap[fontName];
	if (!fontUrl) {
		console.error(`Font URL not found for font: ${fontName}`);
		return;
	}

	const existingLink = Array.from(document.head.getElementsByTagName("link")).find(
		(link) => link.href === fontUrl
	);
	if (existingLink) {
		return;
	}

	const linkElement = document.createElement("link");
	linkElement.rel = "stylesheet";
	linkElement.href = fontUrl;
	document.head.appendChild(linkElement);
};


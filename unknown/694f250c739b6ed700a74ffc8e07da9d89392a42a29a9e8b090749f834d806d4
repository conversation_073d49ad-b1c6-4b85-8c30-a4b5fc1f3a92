import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { IAccount } from "../modules/account/account.interfaces";
import { IInvitation } from "src/modules/invitation/invitation.interfaces";

describe("GET /invitations", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.invitations.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] for missing account token", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 200 [OK] with no invitation documents", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitations");
		expect(res.body.invitations).toEqual([]);
	});

	it("Should return 200 [OK] with invitation documents", async () => {
		const invite1 = await testHelper.createInvitation(accountToken, "<EMAIL>", accessToken);
		const invite2 = await testHelper.createInvitation(accountToken, "<EMAIL>", accessToken);
		const invite3 = await testHelper.createInvitation(accountToken, "<EMAIL>", accessToken);

		const res = await supertest(expressApp)
			.get("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitations");
		const invitations : IInvitation[] = res.body.invitations;

		expect(invitations.filter(i => i.email == "<EMAIL>" && i._id == invite1._id).length).toBe(1);
		expect(invitations.filter(i => i.email == "<EMAIL>" && i._id == invite2._id).length).toBe(1);
		expect(invitations.filter(i => i.email == "<EMAIL>" && i._id == invite3._id).length).toBe(1);
	});
});

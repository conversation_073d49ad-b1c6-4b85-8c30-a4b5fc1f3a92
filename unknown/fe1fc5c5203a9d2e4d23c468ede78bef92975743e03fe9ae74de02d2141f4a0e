import {
	Request,
	Response
} from "express";
import {
	EmailInput,
	sendTransactionalEmail
} from "../../services/email/email.service";
import {
	APIErrorName,
	AdminLinkType
} from "../../interfaces/apiTypes";
import { IForgotPasswordPayload } from "./forgotpassword.interfaces";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { APIError } from "../../utils/helpers/apiError";
import { buildAdminLink } from "../../utils/helpers/gp.helper";
import { signJwt } from "../../utils/helpers/gpJwt.helper";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import { AuthMethods } from "../authentication/authentication.enums";
import { findAuthentication } from "../signin/signinEmail.controller";

export const forgotPasswordController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const secrets: ISecrets = await getSecrets();
		const payload = req.body as IForgotPasswordPayload;
		payload.email = payload.email.toLowerCase().trim();

		const authRecord = await findAuthentication(payload.email);

		if (authRecord.method !== AuthMethods.EMAIL_PASSWORD) {
			throw new APIError(APIErrorName.E_AUTH_METHOD_NOT_SUPPORTED,
				"Password reset is not available for this authentication method");
		}

		const jwtValue = signJwt(
			{ authenticationId: authRecord._id },
			`${secrets.hashkey.key}${authRecord.salt}`
		);

		const baseURLCallback = payload.callbackEndpoint;
		const link = buildAdminLink({
			linkType: AdminLinkType.FORGOT_PASSWORD,
			baseUrl: baseURLCallback,
			token: jwtValue
		});

		const loginLink = buildAdminLink({
			linkType: AdminLinkType.SIGN_IN,
			baseUrl: baseURLCallback,
			token: jwtValue
		});

		const emailInput: EmailInput = {
			template: "reset-password",
			to: payload.email,
			subject: "Reset Password",
			data: { link, loginLink },
			locale: payload.locale
		};

		const emailSent = await sendTransactionalEmail(emailInput);

		if (!emailSent) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, `Failed to send email to: ${payload.email}`);
		}

		gpLog({
			message: "Successfully sent password reset",
			trace: "controllers.auth.forgotPasswordController",
			scope: LogScope.INFO
		});
		return res.status(200).send();
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import TestHelper from "../../../__tests__/mocks/testHelper";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import jwt, { JwtPayload } from "jsonwebtoken";
import { ISignupEmailPayload } from "../../../modules/signup/signup.interfaces";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { IAccount } from "src/modules/account/account.interfaces";
import { Express } from "express";


describe("POST /api/auth/link", () => {
	let expressApp: Express;
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		const testHelper = new TestHelper(expressApp);

		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("should succeed when the returned JSON contains a valid authLinkToken that is valid for 2 minutes", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("authLinkToken");
		expect(res.body.authLinkToken).not.toBeUndefined();
		expect(res.body.authLinkToken).not.toBeUndefined();
		expect(res.body.authLinkToken).not.toBeNull();

		const authLinkToken = res.body.authLinkToken;
		const decoded = jwt.decode(authLinkToken) as JwtPayload;
		expect(decoded).toHaveProperty("exp");
		expect(decoded).toHaveProperty("authenticationId");
		expect(decoded).toHaveProperty("accountId");
		expect(decoded).toHaveProperty("type");
		expect(decoded.type).toBe("authlink");

		const exp = (decoded.exp ?? 0) * 1000;
		const now = Date.now();
		const expiresIn = exp - now;
		const twoMinutes = 120000;
		expect(expiresIn).toBeLessThanOrEqual(twoMinutes);
	});
});

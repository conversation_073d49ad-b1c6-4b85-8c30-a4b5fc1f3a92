import mongoose, { ClientSession } from "mongoose";

export interface IDBALInput {
	permissions: Permission[];
	path: string;
	query: any;
	dbSession?: ClientSession | null;
	sort?: [string, mongoose.SortOrder][] | null | undefined;
}

export enum APITokenType {
	ACCESS_TOKEN = "accesstoken",
	REFRESH_TOKEN = "refreshtoken"
}

export enum Permission {
	ALL = "ALL",
	READ = "READ",
	WRITE = "WRITE",
	DELETE = "DELETE"
}

export enum APIErrorName {
	E_INTERNAL_ERROR = "E_INTERNAL_ERROR",
	E_MISSING_EVENT_NAME = "E_MISSING_EVENT_NAME",
	E_MISSING_EVENT_DATA = "E_MISSING_EVENT_DATA",
	E_MISSING_ACCOUNT_ID = "E_MISSING_ACCOUNT_ID",
	E_PASSWORD_COMPLEXITY = "E_PASSWORD_COMPLEXITY",
	E_ACCOUNT_EXISTS = "E_ACCOUNT_EXISTS",
	E_KEY_EXISTS = "E_KEY_EXISTS",
	E_HASH_FAILURE = "E_HASH_FAILURE",
	E_SERVICE_FAILED = "E_SERVICE_FAILED",
	E_INVALID_ACCOUNT = "E_INVALID_ACCOUNT",
	E_MISSING_HASHKEY = "E_MISSING_HASHKEY",
	E_FILE_WRITE_FAILURE = "E_FILE_WRITE_FAILURE",
	E_INVALID_INPUT = "E_INVALID_INPUT",
	E_DOCUMENT_NOT_FOUND = "E_DOCUMENT_NOT_FOUND",
	E_MISSING_AUTHORIZATION = "E_MISSING_AUTHORIZATION",
	E_INVALID_AUTHORIZATION = "E_INVALID_AUTHORIZATION",
	E_USER_NOT_VERIFIED = "E_USER_NOT_VERIFIED",
	E_DATABASE_FAILURE = "E_DATABASE_FAILURE",
	E_INVALID_METHOD = "E_INVALID_METHOD",
	E_NOT_AUTHENTICATED = "E_NOT_AUTHENTICATED",
	E_EMAIL_NOT_FOUND = "E_EMAIL_NOT_FOUND",
	E_MISSING_KEY = "E_MISSING_KEY",
	E_MISSING_ID = "E_MISSING_ID",
	E_EMAIL_DELIVERY = "E_EMAIL_DELIVERY",
	E_ACCESS_FORBIDDEN = "E_ACCESS_FORBIDDEN",
	E_COLLECTION_NOT_FOUND = "E_COLLECTION_NOT_FOUND",
	E_INVALID_PRODUCT_URL = "E_INVALID_PRODUCT_URL",
	E_ALREADY_VERIFIED = "E_ALREADY_VERIFIED",
	E_REQUEST_FORBIDDEN = "E_REQUEST_FORBIDDEN",
	E_INVALID_METRIC_INPUT = "E_INVALID_METRIC_INPUT",
	E_SIGN_UP_EXISTING_EMAIL = "E_SIGN_UP_EXISTING_EMAIL",
	E_SIGN_UP_INVALID_FORMAT = "E_SIGN_UP_INVALID_FORMAT",
	E_SIGN_UP_GENERIC = "E_SIGN_UP_GENERIC",
	E_EMAIL_SIGN_IN_NO_EMAIL = "E_EMAIL_SIGN_IN_NO_EMAIL",
	E_EMAIL_SIGN_IN_INVALID_FORMAT = "E_EMAIL_SIGN_IN_INVALID_FORMAT",
	E_EMAIL_SIGN_IN_GENERIC = "E_EMAIL_SIGN_IN_GENERIC",
	E_SIGN_IN_INCORRECT = "E_SIGN_IN_INCORRECT",
	E_EMAIL_SIGN_INVALID_FORMAT = "E_EMAIL_SIGN_INVALID_FORMAT",
	E_SIGN_IN_GENERIC = "E_SIGN_IN_GENERIC",
	E_FORGOT_PASSWORD_NO_EMAIL = "E_FORGOT_PASSWORD_NO_EMAIL",
	E_FORGOT_PASSWORD_GENERIC = "E_FORGOT_PASSWORD_GENERIC",
	E_VIDEO_LIBRARY_FILE_TOO_LARGE = "E_VIDEO_LIBRARY_FILE_TOO_LARGE",
	E_VIDEO_LIBRARY_WRONG_FILE_TYPE = "E_VIDEO_LIBRARY_WRONG_FILE_TYPE",
	E_VIDEO_LIBRARY_VIDEO_NOT_PROCESSED = "E_VIDEO_LIBRARY_VIDEO_NOT_PROCESSED",
	E_VIDEO_LIBRARY_CANT_LOAD_VIDEOS = "E_VIDEO_LIBRARY_CANT_LOAD_VIDEOS",
	E_VIDEO_LIBRARY_GENERIC = "E_VIDEO_LIBRARY_GENERIC",
	E_VIDEO_MANAGER_CANT_LOAD_VIDEOS = "E_VIDEO_MANAGER_CANT_LOAD_VIDEOS",
	E_VIDEO_MANAGER_CANT_REORDER = "E_VIDEO_MANAGER_CANT_REORDER",
	E_VIDEO_MANAGER_GENERIC = "E_VIDEO_MANAGER_GENERIC",
	E_VIDEO_DETAIL_NO_TITLE = "E_VIDEO_DETAIL_NO_TITLE",
	E_VIDEO_DETAIL_NO_VIDEO = "E_VIDEO_DETAIL_NO_VIDEO",
	E_VIDEO_DETAIL_NO_TITLE_LINK = "E_VIDEO_DETAIL_NO_TITLE_LINK",
	E_VIDEO_DETAIL_NO_LINK_IMAGE = "E_VIDEO_DETAIL_NO_LINK_IMAGE",
	E_VIDEO_DETAIL_FILE_SIZE = "E_VIDEO_DETAIL_FILE_SIZE",
	E_VIDEO_DETAIL_WRONG_FORMAT = "E_VIDEO_DETAIL_WRONG_FORMAT",
	E_VIDEO_DETAIL_CANT_PROCESS = "E_VIDEO_DETAIL_CANT_PROCESS",
	E_VIDEO_DETAIL_CANT_SAVE = "E_VIDEO_DETAIL_CANT_SAVE",
	E_VIDEO_DETAIL_GENERIC = "E_VIDEO_DETAIL_GENERIC",
	E_COLLECTIONS_CANT_LOAD = "E_COLLECTIONS_CANT_LOAD",
	E_COLLECTIONS_CANT_DELETE = "E_COLLECTIONS_CANT_DELETE",
	E_COLLECTIONS_GENERIC = "E_COLLECTIONS_GENERIC",
	E_COLLECTION_DETAIL_CANT_LOAD = "E_COLLECTION_DETAIL_CANT_LOAD",
	E_COLLECTION_DETAIL_CANT_REORDER = "E_COLLECTION_DETAIL_CANT_REORDER",
	E_COLLECTION_DETAIL_CANT_SAVE = "E_COLLECTION_DETAIL_CANT_SAVE",
	E_COLLECTION_DETAIL_CANT_ADD_VIDEOS = "E_COLLECTION_DETAIL_CANT_ADD_VIDEOS",
	E_COLLECTION_DETAIL_GENERIC = "E_COLLECTION_DETAIL_GENERIC",
	E_PROFILE_CANT_LOAD = "E_PROFILE_CANT_LOAD",
	E_PROFILE_CANT_SAVE = "E_PROFILE_CANT_SAVE",
	E_PROFILE_GENERIC = "E_PROFILE_GENERIC",
	E_PROFILE_CANT_SEND_RESET = "E_PROFILE_CANT_SEND_RESET",
	E_SETTINGS_CANT_LOAD = "E_SETTINGS_CANT_LOAD",
	E_SETTINGS_CANT_SAVE = "E_SETTINGS_CANT_SAVE",
	E_SETTINGS_GENERIC = "E_SETTINGS_GENERIC",
	E_SETTINGS_CANT_INVITE_USER = "E_SETTINGS_CANT_INVITE_USER",
	E_SETTINGS_INVALID_INVITE_EMAIL = "E_SETTINGS_INVALID_INVITE_EMAIL",
	E_SETTINGS_CANT_DELETE_USER = "E_SETTINGS_CANT_DELETE_USER",
	E_SETTINGS_WRONG_FILE_TYPE = "E_SETTINGS_WRONG_FILE_TYPE",
	E_PERFORMANCE_CANT_LOAD = "E_PERFORMANCE_CANT_LOAD",
	E_PERFORMANCE_CANT_REFRESH = "E_PERFORMANCE_CANT_REFRESH",
	E_PERFORMANCE_NO_AVAILABLE_DATA = "E_PERFORMANCE_NO_AVAILABLE_DATA",
	E_PERFORMANCE_TIMEOUT = "E_PERFORMANCE_TIMEOUT",
	E_PERFORMANCE_GENERIC = "E_PERFORMANCE_GENERIC",
	E_GENERAL_ERROR_CANT_LOAD = "E_GENERAL_ERROR_CANT_LOAD",
	E_GENERAL_ERROR_GENERIC = "E_GENERAL_ERROR_GENERIC",
	E_GENERAL_ERROR_SIGNED_OUT = "E_GENERAL_ERROR_SIGNED_OUT",
	E_RESOURCE_CONFLICT = "E_RESOURCE_CONFLICT",
	E_INVALID_STRIPE_SIGNATURE = "E_INVALID_STRIPE_SIGNATURE",
	E_NOT_IMPLEMENTED = "E_NOT_IMPLEMENTED",
	E_IMPRESSION_LIMIT_REACHED = "E_IMPRESSION_LIMIT_REACHED",
	E_MISSING_CUSTOMER_ID = "E_MISSING_CUSTOMER_ID",
	E_MISSING_OIDC_DATA= "E_MISSING_OIDC_DATA",
	E_AUTH_METHOD_NOT_SUPPORTED = "E_AUTH_METHOD_NOT_SUPPORTED",
	E_UPDATE_EMAIL_CONFLICT = "E_UPDATE_EMAIL_CONFLICT",
	E_AUTH_EXISTS_DUPLICIATE = "E_AUTH_DUPLICIATE",
	E_STRIPE_CUSTOMER_NOT_FOUND = "E_STRIPE_CUSTOMER_NOT_FOUND",
	E_SCHEMA_VALIDATION_ERROR = "E_SCHEMA_VALIDATION_ERROR",
	E_FILE_TOO_LARGE = "E_FILE_TOO_LARGE",
	E_FILE_UPLOAD_EXISTING_FILENAME = "E_FILE_UPLOAD_EXISTING_FILENAME",
	E_USER_LIMIT_REACHED = "E_USER_LIMIT_REACHED",
	E_EMAIL_ALREADY_EXISTS = "E_EMAIL_ALREADY_EXISTS",
	E_EMAIL_ALREADY_INVITED = "E_EMAIL_ALREADY_INVITED",
	E_VIDEO_PROFILE_EXISTS = "E_VIDEO_PROFILE_EXISTS",
}

export enum LocaleAPI {
	EN_US = "en_US",
	EN_CA = "en_CA",
	FR = "fr"
}

export enum AdminLinkType {
	VERIFY = "/confirm-verification",
	FORGOT_PASSWORD = "/forgot-password",
	SIGN_IN = "/verify-sign-in"
}

export enum SearchType {
	SEARCH_PREFIX = "SEARCH_PREFIX",
	SEARCH_ALL_NUMBER = "SEARCH_ALL_NUMBER",
	SEARCH_ALL_SPECIAL = "SEARCH_ALL_SPECIAL"
}

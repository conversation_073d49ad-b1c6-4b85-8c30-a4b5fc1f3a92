import React, { useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import SelectCompany from "@src/components/company/SelectCompany";

const Company: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.companyPage.pageTitle;
	}, [translation.companyPage.pageTitle]);

	return (
		<>
			<Header auth={true} showAllOptions={true} />
			<SelectCompany />
		</>
	);
};

export default Company;

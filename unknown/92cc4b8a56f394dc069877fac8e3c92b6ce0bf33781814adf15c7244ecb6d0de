import Jo<PERSON> from "joi";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";

const emailSchema = Joi.string()
	.lowercase()
	.email({
		tlds: { allow: false }
	})
	.trim();

export const signUpEmailSchema = {
	data: Joi.object<ISignupEmailPayload>({
		email: emailSchema.required(),
		callbackEndpoint: Joi.string().required(),
		locale: Joi.string().custom((value, helper) => {
			if (Object.values(LocaleAPI).includes(value)) {
				return true;
			}
			return helper.message({
				custom: "language is not supported."
			});

		})
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

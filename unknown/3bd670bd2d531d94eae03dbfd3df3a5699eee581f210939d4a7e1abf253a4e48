export const fetchSnippetData = async <T>(
	urls: string[],
	callback: (url: string, data: T) => void
): Promise<void> => {
	try {
		const promises = urls.map(async (url) => {
			try {
				const response = await fetch(url);
				const data = await response.json();
				callback(url, data as T);
			} catch (error: unknown) {
				console.error(`Failed to fetch data: ${(error as Error).message}`);
			}
		});
		await Promise.allSettled(promises);
	} catch (error: unknown) {
		console.error(`Failed to fetch data from urls: ${urls}. error: ${(error as Error).message}`);
	}
};

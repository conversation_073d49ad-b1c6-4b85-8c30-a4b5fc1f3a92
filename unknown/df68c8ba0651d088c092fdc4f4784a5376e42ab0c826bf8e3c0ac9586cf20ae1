import {
	ObjectId,
	Document
} from "mongoose";
import { PlayPercentCounts } from "./metricVideoPlayTime.enum";

export interface MetricVideoPlayTimeCreateOne {
	createdAt: Date;
	updatedAt: Date;
	videoId: string;
	playId: string;
	appSessionId: string;
	totalPlayTimeSeconds: number;
	collectionShare?: string;
	videoShare?: string;
	videoPlayStatus: "playing" | "stopped";
}

export interface IMetricVideoPlayTime extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	videoId: ObjectId;
	collectionShare?: ObjectId;
	videoShare?: ObjectId;
	playId: ObjectId;
	appSessionId: ObjectId;
	totalPlayTimeSeconds: number;
	videoPlayStatus: "playing" | "stopped";
	createdAt: Date;
	updatedAt: Date;
}

export interface IVideoPlayTimeInput {
	accountId: string;
	videoId: string;
	playId: string;
	appSessionId: string;
	videoPlayStatus: "playing" | "stopped";
	videoSecondsPosition: number;
	videoSecondsLength: number;
	videoShare?: string;
	collectionShare?: string;
}

export interface IVideoPlayTimeOutput {
	newPercentToInc: PlayPercentCounts | null;
	oldPercentToDec: PlayPercentCounts | null;
	oldPlayDurationSeconds: number | null;
	wasCreated: boolean;
	oldDocPlayTimeSeconds: number;
}

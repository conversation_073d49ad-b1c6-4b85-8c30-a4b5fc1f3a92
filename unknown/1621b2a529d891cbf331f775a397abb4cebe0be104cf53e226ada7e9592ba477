import express, {
	Express,
	Request,
	Response
} from "express";
import { JobCleanController } from "./job.clean.controller";

export class Job<PERSON>leanRouter {
	private controller: JobCleanController = new JobCleanController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[express.json({ limit: "2MB" })],
			(request: Request, response: Response) => {
				return this.controller.post(request, response);
			}
		);
	}

	public use = (expressServer: Express): void => {
		expressServer.use("/api/jobs/clean", this.router);
	};
}

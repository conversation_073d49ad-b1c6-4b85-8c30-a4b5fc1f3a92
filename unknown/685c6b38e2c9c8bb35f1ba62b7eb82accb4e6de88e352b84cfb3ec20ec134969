import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../signup/signup.interfaces";
import { AuthorizationServerMock } from "../__mocks__/oidc.authorization.server";


describe("OIDC Authentication Tests Positive Mode | POST /api/oauth/oidc", () => {
	let expressApp: express.Express;
	const oidcPostApi = "/api/oauth/oidc";
	const oidcClientId = "client-id-1";
	let idToken: string;
	let accessToken: string;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("should authenticate a new user and return 201.", async () => {
		const email = "<EMAIL>";
		const sub = "oidc.post.test.positive.mode.new.user@sub";
		({ idToken, accessToken } = AuthorizationServerMock.generateTokens(oidcClientId, sub, email));

		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "1")
			.field("oidcIdToken", idToken)
			.field("oidcAccessToken", accessToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(201);
		expect(res.body).toHaveProperty("accessToken");
	});

	it("should authenticate an existing user and return 200.", async () => {
		const email = "<EMAIL>";
		const sub = "oidc.post.test.positive.mode.existing.user@sub";
		({ idToken, accessToken } = AuthorizationServerMock.generateTokens(oidcClientId, sub, email));

		const signupPayload: ISignupPayload = {
			email: email,
			password: "Password",
			firstName: "oidc",
			lastName: "post-test",
			companyName: "Oidc Post Test Positive ModeInc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		if (signUpResponse.status !== 200) {
			console.error("Signup failed. Response:", signUpResponse.body);
			throw new Error(`Signup failed with status ${signUpResponse.status}`);
		}

		expect(signUpResponse.body).toHaveProperty("accessToken");

		const res = await supertest(expressApp)
			.post(oidcPostApi)
			.set("x-api-version", "1")
			.field("oidcIdToken", idToken)
			.field("oidcAccessToken", accessToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
	});
});


import React from "react";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup,
	screen,
	RenderResult
} from "@testing-library/react";
import { act } from "react-dom/test-utils";
import {
	App,
	isPortraitAtom
} from "@player/app";
import { RecoilRoot } from "recoil";
import * as SliderService from "@player/slider/slider.service";
import {
	videoDataMock,
	collectionDataCacheMock,
	gpSessionIdMock,
	cdnUrlMock,
	accountManifestMock,
	videoDataCacheMock
} from "./mocks/data.mock";
import { SubscriptionTypeEnum } from "@player/app/app.interface";
const renderWithRouter = (ui: React.ReactElement, { route = "/" } = {}): RenderResult => {
	window.history.pushState({}, "Test page", route);
	return render(ui);
};

const accountId = videoDataMock.interactiveVideos[0].accountId;
const accountUrl = `${cdnUrlMock}manifests/${accountId}/account-manifest.json`;

const collectionId = videoDataMock.interactiveVideos[0].collectionId;
const collectionUrl = `${cdnUrlMock}data-cache/collections/${collectionId}-collection.json`;

const videoId = videoDataMock.interactiveVideos[0]._id;
const videoUrl = `${cdnUrlMock}data-cache/videos/${videoId}-video.json`;

describe("App Component.", () => {
	afterEach(cleanup);

	beforeAll(async () => {
		jest.clearAllMocks();
		global.fetch = jest.fn(url =>
			Promise.resolve({
				ok: true,
				json: () => {
					if (url === collectionUrl) {
						return Promise.resolve(collectionDataCacheMock);
					} else if (url === accountUrl) {
						return Promise.resolve({ ...accountManifestMock,
							...{ subscriptionType: SubscriptionTypeEnum.PRO } });
					} else if (url === videoUrl) {
						return Promise.resolve(videoDataCacheMock);
					}
				},
				status: 200,
				statusText: "OK"
			})
		) as jest.Mock;
	});

	afterAll(() => {
		jest.restoreAllMocks();
	});

	test("Should render App for root route with params.", async () => {
		const params = {
			accountId: accountId,
			collectionId: collectionId,
			videoId: videoId,
			gpSession: gpSessionIdMock
		};

		global.URLSearchParams = jest.fn(() => ({
			get: jest.fn((param: keyof typeof params) => {
				return params[param];
			})
		})) as jest.Mock;
		const spy = jest.spyOn(SliderService, "fetchSliderData");

		await act(async () => {
			renderWithRouter(
				<RecoilRoot initializeState={(snap): void => snap.set(isPortraitAtom, true)}>
					<App />
				</RecoilRoot>,
				{ route: "/" }
			);
		});

		const mobileLayout = screen.getByTestId("mobile-layout");
		expect(mobileLayout).toBeInTheDocument();
		expect(spy).toHaveBeenCalledTimes(1);
		expect(spy).toHaveBeenCalledWith(undefined, undefined);
		spy.mockRestore();
	});

	test("Should render App for route /c/collectionId.", async () => {
		const spy = jest.spyOn(SliderService, "fetchSliderData");
		await act(async () => {
			renderWithRouter(
				<RecoilRoot initializeState={(snap): void => snap.set(isPortraitAtom, true)}>
					<App />
				</RecoilRoot>,
				{ route: `/c/${collectionId}` }
			);
		});

		expect(true).toBeTruthy();
		const mobileLayout = screen.getByTestId("mobile-layout");
		expect(mobileLayout).toBeInTheDocument();
		expect(spy).toHaveBeenCalledTimes(1);
		expect(spy).toHaveBeenCalledWith(collectionId, undefined);
		spy.mockRestore();
	});

	test("Should render App for route /v/videoId.", async () => {
		const spy = jest.spyOn(SliderService, "fetchSliderData");
		await act(async () => {
			renderWithRouter(
				<RecoilRoot initializeState={(snap): void => snap.set(isPortraitAtom, true)}>
					<App />
				</RecoilRoot>,
				{ route: `/v/${videoId}` }
			);
		});

		expect(true).toBeTruthy();
		const mobileLayout = screen.getByTestId("mobile-layout");
		expect(mobileLayout).toBeInTheDocument();
		expect(spy).toHaveBeenCalledTimes(1);
		expect(spy).toHaveBeenCalledWith(undefined, videoId);
		spy.mockRestore();
	});

	test("Should render NotFound page for unknown route.", async () => {
		await act(async () => {
			renderWithRouter(
				<RecoilRoot initializeState={(snap): void => snap.set(isPortraitAtom, true)}>
					<App />
				</RecoilRoot>,
				{ route: "/random/route" }
			);
		});

		expect(true).toBeTruthy();
		const errorModalElement = screen.getByText("This video is unavailable.");
		expect(errorModalElement).toBeInTheDocument();
	});
});

/* eslint-disable max-lines-per-function */
import <PERSON>Helper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import mongoose from "mongoose";
import { AccountModel } from "../modules/account/account.model";
import { IAccount } from "../modules/account/account.interfaces";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { IInvitation } from "../modules/invitation/invitation.interfaces";
import { InvitationStatus } from "../modules/invitation/invitation.enum";
import { createInvitationToken } from "../utils/tokens";
import {
	Subscription,
	SubscriptionDefaults
} from "../modules/subscription/subscription.interfaces";
import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { UserModel } from "../modules/user/user.model";
import { IUser } from "../modules/user/user.interfaces";
import { InteractiveCollectionModel } from "../modules/interactiveCollection/interactiveCollection.model";
import { IShoppableCollection } from "../modules/interactiveCollection/interactiveCollection.interface";
import {
	IAuthentication,
	UserAuthenticationData
} from "../modules/authentication/authentication.interface";
import { readInvitation } from "../services/mongodb/invitations.service";


describe("POST /auth/signup positive mode", () => {
	let expressApp: express.Express;
	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("full ISignupPayload", async () => {
		const createUserPayload = {
			firstName: "Johnny",
			lastName: "SigninFlow",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "Test SigninFlow Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send(createUserPayload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");

		const userModel = new UserModel(null);
		const userData = await userModel.readOneByEmail(createUserPayload.email);

		expectIUser(createUserPayload, userData);

		const userId = userData._id.toString();

		const accountModel = new AccountModel(null);
		const accountData = await accountModel.readOneByOwnerId(userId);

		expectIAccount(createUserPayload, accountData);

		const accountId = accountData._id.toString();
		const defaultCollectionId = accountData.defaultCollectionId.toString();
		const ownerId = accountData.ownerUserId.toString();
		expect(ownerId).toBe(userId);

		const collectionModel: InteractiveCollectionModel = new InteractiveCollectionModel(null);
		const collectionData = await collectionModel
			.readOneById(defaultCollectionId) as IShoppableCollection;

		expectIShoppableCollection(collectionData);

		expect(defaultCollectionId).toBe(collectionData._id.toString());
		expect(accountId).toBe(collectionData.accountId.toString());

		const authenticationModel = new AuthenticationModel(null);
		const authenticationData =
			await authenticationModel.readOneByEmail(createUserPayload.email) as IAuthentication;

		expectIAuthentication(createUserPayload, authenticationData);

		expect(userId).toBe(authenticationData.userId.toString());
		expect(accountId).toBe(authenticationData.accounts[0]._id.toString());
	});


	it("only username and password", async () => {
		const createUserPayload = {
			email: "<EMAIL>",
			password: "Password1!",
			callbackEndpoint: "https://domain.tld/callback"
		};

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send(createUserPayload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");

		const userModel = new UserModel(null);
		const userData = await userModel.readOneByEmail(createUserPayload.email);

		expectIUser(createUserPayload, userData);

		const userId = userData._id.toString();

		const accountModel = new AccountModel(null);
		const accountData = await accountModel.readOneByOwnerId(userId);

		expectIAccount(createUserPayload, accountData);

		const accountId = accountData._id.toString();
		const defaultCollectionId = accountData.defaultCollectionId.toString();
		const collectionModel: InteractiveCollectionModel = new InteractiveCollectionModel(null);
		const collectionData = await collectionModel
			.readOneById(defaultCollectionId) as IShoppableCollection;

		expectIShoppableCollection(collectionData);

		const authenticationModel = new AuthenticationModel(null);
		const authenticationData =
			await authenticationModel.readOneByEmail(createUserPayload.email) as IAuthentication;

		expectIAuthentication(createUserPayload, authenticationData);

		expect(userId).toBe(authenticationData.userId.toString());
		expect(accountId).toBe(authenticationData.accounts[0]._id.toString());
	});

	it("invitation token", async () => {
		const userWhoWantsToInviteTheirFriendPayload = {
			firstName: "Johnny",
			lastName: "SigninFlowWithInvitation",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "Test SigninFlow With Invitation Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const theFriendWhoReceivesTheInvitationPayload = {
			firstName: "Freddy",
			lastName: "GoodTimes",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: userWhoWantsToInviteTheirFriendPayload.companyName,
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		const { accessToken, accountToken, account } = await testHelper.createUserAndAccount(
			userWhoWantsToInviteTheirFriendPayload);
		const accountId = account._id.toString();

		const resInvitations = await supertest(expressApp)
			.post("/api/invitations")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.field("email", theFriendWhoReceivesTheInvitationPayload.email)
			.field("locale", LocaleAPI.EN_US)
			.field("callbackEndpoint", "https://domain.tld/invitations/accept");

		expect(resInvitations.statusCode).toBe(200);


		const invitationData: IInvitation = resInvitations.body.invitation;
		expect(invitationData.accountId.toString()).toBe(accountId);
		expect(invitationData.email).toBe(theFriendWhoReceivesTheInvitationPayload.email);
		expect(invitationData.status as InvitationStatus).toBe(InvitationStatus.PENDING);

		const createdAt: Date = new Date(invitationData.createdAt);
		const convertMStoMinutes = 60000;
		expect(createdAt.getTime() / convertMStoMinutes).toBeCloseTo(Date.now() / convertMStoMinutes, 0);

		const query = {
			_id: invitationData._id
		};
		const invitationDocument = await readInvitation(query, null);
		if (!invitationDocument) throw new Error("readInvitation could not find your invitation");

		expect(invitationDocument.accountId.toString()).toBe(invitationData.accountId.toString());
		expect(invitationDocument.email).toBe(invitationData.email);

		const invitationToken = await createInvitationToken(invitationDocument, account as IAccount, false);

		theFriendWhoReceivesTheInvitationPayload.inviteToken = invitationToken;
		const resSignup = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send(theFriendWhoReceivesTheInvitationPayload);
		expect(resSignup.statusCode).toBe(200);

		const userModel = new UserModel(null);
		const userData = await userModel.readOneByEmail(theFriendWhoReceivesTheInvitationPayload.email);

		expectIUser(theFriendWhoReceivesTheInvitationPayload, userData);
	});

	const expectIUser = (
		userPayload: any,
		userData: IUser): void => {

		let check : any;

		expect(userData._id).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(userData._id.toString())).toBe(true);
		expect(userData.email).toBe(userPayload.email);

		check = userPayload.firstName ? userPayload.firstName : "";
		expect(userData.firstName).toBe(check);

		check = userPayload.lastName ? userPayload.lastName : "";
		expect(userData.lastName).toBe(check);

		expect(userData.team).toBe(undefined);
		expect(userData.postSignupCompleted).toBe(false);
	};

	const expectISubscription = (subscription: Subscription): void => {
		expect(subscription).toHaveProperty("stripePriceId");
		expect(subscription.stripePriceId).toEqual(expect.stringMatching(/^.+$/));

		expect(subscription).toHaveProperty("stripeSubscriptionId");
		expect(subscription.stripePriceId).toEqual(expect.stringMatching(/^.+$/));

		expect(subscription).toHaveProperty("hideVanityBranding");
		expect(subscription.hideVanityBranding).toBe(SubscriptionDefaults.hideVanityBranding);

		expect(subscription).toHaveProperty("type");
		expect(subscription.type).toBe(SubscriptionDefaults.type);

		expect(subscription).toHaveProperty("enableConversionMetrics");
		expect(subscription.enableConversionMetrics).toBe(SubscriptionDefaults.enableConversionMetrics);

		expect(subscription).toHaveProperty("enableEngagementMetrics");
		expect(subscription.enableEngagementMetrics).toBe(SubscriptionDefaults.enableEngagementMetrics);

		expect(subscription).toHaveProperty("maxClicksMetricPerMonth");
		expect(subscription.maxClicksMetricPerMonth).toBe(SubscriptionDefaults.maxClicksMetricPerMonth);

		expect(subscription).toHaveProperty("maxImpressionsPerCycle");
		expect(subscription.maxImpressionsPerCycle).toBe(SubscriptionDefaults.maxImpressionsPerCycle);

		expect(subscription).toHaveProperty("maxInteractiveCollectionLimit");
		expect(subscription.maxInteractiveCollectionLimit).toBe(SubscriptionDefaults.maxInteractiveCollectionLimit);

		expect(subscription).toHaveProperty("maxInteractiveVideoLimit");
		expect(subscription.maxInteractiveVideoLimit).toBe(SubscriptionDefaults.maxInteractiveVideoLimit);

		expect(subscription).toHaveProperty("maxPlaysMetricPerMonth");
		expect(subscription.maxPlaysMetricPerMonth).toBe(SubscriptionDefaults.maxPlaysMetricPerMonth);

		expect(subscription).toHaveProperty("maxVideoProductLinksLimit");
		expect(subscription.maxVideoProductLinksLimit).toBe(SubscriptionDefaults.maxVideoProductLinksLimit);

		expect(subscription).toHaveProperty("trialActive");
		expect(subscription.trialActive).toBe(SubscriptionDefaults.trialActive);

		expect(subscription).toHaveProperty("hasPaymentMethod");
		expect(subscription.hasPaymentMethod).toBe(SubscriptionDefaults.hasPaymentMethod);

		expect(subscription).toHaveProperty("firstPaymentDate");
		expect(subscription.firstPaymentDate).toBe(SubscriptionDefaults.firstPaymentDate);

		expect(subscription).toHaveProperty("lastPaymentDate");
		expect(subscription.lastPaymentDate).toBe(SubscriptionDefaults.lastPaymentDate);

		expect(subscription).toHaveProperty("maxUserLimit");
		expect(subscription.maxUserLimit).toBe(SubscriptionDefaults.maxUserLimit);
	};

	const expectIAccount = (
		createUserPayload: any,
		accountData: IAccount): void => {

		expect(accountData._id).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(accountData._id.toString())).toBe(true);

		const check = createUserPayload.companyName ?
			createUserPayload.companyName :
			createUserPayload.email.split("@")[0] + "-domain";

		expect(accountData.companyName).toBe(check);
		expect(accountData.companyLogo).toBeUndefined();
		expect(accountData.companyURL).toBeUndefined();
		expect(accountData.platform).toBeUndefined();
		expect(accountData.defaultCollectionId).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(accountData.defaultCollectionId.toString())).toBe(true);
		expect(accountData.ownerUserId).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(accountData.ownerUserId.toString())).toBe(true);

		expect(accountData).toHaveProperty("subscription");
		expectISubscription(accountData.subscription);

		expect(accountData).toHaveProperty("invoices");
		expect(accountData.invoices.length).toBe(0);
	};

	const expectIShoppableCollection = (
		collectionData: IShoppableCollection): void => {

		expect(collectionData._id).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(collectionData._id.toString())).toBe(true);
		expect(collectionData.title).toBe("Default");
		expect(collectionData.accountId).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(collectionData.accountId.toString())).toBe(true);
		expect(collectionData.shoppableVideos.length).toBe(0);
		expect(collectionData.orderCount).toBe(0);
		expect(collectionData.userSessionCount).toBe(0);
		expect(collectionData.userCVR).toBe(0);
		expect(collectionData.buttonBackgroundColor).toBe("");
		expect(collectionData.buttonBackgroundBlur).toBe(true);
		expect(collectionData.iconTextColor).toBe("");
		expect(collectionData.displayFont).toBe("");
	};

	const expectIAuthentication = (
		createUserPayload: any,
		authenticationData: IAuthentication
	) : void => {
		expect(authenticationData._id).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(authenticationData._id.toString())).toBe(true);
		expect(authenticationData).toHaveProperty("data");
		expect((authenticationData.data as UserAuthenticationData).email).toBe(createUserPayload.email);
		expect((authenticationData.data as UserAuthenticationData).passwordHash).not.toBeNull();
		expect(authenticationData.method).toBe("email/password");
		expect(authenticationData.salt).not.toBeNull();
		expect(authenticationData.userId).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(authenticationData.userId.toString())).toBe(true);
		expect(authenticationData.verified).toBe(false);
		expect(authenticationData.legalAgreement).toBe(true);
		expect(authenticationData.super).toBe(false);

		expect(authenticationData.accounts).toBeTruthy();
		expect(authenticationData.accounts.length).toBe(1);

		const authenticationLinkedAccount = authenticationData.accounts[0];
		expect(authenticationLinkedAccount._id).toBeInstanceOf(mongoose.Types.ObjectId);
		expect(mongoose.Types.ObjectId.isValid(authenticationLinkedAccount._id.toString())).toBe(true);
	};
});

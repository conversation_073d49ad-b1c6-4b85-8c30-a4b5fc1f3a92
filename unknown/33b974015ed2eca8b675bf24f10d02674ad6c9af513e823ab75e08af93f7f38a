import React, {
	useState,
	useRef,
	useMemo
} from "react";
import styled from "styled-components/macro";
import {
	IVideoData,
	VideoDisplayModeEnum
} from "@player/app/app.interface";
import {
	Flex,
	Box
} from "@player/components";
import { toggleWidgetContainers } from "../core.util";
import { default as VolumeOffIcon } from "@player/assets/icon-volume-off.svg";
import { default as VolumeOnIcon } from "@player/assets/icon-volume-on.svg";
import { IconButton } from "@player/components/button/IconButton";
import { default as TimesIcon } from "@player/assets/icon-times.svg";

const WidgetContainer = styled(Box)<{hide?: boolean, isLandscape: boolean}>`
    position: fixed;
    cursor: pointer;
    bottom: 20px;
	right: ${(props): string => props.theme.widgetPosition === "right" ? "20px" : "auto"};
	left: ${(props): string => props.theme.widgetPosition === "left" ? "20px" : "auto"};
    overflow: hidden;
    border-radius: ${(props):string => props.theme.widgetBorderRadius + "px"};
    z-index: 2147483637;
	display: ${(props): string =>props.hide ? "none" : "block"};
	width: ${(props): string => (props.isLandscape ? "300px" : "150px")};
	aspect-ratio: ${(props): string => (props.isLandscape ? "16/9" : "9/16")};
`;

const Wrapper = styled(Flex)`
    flex-direction: column;
    height: 100%;
    padding: 3px;
    justify-content: space-between;
`;

interface Props {
	videoData: IVideoData[];
	handleCTAClick: (videoId: string) => void;
}

export const Widget: React.FC<Props> = ({ videoData, handleCTAClick }) => {
	const [hideWidget, setHideWidget] = useState<boolean>(false);
	const [playingAudio, setPlayingAudio] = useState<boolean>(false);
	const playerRef = useRef<HTMLVideoElement>(null);

	const handleCloseClick = (event: React.MouseEvent<HTMLDivElement>): void => {
		event.stopPropagation();
		setPlayingAudio(false);
		setHideWidget(true);
	};

	const handleAudioClick = (event: React.MouseEvent<HTMLDivElement>): void => {
		event.stopPropagation();
		setPlayingAudio((prev) => !prev);
	};

	const backgroundContent = useMemo(() => {
		return (
			<video
				ref={playerRef}
				src={videoData[0].videoURL}
				autoPlay
				muted={!playingAudio}
				loop
				playsInline
				style={{
					position: "absolute",
					objectFit: "cover",
					height: "100%",
					width: "100%",
					top: 0,
					left: 0,
					zIndex: -1
				}}
				preload="metadata"
				title={videoData[0].title}
				aria-describedby={videoData[0].description ? `video-description-${videoData[0]._id}` : undefined}
			>
				{videoData[0].description && (
					<p id={`video-description-${videoData[0]._id}`} hidden>
						{videoData[0].description}
					</p>
				)}
			</video>
		);
	}, [playingAudio, videoData]);

	return (
		<WidgetContainer
			isLandscape={videoData[0].videoDisplayMode === VideoDisplayModeEnum.LANDSCAPE}
			data-gp-core={"gp-widget-cta"}
			data-testid={"widget-container"}
			hide={hideWidget}
			onClick={(): void => {
				setPlayingAudio(false);
				handleCTAClick(videoData[0]._id);
				toggleWidgetContainers(false);
			}}
		>
			<Wrapper>
				{backgroundContent}
				<Flex justifyContent="space-between">
					<Box data-testid={"widget-icon-volume"}>
						<IconButton svgIcon={ playingAudio ? VolumeOnIcon : VolumeOffIcon} backEnabled={false}
							isPortrait={false} onClick={(e):void => handleAudioClick(e)}/>
					</Box>
					<Box data-testid={"widget-icon-times"}>
						<IconButton svgIcon={TimesIcon} backEnabled={false}
							isPortrait={false} onClick={(e):void => handleCloseClick(e)}/>

					</Box>
				</Flex>
			</Wrapper>
		</WidgetContainer>
	);
};

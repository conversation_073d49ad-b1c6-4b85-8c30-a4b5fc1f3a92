import mongoose, { Schema } from "mongoose";
import { IMetricVideoPlayTime } from "../../modules/metricVideoPlayTime/metricVideoPlayTime.interfaces";

const MetricVideoPlayTimeSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	videoId: { type: Schema.Types.ObjectId, required: true },
	playId: { type: Schema.Types.ObjectId, required: true },
	appSessionId: { type: Schema.Types.ObjectId, required: true },
	totalPlayTimeSeconds: { type: Schema.Types.Number, required: true },
	videoShare: { type: Schema.Types.ObjectId, required: false },
	collectionShare: { type: Schema.Types.ObjectId, required: false },
	videoPlayStatus: { type: Schema.Types.String, required: true, enum: ["playing", "stopped"] },
	createdAt: { type: Schema.Types.Date, required: true },
	updatedAt: { type: Schema.Types.Date, required: true }
});

export const MetricVideoPlayTimeDBModel = mongoose.model<IMetricVideoPlayTime>(
	"metric_video_play_times",
	MetricVideoPlayTimeSchema
);

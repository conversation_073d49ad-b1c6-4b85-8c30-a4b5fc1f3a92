import { ITheme } from "./theme.interface";
import { ThemeColorsEnum } from "./theme.enum";
import {
	desktopFont,
	mobileFont
} from "./fonts";
import { CollectionDataCache } from "@player/app/app.interface";

export type ThemeColorType = keyof typeof defaultTheme.colors;

export const defaultTheme: ITheme = {
	colors: {
		primary: ThemeColorsEnum.Primary,
		secondary: ThemeColorsEnum.Secondary,
		accent: ThemeColorsEnum.Accent,
		error: ThemeColorsEnum.Accent,
		transparentBlack: ThemeColorsEnum.TransparentBlack
	},
	textColor: "#FFFFFF",
	buttons: {
		backgroundColor: "#000000",
		backgroundBlur: "blur(10px)",
		variants: {
			primary: {
				color: ThemeColorsEnum.Secondary,
				main: ThemeColorsEnum.Primary,
				hover: ThemeColorsEnum.PrimaryHover,
				active: ThemeColorsEnum.PrimaryActive
			},
			secondary: {
				color: ThemeColorsEnum.Primary,
				main: ThemeColorsEnum.Secondary,
				hover: ThemeColorsEnum.SecondaryHover,
				active: ThemeColorsEnum.SecondaryActive
			}
		}
	},
	fonts: {
		family: "Readex Pro",
		desktop: desktopFont,
		mobile: mobileFont
	},
	carouselBorderRadius: 10,
	carouselIsCentered: false,
	carouselMargin: 0,
	carouselGap: 24,
	widgetBorderRadius: 10,
	widgetPosition: "right",
	inlineBorderRadius: 10
};

export const generateCustomTheme =
(allowThemes: boolean, collectionDataCache: CollectionDataCache | null): ITheme => {
	if (!allowThemes || !collectionDataCache) {
		return defaultTheme;
	}
	return mergeCustomThemeWithDefault(collectionDataCache);
};

const mergeCustomThemeWithDefault = (customTheme: CollectionDataCache): ITheme => {
	const blur = customTheme.buttonBackgroundBlur ? defaultTheme.buttons.backgroundBlur : "unset";
	const textColor = customTheme.iconTextColor || defaultTheme.textColor;
	const backgroundColor = customTheme.buttonBackgroundColor || defaultTheme.buttons.backgroundColor;
	const fontFamily = customTheme.displayFont || defaultTheme.fonts.family;

	const carouselBorderRadius = (customTheme.carouselBorderRadius !== undefined) ?
		customTheme.carouselBorderRadius : defaultTheme.carouselBorderRadius;
	const carouselIsCentered = (customTheme.carouselIsCentered !== undefined) ?
		customTheme.carouselIsCentered : defaultTheme.carouselIsCentered;
	const carouselMargin = customTheme.carouselMargin !== undefined ?
		customTheme.carouselMargin : defaultTheme.carouselMargin;
	const carouselGap = customTheme.carouselGap !== undefined ?
		customTheme.carouselGap : defaultTheme.carouselGap;
	const widgetBorderRadius = customTheme.widgetBorderRadius !== undefined ?
		customTheme.widgetBorderRadius : defaultTheme.widgetBorderRadius;
	const widgetPosition = customTheme.widgetPosition || defaultTheme.widgetPosition;
	const inlineBorderRadius = customTheme.inlineBorderRadius !== undefined ?
		customTheme.inlineBorderRadius : defaultTheme.inlineBorderRadius;

	const theme: ITheme = {
		...defaultTheme,
		textColor: textColor,
		buttons: {
			...defaultTheme.buttons,
			backgroundColor: backgroundColor,
			backgroundBlur: blur
		},
		fonts: {
			...defaultTheme.fonts,
			family: fontFamily
		},
		carouselBorderRadius,
		carouselIsCentered,
		carouselMargin,
		carouselGap,
		widgetBorderRadius,
		widgetPosition,
		inlineBorderRadius
	};
	return theme;
};

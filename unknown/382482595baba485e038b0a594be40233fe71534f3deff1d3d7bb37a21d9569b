import { InvitationCreateOneResult } from "./invitation.interfaces";
import { InvitationModel } from "./invitation.model";
import { InvitationStatus } from "./invitation.enum";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { IAccount } from "../account/account.interfaces";
import { ISignupPayload } from "../signup/signup.interfaces";
import { LocaleAPI } from "../../interfaces/apiTypes";

const expressApp = createServer();
initExpressRoutes(expressApp);

const emailAddress = "<EMAIL>";

describe("invitation.model tests", () => {
	let accessToken: string;
	let account: IAccount;

	beforeAll(async () => {
		const signupPayload: ISignupPayload = {
			email: emailAddress,
			password: "Password1!",
			firstName: "John",
			lastName: "Doe",
			companyName: "John Doe Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		expect(signUpResponse.statusCode).toBe(200);

		accessToken = signUpResponse.body.accessToken;

		const accountsResponse = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json");

		expect(accountsResponse.statusCode).toBe(200);
		expect(accountsResponse.body.accounts.length).toBe(1);

		account = accountsResponse.body.accounts[0];
	});

	it("should create an invitation document and token", async () => {
		const email = "<EMAIL>";

		const invitationModel = new InvitationModel(null);
		const invitationResult: InvitationCreateOneResult = await invitationModel.createOne(
			account._id.toString(),
			email
		);
		expect(invitationResult.document.accountId.toString()).toBe(account._id.toString());
		expect(invitationResult.document.email).toBe(email);
		expect(invitationResult.document.status).toBe(InvitationStatus.PENDING);
		expect(invitationResult.document.salt).toBeDefined();
		expect(invitationResult.document.userId).toBeUndefined();
		expect(invitationResult.token).toBeDefined();
	});
});

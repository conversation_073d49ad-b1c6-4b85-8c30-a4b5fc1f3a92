import React, { useEffect } from "react";
import { Container, Col } from "react-bootstrap";
import SignInForm from "@src/components/authentication/SignInForm";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Footer from "./Footer";
import ImageContentPanel from "@src/components/authentication/ImageContentPanel";
import { signInBackgroundImage } from "@src/assets";
import { FlexCol, FullHeightRow } from "@src/styles/components";

const SignIn: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.general.signIn;
	}, [translation.general.signIn]);

	return (
		<Container fluid>
			<FullHeightRow pl="0" pr="0">
				<Col sm="12" md={{ span: 8 }} style={{ paddingLeft: 0, paddingRight: 0 }}>
					<ImageContentPanel
						title={translation.signInPage.imgTitle}
						copy={translation.signInPage.imgCopy}
						backgroundUrl={signInBackgroundImage}
					/>
				</Col>
				<FlexCol sm="12" md={{ span: 4 }}>
					<Header auth={false} />
					<SignInForm />
					<Footer />
				</FlexCol>
			</FullHeightRow>
		</Container>
	);
};

export default SignIn;

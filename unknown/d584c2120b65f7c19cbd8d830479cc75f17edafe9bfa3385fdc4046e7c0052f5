/* eslint-disable max-lines-per-function */
import TestHelper from "../../__tests__/mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { CardPaymentMethod } from "./payment.interfaces";
import Stripe from "../../__mocks__/stripe";
import { AccountModel } from "../account/account.model";
import { IAccount } from "../account/account.interfaces";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../signup/signup.interfaces";


describe("/api/stripe/payment", () => {
	let expressApp: express.Express;
	let accessToken: string;
	let accountToken: string;
	let accountId: string;
	let stripeCustomerId: string;
	const accountModel = new AccountModel(null);
	const stripe = new Stripe("");

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const userPayload = {
			firstName: "Johnny",
			lastName: "stripepayment",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "stripepayment Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(userPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		stripeCustomerId = account.stripeCustomerId;

		accountToken = await testHelper.getAccountToken(accountId, accessToken);
	});

	it("MEGA TEST => POST (aka attach) && GET && DELETE (aka detach)", async () => {
		// Verify customer has no default payment method
		const account: IAccount = await accountModel.readOneById(accountId);
		expect(account.subscription.hasPaymentMethod).toBe(false);

		// create a payment method in stripe mock
		const paymentParams = {
			type: "card",
			card: {
				brand: "Visa",
				last4: "9090"
			}
		};
		const payment = await stripe.paymentMethods.create(paymentParams);

		// attach payment method
		const resAttach = await supertest(expressApp)
			.post("/api/stripe/payment")
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send({
				paymentMethodId: payment.id
			});

		expect(resAttach.statusCode).toBe(200);

		// Manually trigger stripe event
		const webhookPayload = {
			type: "payment_method.attached",
			data: {
				object: {
					id: payment.id,
					customer: stripeCustomerId
				}
			}
		};

		const resEvent = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayload);

		expect(resEvent.statusCode).toBe(200);
		expect(resEvent).toHaveProperty("body");

		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.hasPaymentMethod).toBe(true);
		// customer now has a default payment method

		// Get the payment that was just created and attached
		const resPayment = await supertest(expressApp)
			.get("/api/stripe/payment")
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		expect(resPayment.statusCode).toBe(200);
		expect(resPayment).toHaveProperty("body");
		expect(resPayment.body).toHaveProperty("paymentMethods");
		expect(resPayment.body.paymentMethods.length).toBe(1);

		const paymentMethod : CardPaymentMethod = resPayment.body.paymentMethods[0];
		expect(paymentMethod).toHaveProperty("id");
		expect(paymentMethod.brand).toBe(paymentParams.card.brand);
		expect(paymentMethod.last4).toBe(paymentParams.card.last4);
		expect(paymentMethod.default).toBe(true);

		const res = await supertest(expressApp)
			.delete("/api/stripe/payment/" + paymentMethod.id)
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res).toHaveProperty("body");

		// maually trigger the event payment_method.detached
		const webhookPayloadDetached = {
			type: "payment_method.detached",
			data: {
				// eslint-disable-next-line camelcase
				previous_attributes: {
					customer: stripeCustomerId
				}
			}
		};

		const resDetach = await supertest(expressApp)
			.post("/api/stripe/webhook")
			.set("content-type", "application/json")
			.set("stripe-signature", "does_not_matter_it_is_mocked")
			.accept("json")
			.send(webhookPayloadDetached);

		expect(resDetach.statusCode).toBe(200);
		expect(resDetach).toHaveProperty("body");

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.hasPaymentMethod).toBe(false);
		// customer no longer has a default payment method

		// Get the payments again to ensure it is now at 0
		const resPayment2 = await supertest(expressApp)
			.get("/api/stripe/payment")
			.set("x-api-version", "3")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		expect(resPayment2.statusCode).toBe(200);
		expect(resPayment2).toHaveProperty("body");
		expect(resPayment2.body).toHaveProperty("paymentMethods");
		expect(resPayment2.body.paymentMethods.length).toBe(0);
	});
});


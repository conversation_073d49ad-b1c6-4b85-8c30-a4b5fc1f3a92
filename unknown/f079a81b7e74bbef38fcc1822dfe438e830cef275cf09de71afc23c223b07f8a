import {
	Request,
	Response
} from "express";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import mongoose from "mongoose";
import {
	APIErrorName,
	IDBALInput,
	Permission
} from "../../interfaces/apiTypes";
import { IShoppableVideo } from "./interactiveVideo.interface";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { deleteAssetsFromCloud } from "../../services/gp/bucket.service";
import { readShoppableCollections } from "../../services/mongodb/shoppableCollection.service";
import {
	readShoppableVideos,
	deleteShoppableVideo
} from "../../services/mongodb/shoppableVideo.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import {
	getBucketFilename,
	CDN_DIR
} from "../../utils/helpers/gp.helper";

const getFilesToBeDeleted = (videoDoc: IShoppableVideo): string[] => {
	const filesToBeDeleted: string[] = [];
	videoDoc.products.forEach((p) => {
		if (p.productThumbnail) {
			filesToBeDeleted.push(p.productThumbnail);
		}
	});
	return filesToBeDeleted;
};

export const deleteVideoController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		await startDBTransaction(res.locals.session);

		const secrets: ISecrets = await getSecrets();

		// eslint-disable-next-line camelcase
		const input_video: IDBALInput = {
			query: {
				_id: req.params.videoId,
				accountId: req.accountToken.account._id
			},
			path: req.url,
			permissions: [Permission.ALL]
		};

		const videoDocs: IShoppableVideo[] = await readShoppableVideos(input_video);

		if (!videoDocs || videoDocs.length < 1) {
			throw new APIError(
				APIErrorName.E_DATABASE_FAILURE,
				`Failed to read shoppable video with id: ${req.params.videoId}`
			);
		}

		const collectionDocuments = await readShoppableCollections({
			query: {
				shoppableVideos: req.params.videoId
			},
			path: req.url,
			permissions: [Permission.ALL]
		});

		if (!collectionDocuments || collectionDocuments.length < 1) {
			throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND,
				`Failed to read collections referencing video with id: ${req.params.videoId}`);
		}

		const filesToBeDeleted = getFilesToBeDeleted(videoDocs[0]);

		const collectionModel: InteractiveCollectionModel = new InteractiveCollectionModel(res.locals.session);
		const promises = [];
		for (const collectionDocument of collectionDocuments) {
			const excludedVideoId = videoDocs[0]._id.toString();
			const videoIds = collectionDocument.shoppableVideos
				.filter((id: { toString: () => string; }) => id.toString() !== excludedVideoId)
				.map((id: { toString: () => string; }) => new mongoose.Types.ObjectId(id.toString()));

			const promise = collectionModel.updateOneById(
				collectionDocument._id.toString(), { shoppableVideos: videoIds });
			promises.push(promise);
		}

		await Promise.all(promises);

		const inputDelVideo: IDBALInput = {
			query: { _id: videoDocs[0]._id },
			path: req.url,
			permissions: [Permission.ALL]
		};
		await deleteShoppableVideo(inputDelVideo);

		const filenamesToBeDeleted = getBucketFilename(
			filesToBeDeleted,
			secrets.cdn.host
		);
		if (filenamesToBeDeleted.length > 0) {
			await deleteAssetsFromCloud(filenamesToBeDeleted);
		}

		const filenameToBeDeleted =
		`${CDN_DIR.DATACACHE}videos/${videoDocs[0]._id}-video.json`;
		const fileDeleted = await deleteAssetsFromCloud([filenameToBeDeleted]);
		if (!fileDeleted) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Failed to delete Video DataCache file."
			);
		}

		await completeDBTransaction(res.locals.session);

		res.status(200);
		return res.send({});

	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

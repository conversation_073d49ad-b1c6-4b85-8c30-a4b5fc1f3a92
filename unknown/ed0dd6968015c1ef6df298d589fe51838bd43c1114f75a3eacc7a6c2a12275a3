import { useContext } from "react";
import LocalizedStrings from "react-localization";
import localization from "@src/localization";
import { LanguageContext } from "../contexts/LanguageContext";

const useTranslation = () => {
	const { language } = useContext(LanguageContext);
	const translation = new LocalizedStrings(localization);

	translation.setLanguage(language);
	return translation;
};

const FormatString = (str: string, ...val: string[]) => {
	for (let index = 0; index < val.length; index++) {
		str = str.replace(`{${index}}`, val[index]);
	}
	return str;
};

export { useTranslation, FormatString };

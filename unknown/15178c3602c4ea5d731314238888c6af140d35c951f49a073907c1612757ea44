import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { IAccount } from "../../modules/account/account.interfaces";
import { AccountModel } from "../../modules/account/account.model";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IShoppableVideo } from "../../modules/interactiveVideo/interactiveVideo.interface";
import { ISignupPayload } from "src/modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { APIError } from "../../utils/helpers/apiError";


describe("GET /shoppable-video", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.video.s.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_SERVICE_FAILED]. return 500", async () => {
		jest.spyOn(InteractiveVideoModel.prototype, "readManyByAccountId").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "does not matter")
		);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("[200]. return 200", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(200);

		const interactiveVideos: IShoppableVideo[] = res.body.shoppableVideos;
		const videoMatch = interactiveVideos.find(iv => iv._id.toString() == newShoppableVideo._id.toString());

		expect(videoMatch?._id.toString()).toBe(newShoppableVideo._id.toString());
	});

	it("[200]. return 200 when passing query params.", async () => {
		const limit = 2;

		const newVideo1 = await testHelper.createVideo(account._id.toString());
		await testHelper.createShoppableVideo(
			newVideo1._id.toString(),
			accountToken,
			accessToken);

		const newVideo2 = await testHelper.createVideo(account._id.toString());
		await testHelper.createShoppableVideo(
			newVideo2._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.get(`/api/shoppable-videos?limit=${limit}&sortKey=videoScore&SortBy=dsc`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(200);
		const interactiveVideos: IShoppableVideo[] = res.body.shoppableVideos;

		expect(interactiveVideos.length).toBe(limit);
	});

	it("[400]. return 400 when passing invalid query params.", async () => {
		const badLimit = "Five";

		const newVideo = await testHelper.createVideo(account._id.toString());
		await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.get(`/api/shoppable-videos?limit=${badLimit}&sortKey=videoScore&SortBy=dsc`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[200]. return 200 for x-api-version >=3 enableEngagementMetrics==false", async () => {
		const enableEngagementMetrics = false;

		const accountModel = new AccountModel(null);
		const updateAccount = {
			"subscription.enableEngagementMetrics": enableEngagementMetrics
		};
		const updatedAccount = await accountModel.updateOneById(account._id.toString(), updateAccount);
		expect(updatedAccount.subscription.enableEngagementMetrics).toBe(enableEngagementMetrics);

		const newVideo = await testHelper.createVideo(account._id.toString());
		const newInteractiveVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const interactiveVideoModel = new InteractiveVideoModel(null);
		const updateInteractiveVideo = {
			videoScore: 5
		} as any;
		const updatedInteractiveVideo = await interactiveVideoModel.updateOneById(
			newInteractiveVideo._id.toString(),
			updateInteractiveVideo);

		expect(updatedInteractiveVideo.videoScore).toBeGreaterThan(0);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("documents");
		expect(res.body).toHaveProperty("totalDocuments");

		const interactiveVideos: IShoppableVideo[] = res.body.documents;
		expect(interactiveVideos.length).toBeGreaterThan(0);

		const someVideosAreNotRedacted = interactiveVideos.some(iv =>
			iv.playPercentCount20 > 0 ||
			iv.playPercentCount40 > 0 ||
			iv.playPercentCount60 > 0 ||
			iv.playPercentCount80 > 0 ||
			iv.playPercentCount100 > 0 ||
			iv.videoScore > 0
		);

		expect(someVideosAreNotRedacted).toBe(false);
	});

	it("[200]. return 200 for x-api-version >=3 enableEngagementMetrics==true", async () => {
		const enableEngagementMetrics = true;

		const accountModel = new AccountModel(null);
		const updateAccount = {
			"subscription.enableEngagementMetrics": enableEngagementMetrics
		};
		const updatedAccount = await accountModel.updateOneById(account._id.toString(), updateAccount);
		expect(updatedAccount.subscription.enableEngagementMetrics).toBe(enableEngagementMetrics);

		const newVideo = await testHelper.createVideo(account._id.toString());
		const newInteractiveVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const interactiveVideoModel = new InteractiveVideoModel(null);
		const updateInteractiveVideo = {
			videoScore: 5
		} as any;
		const updatedInteractiveVideo = await interactiveVideoModel.updateOneById(
			newInteractiveVideo._id.toString(),
			updateInteractiveVideo);

		expect(updatedInteractiveVideo.videoScore).toBeGreaterThan(0);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("documents");
		expect(res.body).toHaveProperty("totalDocuments");

		const interactiveVideos: IShoppableVideo[] = res.body.documents;
		expect(interactiveVideos.length).toBeGreaterThan(0);

		const someVideosAreNotRedacted = interactiveVideos.some(iv =>
			iv.playPercentCount20 > 0 ||
			iv.playPercentCount40 > 0 ||
			iv.playPercentCount60 > 0 ||
			iv.playPercentCount80 > 0 ||
			iv.playPercentCount100 > 0 ||
			iv.videoScore > 0
		);

		expect(someVideosAreNotRedacted).toBe(true);
	});
});

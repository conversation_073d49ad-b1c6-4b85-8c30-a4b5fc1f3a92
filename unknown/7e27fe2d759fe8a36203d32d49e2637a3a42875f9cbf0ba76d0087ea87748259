import Stripe from "stripe";
import { getSecrets } from "../../secrets/secrets.model";

export class StripeProductsModel {
	constructor(public products: Stripe.Product[] = []) {}

	public async getProducts (): Promise<Stripe.Product[]> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripeProducts = await stripe.products.list({
			active: true
		});

		stripeProducts.data = this.filterProducts(stripeProducts.data);
		this.products = stripeProducts.data;
		return this.products;
	}

	private filterProducts (stripeProducts: Stripe.Product[]): Stripe.Product[] {
		return stripeProducts.filter((product) => {
			return product.metadata.gp?.toLocaleLowerCase().trim() === "true";
		});
	}
}

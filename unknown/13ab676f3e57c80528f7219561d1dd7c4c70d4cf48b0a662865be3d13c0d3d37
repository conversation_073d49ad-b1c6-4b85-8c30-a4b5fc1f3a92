export const formatVideoName = (path?: string) => {
	if (!path) return "";

	const segments = path.split("/");
	const filename = segments[segments.length - 1];
	const formattedName = filename.replace(/_[a-f0-9]{24}/, "");
	const finalName = formattedName.replace(/%2B/g, "+").replace(/%_/g, "%").replace(/%/g, "%25");

	try {
		return decodeURIComponent(finalName);
	} catch (error) {
		console.error("Error decoding URI component:", error);
		return finalName;
	}
};

export const removeParamFromURL = (param: string): void => {
	const newUrl = removeQueryParam(window.location.href, param);
	window.history.replaceState({}, document.title, newUrl);
};

const removeQueryParam = (url: string, param: string): string => {
	const urlObject = new URL(url);
	urlObject.searchParams.delete(param);
	return urlObject.href;
};

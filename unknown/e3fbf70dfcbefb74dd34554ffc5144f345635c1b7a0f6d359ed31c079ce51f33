import Joi from "joi";
import { IPostMetricConversionPayload } from "../modules/metricConversion/metricConversion.interfaces";
import { APIErrorName } from "../interfaces/apiTypes";
import { IGetMetricPayload } from "../modules/metrics/metrics.interface";

export const getMetricsSchema = {
	data: Joi.object<IGetMetricPayload>({
		startDate: Joi.date().iso().required(),
		endDate: Joi.date().iso().required()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"date.format": APIErrorName.E_INVALID_INPUT
	})
};

export const postMetricConversionSchema = {
	data: Joi.object<IPostMetricConversionPayload>({
		accountId: Joi.string().hex().length(24).required(),
		userSessionId: Joi.string().hex().length(24).required(),
		orderItemsCount: Joi.number().integer().required(),
		collectionIds: Joi.array().items(
			Joi.string().hex().length(24).required()
		).min(1).required().messages({
			"array.base": APIErrorName.E_INVALID_INPUT,
			"array.min": APIErrorName.E_INVALID_INPUT,
			"string.hex": APIErrorName.E_INVALID_INPUT,
			"string.length": APIErrorName.E_INVALID_INPUT
		})
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"array.includesRequiredUnknowns": APIErrorName.E_INVALID_INPUT
	})
};



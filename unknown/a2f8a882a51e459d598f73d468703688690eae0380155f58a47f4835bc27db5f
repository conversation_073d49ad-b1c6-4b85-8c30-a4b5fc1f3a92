import React, { useState } from "react";
import { useTranslation } from "../hooks/translations";
import ListItem from "./ListItem";
import { Flex, ListBox, HeaderListTitle } from "@src/styles/components";
import { shoppableVideo } from "@src/types/videos";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";
import { Account } from "../../types/account";

interface Props {
	videos: shoppableVideo[];
	setVideos: (value: shoppableVideo[]) => void;
	refreshList: () => void;
	showConfirmationText: (value: string) => void;
	showDeleteError: (value: string) => void;
	updateList: (videos: shoppableVideo[]) => void;
	accountData: Account | undefined;
}

const ShoppableVideos: React.FC<Props> = ({ videos, setVideos, refreshList, showConfirmationText, showDeleteError, updateList, accountData }) => {
	const translation = useTranslation();
	const [isDropDisabled, setIsDropDisabled] = useState(false);

	const handleOnDragEnd = (result: DropResult) => {
		if (!result.destination) {
			return;
		}

		const videosCopy = [...videos];
		const [reorderedItem] = videosCopy.splice(result.source.index, 1);
		videosCopy.splice(result.destination.index, 0, reorderedItem);

		setVideos(videosCopy);
		updateList(videosCopy);
	};

	return (
		<DragDropContext onDragEnd={handleOnDragEnd}>
			<div className="mt-4">&nbsp;</div>
			<Flex className="d-none d-lg-flex">
				<ListBox style={{ width: "2rem" }}>
					<HeaderListTitle data-testid="videoListGridBox" />
				</ListBox>
				<ListBox style={{ width: "30%" }}>
					<HeaderListTitle style={{ padding: "15px" }} data-testid="videoListTitle">
						{translation.dashboardPage.title}
					</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "10%" }}>
					<HeaderListTitle data-testid="videoListProducts">{translation.dashboardPage.products}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ minWidth: "12.5rem" }}>
					<HeaderListTitle data-testid="videoListDate">{translation.dashboardPage.dateCreated}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "10%", paddingLeft: "2px" }}>
					<HeaderListTitle style={{ paddingLeft: "0" }} data-testid="videoListPlays">
						{translation.dashboardPage.plays}
					</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "10%", paddingLeft: "2px" }}>
					<HeaderListTitle style={{ paddingLeft: "0" }} data-testid="videoListClicks">
						{translation.dashboardPage.clicks}
					</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "10%", paddingLeft: "2px" }}>
					<HeaderListTitle style={{ paddingLeft: "0" }} data-testid="videoListLikes">
						{translation.dashboardPage.likes}
					</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "10%" }}>
					{accountData?.subscription.enableEngagementMetrics && (
						<HeaderListTitle style={{ paddingLeft: "0" }} data-testid="videoListScore">
							{translation.dashboardPage.score}
						</HeaderListTitle>
					)}
				</ListBox>
				<ListBox last={true} style={{ width: "3.5rem" }}>
					<HeaderListTitle data-testid="videoListActions" />
				</ListBox>
			</Flex>
			<Droppable droppableId="videos">
				{(provided) => (
					<div {...provided.droppableProps} ref={provided.innerRef}>
						{videos.map((video: shoppableVideo, index: number) => (
							<Draggable key={video._id} draggableId={video._id} isDragDisabled={isDropDisabled} index={index}>
								{(provided) => (
									<div {...provided.draggableProps} {...provided.dragHandleProps} ref={provided.innerRef} className="mb-4">
										<ListItem numberId={index} key={video._id} videoItem={video} refreshList={refreshList} setIsDropDisabled={setIsDropDisabled} showConfirmationText={showConfirmationText} showDeleteError={showDeleteError} accountData={accountData} />
									</div>
								)}
							</Draggable>
						))}
						{provided.placeholder}
					</div>
				)}
			</Droppable>
		</DragDropContext>
	);
};

export default ShoppableVideos;

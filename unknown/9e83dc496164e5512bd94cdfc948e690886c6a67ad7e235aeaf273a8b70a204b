import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { UserModel } from "../modules/user/user.model";
import jwt from "jsonwebtoken";
import { IAccessToken } from "../modules/accessToken/accessToken.interface";
import { APIErrorName } from "../interfaces/apiTypes";

const expressApp = createServer();
initExpressRoutes(expressApp);

describe("GET api/users/:userId", () => {
	let accessToken1: string;
	let accessTokenData1: IAccessToken;
	let accessToken2: string;
	let accessTokenData2: IAccessToken;

	beforeAll(async () => {
		const signupRes1 = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: "<EMAIL>",
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (signupRes1.statusCode !== 200) {
			console.error(signupRes1.body);
		}

		expect(signupRes1.statusCode).toBe(200);
		expect(signupRes1.body).toHaveProperty("accessToken");

		accessToken1 = signupRes1.body.accessToken;

		// decode the access token to get the user id
		accessTokenData1 = jwt.decode(accessToken1) as IAccessToken;

		const signupRes2 = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: "<EMAIL>",
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (signupRes2.statusCode !== 200) {
			console.error(signupRes2.body);
		}

		expect(signupRes2.statusCode).toBe(200);
		expect(signupRes2.body).toHaveProperty("accessToken");

		accessToken2 = signupRes2.body.accessToken;

		// decode the access token to get the user id
		accessTokenData2 = jwt.decode(accessToken2) as IAccessToken;
	});

	it("Should return 401 [E_INVALID_AUTHORIZATION] when invalid userId in the authentication token", async () => {
		const res = await supertest(expressApp)
			.get(`/api/users/${accessTokenData1.userId}`)
			.set("Authorization", "Bearer 123")
			.set("x-api-version", "3");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("Should return 403 [E_REQUEST_FORBIDDEN] when userId in parameter does not match userId in access token",
		async () => {
			const res = await supertest(expressApp)
				.get(`/api/users/${accessTokenData2.userId}`)
				.set("Authorization", `Bearer ${accessToken1}`)
				.set("x-api-version", "3");

			expect(res.statusCode).toBe(403);
			expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
		});

	it("Should return 200 with document result", async () => {
		const res = await supertest(expressApp)
			.get(`/api/users/${accessTokenData1.userId}`)
			.set("Authorization", `Bearer ${accessToken1}`)
			.set("x-api-version", "3");

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("user");
		expect(res.body.user).toHaveProperty("_id");
		expect(res.body.user).toHaveProperty("firstName");
		expect(res.body.user).toHaveProperty("lastName");
		expect(res.body.user).toHaveProperty("email");
		expect(res.body.user).toHaveProperty("createdAt");
		expect(res.body.user).toHaveProperty("updatedAt");
		expect(res.body.user._id).toBe(accessTokenData1.userId);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the user document does not exist", async () => {
		const resSignup = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "3")
			.send({
				email: "<EMAIL>",
				callbackEndpoint: "https://domain.tld/callback"
			});

		if (resSignup.statusCode !== 200) {
			console.error(resSignup.body);
		}

		expect(resSignup.statusCode).toBe(200);
		const accessToken = resSignup.body.accessToken;
		const decodedToken = jwt.decode(accessToken) as IAccessToken;

		const userModel = new UserModel(null);
		await userModel.deleteOneById(decodedToken.userId);

		const res = await supertest(expressApp)
			.get(`/api/users/${decodedToken.userId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});
});


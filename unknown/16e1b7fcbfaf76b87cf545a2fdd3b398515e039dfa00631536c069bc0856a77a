import React, { useEffect, useState } from "react";
import {
	<PERSON><PERSON>,
	H2,
	<PERSON><PERSON>utton,
	OriginalPrice,
	PlanOption,
	PlansPageDescription,
	PlansPageUl,
	SelectionInfo,
	SelectPlanContainer
} from "../../styles/components";
import { useTranslation } from "../hooks/translations";
import { daysBetweenTimestampAndNow } from "../../utils/time";
import { Account } from "../../types/account";
import { Product, ProductTypeEnum } from "../../types/product";
import usePlansPricing from "../hooks/usePlansPricing";

interface PlansPricingBasicProps {
	accountData: Account | undefined;
	planID: string;
	priceId: string;
	setPlanID: (value: string) => void;
	price: string;
	setPrice: (value: string) => void;
	pendingChangeDate: number | null | undefined;
	trialAvailable: boolean;
	clockTime: number | undefined;
	changePlan: (
		priceId: string,
		trialAvailable: boolean,
		productType: ProductTypeEnum,
		subscriptionId?: string
	) => Promise<void>;
	product: Product | null;
}

export const PlansPricingBasic: React.FC<PlansPricingBasicProps> = (props: PlansPricingBasicProps) => {
	const translation = useTranslation();
	const { formatPrice, sortPrices } = usePlansPricing();
	const [current, setCurrent] = useState(false);

	useEffect(() => {
		if (props.planID === props.priceId) {
			setCurrent(true);
		} else {
			setCurrent(false);
		}
	}, [props.planID, props.priceId]);

	return (
		<>
			<PlanOption upgrade={true}>
				<Flex style={{ justifyContent: "space-between", marginBottom: "0.8rem" }}>
					<H2>{translation.general.basic}</H2>
					{current && <SelectionInfo selected={true}>{translation.plansPage.currentPlan}</SelectionInfo>}
				</Flex>
				<PlansPageDescription>{translation.plansPage.freePlanDescription}</PlansPageDescription>
				<SelectPlanContainer className="mt-4">
					<select
						id="basicPlan"
						onChange={(e) => {
							props.setPlanID(e.target.value);
							const selectedPrice = props.product?.prices.find((price) => price.id === e.target.value);
							if (selectedPrice) {
								props.setPrice(formatPrice(selectedPrice));
							}
						}}
						value={props.planID}
					>
						{props.product &&
							sortPrices(props.product.prices).map((price, index) => (
								<option key={index} value={price.id}>
									{price.name}
								</option>
							))}
					</select>
				</SelectPlanContainer>
				<div className="mt-4">
					<OriginalPrice strikethrough={false}>{props.price}</OriginalPrice>
				</div>
				<div className="mt-4">
					<b>{translation.plansPage.apProIncludes}</b>
				</div>
				<PlansPageUl>
					<li>{translation.plansPage.adFreeVideoPlayer}</li>
					<li>{translation.plansPage.videoLinkEmbed}</li>
					<li>{translation.plansPage.standardVideoDisplayOptions}</li>
					<li>{translation.plansPage.basicPerformanceMetrics}</li>
				</PlansPageUl>
				<div style={{ flexGrow: 1 }}></div>

				{props.planID === props.priceId ? (
					<MainButton style={{ width: "100%" }} disabled={true}>
						{translation.plansPage.currentPlan}
					</MainButton>
				) : props.pendingChangeDate && props.planID === props.accountData?.subscription?.pendingChangePriceId ? (
					<MainButton style={{ width: "100%" }} disabled={true}>
						{translation.plansPage.proExpiryText1}
						{" " + daysBetweenTimestampAndNow(props.pendingChangeDate, props.clockTime) + " "}
						{translation.plansPage.proExpiryText2}
					</MainButton>
				) : (
					<MainButton
						style={{ width: "100%" }}
						onClick={() => {
							props.changePlan(props.planID, props.trialAvailable, ProductTypeEnum.BASIC);
						}}
					>
						{translation.general.select}
					</MainButton>
				)}
			</PlanOption>
		</>
	);
};

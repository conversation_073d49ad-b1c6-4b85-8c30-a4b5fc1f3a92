import jwt, { JwtPayload } from "jsonwebtoken";
import { IAccessToken } from "./accessToken.interface";
import { AuthenticationModel } from "../authentication/authentication.model";
import { getSecrets } from "../secrets/secrets.model";

export class AccessTokenModel {
	public static async verifyAndDecode (accessToken: string): Promise<IAccessToken> {
		const saltedHash = await this.createSaltedHash(accessToken);
		const decodedJWT = jwt.verify(accessToken, saltedHash);

		if (!decodedJWT) {
			throw new Error("Failed to verify access token");
		}

		return decodedJWT as IAccessToken;
	}

	private static async createSaltedHash (accessToken: string): Promise<string> {
		const decodedAuthentication = jwt.decode(accessToken) as JwtPayload;

		if (!decodedAuthentication?.authenticationId) {
			throw new Error("Failed to retrieve authenticationId from provided access token");
		}

		const authenticationId = decodedAuthentication.authenticationId;
		const authModel = new AuthenticationModel(null);
		const authDocument = await authModel.readOneById(authenticationId);

		const secrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		const saltedHash = `${gpSecretKey}${authDocument.salt}`;
		return saltedHash;
	}
}

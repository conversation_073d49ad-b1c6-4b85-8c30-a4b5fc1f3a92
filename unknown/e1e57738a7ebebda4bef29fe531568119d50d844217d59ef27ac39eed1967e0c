import mongoose, {
	ObjectId,
	Document,
	Schema
} from "mongoose";

export interface MetricPhonePress extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	videoId: ObjectId;
}

const MetricPhonePressSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	videoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricPhonePressDBModel = mongoose.model<MetricPhonePress>(
	"metric_phone_press",
	MetricPhonePressSchema,
	"metric_phone_press"
);

import mongoose, { Schema } from "mongoose";
import { <PERSON><PERSON><PERSON> } from "./apiKey.interfaces";

const KeySchema: Schema = new Schema({
	accountId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	authenticationId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	keyLast4: {
		type: String,
		required: true
	},
	createdAt: {
		type: Date,
		default: new Date().toISOString(),
		required: true
	},
	createdBy: {
		type: Schema.Types.ObjectId,
		required: false
	},
	meta: {
		type: Map,
		of: String,
		required: false
	}
});

export const APIKeyDBModel = mongoose.model<IKey>(
	"Keys",
	KeySchema
);

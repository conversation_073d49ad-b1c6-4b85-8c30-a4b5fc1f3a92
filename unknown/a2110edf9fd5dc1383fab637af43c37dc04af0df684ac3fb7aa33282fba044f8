import { APIErrorName } from "../../interfaces/apiTypes";
import path from "path";
import stream, { Readable } from "stream";
import { promisify } from "util";
import fs from "fs";
import {
	gpLog,
	LogScope
} from "../managers/gpLog.manager";
import { APIError } from "./apiError";
import { BucketModel } from "../../modules/bucket/bucket.model";
import os from "os";
const logTrace = path.basename(__filename);

export interface IFileInfo {
	filePath: string;
	fileName: string;
	fileExt: string;
}

export const saveFileURL = async (fileURL: string, suffix: string): Promise<IFileInfo> => {
	try {
		const response = await fetch(fileURL);

		if (!response.ok) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`unexpected response ${response.statusText}`
			);
		}

		if (!response.body) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"response body is undefined"
			);
		}

		const fileContentType = response.headers.get("content-type");
		const filename = fileWithMimeExtension(fileURL, fileContentType);

		const finalFilename = getTempOutputFilename(filename, suffix);
		const tempDirectory = os.tmpdir();
		const finalFilePath = `${tempDirectory}/${finalFilename}`;

		await saveTempFile(finalFilePath, response.body);

		const iFileInfo: IFileInfo = {
			filePath: finalFilePath,
			fileName: finalFilename,
			fileExt: path.extname(finalFilename)
		};

		return iFileInfo;
	} catch (error: unknown) {
		gpLog({
			message: `Failed to save file from URL: ${fileURL}`,
			objData: { error: error },
			trace: `${logTrace} | saveFileURL`,
			scope: LogScope.ERROR
		});
		throw error;
	}
};

// eslint-disable-next-line no-undef
async function saveTempFile (filePath: string, data: ReadableStream<Uint8Array>): Promise<void> {
	const nodeReadable = readableStreamToNodeReadable(data);
	const fileStream = fs.createWriteStream(filePath);
	const pipeline = promisify(stream.pipeline);
	await pipeline(nodeReadable, fileStream);
}

function getTempOutputFilename (filename: string, suffix: string): string {
	const baseFilename = path.basename(filename);
	const decodedFilename = decodeURIComponent(baseFilename);
	const finalFilename = BucketModel.sanitizeFileName(decodedFilename, suffix);
	return finalFilename;
}

function deriveExtensionFromMimeType (mimeType: string): string {
	const parts = mimeType.split("/");
	return parts.length > 1 ? "." + parts[1] : "";
}

function fileWithMimeExtension(fileURL: string, contentType: string | null): string {
	if (!contentType) {
		return fileURL;
	}

	const mimeExtension = deriveExtensionFromMimeType(contentType);

	if (!mimeExtension) {
		return fileURL;
	}

	const parsedURL = new URL(fileURL);
	const baseName = path.basename(parsedURL.pathname, path.extname(parsedURL.pathname));

	return `${baseName}${mimeExtension}`;
}

// eslint-disable-next-line no-undef
function readableStreamToNodeReadable(readableStream: ReadableStream<Uint8Array>): Readable {
	const reader = readableStream.getReader();

	return new Readable({
		async read(): Promise<void> {
			try {
				const { done, value } = await reader.read();
				if (done) {
					this.push(null);
				} else {
					this.push(Buffer.from(value));
				}
			} catch (error: unknown) {
				if (error instanceof Error) {
					this.destroy(error);
				} else {
					this.destroy(new Error(String(error)));
				}
			}
		}
	});
}

export const removeLastObjectIdFromFileName = (
	inputStr: string,
	delimiter = "_"
): string => {
	try {
		const substrings = inputStr.split(delimiter);
		// Check if the last substring matches the pattern of a MongoDB ObjectId
		if (/^[a-fA-F0-9]{24}$/.test(substrings[substrings.length - 1])) {
			substrings.pop();
			return substrings.join(delimiter);
		}
		return inputStr;
	} catch (err) {
		gpLog({
			message: `Failed to remove last objectId from fileName: ${inputStr}`,
			objData: { error: err },
			trace: `${logTrace} | removeLastSubStringFromFileName`,
			scope: LogScope.ERROR
		});
		return inputStr;
	}
};

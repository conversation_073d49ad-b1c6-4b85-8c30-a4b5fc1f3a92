import {
	type Request,
	type Response
} from "express";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../services/mongodb/transaction.service";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import Joi from "joi";
import multer from "multer";
import { VersionJoi } from "../base/base.joi";
import { OIDCAuthenticationService } from "./oidc.authentication.service";

interface OIDCPostPayload {
	apiVersion: number;
	oidcIdToken: string;
	oidcAccessToken: string;
	oidcClientId: string;
	inviteToken?: string;
}

const OIDCPostPayloadSchema = Joi.object<OIDCPostPayload>({
	apiVersion: VersionJoi.required(),
	oidcIdToken: Joi.string().required(),
	oidcAccessToken: Joi.string().required(),
	oidcClientId: Joi.string().required(),
	inviteToken: Joi.string().optional()
});
export class OIDCController extends Controller {
	constructor() {
		super();

		this.router.post("/", [multer().none()], this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: OIDCPostPayload;
		try {
			validPayload = await OIDCPostPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				oidcIdToken: request.body.oidcIdToken,
				oidcAccessToken: request.body.oidcAccessToken,
				oidcClientId: request.body.oidcClientId,
				inviteToken: request.body.inviteToken
			});
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await startDBTransaction(response.locals.session);

			const oidcAuthService = new OIDCAuthenticationService(response.locals.session);
			const result = await oidcAuthService.authenticateWithOIDC(
				validPayload.oidcClientId,
				validPayload.oidcAccessToken,
				validPayload.oidcIdToken,
				validPayload.inviteToken
			);

			await completeDBTransaction(response.locals.session);
			return response.status(result.statusCode).json({
				accessToken: result.accessToken
			});

		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.sessionn);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}



}

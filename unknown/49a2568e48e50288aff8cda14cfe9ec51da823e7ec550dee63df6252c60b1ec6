import React from "react";
import styled from "styled-components/macro";
import { Box } from "./Box";

const Wrapper = styled(Box)`
  display: inline-flex;
  align-items: baseline;
`;

const PoweredBy = styled.span`
  font-size: 7px;
  font-weight: 300;
  opacity: 0.6;
  color: white;
  letter-spacing: 2px;
`;

const CompanyName = styled.span`
  font-size: 14px;
  font-weight: bold;
  margin-left: 4px;
  color: white;
`;

const Trademark = styled.sup`
  font-size: 5px;
  margin-left: 2px;
  color: white;
`;

export const TradeMark: React.FC = () => (
	<Wrapper>
		<PoweredBy></PoweredBy>
		<CompanyName></CompanyName>
		<Trademark></Trademark>
	</Wrapper>
);

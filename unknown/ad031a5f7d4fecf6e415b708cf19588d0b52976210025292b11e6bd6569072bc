import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";

export const fetchProductData = async (url: string): Promise<any> => {
	const secrets: ISecrets = await getSecrets();
	const iframelyHost = secrets.iframely.host;
	const iframelyApiKey = secrets.iframely.apiKey;

	const response = await fetch(
		`${iframelyHost}/api/iframely?url=${encodeURIComponent(
			url
		)}&api_key=${iframelyApiKey}`
	);

	if (!response.ok) {
		throw new Error(`HTTP error! status: ${response.status}`);
	}

	const data = await response.json();
	return data;
};

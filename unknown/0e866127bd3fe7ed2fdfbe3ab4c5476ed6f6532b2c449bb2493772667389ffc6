import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { UserModel } from "../modules/user/user.model";
import { InteractiveCollectionModel } from "../modules/interactiveCollection/interactiveCollection.model";
import { IShoppableCollection } from "../modules/interactiveCollection/interactiveCollection.interface";
import { ModelResponse } from "../modules/modelResponse/modelResponse.model";
import { APIErrorName } from "../interfaces/apiTypes";
import { APIError } from "../utils/helpers/apiError";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { AccountModel } from "../modules/account/account.model";
import { randomBytes } from "crypto";


describe("POST /auth/sign-up/email failure mode tests", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	it("Should return 400 [E_SCHEMA_VALIDATION_ERROR] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.send({});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("Should return 400 [E_SCHEMA_VALIDATION_ERROR] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "0")
			.send({});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "x")
			.send({});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create a user", async () => {
		jest.spyOn(UserModel.prototype, "createOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to create user.")
		);

		const unique = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "2")
			.send({
				email: `${unique}@domain.tld`,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create an account", async () => {
		jest.spyOn(AccountModel.prototype, "create").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create account")
		);

		const unique = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "2")
			.send({
				email: `${unique}@domain.tld`,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create a shoppable collection", async () => {
		jest.spyOn(InteractiveCollectionModel.prototype, "createOne").mockResolvedValueOnce(
			new ModelResponse<IShoppableCollection>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create collection.")
			)
		);

		const unique = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "2")
			.send({
				email: `${unique}@domain.tld`,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 409 [E_AUTH_EXISTS_DUPLICIATE] if an authentication document already exists", async () => {
		jest.spyOn(AuthenticationModel.prototype, "exists").mockResolvedValueOnce(true);

		const unique = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/auth/sign-up/email")
			.set("x-api-version", "2")
			.send({
				email: `${unique}@domain.tld`,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(409);
		expect(res.body.error).toEqual(APIErrorName.E_AUTH_EXISTS_DUPLICIATE);
	});
});

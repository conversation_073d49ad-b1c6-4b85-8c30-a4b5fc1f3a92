import React from "react";
import { ThemeProvider } from "styled-components";
import { createRoot } from "react-dom/client";
import { startPlayerApp } from "@player/core/core.util";
import { Player } from "@player/core/components/Player";
import { Onsite } from "@player/core/components/Onsite";
import { Widget } from "@player/core/components/Widget";
import { Carousel } from "@player/core/components/Carousel";
import {
	IVideoData,
	SnippetTypeEnum
} from "@player/app";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";
import { ITheme } from "@player/theme/theme.interface";

interface IRenderSnippet {
	accountId: string;
	collectionId?: string;
	videoId?: string;
	element: HTMLElement;
	dataGpType: SnippetTypeEnum;
	appUrl: string;
	videoData: IVideoData[];
	theme: ITheme;
}
export const renderSnippet = (input: IRenderSnippet): void => {
	const { accountId, collectionId, videoId, element, dataGpType, appUrl, videoData, theme } = input;
	if (
		dataGpType === SnippetTypeEnum.WIDGET &&
        element.parentNode &&
        element.parentNode !== document.body
	) {
		element.parentNode.removeChild(element);
		document.body.appendChild(element);
	}
	const root = createRoot(element);
	sendAppEvent({
		eventName: EventNameEnum.SNIPPET_IMPRESSION,
		accountId,
		collectionId,
		videoId,
		snippet: { type: dataGpType }
	});

	const handleCTAClick = (videoId: string): void => {
		const url = new URL(appUrl);
		url.searchParams.delete("videoId");
		url.searchParams.append("videoId", videoId);
		const appUrlWithVideoId = url.toString();
		sendAppEvent({
			eventName: EventNameEnum.THUMBNAIL_CTA_PRESS,
			videoId: videoId,
			accountId,
			collectionId,
			snippet: { type: dataGpType }
		});
		startPlayerApp(appUrlWithVideoId);
	};

	const snippetComponent: Record<SnippetTypeEnum, () => void> = {
		[SnippetTypeEnum.CAROUSEL]: () => {
			collectionId && root.render(
				<ThemeProvider theme={theme}>
					<Carousel
						videoData={videoData}
						handleCTAClick={handleCTAClick}
						collectionId={collectionId} />
				</ThemeProvider>
			);
		},
		[SnippetTypeEnum.WIDGET]: () => {
			root.render(
				<ThemeProvider theme={theme}>
					<Widget
						videoData={videoData}
						handleCTAClick={handleCTAClick} />
				</ThemeProvider>
			);
		},
		[SnippetTypeEnum.ONSITE]: () => {
			if (!collectionId){
				console.error("Missing collectionId for onsite snippet");
				return;
			}
			const url = new URL(appUrl);
			url.searchParams.delete("videoId");
			url.searchParams.append("videoId", videoData[0]._id);
			url.searchParams.append("snippetType", SnippetTypeEnum.ONSITE);
			const onSiteAppUrl = url.toString();
			root.render(
				<ThemeProvider theme={theme}>
					<Onsite
						appUrl={onSiteAppUrl}
						videoData={videoData}
						collectionId={collectionId} />
				</ThemeProvider>
			);
		},
		[SnippetTypeEnum.PLAYER]: () => {
			const url = new URL(appUrl);
			url.searchParams.delete("collectionId");
			url.searchParams.append("snippetType", SnippetTypeEnum.PLAYER);
			const playerAppUrl = url.toString();
			const videoWidth = element.getAttribute("data-video-width") || "338";
			const videoHeight = element.getAttribute("data-video-height") || "600";
			root.render(
				<ThemeProvider theme={theme}>
					<Player
						appUrl={playerAppUrl}
						videoData={videoData[0]}
						playerWidth={videoWidth}
						playerHeight={videoHeight} />
				</ThemeProvider>
			);

		}
	};

	if (snippetComponent[dataGpType]) {
		snippetComponent[dataGpType]();
	} else {
		console.error("Snippet data type not supported: ", dataGpType);
	}

};

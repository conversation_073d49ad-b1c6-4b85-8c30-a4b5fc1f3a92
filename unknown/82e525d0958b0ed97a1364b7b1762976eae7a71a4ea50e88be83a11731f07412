import { Translation } from "@src/types/translations";

export const checkVideoURL = (linkURL: string, translation: Translation) => {
	const instagramRegex = /^(https?:\/\/)?(www\.)?instagram\.com\/.+/;
	const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
	const vimeoRegex = /^(https?:\/\/)?(www\.)?vimeo\.com\/.+/;
	const tiktokRegex = /^(https?:\/\/)?(www\.)?tiktok\.com\/.+/;

	if (instagramRegex.test(linkURL)) {
		return translation.errors.instagramUrlError;
	}

	if (youtubeRegex.test(linkURL)) {
		return translation.errors.youTubeUrlError;
	}

	if (vimeoRegex.test(linkURL)) {
		return translation.errors.vimeoUrlError;
	}

	if (tiktokRegex.test(linkURL)) {
		return translation.errors.tiktokUrlError;
	}

	// Return null if no platform matches
	return null;
};

export function secondsToFormattedTime(totalSeconds: number): string {
	const hours = Math.floor(totalSeconds / 3600);
	const minutes = Math.floor((totalSeconds % 3600) / 60);
	const seconds = totalSeconds % 60;

	let result = "";

	if (hours > 0) {
		result += `${hours}h `;
	}
	if (minutes > 0 || hours > 0) {
		result += `${minutes}m `;
	}
	result += `${seconds.toString().padStart(seconds > 0 ? 2 : 1, "0")}s`;

	return result;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const timeAgo = (createdAt: string, translation: any): string => {
	const now = new Date();
	const createdDate = new Date(createdAt);
	const diffInSeconds = Math.floor((now.getTime() - createdDate.getTime()) / 1000);

	const years = Math.floor(diffInSeconds / (365 * 24 * 60 * 60));
	if (years === 1) {
		return `1 ${translation.general.yearAgo}`;
	} else if (years > 1) {
		return `${years} ${translation.general.yearsAgo}`;
	}

	const months = Math.floor(diffInSeconds / (30 * 24 * 60 * 60));
	if (months === 1) {
		return `1 ${translation.general.monthAgo}`;
	} else if (months > 1) {
		return `${months} ${translation.general.monthsAgo}`;
	}

	const weeks = Math.floor(diffInSeconds / (7 * 24 * 60 * 60));
	const daysOffset = now.getDate() - createdDate.getDate();
	if ((months === 1 && daysOffset >= 0) || months >= 2 || weeks >= 1) {
		if (weeks === 1) {
			return `1 ${translation.general.weekAgo}`;
		}

		return `${weeks} ${translation.general.weeksAgo}`;
	}

	const days = Math.floor(diffInSeconds / (24 * 60 * 60));
	if (days === 1) {
		return `1 ${translation.general.dayAgo}`;
	} else if (days > 1) {
		return `${days} ${translation.general.daysAgo}`;
	}

	const hours = Math.floor(diffInSeconds / (60 * 60));
	if (hours === 1) {
		return `1 ${translation.general.hourAgo}`;
	} else if (hours > 1) {
		return `${hours} ${translation.general.hoursAgo}`;
	}

	const minutes = Math.floor(diffInSeconds / 60);
	if (minutes === 1) {
		return `1 ${translation.general.minuteAgo}`;
	} else if (minutes > 1) {
		return `${minutes} ${translation.general.minutesAgo}`;
	}

	if (diffInSeconds < 60) {
		if (diffInSeconds === 1) {
			return `1 ${translation.general.secondAgo}`;
		}

		return `${diffInSeconds} ${translation.general.secondsAgo}`;
	}

	return translation.general.justNow;
};

export const daysBetweenTimestampAndNow = (unixTimestamp: number, clockTime?: number | null) => {
	const dateFromTimestamp = new Date(unixTimestamp);
	const currentDate = clockTime ? new Date(clockTime) : new Date();
	const differenceInMilliseconds = dateFromTimestamp.getTime() - currentDate.getTime();
	const differenceInDays = Math.ceil(differenceInMilliseconds / (1000 * 60 * 60 * 24));
	return differenceInDays;
};

export const formatUnixTimestamp = (unixTimestamp: number): string => {
	const date = new Date(unixTimestamp);
	const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "long", day: "numeric" };
	return date.toLocaleDateString("en-US", options);
};

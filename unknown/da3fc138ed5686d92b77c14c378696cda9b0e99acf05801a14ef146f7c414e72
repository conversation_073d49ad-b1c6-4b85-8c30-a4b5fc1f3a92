import express, {
	Request,
	Response
} from "express";
import { verifyController } from "../modules/verify/verify.controller";
import { signupEmailController } from "../modules/signup/signupEmail.controller";
import { signupController } from "../modules/signup/signup.controller";
import { signInEmailController } from "../modules/signin/signinEmail.controller";
import { signInController } from "../modules/signin/signIn.controller";
import { resetPasswordController } from "../modules/resetPassword/resetPassword.controller";
import { resendVerificationController } from "../modules/verify/resendVerification.controller";
import { forgotPasswordController } from "../modules/forgotPassword/forgotPassword.controller";
import { forgotPasswordSchema } from "../middleware/auth/forgotPassword.mw";
import { resendVerificationSchema } from "../middleware/auth/resendVerification.mw";
import { resetPasswordSchema } from "../middleware/auth/resetPassword.mw";
import { signInSchema } from "../middleware/auth/signin.mw";
import { signInEmailSchema } from "../middleware/auth/signinEmail.mw";
import { signUpSchema } from "../middleware/auth/signup.mw";
import { signUpEmailSchema } from "../middleware/auth/signupEmail.mw";
import { verifySchemas } from "../middleware/auth/verify.mw";
import { isSchemaValid } from "../middleware/isSchemaValid.mw";

const router = express.Router();

router.post(
	"/signup",
	[express.json({ limit: "2MB" }), isSchemaValid(signUpSchema.data)],
	signupController
);

router.post(
	"/sign-up/email",
	[express.json({ limit: "2MB" }), isSchemaValid(signUpEmailSchema.data)],
	signupEmailController
);

router.post(
	"/sign-in",
	[express.json({ limit: "2MB" }), isSchemaValid(signInSchema.data)],
	signInController
);

router.post(
	"/sign-in/email",
	[express.json({ limit: "2MB" }), isSchemaValid(signInEmailSchema.data)],
	signInEmailController
);

router.post(
	"/verify",
	[express.json({ limit: "2MB" }), isSchemaValid(verifySchemas.data)],
	verifyController
);

router.post(
	"/resend-verification",
	[
		express.json({ limit: "2MB" }),
		isSchemaValid(resendVerificationSchema.data)
	],
	resendVerificationController
);

router.post(
	"/forgot-password",
	[
		express.json({ limit: "2MB" }),
		isSchemaValid(forgotPasswordSchema.data)
	],
	forgotPasswordController
);

router.post(
	"/reset-password",
	[
		express.json({ limit: "2MB" }),
		isSchemaValid(resetPasswordSchema.data)
	],
	resetPasswordController
);

router.post("/invite", (req: Request, res: Response) => {
	return res.status(200).json("Thanks for doing a POST to /auth/invite");
});

export default router;

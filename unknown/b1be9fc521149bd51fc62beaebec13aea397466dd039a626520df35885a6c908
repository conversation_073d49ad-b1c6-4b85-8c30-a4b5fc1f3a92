import Jo<PERSON> from "joi";
import { AuthMethods } from "../../modules/authentication/authentication.enums";
import {
	LocaleAPI,
	APIErrorName
} from "../../interfaces/apiTypes";
import { IForgotPasswordPayload } from "../../modules/forgotPassword/forgotpassword.interfaces";

const emailSchema = Joi.string()
	.lowercase()
	.email({
		tlds: { allow: false }
	})
	.trim();

export const forgotPasswordSchema = {
	data: Joi.object<IForgotPasswordPayload>({
		method: Joi.string().valid(AuthMethods.EMAIL_PASSWORD).required(),
		email: emailSchema.required(),
		callbackEndpoint: Joi.string().required(),
		locale: Joi.string().custom((value, helper) => {
			if (Object.values(LocaleAPI).includes(value)) {
				return true;
			}
			return helper.message({
				custom: "language is not supported."
			});

		})
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

import {
	Request,
	Response
} from "express";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { VersionValidator } from "../../version/version.validators";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../../services/mongodb/transaction.service";
import { InteractiveVideoModel } from "../interactiveVideo.model";
import { ObjectId } from "mongodb";
import Joi from "joi";

const objectIdValidator = Joi.custom((value, helpers) => {
	if (!ObjectId.isValid(value)) {
		return helpers.error("any.invalid");
	}
	return value.toString();
});

export class InteractiveVideoLikeController {
	public post = async (request: Request, response: Response): Promise<Response> => {
		let videoId: string;
		try {
			await VersionValidator.required().validateAsync(request.headers["x-api-version"]);
			videoId = await objectIdValidator.required().validateAsync(request.params.videoId);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(response);
		}

		try {
			await startDBTransaction(response.locals.session);

			const interactiveVideoModel: InteractiveVideoModel = new InteractiveVideoModel(response.locals.session);
			await interactiveVideoModel.incrementLikesById(videoId);

			await completeDBTransaction(response.locals.session);
			return response.status(200).send({});
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	};
}

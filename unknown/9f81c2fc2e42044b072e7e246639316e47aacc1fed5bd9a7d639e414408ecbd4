import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../base/base.controller";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../services/mongodb/transaction.service";
import {
	PaymentDeleteValidator,
	PaymentListValidator,
	PaymentPostValidator
} from "./payment.joi";
import { AccountModel } from "../account/account.model";
import { StripePaymentModel } from "../stripe/payment/payment.model";
import { APIError } from "../../utils/helpers/apiError";
import { PaymentModel } from "./payment.model";
import { APIErrorName } from "../../interfaces/apiTypes";

export class PaymentController extends Controller {
	constructor() {
		super();

		this.router.get(
			"/",
			async (request: Request, response: Response): Promise<Response> => {
				try {
					const validatedRequest = await PaymentListValidator.validateAsync(
						{
							apiVersion: request.headers["x-api-version"],
							accessToken: request.headers.authorization,
							accountToken: request.headers["x-account-token"]
						}
					);

					await this.verifyAccessToken(validatedRequest.accessToken);
					const accountToken = await this.verifyAccountToken(validatedRequest.accountToken);

					const accountModel = new AccountModel(null);
					const account = await accountModel.readOneById(accountToken.account._id);
					if (!account.stripeCustomerId) {
						throw new APIError(APIErrorName.E_MISSING_CUSTOMER_ID, "missing stripe customer id");
					}

					const stripePaymentModel = new StripePaymentModel(null);
					const paymentMethods = await stripePaymentModel.readByCustomerId(
						account.stripeCustomerId
					);

					return response.status(200).send({
						paymentMethods: paymentMethods
					});
				} catch (error: unknown) {
					if ((error as any).code === "resource_missing") {
						return new APIError(APIErrorName.E_STRIPE_CUSTOMER_NOT_FOUND, "No such customer")
							.log().setResponse(response);
					}

					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.post(
			"/",
			[
				express.json({ limit: "2MB" })
			],
			async (request: Request, response: Response): Promise<Response> => {
				try {
					const validatedRequest = await PaymentPostValidator.validateAsync(
						{
							apiVersion: request.headers["x-api-version"],
							accessToken: request.headers.authorization,
							accountToken: request.headers["x-account-token"],
							paymentMethodId: request.body.paymentMethodId
						}
					);

					await this.verifyAccessToken(validatedRequest.accessToken);
					const accountToken = await this.verifyAccountToken(validatedRequest.accountToken);

					await startDBTransaction(response.locals.session);

					const stripePaymentModel = new StripePaymentModel(response.locals.session);
					const paymentMethod = await stripePaymentModel.attach(
						accountToken.account._id,
						validatedRequest.paymentMethodId
					);

					await completeDBTransaction(response.locals.session);

					return response.status(200).send(paymentMethod);
				} catch (error: unknown) {
					await cancelDBTransaction(response.locals.session);
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.delete(
			"/:id",
			async (request: Request, response: Response): Promise<Response> => {
				try {
					const validatedRequest = await PaymentDeleteValidator.validateAsync(
						{
							apiVersion: request.headers["x-api-version"],
							accessToken: request.headers.authorization,
							accountToken: request.headers["x-account-token"],
							paymentMethodId: request.params.id
						}
					);

					await this.verifyAccessToken(validatedRequest.accessToken);
					const accountToken = await this.verifyAccountToken(validatedRequest.accountToken);

					await startDBTransaction(response.locals.session);

					const paymentModel = new PaymentModel(accountToken.account._id, response.locals.session);
					await paymentModel.deleteOne(validatedRequest.paymentMethodId);

					await completeDBTransaction(response.locals.session);

					return response.status(200).send({});
				} catch (error: unknown) {
					await cancelDBTransaction(response.locals.sessionn);
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}
}

import {
	type Request,
	type Response
} from "express";
import { StripeProductsModel } from "../stripe/product/products.model";
import { ProductListJoi } from "./product.joi";
import { APIError } from "../../utils/helpers/apiError";
import { Controller } from "../base/base.controller";
import { ProductsModel } from "./products.model";

export class ProductController extends Controller {
	constructor() {
		super();

		this.router.get(
			"/",
			async (request: Request, response: Response) => {
				try {
					const validatedRequest = await ProductListJoi.validateAsync({
						apiVersion: request.headers["x-api-version"],
						accessToken: request.headers.authorization
					});

					await this.verifyAccessToken(validatedRequest.accessToken);

					const stripeProductsModel = new StripeProductsModel();
					const stripeProducts = await stripeProductsModel.getProducts();

					const productsModel = new ProductsModel();
					const products = await productsModel.fromStripeProducts(stripeProducts);

					return response.json(products);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}
}

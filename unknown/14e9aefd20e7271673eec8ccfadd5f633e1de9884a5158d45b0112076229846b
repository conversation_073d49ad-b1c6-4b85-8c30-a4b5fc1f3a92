import path from "path";
import fs from "fs";
import os from "os";
import { Writable } from "stream";


class MockStorage {
	bucket(name: string): MockedBucket {
		return new MockedBucket(name);
	}
}

const uniqueId = Date.now();

const mockedFile: any = {
	getSignedUrl: jest.fn().mockResolvedValue(["https://domain.tld/signed-url"]),
	getMetadata: jest.fn().mockResolvedValue([{ size: "1024" }]),
	filePath: "",
	download: jest.fn().mockImplementation(() => {
		const data: string = fs.readFileSync(mockedFile.filePath, "utf8");
		return [data];
	}),
	save: jest.fn().mockImplementation((data: string) => {
		const dirPath = path.dirname(mockedFile.filePath);
		fs.mkdirSync(dirPath, { recursive: true });
		fs.writeFileSync(mockedFile.filePath, data);
		return data;
	}),
	delete: jest.fn().mockImplementation(() => {
		return true;
	}),
	createWriteStream: jest.fn().mockImplementation(() => {
		return new Writable({
			write(chunk, encoding, callback): any {
				callback();
			},
			final(callback): any {
				this.emit("finish");
				callback();
			}
		});
	})
};

class MockedBucket {
	public name: string;
	existsDefaultReturn: [boolean] = [true];
	constructor (public buncketName: string) {
		this.name = buncketName;
	}

	public file(fileName: string): any {
		const tmpDir = os.tmpdir();
		const uniqueFileName = path.join(path.dirname(fileName), uniqueId.toString(), path.basename(fileName));
		mockedFile.filePath = path.resolve(tmpDir, uniqueFileName);
		return mockedFile;
	}

	public exists(): [boolean] {
		// use the "badbucket" name in secretsMock.storage.bucketName
		// if you want the exists check to return false
		let ret: [boolean] = [true];
		if (this.name == "badbucket") {
			ret = [false];
		}

		return ret;
	}
}

export { MockStorage as Storage };


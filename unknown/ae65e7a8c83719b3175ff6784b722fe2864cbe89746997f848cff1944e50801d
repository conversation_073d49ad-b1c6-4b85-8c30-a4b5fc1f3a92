import { ClientSession } from "mongoose";
import { IAccount } from "../../modules/account/account.interfaces";
import { AccountDBModel } from "../../modules/account/accountDB.model";
import { AccountManifestModel } from "../../modules/account/manifest/account.manifest.model";
import {
	IDBALInput,
	APIErrorName
} from "../../interfaces/apiTypes";
import { isAccessPermitted } from "../../utils/helpers/gp.helper";

export const readAccount = async (
	dbInput: IDBALInput
): Promise<IAccount | null> => {
	return new Promise((resolve, reject) => {
		isAccessPermitted(dbInput);
		AccountDBModel.find(dbInput.query, (error: Error, docs: Array<IAccount>) => {
			if (error) {
				const err = new Error();
				err.message = `<readAccount.service> | ${error.name} | ${error.message}`;
				err.name = APIErrorName.E_SERVICE_FAILED;
				reject(err);
			}
			if (!docs) resolve(null);
			resolve(docs[0]);
		});
	});
};

export const readAccount2 = async (query: any, session: ClientSession | null): Promise<IAccount | null> => {
	try {
		const document: IAccount | null = await AccountDBModel.findOne(query).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<account.service> readAccount2 | ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const readAccounts = async (dbInput: IDBALInput): Promise<IAccount[]> => {
	return new Promise((resolve, reject) => {
		isAccessPermitted(dbInput);
		AccountDBModel.find(
			dbInput.query,
			(err: Error, docs: IAccount[]) => {
				if (err) reject(err);
				resolve(docs);
			}
		).sort({ createdAt: -1 }).session(dbInput.dbSession ?? null);
	});
};

export const createAccount2 = async (insert: any, session: ClientSession | null): Promise<IAccount> => {
	try {
		const model = new AccountDBModel(insert);
		const document = await model.save({ session });
		const accountManifestModel = new AccountManifestModel(document);
		await accountManifestModel.update();

		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<accounts.service> createAccount2| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { APIError } from "../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { APIKeyModel } from "../modules/apiKey/apiKey.model";


describe("POST /api/keys Negative Tests", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	let accessToken: string;
	let accountToken: string;

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "apikey.createkey.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 401 for invalid access token", async () => {
		const res = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", "Bearer InvalidToken")
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);
		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[E_MISSING_AUTHORIZATION]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.post("/api/keys");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("[Service Failure]. Should return 500 for internal server error", async () => {
		jest.spyOn(APIKeyModel.prototype, "createOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "some error - does not matter")
		);

		const res = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		if (res.statusCode !== 500) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(500);
	});

	it("[User authentication not found]. Should return 404", async () => {
		jest.spyOn(AuthenticationModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found")
		);

		const res = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);
		expect(res.statusCode).toBe(404);
	});
});

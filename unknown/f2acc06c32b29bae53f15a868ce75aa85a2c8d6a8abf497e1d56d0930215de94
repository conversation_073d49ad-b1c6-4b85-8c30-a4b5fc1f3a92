import Jo<PERSON> from "joi";
import { APIErrorName } from "../interfaces/apiTypes";
import {
	IGetShoppableCollectionPayload,
	IPostCollectionPayload,
	IPutCollectionPayload
} from "../modules/interactiveCollection/interactiveCollection.interface";

export const getCollectionsSchema = {
	data: Joi.object<IGetShoppableCollectionPayload>({
		limit: Joi.number().integer().positive(),
		sortKey: Joi.string(),
		SortBy: Joi.string().valid("asc", "dsc")
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT,
		"number.base": APIErrorName.E_INVALID_INPUT,
		"number.integer": APIErrorName.E_INVALID_INPUT,
		"number.positive": APIErrorName.E_INVALID_INPUT,
		"string.valid": APIErrorName.E_INVALID_INPUT
	})
};

export const postCollectionSchema = {
	data: Joi.object<IPostCollectionPayload>({
		title: Joi.string().required(),
		shoppableVideos: Joi.array().items(Joi.string().hex().length(24)).max(100).required(),
		buttonBackgroundColor: Joi.string().optional(),
		buttonBackgroundBlur: Joi.boolean().optional(),
		iconTextColor: Joi.string().optional(),
		displayFont: Joi.string().optional(),
		carouselBorderRadius: Joi.number().optional(),
		carouselIsCentered: Joi.boolean().optional(),
		carouselMargin: Joi.number().optional(),
		carouselGap: Joi.number().optional(),
		widgetBorderRadius: Joi.number().optional(),
		widgetPosition: Joi.string().valid("left", "right").optional(),
		inlineBorderRadius: Joi.number().optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

export const putShoppableCollectionSchema = {
	data: Joi.object<IPutCollectionPayload>({
		title: Joi.string(),
		shoppableVideos: Joi.array().items(Joi.string().hex().length(24)).max(100),
		buttonBackgroundColor: Joi.string().optional(),
		buttonBackgroundBlur: Joi.boolean().optional(),
		iconTextColor: Joi.string().optional(),
		displayFont: Joi.string().optional(),
		carouselBorderRadius: Joi.number().optional(),
		carouselIsCentered: Joi.boolean().optional(),
		carouselMargin: Joi.number().optional(),
		carouselGap: Joi.number().optional(),
		widgetBorderRadius: Joi.number().optional(),
		widgetPosition: Joi.string().valid("left", "right").optional(),
		inlineBorderRadius: Joi.number().optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};



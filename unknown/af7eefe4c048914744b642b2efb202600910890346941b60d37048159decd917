import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { IPostSignupPayload } from "../../../modules/signup/signup.interfaces";
import { readAccount2 } from "../../../services/mongodb/account.service";
import { deleteInvitations } from "../../../services/mongodb/invitations.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../../services/mongodb/transaction.service";
import { APIError } from "../../../utils/helpers/apiError";
import { AuthenticationModel } from "../../authentication/authentication.model";
import { UserModel } from "../../user/user.model";
import { AccountModel } from "../account.model";
import { UserUpdateOneInput } from "../../user/user.interfaces";

export class AccountUsersController {
	async delete (request: Request, response: Response): Promise<Response> {
		return deleteAccountUserController(request, response);
	}

	async get (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async list (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async post (request: Request, response: Response): Promise<Response> {
		return postSignupController(request, response);
	}

	async patch (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async put (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}
}

const deleteAccountUserController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing access token");
		}

		const userId = req.params.userId;

		if (req.accessToken.userId === userId) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Cannot delete yourself");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing account token");
		}

		const accountId = req.accountToken.account._id;

		const account = await readAccount2({
			_id: accountId
		}, null);

		if (!account) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "the account does not exist");
		}

		if (account.ownerUserId.toString() === userId) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Cannot delete the owner user");
		}

		await startDBTransaction(res.locals.session);

		const authenticationModel = new AuthenticationModel(res.locals.session);
		await authenticationModel.detachAccountByUserId(userId, accountId);

		await deleteInvitations({
			accountId: accountId,
			userId: userId
		}, res.locals.session);

		await completeDBTransaction(res.locals.session);
		return res.status(200).send({});
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

const postSignupController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version"
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version"
			);
		}

		if (!req.accountToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header"
			);
		}

		if (!req.accessToken?.userId) {
			throw new APIError(
				APIErrorName.E_INVALID_AUTHORIZATION,
				"Missing userId in access token"
			);
		}

		await startDBTransaction(res.locals.session);

		const postSignupPayload = req.body as IPostSignupPayload;

		const userSet: UserUpdateOneInput = {
			_id: req.accessToken.userId,
			postSignupCompleted: true
		};
		const accountSet: any = {};

		if (postSignupPayload.name) {
			const nameComponents = postSignupPayload.name.trim().split(/\s+/);

			if (nameComponents.length === 1) {
				userSet.firstName = nameComponents[0];
				userSet.lastName = "";
			} else {
				userSet.lastName = nameComponents.pop();
				userSet.firstName = nameComponents.join(" ");
			}
		}

		if (postSignupPayload.team) {
			userSet.team = postSignupPayload.team;
		}

		if (postSignupPayload.companyName) {
			accountSet.companyName = postSignupPayload.companyName;
		}

		if (postSignupPayload.platform) {
			accountSet.platform = postSignupPayload.platform;
		}


		const userModel = new UserModel(res.locals.session);
		const userDocument = await userModel.updateOneById(userSet);

		const accountModel = new AccountModel(res.locals.session);
		const accountDocument = await accountModel.updateOneById(req.accountToken.account._id, accountSet);

		const result = {
			user: userDocument,
			account: accountDocument
		};

		await completeDBTransaction(res.locals.session);
		return res.status(200).json(result);
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

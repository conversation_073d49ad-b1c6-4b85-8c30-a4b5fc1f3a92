import { EmailPasswordAuthentication } from "./../authentication/emailpassword.authentication.model";
import { UserModel } from "./user.model";
import {
	IUser,
	UserCreateOneInput
} from "./user.interfaces";
import { APIErrorName } from "../../interfaces/apiTypes";
import { UserTeam } from "./user.enums";
import { AccountModel } from "../account/account.model";
import {
	AccountCreateData,
	IAccount
} from "../account/account.interfaces";
import { UserDBModel } from "./userDB.model";

describe("UserModel", () => {
	const userModel = new UserModel(null);
	let user1: IUser;
	let user2: IUser;
	let account: IAccount;

	beforeAll(async () => {
		user1 = await userModel.createOne({
			email: "<EMAIL>",
			firstName: "User",
			lastName: "Model",
			postSignupCompleted: false,
			isPasswordSet: false
		});

		expect(user1._id).toBeDefined();

		const accountModel = new AccountModel(null);
		expect(accountModel).toBeDefined();

		const accountCreateData: AccountCreateData = {
			companyName: "Read Many By Company Test",
			companyURL: "",
			user: user1
		};

		account = await accountModel.create(accountCreateData);

		expect(account._id).toBeDefined();
		expect(account.ownerUserId.toString()).toEqual(user1._id.toString());

		const emailAuthenticationModel = new EmailPasswordAuthentication(null);
		const authentication = await emailAuthenticationModel.createOne(
			user1._id.toString(),
			account._id.toString(),
			"password",
			{
				verified: false,
				legalAgreement: true
			}
		);
		expect(authentication).toBeDefined();


		user2 = await userModel.createOne({
			email: "<EMAIL>",
			firstName: "User",
			lastName: "Model",
			postSignupCompleted: false,
			isPasswordSet: false
		});

		expect(user2._id).toBeDefined();

		const authentication2 = await emailAuthenticationModel.createOne(
			user2._id.toString(),
			account._id.toString(),
			"password",
			{
				verified: false,
				legalAgreement: true
			}
		);
		expect(authentication2).toBeDefined();
	});

	it("should be able to read an existing user by id", async () => {
		const user = await userModel.readOneById(user1._id.toString());
		expect(user.email).toBe(user1.email);
		expect(user.firstName).toBe(user1.firstName);
		expect(user.lastName).toBe(user1.lastName);
	});

	it("should be able to read an existing user by email", async () => {
		const user = await userModel.readOneByEmail(user1.email);
		expect(user.email).toBe(user1.email);
		expect(user.firstName).toBe(user1.firstName);
		expect(user.lastName).toBe(user1.lastName);
	});

	it("should be able to read all users by account id", async () => {
		const users = await userModel.readManyByAccountId(account._id.toString());
		expect(users).toHaveLength(2);
		expect(users[0].email).toBe(user1.email);
		expect(users[0].firstName).toBe(user1.firstName);
		expect(users[0].lastName).toBe(user1.lastName);
		expect(users[1].email).toBe(user2.email);
		expect(users[1].firstName).toBe(user2.firstName);
		expect(users[1].lastName).toBe(user2.lastName);
	});

	it("should fail to read a non existing user by email", async () => {
		const user = userModel.readOneByEmail("<EMAIL>");
		expect(user).rejects.toHaveProperty("name", APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("should fail to read a non existing user by id", async () => {
		const user = userModel.readOneById("000000000000000000000000");
		await expect(user).rejects.toHaveProperty("name", APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("should fail to create a user with an existing email", async () => {
		await expect(userModel.createOne({
			email: user1.email,
			firstName: "User",
			lastName: "Model",
			postSignupCompleted: false,
			isPasswordSet: false
		})).rejects.toHaveProperty("name", APIErrorName.E_SIGN_UP_EXISTING_EMAIL);
	});

	it("should attempt to create 2 users at the same time and fail to create the second one", async () => {
		const email = "<EMAIL>";
		const userData: UserCreateOneInput = {
			email: email,
			firstName: "User",
			lastName: "Model",
			postSignupCompleted: false,
			isPasswordSet: false
		};

		const user1 = userModel.createOne(userData);
		const user2 = userModel.createOne(userData);

		const [result1, result2] = await Promise.allSettled([user1, user2]);

		expect([result1.status, result2.status]).toContain("fulfilled");
		expect([result1.status, result2.status]).toContain("rejected");

		if (result1.status === "rejected") {
			expect(result1.reason).toHaveProperty("name", APIErrorName.E_SIGN_UP_EXISTING_EMAIL);
			expect(result2.status).toBe("fulfilled");
		}

		if (result2.status === "rejected") {
			expect(result2.reason).toHaveProperty("name", APIErrorName.E_SIGN_UP_EXISTING_EMAIL);
			expect(result1.status).toBe("fulfilled");
		}
	});

	it("should throw when the database update fails", async () => {
		const error = new Error("Database error");

		jest.spyOn(UserDBModel.prototype, "save").mockImplementationOnce(() => {
			return Promise.reject(error);
		});

		const email = "<EMAIL>";
		const userData: UserCreateOneInput = {
			email: email,
			firstName: "User",
			lastName: "Model",
			postSignupCompleted: false,
			isPasswordSet: false
		};

		await expect(userModel.createOne(userData)).rejects.toBe(error);
	});

	it("should fail to update a user with an existing email", async () => {
		await expect(userModel.updateOneById({
			_id: user1._id.toString(),
			email: user2.email
		})).rejects.toThrow();
	});

	it("should fail to update a user that does not exist", async () => {
		await expect(userModel.updateOneById({
			_id: "000000000000000000000000",
			firstName: "User"
		})).rejects.toHaveProperty("name", APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("should update a user with with no changes performed", async () => {
		const updatedUser = await userModel.updateOneById({
			_id: user1._id.toString()
		});

		expect(updatedUser.email).toBe(user1.email);
		expect(updatedUser.firstName).toBe(user1.firstName);
		expect(updatedUser.lastName).toBe(user1.lastName);
	});

	it("should update a user with a non conflicting email", async () => {
		const email = "<EMAIL>";
		const updatedUser = await userModel.updateOneById({
			_id: user1._id.toString(),
			email: email,
			firstName: "User Updated",
			lastName: "Model Updated",
			team: UserTeam.ENGINEERING_IT,
			isPasswordSet: true,
			postSignupCompleted: true
		});

		expect(updatedUser.email).toBe(email);
		expect(updatedUser.firstName).toBe("User Updated");
		expect(updatedUser.lastName).toBe("Model Updated");
	});

	it("exists", async () => {
		const response1 = userModel.exists(user2.email);
		await expect(response1).resolves.toBe(true);

		const response2 = userModel.exists("<EMAIL>");
		await expect(response2).resolves.toBe(false);
	});

	// must be last
	it("should successfully delete the users", async () => {
		const response1 = userModel.deleteOneById(user1._id.toString());
		await expect(response1).resolves.toBe(undefined);

		const response2 = userModel.deleteOneById(user2._id.toString());
		await expect(response2).resolves.toBe(undefined);
	});
});

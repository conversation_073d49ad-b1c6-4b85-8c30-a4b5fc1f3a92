import React from "react";
import {
	BarGraphContainer,
	BarGraphItem,
	BarGraphItemContainer,
	BarGraphLabel,
	BarGraphNumber,
	GridLines
} from "@src/styles/components";
import { MetricTypes } from "@src/types/metrics";
import { formatDate } from "@src/utils/dates";

const PerformanceBarGraph = ({
	currentValue,
	previousValue,
	metricType,
	startDate,
	endDate
}: {
	currentValue: number;
	previousValue: number;
	metricType: MetricTypes;
	startDate: Date;
	endDate: Date;
}) => {
	const maxValue = Math.max(currentValue, previousValue);
	const maxBarHeightPx = 275;

	let prevStartDate;
	let prevEndDate;
	if (startDate && endDate) {
		const delta = endDate.getDate() - startDate.getDate();
		prevStartDate = new Date(startDate);
		prevStartDate.setDate(startDate.getDate() - delta - 1);
		prevEndDate = new Date(endDate);
		prevEndDate.setDate(endDate.getDate() - delta - 1);
	}

	return (
		<>
			<BarGraphContainer largeGraph={true}>
				<BarGraphItemContainer style={{ position: "static", flex: "0", margin: "0" }}>
					<GridLines>
						{Array.from({ length: 11 }).map((_, index) => (
							<div key={index} className="line" />
						))}
					</GridLines>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem
						value={maxValue > 0 ? (previousValue / maxValue) * maxBarHeightPx : 0}
						metricType={metricType}
						num={1}
						zeroBottom={true}
					/>
					<BarGraphNumber value={maxValue > 0 ? (previousValue / maxValue) * maxBarHeightPx : 0}>
						{previousValue}
					</BarGraphNumber>
				</BarGraphItemContainer>
				<BarGraphItemContainer>
					<BarGraphItem
						value={maxValue > 0 ? (currentValue / maxValue) * maxBarHeightPx : 0}
						metricType={metricType}
						num={5}
						zeroBottom={true}
					/>
					<BarGraphNumber value={maxValue > 0 ? (currentValue / maxValue) * maxBarHeightPx : 0}>
						{currentValue}
					</BarGraphNumber>
				</BarGraphItemContainer>
			</BarGraphContainer>
			<BarGraphContainer style={{ height: "2rem", justifyContent: "space-around" }}>
				<BarGraphLabel performance={true}>
					{prevStartDate && formatDate(prevStartDate)} - {prevEndDate && formatDate(prevEndDate)}
				</BarGraphLabel>
				<BarGraphLabel performance={true}>
					{startDate && formatDate(startDate)} - {endDate && formatDate(endDate)}
				</BarGraphLabel>
			</BarGraphContainer>
		</>
	);
};

export default PerformanceBarGraph;

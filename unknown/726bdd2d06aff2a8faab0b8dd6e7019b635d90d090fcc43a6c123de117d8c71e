/* eslint-disable max-lines-per-function */
import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import {
	ISignInPayload,
	SignInMethod
} from "../modules/signin/signin.interfaces";


describe("POST /auth/sign-in positive mode tests", () => {
	let expressApp: express.Express;
	let accountToken: string;
	let accessToken: string;
	let refreshToken: string;
	let accountId: string;

	const newUserPayload = {
		firstName: "Johnny",
		lastName: "Signin",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signin.positive.mode.test Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		const authorizationData = await testHelper.signup(newUserPayload);
		accessToken = authorizationData.accessToken;
		refreshToken = authorizationData.refreshToken;

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();

		accountToken = await testHelper.getAccountToken(accountId, accessToken);
	});

	it("/api/signin with SignInMethod.AUTHLINK", async () => {

		const authlinkResponse = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		const authlink = authlinkResponse.body.authLinkToken;

		const signupPayload = {
			method: SignInMethod.AUTHLINK,
			authlink: authlink
		} as ISignInPayload;

		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "2")
			.send(signupPayload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");
	});

	it("/api/signin with SignInMethod.REFRESH_TOKEN", async () => {
		const signupPayload = {
			method: SignInMethod.REFRESH_TOKEN,
			token: refreshToken
		} as ISignInPayload;

		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "2")
			.send(signupPayload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");
	});

	it("/api/signin with SignInMethod.EMAIL_PASSWORD", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.EMAIL_PASSWORD,
				email: newUserPayload.email,
				password: newUserPayload.password
			});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");
		expect(res.body).not.toHaveProperty("apiKeyId");
	});

	it("/api/signin with SignInMethod.APIKEY", async () => {
		const resKeys = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		const key = resKeys.body.key;
		const apikey = resKeys.body.apikey;

		const res = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "1")
			.send({
				method: SignInMethod.APIKEY,
				apikey: apikey
			});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accessToken");
		expect(res.body).toHaveProperty("refreshToken");
		expect(res.body).toHaveProperty("apiKeyId");

		expect(res.body.apiKeyId).toBe(key._id.toString());
	});

});

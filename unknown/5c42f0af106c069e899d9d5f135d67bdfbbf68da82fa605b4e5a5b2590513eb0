import {
	Request,
	Response
} from "express";
import {
	APIErrorName,
	AdminLinkType
} from "../../interfaces/apiTypes";
import { ISignInEmailPayload } from "./signin.interfaces";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import { sendEmailLink } from "../../utils/helpers/signup.helper";
import { APIError } from "../../utils/helpers/apiError";
import { AuthMethods } from "../authentication/authentication.enums";
import { AuthenticationModel } from "../authentication/authentication.model";
import { IAuthentication } from "../authentication/authentication.interface";
import { UserModel } from "../user/user.model";

export const signInEmailController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const payload = req.body as ISignInEmailPayload;

		const authRecord = await findAuthentication(payload.email);

		if (authRecord.method !== AuthMethods.EMAIL_PASSWORD) {
			throw new APIError(APIErrorName.E_AUTH_METHOD_NOT_SUPPORTED,
				"sign In with email is not available for this authentication method.");
		}

		if (!authRecord.verified) {
			throw new APIError(APIErrorName.E_USER_NOT_VERIFIED, "User not verified").suppressLog();
		}

		const secrets: ISecrets = await getSecrets();

		const gpSecretKey = secrets.hashkey.key;

		const userModel = new UserModel(null);
		// throws if user is not found
		await userModel.readOneById(authRecord.userId.toString());

		await sendEmailLink(
			{
				email: payload.email,
				callbackEndpoint: payload.callbackEndpoint,
				locale: payload.locale,
				linkType: AdminLinkType.SIGN_IN,
				template: "sign-in-email",
				subject: "Sign In to Your Account"
			},
			authRecord,
			gpSecretKey
		);

		return res.sendStatus(200);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

export const findAuthentication = async (email: string): Promise<IAuthentication> => {
	try {
		const authModel = new AuthenticationModel(null);
		return await authModel.readOneByEmail(email);
	} catch (error: unknown) {
		const apiError = APIError.fromUnknownError(error);

		if (apiError.name === APIErrorName.E_DOCUMENT_NOT_FOUND) {
			apiError.name = APIErrorName.E_EMAIL_SIGN_IN_NO_EMAIL;
			apiError.suppressLog();
		}

		throw apiError;
	}
};

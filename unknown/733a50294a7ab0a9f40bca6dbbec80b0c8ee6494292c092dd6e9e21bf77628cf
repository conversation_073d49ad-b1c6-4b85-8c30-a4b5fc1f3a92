import {
	Request,
	Response
} from "express";
import { UserUpdateOneInput } from "./user.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { UserModel } from "./user.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IPatchUserPayload } from "../../modules/user/user.interfaces";

export const patchUserController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken?.userId) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Missing userId in access token");
		}

		if (req.accessToken.userId !== req.params.userId) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"userId in parameter does not match userId in access token");
		}

		const requestBody = req.body as IPatchUserPayload;

		const userSet: UserUpdateOneInput = {
			_id: req.accessToken.userId
		};

		if (requestBody.firstName) {
			userSet.firstName = requestBody.firstName;
		}

		if (requestBody.lastName) {
			userSet.lastName = requestBody.lastName;
		}

		if (requestBody.team) {
			userSet.team = requestBody.team;
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.updateOneById(userSet);

		return res.send({ user: userDocument });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

/* end of file */

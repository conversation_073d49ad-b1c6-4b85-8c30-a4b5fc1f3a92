import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";


describe("POST /auth/sign-in/email negative mode tests", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signin.email.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		await testHelper.signup(createUserPayload);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "0")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "x")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 when missing email", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				locale: "en_US",
				callbackEndpoint: "/"
			});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 when missing callbackEndpoint", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email
			});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_INVALID_INPUT]. Should return 400 when locale unsupported", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				locale: "en_UK",
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_EMAIL_SIGN_IN_NO_EMAIL]. Should return 404 for non-existent IAuthentication", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: "<EMAIL>",
				locale: "en_US",
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_EMAIL_SIGN_IN_NO_EMAIL);
	});

	it("[E_USER_NOT_VERIFIED]. Should return 403 for non verified account", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/sign-in/email")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				callbackEndpoint: "/"
			});

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_USER_NOT_VERIFIED);
	});
});

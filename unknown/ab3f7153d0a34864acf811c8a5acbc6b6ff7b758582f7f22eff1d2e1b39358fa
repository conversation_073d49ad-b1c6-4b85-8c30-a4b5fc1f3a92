import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { APIError } from "../utils/helpers/apiError";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";


describe("GET /invitations/self", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.invitations.self.failure.test Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] for missing access token", async () => {
		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] if the user document cannot be found", async () => {
		jest.spyOn(AuthenticationModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.get("/api/invitations/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});
});

import { LocaleAPI } from "../../interfaces/apiTypes";

export enum SignInMethod {
	EMAIL_PASSWORD = "email/password",
	REFRESH_TOKEN = "refreshtoken",
	APIKEY = "apikey",
	AUTHLINK = "authlink"
}

export interface ISignInEmailPayload {
	email: string;
	callbackEndpoint: string;
	locale: LocaleAPI;
}

export interface ISignInPayload {
	email?: string;
	password?: string;
	apikey?: string;
	method: string;
	token: string;
	authlink?: string;
}

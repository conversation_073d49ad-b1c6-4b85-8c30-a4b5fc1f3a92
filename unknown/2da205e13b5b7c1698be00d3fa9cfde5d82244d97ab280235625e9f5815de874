import React from "react";
import {
	RouterProvider,
	createBrowserRouter,
	RouteObject
} from "react-router-dom";
import {
	SliderShare,
	Slider
} from "@player/slider";
import { useApp } from "./useApp.hook";
import { useAppMessage } from "./useAppMessage.hook";
import { NotFound } from "@player/components/NotFound";



export const App: React.FC = () => {
	useAppMessage();
	useApp();
	const routes: RouteObject[] = [
		{
			path: "/c/:collectionId",
			element: <SliderShare />
		},
		{
			path: "/v/:videoId",
			element: <SliderShare />
		},
		{
			path: "/",
			element: <Slider />
		},
		{
			path: "*",
			element: <NotFound />
		}
	];

	const router = createBrowserRouter(routes);
	return <RouterProvider router={router} fallbackElement={<NotFound />} />;
};

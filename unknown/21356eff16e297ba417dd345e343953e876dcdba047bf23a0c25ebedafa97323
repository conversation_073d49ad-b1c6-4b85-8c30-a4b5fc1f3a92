import {
	useEffect,
	useCallback
} from "react";
import { useSetRecoilState } from "recoil";
import {
	appSessionStorageAtom,
	appPreviewDataAtom
} from "@player/app/app.state";
import {
	AppMessageSecret,
	AppMessageTypeEnum
} from "@player/app/app.message";
import {
	IAppSessionStorage,
	IAppPreviewData
} from "@player/app/app.interface";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";

export const useAppMessage = (): void => {
	const setAppSessionStorage = useSetRecoilState(appSessionStorageAtom);
	const setAppPreviewData = useSetRecoilState(appPreviewDataAtom);

	const onReceiveMessageAtApp = useCallback((event: MessageEvent) => {
		try {
			if (!event?.data?.toString().startsWith(AppMessageSecret)) {
				return;
			}

			const data = event.data.substring(AppMessageSecret.length);
			const parsedData = JSON.parse(data);
			const messageType = parsedData.type as AppMessageTypeEnum;

			if (messageType === AppMessageTypeEnum.RESPONSE_SESSION_STORAGE) {
				const messageData = parsedData.data as IAppSessionStorage;
				setAppSessionStorage(messageData);
			} else if (messageType === AppMessageTypeEnum.RENDER_VIDEO_PREVIEW) {
				const messageData = {
					video: parsedData.data.video,
					account: parsedData.data.account
				} as IAppPreviewData;
				setAppPreviewData(messageData);
			}

		} catch (error) {
			appLog({
				message: (error as Error).message,
				trace: (error as Error).stack + "\n onReceiveMessageAtApp",
				scope: LogScope.ERROR
			});
		}
	}, [setAppPreviewData, setAppSessionStorage]);


	useEffect(() => {
		window.addEventListener("message", onReceiveMessageAtApp);
		return () => {
			window.removeEventListener("message", onReceiveMessageAtApp);
		};
	}, [onReceiveMessageAtApp]);

};

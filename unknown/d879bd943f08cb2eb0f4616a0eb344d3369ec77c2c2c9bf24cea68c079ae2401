import mongoose, { Schema } from "mongoose";
import { IShoppableCollection } from "./interactiveCollection.interface";

const ShoppableCollectionSchema: Schema = new Schema(
	{
		title: String,
		accountId: Schema.Types.ObjectId,
		shoppableVideos: [
			Schema.Types.ObjectId
		],
		orderCount: {
			type: Schema.Types.Number,
			default: 0
		},
		userSessionCount: {
			type: Schema.Types.Number,
			default: 0
		},
		userCVR: {
			type: Schema.Types.Number,
			default: 0
		},
		createdAt: {
			type: Number,
			default: () => Date.now()
		},
		updatedAt: {
			type: Number,
			default: () => Date.now()
		},
		posterURL: {
			type: String,
			default: ""
		},
		buttonBackgroundColor: {
			type: String,
			default: ""
		},
		buttonBackgroundBlur: {
			type: Boolean,
			default: true
		},
		iconTextColor: {
			type: String,
			default: ""
		},
		displayFont: {
			type: String,
			default: ""
		},
		carouselBorderRadius: { type: Schema.Types.Number, required: false },
		carouselIsCentered: { type: Schema.Types.Boolean, required: false },
		carouselMargin: { type: Schema.Types.Number, required: false },
		carouselGap: { type: Schema.Types.Number, required: false },
		widgetBorderRadius: { type: Schema.Types.Number, required: false },
		widgetPosition: { type: Schema.Types.String, required: false },
		inlineBorderRadius: { type: Schema.Types.Number, required: false }
	},
	{
		timestamps: true
	}
);

export const InteractiveCollectionDBModel = mongoose.model<IShoppableCollection>(
	"ShoppableCollections",
	ShoppableCollectionSchema
);

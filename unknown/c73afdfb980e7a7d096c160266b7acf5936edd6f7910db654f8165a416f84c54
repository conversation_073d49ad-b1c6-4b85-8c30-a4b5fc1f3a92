import { ClientSession } from "mongoose";
import { StripePaymentModel } from "../stripe/payment/payment.model";
import { AccountModel } from "../account/account.model";

export class PaymentModel {
	constructor (public accountId: string, public session: ClientSession | null) {
	}

	public async deleteOne (paymentMethodId: string): Promise<void> {
		const stripePaymentModel = new StripePaymentModel(this.session);

		const accountModel = new AccountModel(this.session);
		const account = await accountModel.readOneById(this.accountId);

		const paymentMethods = await stripePaymentModel.readByCustomerId(
			account.stripeCustomerId
		);

		const paymentMethod = paymentMethods.find((paymentMethod) => {
			return paymentMethod.id === paymentMethodId;
		});

		if (!paymentMethod) {
			throw new Error("payment method not found for this account");
		}

		await stripePaymentModel.deleteOneById(paymentMethodId);
	}
}

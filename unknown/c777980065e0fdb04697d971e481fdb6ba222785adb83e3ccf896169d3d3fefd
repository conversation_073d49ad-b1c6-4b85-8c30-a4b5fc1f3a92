import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";

import * as BucketService from "../../services/gp/bucket.service";


describe("PUT /files", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "put.file.s.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
	});

	it("[200]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.put("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("location", "media/63f52a208d16beab51100c15/testVideo_64203ec04ffd3cccaab09452.mp4")
			.set("content-type", "*/*")
			.set("content-length", "783592")
			.set("content-range", "bytes 786432-1570023/1570024")
			.send(Buffer.from("Some data"));

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("publicURL");
	});

	it("[500]. Should return 500", async () => {
		jest.spyOn(BucketService, "uploadAssetChunksToCloud").mockResolvedValueOnce(
			undefined
		);

		const res = await supertest(expressApp)
			.put("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("location", "media/63f52a208d16beab51100c15/testVideo_64203ec04ffd3cccaab09452.mp4")
			.set("content-type", "*/*")
			.set("content-length", "783592")
			.set("content-range", "bytes 786432-1570023/1570024")
			.send(Buffer.from("Some data"));

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("[308]. Should return 308", async () => {
		jest.spyOn(BucketService, "uploadAssetChunksToCloud").mockResolvedValueOnce(
			{ status: 308 }
		);

		const res = await supertest(expressApp)
			.put("/api/files")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("location", "media/63f52a208d16beab51100c15/testVideo_64203ec04ffd3cccaab09452.mp4")
			.set("content-type", "*/*")
			.set("content-length", "783592")
			.set("content-range", "bytes 786432-1570023/1570024")
			.send(Buffer.from("Some data"));

		expect(res.statusCode).toBe(308);
	});
});

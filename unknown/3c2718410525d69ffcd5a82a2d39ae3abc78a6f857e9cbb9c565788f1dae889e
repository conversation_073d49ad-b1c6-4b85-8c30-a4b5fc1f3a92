import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	ISignedURL,
	readBucketSignedUrl
} from "../../services/gp/bucket.service";
import { APIError } from "../../utils/helpers/apiError";
import { CDN_DIR } from "../../utils/helpers/gp.helper";

export const postFilesController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		const accountId = req.accountToken.account._id.toString();
		const contentType = req.headers["content-type"];
		const xFilename = req.headers["x-filename"] as string;

		if (!contentType || !xFilename) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing request Headers.");
		}

		const signedURLInput: ISignedURL = {
			contentType: contentType,
			xFilename: xFilename,
			outputFilePath: CDN_DIR.MEDIA + accountId + "/"
		};

		const locationUrl: string | undefined = await readBucketSignedUrl(
			signedURLInput
		);

		if (!locationUrl) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Failed to read GCP signed URL");
		}

		const result = {
			location: locationUrl
		};

		return res.status(200).json(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

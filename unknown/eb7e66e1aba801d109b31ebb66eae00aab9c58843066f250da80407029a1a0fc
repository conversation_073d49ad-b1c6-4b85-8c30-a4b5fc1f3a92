
import { EventNameEnum } from "./event.enum";
import { SnippetTypeEnum } from "@player/app";
import { ISliderVideoPlayer } from "@player/slider";

export interface IEventInput {
	appSessionId?: string;
	eventName: EventNameEnum;
	accountId: string;
	collectionId?: string;
	videoId?: string | null;
	productActionedURL?: string | null;
	productId?: string | null;
	snippet?: {
		type: SnippetTypeEnum;
	};
	sliderVideoPlayer?: ISliderVideoPlayer | null;
	endedMethod?: "next" | "previous" | "exit" | "loop" | null;
	isShareMode?: boolean;
	videoPlayStatus?: "playing" | "stopped";
}

interface IUser {
	sessionId: string;
	fingerprint?: string;
}

export interface IDevice {
	type?: "Macintosh" | "PC" | "iPhone" | "Android";
	osName?:
    | "MacOS"
    | "iOS"
    | "iPadOS"
    | "Windows"
    | "Linux"
    | "Android"
    | undefined;
	osVersion?: string;
}

export interface IEventDimensions {
	browserCurrentURL?: string;
	browserReferrerURL?: string;
	browserName?: string;
	browserVersion?: string;
	browserLocale?: string;
	browserPixelWidth?: number;
	browserPixelHeight?: number;
	browserUserAgent?: string;
	collectionId?: string;
	videoId?: string;
	videoSecondsLength?: number;
	videoSecondsPosition?: number;
	productActionedURL?: string;
	productId?: string;
	videoPercentagePlayed?: number;
	previousVideoPercentagePlayed?: number;
	videoState?: "unloaded" | "loaded" | null;
	endedMethod?: "next" | "previous" | "exit" | "loop" | null;
	videoPlayStatus?: "playing" | "stopped";
}

export interface IEventPayload {
	eventName: EventNameEnum;
	appId: "gp-player";
	accountId: string;
	user: IUser;
	device: IDevice;
	eventDimensions: IEventDimensions;
	snippet?: {
		type: SnippetTypeEnum;
	};
	playId?: string;
	appSessionId?: string;
	isShareMode?: boolean;
}


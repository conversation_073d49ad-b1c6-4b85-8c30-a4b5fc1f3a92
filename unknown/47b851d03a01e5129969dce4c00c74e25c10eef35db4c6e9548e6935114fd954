import React from "react";
import styled, { keyframes } from "styled-components/macro";
import { Flex } from "./Flex";
import { Box } from "./Box";

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const Spinner = styled(Box)`
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: ${spin} 1s linear infinite;
`;

const LoadingContainer = styled(Flex)`
  justify-content: center;
  align-items: center;
  width: 100%;
`;

export const LoadingSpinner: React.FC = () => (
	<LoadingContainer data-testid="loading-spinner">
		<Spinner />
	</LoadingContainer>
);

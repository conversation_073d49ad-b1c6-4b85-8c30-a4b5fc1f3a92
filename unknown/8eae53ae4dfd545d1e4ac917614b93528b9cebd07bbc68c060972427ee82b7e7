import Jo<PERSON> from "joi";
import { SubscriptionPutRequest } from "./subscription.interfaces";
import {
	AccessTokenJoi,
	VersionJoi
} from "../base/base.joi";

export const SubscriptionPutJoi = Joi.object<SubscriptionPutRequest>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	subscriptionId: Joi.string().required(),
	priceId: Joi.string().required(),
	accountToken: Joi.string().required()
});

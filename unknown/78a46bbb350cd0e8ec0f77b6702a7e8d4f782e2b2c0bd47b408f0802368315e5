import { ClientSession } from "mongoose";
import path from "path";
import {
	APIErrorName,
	IDBALInput
} from "../../interfaces/apiTypes";
import { IShoppableVideo } from "../../modules/interactiveVideo/interactiveVideo.interface";
import { InteractiveVideoDBModel } from "../../modules/interactiveVideo/interactiveVideoDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { isAccessPermitted } from "../../utils/helpers/gp.helper";

const logTrace = path.basename(__filename);

export const readShoppableVideos = async (
	dbInput: IDBALInput
): Promise<IShoppableVideo[]> => {
	try {
		isAccessPermitted(dbInput);
		const documents = await InteractiveVideoDBModel.find(dbInput.query).sort({ createdAt: -1 })
			.session(dbInput.dbSession ?? null);

		return documents;

	} catch (error) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`Failed to read Shoppable Collections: ${error instanceof Error ? error.message : "Unknown error"}`
		);
	}
};

export const deleteShoppableVideo = async (
	dbInput: IDBALInput
): Promise<IShoppableVideo[]> => {
	return new Promise((resolve, reject) => {
		isAccessPermitted(dbInput);
		InteractiveVideoDBModel.deleteOne(
			dbInput.query,
			(error: Error, result: any) => {
				if (error) {
					const err = new Error();
					err.message = `<deleteShoppableVideo.service> | ${error.name} | ${error.message}`;
					err.name = APIErrorName.E_SERVICE_FAILED;
					reject(err);
				}
				return resolve(result);
			}
		).session(dbInput.dbSession ?? null);
	});
};

export const countShoppableVideos = async (
	query: any,
	session: ClientSession | null
): Promise<number> => {
	try {
		const count: number = await InteractiveVideoDBModel.countDocuments(
			query
		).session(session);
		return count;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > countShoppableVideos | Failed to count shoppableVideos. | ${error.name} | ${error.message}`
		);
	}
};

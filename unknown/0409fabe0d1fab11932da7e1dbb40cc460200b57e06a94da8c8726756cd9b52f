import React from "react";
import { createRoot } from "react-dom/client";
import { App } from "@player/app";
import { RecoilRoot } from "recoil";

const playerContainer = document.getElementById("player-root");
// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const root = createRoot(playerContainer!);

root.render(
	<RecoilRoot>
		<App />
	</RecoilRoot>
);

if (module.hot) {
	module.hot.accept();
}

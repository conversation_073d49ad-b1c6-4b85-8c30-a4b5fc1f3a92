import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { IAuthentication } from "../../authentication/authentication.interface";
import { AuthenticationDBModel } from "../../authentication/authenticationDBModel";
import { IAccount } from "../account.interfaces";
import { InvitationModel } from "../../invitation/invitation.model";
import { InvitationStatus } from "../../invitation/invitation.enum";
import { SignInMethod } from "../../signin/signin.interfaces";
import { SubscriptionDefaults } from "../../subscription/subscription.interfaces";
import { AccountModel } from "../account.model";
import { IInvitation } from "../../invitation/invitation.interfaces";

export class AccountUserModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countUsersByAccount (accountId: string): Promise<number> {
		const filter: FilterQuery<IAuthentication> = {
			accounts: {
				$elemMatch: {
					_id: {
						$eq: new mongoose.Types.ObjectId(accountId)
					}
				}
			},
			method: {
				$ne: SignInMethod.APIKEY
			}
		};

		const count: number = await AuthenticationDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async isUserLimitReached (account: IAccount): Promise<boolean> {
		return await this.countRemainingUsers(account._id.toString()) <= 0;
	}

	async countRemainingUsers (accountId: string): Promise<number> {
		const accountModel = new AccountModel(this.session);
		const accountDocument = await accountModel.readOneById(accountId);

		const limit = accountDocument.subscription?.maxUserLimit ?? SubscriptionDefaults.maxUserLimit;

		const filter: FilterQuery<IInvitation> = {
			accountId: new mongoose.Types.ObjectId(accountId),
			status: InvitationStatus.PENDING
		};

		const invitationModel = new InvitationModel(this.session);
		const invitationCount = await invitationModel.countDocuments(filter);

		const authUserCount = await this.countUsersByAccount(accountId);

		const totalCount = invitationCount + authUserCount;
		const count = totalCount > limit ? 0 : limit - totalCount;

		return count;
	}

	public async readOneByEmail (email: string, accountId: string): Promise<IAuthentication | null> {
		const filter: FilterQuery<IAuthentication> = {
			"data.email": email,
			accounts: {
				$elemMatch: {
					_id: {
						$eq: new mongoose.Types.ObjectId(accountId)
					}
				}
			}
		};

		const authUser = await AuthenticationDBModel.findOne(filter).session(
			this.session
		);

		return authUser;
	}
}

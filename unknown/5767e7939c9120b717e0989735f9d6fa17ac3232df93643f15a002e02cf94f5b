export interface ISliderConfig {
	accountId: string;
	collectionId: string | undefined;
	hideVanityBranding?: boolean;
	isShareMode: boolean;
}

export interface ISliderVideoPlayer {
	videoId: string;
	videoState: "unloaded" | "loaded";
	totalSeconds: number;
	playedPercent: number;
	playedSeconds: number;
	audioState: boolean;
	playId: string;
}


export interface ISliderVideoProgress {
	totalSeconds: number;
	playedPercent: number;
	playedSeconds: number;
}

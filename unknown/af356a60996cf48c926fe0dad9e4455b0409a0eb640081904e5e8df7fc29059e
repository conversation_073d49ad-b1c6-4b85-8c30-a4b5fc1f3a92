import { LocaleAPI } from "../../interfaces/apiTypes";
import {
	EventAppIds,
	EventNames
} from "../events/events.enums";
import { IShoppableCollection } from "../interactiveCollection/interactiveCollection.interface";
import { InteractiveCollectionDBModel } from "../interactiveCollection/interactiveCollectionDB.model";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { SignUpModel } from "../signup/signup.model";
import { EventBuffer } from "./eventBuffer.interfaces";
import { EventBufferModel } from "./eventBuffer.model";
import { EventBufferDBModel } from "./eventBufferDB.model";

describe("EventBufferModel", () => {
	let accountId: string;
	let collectionId: string;

	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld"
		};

		const signupModel = new SignUpModel(null);
		const signupResult = await signupModel.createFromEmail({
			email: signupEmailPayload.email,
			callbackEndpoint: signupEmailPayload.callbackEndpoint,
			locale: signupEmailPayload.locale
		});

		expect(signupResult).toHaveProperty("authenticationDoc");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts.0");

		const account = signupResult.authenticationDoc.accounts[0];
		accountId = account._id.toString();

		const collection: IShoppableCollection[] = await InteractiveCollectionDBModel.find({ accountId: accountId });
		collectionId = collection[0]._id.toString();
	});

	it("should create a new event in the buffer", async () => {
		const eventBufferModel = new EventBufferModel();
		const result = await eventBufferModel.createOne({
			accountId: accountId,
			eventName: EventNames.SNIPPET_IMPRESSION,
			appId: EventAppIds.GP_PLAYER,
			eventDimensions: {
				collectionId: collectionId
			}
		});

		expect(result).toHaveProperty("_id");
		expect(result).toHaveProperty("attempts", 0);
		expect(result).toHaveProperty("buffer");
		expect(result).toHaveProperty("createdAt");

		let eventBuffer: EventBuffer | null = await EventBufferDBModel.findById(result._id);
		if (!eventBuffer) {
			throw new Error("Event not found in the buffer");
		}

		expect(eventBuffer).toBeTruthy();
		expect(eventBuffer).toHaveProperty("_id");
		expect(eventBuffer).toHaveProperty("attempts", 0);
		expect(eventBuffer).toHaveProperty("buffer");
		expect(eventBuffer).toHaveProperty("createdAt");

		expect(eventBuffer._id).toEqual(result._id);
		expect(eventBuffer.buffer).toEqual(result.buffer);
		expect(eventBuffer.createdAt).toEqual(result.createdAt);

		const payload = JSON.parse(eventBuffer.buffer.toString());
		expect(payload).toHaveProperty("accountId", accountId);
		expect(payload).toHaveProperty("eventName", EventNames.SNIPPET_IMPRESSION);
		expect(payload).toHaveProperty("appId", EventAppIds.GP_PLAYER);

		await EventBufferDBModel.deleteOne({ _id: result._id });
		eventBuffer = await EventBufferDBModel.findById(eventBuffer._id);
		expect(eventBuffer).toBeNull();
	});
});

import {
	IDBALInput,
	Permission,
	APIErrorName,
	AdminLinkType
} from "../../interfaces/apiTypes";
import fs from "fs";
import util from "util";
import path from "path";
import {
	gpLog,
	LogScope
} from "../managers/gpLog.manager";

export const isAccessPermitted = (dbInput: IDBALInput): boolean => {
	if (dbInput.permissions?.includes(Permission.ALL)) {
		return true;
	}
	const err = new Error();
	err.message = "Access permission denied";
	err.name = APIErrorName.E_DATABASE_FAILURE;
	throw err;

};

interface ILinkBuilder {
	linkType: AdminLinkType;
	baseUrl: string;
	token: string;
}

export const buildAdminLink = ({ linkType, baseUrl, token }: ILinkBuilder):string => {
	let fullURL = baseUrl + linkType;

	if (fullURL && token) {
		const params = baseUrl.includes("?")
			? `&token=${token}`
			: `?token=${token}`;
		fullURL = fullURL + params;
	}
	return fullURL;
};

export const validateBackslash = (url: string): string => {
	if (url[url.length - 1] === "/") {
		return url;
	}
	return url + "/";

};

export enum CDN_DIR {
	MEDIA = "media/",
	DATACACHE = "data-cache/",
	VIDEOS = "videos/",
	POSTERS = "posters/",
	GIFS = "gifs/",
}

export const getBucketFilename = (urls: string[], host: string): string[] => {
	const hostUrl = validateBackslash(host);
	return urls
		.map((url) => {
			if (url.startsWith(hostUrl)) {
				return url.substring(hostUrl.length);
			}
		})
		.filter((url) => url !== undefined) as string[];
};

const writeFileAsync = util.promisify(fs.writeFile);
const mkdirAsync = util.promisify(fs.mkdir);
const accessAsync = util.promisify(fs.access);

export const createEmptyFile = async (filePath: string): Promise<void> => {
	try {
		await accessAsync(filePath, fs.constants.F_OK);
	} catch (err: any) {
		if (err.code === "ENOENT") {
			const dirPath = path.dirname(filePath);
			await mkdirAsync(dirPath, { recursive: true });
			await writeFileAsync(filePath, "");
		} else {
			throw err;
		}
	}
};

export enum LocalPathType {
	STORAGE = "STORAGE",
	SECRETS = "SECRETS"
}

export const getLocalPath = (type: LocalPathType): string | undefined => {
	try {
		let envPath: string | undefined;
		let defaultPath: string;

		if (type === LocalPathType.STORAGE) {
			envPath = process.env.VOLUME_STORAGE_PATH as string;
			defaultPath = "/storage/";
		} else if (type === LocalPathType.SECRETS) {
			envPath = process.env.VOLUME_SECRETS_PATH as string;
			defaultPath = "/secrets/";
		} else {
			return undefined;
		}

		if (fs.existsSync(envPath)) {
			// when running node app standalone
			return validateBackslash(envPath);
		} else if (fs.existsSync(defaultPath)) {
			// when running node app through docker framework
			return defaultPath;
		}
		return undefined;

	} catch (error: any) {
		gpLog({
			message: "Failed to read local path.",
			objData: { error: error?.message, pathType: type },
			trace: "gp.helper | getLocalPath",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

export const isLastFileChunk = (contentRange: string): boolean | undefined => {
	try {
		const contentRangeRegex = /bytes (\d+)-(\d+)\/(\d+)/;
		const match = contentRange.match(contentRangeRegex);

		if (match) {
			const end = parseInt(match[2], 10);
			const total = parseInt(match[3], 10);

			if (end + 1 >= total) {
				return true;
			}
			return false;

		}
		throw new Error(
			"The provided contentRange does not match the proper [bytes start-end/total] template"
		);

	} catch (error: any) {
		gpLog({
			message: "Failed to process contentRange.",
			objData: { error: error?.message },
			trace: "gp.helper | isLastFileChunk",
			scope: LogScope.ERROR
		});
		throw new Error(`Failed to process contentRange. error: ${error?.message}`);
	}
};

export const SUPPORTED_VIDEO_FORMATS = [
	"video/mp4",
	"video/avi",
	"video/x-msvideo",
	"video/x-ms-asf",
	"video/quicktime"
];

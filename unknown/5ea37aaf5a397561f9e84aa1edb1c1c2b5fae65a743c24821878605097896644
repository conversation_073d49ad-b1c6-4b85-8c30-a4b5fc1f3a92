import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import mongoose from "mongoose";
import { InteractiveCollectionModel } from "./interactiveCollection.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IGetShoppableCollectionPayload } from "./interactiveCollection.interface";
import { aggregateShoppableCollection } from "../../services/mongodb/shoppableCollection.service";

export const getCollectionsController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}


		const payload = req.query as unknown as IGetShoppableCollectionPayload;
		const defaultSortKey = "createdAt";
		const finalSortKey = payload.sortKey || defaultSortKey;
		const finalSortBy = payload.SortBy === "dsc" ? -1 : 1;

		const pipeline: any[] = [
			{
				$match: {
					accountId: new mongoose.Types.ObjectId(req.accountToken.account._id)
				}
			},
			{ $sort: { [finalSortKey]: finalSortBy } }
		];
		if (payload.limit) {
			pipeline.push({ $limit: parseInt(payload.limit) });
		}

		const docs = await aggregateShoppableCollection(pipeline, null);

		if (!docs) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to read shoppable collections");
		}

		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		if (Number(req.headers["x-api-version"]) >= 3) {
			const totalDocuments = await interactiveCollectionModel.countDocuments({
				accountId: req.accountToken.account._id
			});

			const result = {
				documents: await interactiveCollectionModel.redactData(docs),
				totalDocuments: totalDocuments
			};
			return res.send(result);
		}

		return res.send(await interactiveCollectionModel.redactData(docs));
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

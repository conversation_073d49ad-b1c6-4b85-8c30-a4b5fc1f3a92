import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { AuthenticationModel } from "./authentication.model";
import {
	ApikeyAuthenticationData,
	OIDCAuthenticationData,
	UserAuthenticationData
} from "./authentication.interface";
import { APIError } from "../../utils/helpers/apiError";
import { AuthorizationServerMock } from "../oidc/__mocks__/oidc.authorization.server";
import { AccountModel } from "../account/account.model";
import { AccountCreateData } from "../account/account.interfaces";
import { UserModel } from "../user/user.model";

describe("AuthenticationModel Tests", () => {
	let testHelper: TestHelper;

	const expressApp = createServer();
	initExpressRoutes(expressApp);
	beforeAll(async () => {
		testHelper = new TestHelper(expressApp);
	});


	it("readOneByEmail() email exists", async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		await testHelper.signupEmail(signupEmailPayload);

		const authmodel = new AuthenticationModel(null);
		const authDoc = await authmodel.readOneByEmail(signupEmailPayload.email);
		expect(authDoc).toBeTruthy();

		const authData = authDoc.data as UserAuthenticationData;
		expect(authData.email).toBeTruthy();
		expect(authData.passwordHash).toBeTruthy();
		expect(authData.email).toBe(signupEmailPayload.email);
	});

	it("readOneByEmail() throws E_DOCUMENT_NOT_FOUND when email does not exist", async () => {
		const authmodel = new AuthenticationModel(null);

		await expect(authmodel.readOneByEmail("bogus email"))
			.rejects.toThrowError(
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found")
			);
	});

	it("readOneById() id exists", async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);

		const authentication = await testHelper.getAuthentication(accessToken);

		const authmodel = new AuthenticationModel(null);
		const authDoc = await authmodel.readOneById(authentication._id.toString());
		expect(authDoc).toBeTruthy();

		const authData = authDoc.data as UserAuthenticationData;
		expect(authData.email).toBeTruthy();
		expect(authData.passwordHash).toBeTruthy();
		expect(authData.email).toBe(signupEmailPayload.email);
	});

	it("readOneById() throws APIErrorName.E_SERVICE_FAILED when id does not conform", async () => {
		const authmodel = new AuthenticationModel(null);

		await expect(authmodel.readOneById("bogus id"))
			.rejects.toThrowError(
				new APIError(
					APIErrorName.E_SERVICE_FAILED,
					"Argument passed in must be a string of 12 bytes or a string of 24 hex characters or an integer"
				)
			);
	});

	it("readOneById() throws APIErrorName.E_DOCUMENT_NOT_FOUND when id does not exist", async () => {
		const authmodel = new AuthenticationModel(null);

		await expect(authmodel.readOneById("123456789012"))
			.rejects.toThrowError(
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication document not found")
			);
	});

	it("exists() when it exists AuthMethods.EMAIL_PASSWORD", async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const authentication = await testHelper.getAuthentication(accessToken);

		const authData = authentication.data as UserAuthenticationData;

		const authmodel = new AuthenticationModel(null);
		const itExists = await authmodel.exists(authData);

		expect(itExists).toBe(true);
	});

	it("exists() when it exists AuthMethods.OIDC", async () => {
		const oidcClientId = "client-id-1";
		const email = "<EMAIL>";
		const { idToken, accessToken } = AuthorizationServerMock.generateTokens(oidcClientId, "subject", email);

		const res = await supertest(expressApp)
			.post("/api/oauth/oidc")
			.set("x-api-version", "1")
			.field("oidcIdToken", idToken)
			.field("oidcAccessToken", accessToken)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(201);
		expect(res.body).toHaveProperty("accessToken");

		const oidcAccessToken = res.body.accessToken;
		const authentication = await testHelper.getAuthentication(oidcAccessToken);

		const authData = authentication.data as OIDCAuthenticationData;

		const authmodel = new AuthenticationModel(null);
		const itExists = await authmodel.exists(authData);

		expect(itExists).toBe(true);
	});

	it("exists() when it exists AuthMethods.APIKEY", async () => {
		const email = "<EMAIL>";
		const signupEmailPayload: ISignupEmailPayload = {
			email: email,
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const account = await testHelper.getAccount(accessToken);
		const accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);

		const res = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		const authenticationId = res.body.key.authenticationId ;

		const authModel = new AuthenticationModel(null);
		const authentication = await authModel.readOneById(authenticationId);

		const authData = authentication.data as ApikeyAuthenticationData;

		const authmodel = new AuthenticationModel(null);
		const itExists = await authmodel.exists(authData);

		expect(itExists).toBe(true);
	});

	it("delete() id exists", async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const authentication = await testHelper.getAuthentication(accessToken);

		const authData = authentication.data as UserAuthenticationData;

		const authmodel = new AuthenticationModel(null);
		const itExists = await authmodel.exists(authData);

		expect(itExists).toBe(true);

		await authmodel.delete(authentication._id.toString());
		const itExistsPostDelete = await authmodel.exists(authData);

		expect(itExistsPostDelete).toBe(false);
	});

	/**
	 * After creating a user and default account, attach a second account to the
	 * IAuthentication and then detach it.
	 */
	it("attachAccountByUserId() and detachAccountByUserId()", async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);

		const userId = testHelper.getUserId(accessToken);
		const userModel = new UserModel(null);
		const user = await userModel.readOneById(userId);
		const accountModel = new AccountModel(null);
		const accountCreateData: AccountCreateData = {
			companyName: "detachaccountbyuserid account 2",
			companyURL: "doesnotmatter.tld",
			user: user
		};
		const account2 = await accountModel.create(accountCreateData);

		let authentication;
		const authmodel = new AuthenticationModel(null);

		authentication = await authmodel.attachAccountByUserId(userId, account2._id.toString());
		const countPostAttach = authentication.accounts.filter((a) => a._id.toString() == account2._id.toString());
		expect(countPostAttach.length).toBe(1);

		authentication = await authmodel.detachAccountByUserId(userId, account2._id.toString());
		const countPostDetach = authentication.accounts.filter((a) => a._id.toString() == account2._id.toString());
		expect(countPostDetach.length).toBe(0);
	});
});

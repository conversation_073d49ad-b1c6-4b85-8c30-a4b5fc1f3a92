import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { AccountDBModel } from "../modules/account/accountDB.model";
import { IAccount } from "../modules/account/account.interfaces";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";

const createUserPayload = {
	firstName: "John3",
	lastName: "Doe3",
	email: "<EMAIL>",
	password: "Password1!",
	companyName: "Test Co.3",
	locale: LocaleAPI.EN_US,
	callbackEndpoint: "https://domain.tld/callback",
	legalAgreement: true
} as ISignupPayload;

describe("GET /users failure mode testing special case", () => {
	let accessToken: string;
	let accountToken: string;
	let accountId: string;
	let expressApp: express.Express;
	let account: IAccount;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		({ accessToken, accountToken, account } = await testHelper.createUserAndAccount(createUserPayload));
		accountId = account._id.toString();
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] if the account doesn't exist", async () => {
		const query = {
			_id: accountId
		};

		// delete the account to produce 404
		await AccountDBModel.deleteOne(query).session(null);

		// check to make sure it is deleted
		const document: IAccount | null = await AccountDBModel.findOne(query).session(null);
		expect(document).toBeNull();

		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});
});

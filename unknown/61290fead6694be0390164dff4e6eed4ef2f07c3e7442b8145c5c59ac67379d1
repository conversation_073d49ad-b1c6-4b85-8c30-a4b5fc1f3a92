import mongoose, { Schema } from "mongoose";
import { IMetricUserEngagement } from "./metricUserEngagement.interfaces";

const MetricUserEngagementSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	collectionId: { type: Schema.Types.ObjectId, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	userSessionId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		required: true
	}
});

export const MetricUserEngagementDBModel = mongoose.model<IMetricUserEngagement>(
	"metric_user_engagements",
	MetricUserEngagementSchema
);

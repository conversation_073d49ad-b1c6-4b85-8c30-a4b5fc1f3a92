import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { randomBytes } from "crypto";
import { AccountModel } from "../../modules/account/account.model";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";

import * as MetricUserEngagementService from "../../services/mongodb/metricUserEngagement.service";


describe("POST /metrics/conversion", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountId: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "post.metric.conversion.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();

		const accountModel = new AccountModel(null);
		const updateAccount = {
			"subscription.enableConversionMetrics": true
		};
		const updatedAccount = await accountModel.updateOneById(account._id.toString(), updateAccount);
		expect(updatedAccount.subscription.enableConversionMetrics).toBe(true);
	});

	it("[OK]. Should return 201", async () => {
		const userSessionCount = 10;
		jest.spyOn(MetricUserEngagementService, "aggregateMetricUserEngagement").mockResolvedValueOnce(
			[{ uniqueSessions: userSessionCount }]
		);

		const collectionModel = new InteractiveCollectionModel(null);
		const defaultCollectionBefore = await collectionModel.readOneById(account.defaultCollectionId.toString());

		const mockSession = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", accountId)
			.field("userSessionId", mockSession)
			.field("orderItemsCount", 10)
			.field("collectionIds[]", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(201);
		const defaultCollectionAfter = await collectionModel.readOneById(account.defaultCollectionId.toString());

		expect(defaultCollectionAfter.orderCount).toBe(defaultCollectionBefore.orderCount + 1);
		expect(defaultCollectionAfter.userCVR).toBe((defaultCollectionBefore.orderCount + 1 / userSessionCount) * 100);
		expect(defaultCollectionAfter.userSessionCount).toBe(userSessionCount);
	});

	it("[E_INVALID_INPUT]. Should return 400. missing x-api-version header", async () => {
		const mockSession = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.field("accountId", accountId)
			.field("userSessionId", mockSession)
			.field("orderItemsCount", 10)
			.field("collectionIds[]", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400. missing userSessionId", async () => {
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", accountId)
			.field("orderItemsCount", 10)
			.field("collectionIds[]", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. return 400 - collectionIds needs to be array", async () => {
		const mockSession = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", accountId)
			.field("orderItemsCount", 10)
			.field("userSessionId", mockSession)
			.field("collectionIds", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});


	it("[E_MISSING_ACCOUNT_ID]. Should return 500. missing account", async () => {
		const mockSession = randomBytes(12).toString("hex");
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", mockId)
			.field("orderItemsCount", 10)
			.field("userSessionId", mockSession)
			.field("collectionIds[]", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_ACCOUNT_ID);
	});

	it("[E_REQUEST_FORBIDDEN] return 500. enableConversionMetrics = false ",	async () => {
		const accountModel = new AccountModel(null);
		const setEnableConversionMetricsFalse = {
			"subscription.enableConversionMetrics": false
		};
		const updatedAccountFalse = await accountModel.updateOneById(
			account._id.toString(),
			setEnableConversionMetricsFalse);

		expect(updatedAccountFalse.subscription.enableConversionMetrics).toBe(false);

		const mockSession = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", accountId)
			.field("orderItemsCount", 10)
			.field("userSessionId", mockSession)
			.field("collectionIds[]", account.defaultCollectionId.toString());

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);

		const setEnableConversionMetricsTrue = {
			"subscription.enableConversionMetrics": true
		};
		const updatedAccountTrue = await accountModel.updateOneById(
			account._id.toString(),
			setEnableConversionMetricsTrue);
		expect(updatedAccountTrue.subscription.enableConversionMetrics).toBe(true);
	});

	it("return 200. Ignore error If cant find collection in collectionIds[].", async () => {
		const mockSession = randomBytes(12).toString("hex");
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.post("/api/metrics/conversion")
			.set("x-api-version", "1")
			.field("accountId", accountId)
			.field("orderItemsCount", 10)
			.field("userSessionId", mockSession)
			.field("collectionIds[]", mockId);

		expect(res.statusCode).toBe(201);
	});
});

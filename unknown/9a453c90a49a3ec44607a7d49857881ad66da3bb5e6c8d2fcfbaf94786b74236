import axios from "axios";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

export interface ISendEmail {
	host: string;
	path: string;
	apiKey: string;
	data: any;
}

export const sendEmail = async (input: ISendEmail): Promise<boolean | undefined> => {
	try {
		const { host, path, data, apiKey } = input;
		const url = host + path;
		const response = await axios.post(url, data, {
			headers: {
				Authorization: `Bearer ${apiKey}`,
				"Content-Type": "application/json"
			}
		});
		return response.status === 202;
	} catch (error: any) {
		gpLog({
			message: "Failed to send Email",
			objData: {
				error: error.message
			},
			trace: "email.service | sendEmail",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

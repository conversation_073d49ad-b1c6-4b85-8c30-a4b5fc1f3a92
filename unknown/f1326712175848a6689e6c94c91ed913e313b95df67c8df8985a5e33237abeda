import mongoose from "mongoose";
import { EventProcessorModel } from "./eventProcessor.model";
import {
	formatMongoURIFromSecrets,
	initMongoDB
} from "../../mongo";
import { workerData } from "worker_threads";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import { APIError } from "../../utils/helpers/apiError";

const process = async (): Promise<void> => {
	try {
		await initMongoDB(formatMongoURIFromSecrets(workerData.secrets));
		const session = await mongoose.startSession();
		const eventProcessorModel = new EventProcessorModel();
		await eventProcessorModel.processBuffer(session, 1000, 10);
		session.endSession();
	} catch (error: unknown) {
		gpLog({
			message: "Error in events worker thread",
			scope: LogScope.ERROR,
			objData: APIError.fromUnknownError(error)
		});
	} finally {
		setTimeout(() => {
			process();
		}, 1000);
	}
};

process();

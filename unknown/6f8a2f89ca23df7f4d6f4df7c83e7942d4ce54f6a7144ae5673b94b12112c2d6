import Jo<PERSON> from "joi";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IResetPasswordPayload } from "../../modules/resetPassword/resetpassword.interfaces";

const passwordSchema = Joi.string().min(8).messages({
	"string.min": APIErrorName.E_PASSWORD_COMPLEXITY
});

export const resetPasswordSchema = {
	data: Joi.object<IResetPasswordPayload>({
		password: passwordSchema.required(),
		token: Joi.string()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

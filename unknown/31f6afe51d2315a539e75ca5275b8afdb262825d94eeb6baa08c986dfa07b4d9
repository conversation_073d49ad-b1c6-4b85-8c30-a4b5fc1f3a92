import mongoose, { Schema } from "mongoose";
import { IMetricInteractiveVideoCreated } from "./metricInteractiveVideo.interface";

const MetricInteractiveVideoCreatedSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	interactiveVideoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		default: () => new Date()
	}
}
);

export const MetricInteractiveVideoCreatedDBModel = mongoose.model<IMetricInteractiveVideoCreated>(
	"metric_interactive_video_created",
	MetricInteractiveVideoCreatedSchema,
	"metric_interactive_video_created"
);

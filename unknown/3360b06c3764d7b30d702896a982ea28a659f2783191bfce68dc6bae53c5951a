import express, {
	type Request,
	type Response
} from "express";
import { StripeWebhookModel } from "./webhook.model";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../../services/mongodb/transaction.service";

export class StripeWebhookController extends Controller {
	constructor() {
		super();

		this.router.post(
			"/",
			express.raw({ type: "application/json" }),
			async (request: Request, response: Response) => {
				try {
					await startDBTransaction(response.locals.session);

					const signature: string = request.headers["stripe-signature"] as string;
					const payload: string = request.body;

					const stripeModel = new StripeWebhookModel();
					const event = await stripeModel.constructEvent(signature, payload);

					await stripeModel.handleEvent(event);

					await completeDBTransaction(response.locals.session);

					response.json({ received: true });
				} catch (error: unknown) {
					await cancelDBTransaction(response.locals.session);
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}
}

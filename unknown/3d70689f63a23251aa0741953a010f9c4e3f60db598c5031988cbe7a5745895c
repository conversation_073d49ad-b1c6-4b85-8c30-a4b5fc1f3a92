import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { InteractiveCollectionModel } from "./interactiveCollection.model";
import {
	APIErrorName,
	IDBALInput,
	Permission
} from "../../interfaces/apiTypes";
import { readShoppableCollections } from "../../services/mongodb/shoppableCollection.service";

export const getCollectionController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version"
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version"
			);
		}

		if (!req.accountToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header"
			);
		}

		const input: IDBALInput = {
			query: { _id: req.params.collectionId },
			path: req.url,
			permissions: [Permission.ALL]
		};

		const docs = await readShoppableCollections(input);

		if (docs.length < 1) {
			throw new APIError(
				APIErrorName.E_DOCUMENT_NOT_FOUND,
				`Failed to read shoppable collection: ${req.params.collectionId}`
			);
		}

		if (docs[0].accountId.toString() != req.accountToken.account._id) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Failed to authenticate");
		}
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const result = {
			shoppableCollection: (await interactiveCollectionModel.redactData([docs[0]]))[0]
		};

		return res.send(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

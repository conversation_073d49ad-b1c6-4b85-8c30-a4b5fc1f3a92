import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricUserEngagementDBModel } from "./metricUserEnagementDB.model";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { IMetricUserEngagement } from "./metricUserEngagement.interfaces";

export class MetricUserEngagementModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IMetricUserEngagement>): Promise<number> {
		const count: number = await MetricUserEngagementDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async createOne (createData: any, account: IAccount): Promise<IMetricUserEngagement> {
		try {
			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new MetricUserEngagementDBModel({
				...createData,
				accountId: account._id
			}).save(options);

			return newDocument;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, error);
		}
	}

	async readManyByAccountId(accountId: string): Promise<IMetricUserEngagement[]> {
		try {
			const filter: FilterQuery<IMetricUserEngagement> = {
				accountId: accountId
			};

			const documents: IMetricUserEngagement[] = await MetricUserEngagementDBModel.find(filter).session(
				this.session
			);

			return documents;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, error);
		}
	}
}

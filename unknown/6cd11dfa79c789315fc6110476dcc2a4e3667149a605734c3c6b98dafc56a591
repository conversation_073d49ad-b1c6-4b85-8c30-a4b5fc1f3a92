import { StripePriceModel } from "./price.model";

describe("stripe price model", () => {
	it("retrieve a free price", async () => {
		const stripePriceModel = new StripePriceModel();
		const price = await stripePriceModel.getFreePrice();
		expect(price).toBeDefined();
		expect(price).toHaveProperty("id");
		expect(price).toHaveProperty("unit_amount");
		expect(price).toHaveProperty("currency");
		expect(price).toHaveProperty("nickname");
		expect(price).toHaveProperty("metadata");
		expect(price.metadata).toHaveProperty("gp");
		expect(price.metadata.gp).toBe("true");
	});
});

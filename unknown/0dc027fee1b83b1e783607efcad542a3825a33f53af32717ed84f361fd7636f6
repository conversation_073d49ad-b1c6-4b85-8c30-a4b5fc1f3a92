import Jo<PERSON> from "joi";
import { AuthLinkPostRequest } from "./authlink.interfaces";

export const AuthLinkPostValidator = Joi.object<AuthLinkPostRequest>({
	apiVersion: Joi.string().custom((value, helpers) => {
		if (!value) {
			return helpers.error("any.invalid");
		}

		if (isNaN(value)) {
			return helpers.error("any.invalid");
		}

		const version = Number(value);

		if (version < 1) {
			return helpers.error("any.invalid");
		}

		return version;
	}).required(),
	accessToken: Joi.string().custom((value, helpers) => {
		if (!value) {
			return helpers.error("any.invalid");
		}

		const split: string[] = value.split(" ");
		if (split.length !== 2) {
			return helpers.error("any.invalid");
		}

		const bearer: string = split[0];

		if (bearer.toLowerCase() !== "bearer") {
			return helpers.error("any.invalid");
		}

		const token: string = split[1];

		if (!token || token.length < 1) {
			return helpers.error("any.invalid");
		}

		return token;
	}).required(),
	accountToken: Joi.string().required()
});



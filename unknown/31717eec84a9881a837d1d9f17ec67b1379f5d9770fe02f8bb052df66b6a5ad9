import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { AuthenticationModel } from "../../../modules/authentication/authentication.model";
import { IAuthentication } from "../../../modules/authentication/authentication.interface";
import { APIError } from "../../../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../../../interfaces/apiTypes";
import { ISignupEmailPayload } from "../../../modules/signup/signup.interfaces";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { randomBytes } from "crypto";

import * as AccountService from "../../../services/mongodb/account.service";
import * as InvitationsService from "../../../services/mongodb/invitations.service";


describe("GET /accounts/users/:userId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let userId: string;
	const testHelper = new TestHelper(expressApp);

	const inviterSignupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	beforeAll(async () => {
		({ accessToken } = await testHelper.signupEmail(inviterSignupEmailPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		userId = testHelper.getUserId(accessToken);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_MISSING_AUTHORIZATION] for missing access token", async () => {
		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 403 [E_REQUEST_FORBIDDEN] when trying to remove self", async () => {
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${userId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] for missing account token", async () => {
		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the account document cannot be found", async () => {
		jest.spyOn(AccountService, "readAccount2").mockResolvedValueOnce(null);

		const fakeId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${fakeId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 403 [E_REQUEST_FORBIDDEN] when trying to delete the account owner from the account", async () => {
		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${userId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when authentication document does not exist during update",
		async () => {

			jest.spyOn(AuthenticationModel.prototype, "detachAccountByUserId").mockRejectedValueOnce(
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"Authenticated user does not have accountId attached.")
			);

			const fakeId = randomBytes(12).toString("hex");

			const res = await supertest(expressApp)
				.delete(`/api/accounts/users/${fakeId}`)
				.set("Authorization", `Bearer ${accessToken}`)
				.set("x-account-token", accountToken)
				.set("x-api-version", "2");

			expect(res.statusCode).toBe(404);
			expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
		});

	it("Should return 500 [E_SERVICE_FAILED] when deleting any invitations fail", async () => {
		const friendSignupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};
		const friendTokens = await testHelper.signupEmail(friendSignupEmailPayload);
		const friendUserId = testHelper.getUserId(friendTokens.accessToken);

		const invitation = await testHelper.createInvitation(accountToken, friendSignupEmailPayload.email, accessToken);

		await testHelper.acceptInvitation(invitation._id.toString(), friendTokens.accessToken);

		jest.spyOn(InvitationsService, "deleteInvitations").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "something unexpected happened")
		);

		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${friendUserId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 200 [OK] when the authentication document has been updated", async () => {
		const friendSignupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		const friendTokens = await testHelper.signupEmail(friendSignupEmailPayload);
		const friendUserId = testHelper.getUserId(friendTokens.accessToken);

		let authentication: IAuthentication;
		const authenticationModel = new AuthenticationModel(null);
		authentication = await authenticationModel.readOneByEmail(friendSignupEmailPayload.email);
		expect(authentication.accounts.length).toBe(1);

		const invitation = await testHelper.createInvitation(accountToken, friendSignupEmailPayload.email, accessToken);
		const invitationId = invitation._id.toString();

		await testHelper.acceptInvitation(invitationId, friendTokens.accessToken);

		authentication = await authenticationModel.readOneByEmail(friendSignupEmailPayload.email);
		expect(authentication.accounts.length).toBe(2);

		const res = await supertest(expressApp)
			.delete(`/api/accounts/users/${friendUserId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});

		authentication = await authenticationModel.readOneByEmail(friendSignupEmailPayload.email);
		expect(authentication.accounts.length).toBe(1);
	});
});

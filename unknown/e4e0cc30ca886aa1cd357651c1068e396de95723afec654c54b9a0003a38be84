import { ShareObject, ShareType } from "@src/types/share";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";

export const copyHtmlToClipboard = async (shareObject: ShareObject) => {
	const linkURL = generateLinkURL(shareObject);
	const blobHtml = generateBlobHtmlContent(shareObject, linkURL);
	const blobPlain = generateBlobTextContent(shareObject, linkURL);

	const clipboardItem = new ClipboardItem({
		"text/html": blobHtml,
		"text/plain": blobPlain
	});

	await navigator.clipboard.write([clipboardItem]);

	registerEvent({
		eventName: EventNameEnum.VIDEO_EMBED_COPY_THUMBNAIL,
		videoId: shareObject.videoId
	});
};

export const copylinkURL = (shareObject: ShareObject, linkURL: string) => {
	navigator.clipboard.writeText(linkURL);

	if (shareObject.type === ShareType.SHARE_COLLECTION) {
		registerEvent({
			eventName: EventNameEnum.COLLECTION_SHARE_LINK_COPY,
			collectionId: shareObject.collectionId
		});
	} else {
		registerEvent({
			eventName: EventNameEnum.VIDEO_SHARE_LINK_COPY,
			videoId: shareObject.videoId
		});
	}
};

const generateLinkURL = (shareObject: ShareObject) => {
	const SHARE_ENDPOINT = process.env.SHARE_ENDPOINT;
	let linkURL;

	if (shareObject.type === ShareType.SHARE_COLLECTION) {
		linkURL = SHARE_ENDPOINT + "/c/" + shareObject.collectionId;
	} else {
		linkURL = SHARE_ENDPOINT + "/v/" + shareObject.videoId;
	}

	return linkURL;
};

const generateBlobHtmlContent = (shareObject: ShareObject, linkURL: string) => {
	const htmlContent = "<div><a href='" + linkURL + "'><img style='max-width:300px;' src='" + shareObject.gifURL + "' alt='GIF'></a><br><p><a href='" + linkURL + "'>" + shareObject.videoTitle + "</a></p></div>";
	const blobHtml = new Blob([htmlContent], { type: "text/html" });

	return blobHtml;
};

const generateBlobTextContent = (shareObject: ShareObject, linkURL: string) => {
	const plainTextContent = shareObject.videoTitle + " \n " + linkURL;
	const blobPlain = new Blob([plainTextContent], { type: "text/plain" });

	return blobPlain;
};

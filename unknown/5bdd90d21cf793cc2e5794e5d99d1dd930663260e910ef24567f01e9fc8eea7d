import { ClientSession } from "mongoose";
import { MetricVideoClickDBModel } from "../../modules/metricVideoClick/metricVideoClickDB.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

export const countMetricVideoClicks = async (
	query: any,
	session: ClientSession | null
): Promise<number> => {
	try {
		const count: number | null = await MetricVideoClickDBModel.countDocuments(
			query
		).session(session);
		return count;
	} catch (error: any) {
		gpLog({
			message: "Failed to count metric video clicks.",
			objData: { error: error },
			trace: "metricVideoClicks.service | countMetricVideoClicks",
			scope: LogScope.ERROR
		});

		throw APIError.fromUnknownError(error);
	}
};

import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { MetricImpressionDBModel } from "./metricImpressionDB.model";
import { IAccount } from "../account/account.interfaces";
import { AccountModel } from "../account/account.model";
import { IMetricImpression } from "./metricImpression.interfaces";

export interface MetricImpressionCreateOne{
	createdAt: Date;
	collectionShare?: mongoose.Types.ObjectId;
	videoShare?: mongoose.Types.ObjectId;
	collectionId?: mongoose.Types.ObjectId;
	videoId?: mongoose.Types.ObjectId;
}
export class MetricImpressionModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IMetricImpression>): Promise<number> {
		const count: number = await MetricImpressionDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async createOne (account: IAccount, createData: MetricImpressionCreateOne): Promise<IMetricImpression> {
		const accountModel = new AccountModel(this.session);
		await accountModel.incrementImpressionCountWithinLimit(account._id.toString(),
			account.subscription.maxImpressionsPerCycle);

		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new MetricImpressionDBModel({
			createdAt: createData.createdAt,
			accountId: account._id,
			collectionId: createData.collectionId,
			videoId: createData.videoId,
			collectionShare: createData.collectionShare,
			videoShare: createData.videoShare
		}).save(options);

		return newDocument;
	}

	async readTotalPrevDayByAccounts(accounts: IAccount[]):
	Promise<Map<string, {totalVideoImpressionsDaily: number;latestSnippetImpressionDateDaily: Date;}> | null> {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfPreviousDay = new Date(startOfToday.getTime() - (24 * 60 * 60 * 1000));
		const endOfPreviousDay = new Date(startOfToday.getTime() - 1);

		const accountIds = accounts.map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousDay, $lte: endOfPreviousDay }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalImpressionsDaily: { $sum: 1 },
					latestImpressionDateDaily: { $max: "$createdAt" }
				}
			}
		];

		const results = await MetricImpressionDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return null;
		}

		const totalImpressionsByAccountId: Map<string,
			{totalVideoImpressionsDaily: number; latestSnippetImpressionDateDaily: Date }> =
		new Map(
			results.map(item => [item._id.toString(), { totalVideoImpressionsDaily: item.totalImpressionsDaily,
				latestSnippetImpressionDateDaily: item.latestImpressionDateDaily }])
		);
		return totalImpressionsByAccountId;
	}
}

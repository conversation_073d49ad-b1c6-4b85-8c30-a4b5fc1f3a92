import React, { useRef, useEffect, RefObject } from "react";
import { Row, Col } from "react-bootstrap";
import { MainButton, LinkButton } from "@src/styles/components";
import { <PERSON><PERSON>Header, ColWithBorder } from "@src/styles/modals";
import { XmarkIcon } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { useMediaQuery } from "react-responsive";

interface Props {
	visible: boolean;
	onCancel: () => void;
}

export const HelpModal: React.FC<Props> = ({ visible, onCancel }) => {
	const translation = useTranslation();
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header="" wrapperRef={wrapperRef}>
			{!isScreenSmall ? (
				<LinkButton data-testid="closeButton" onClick={onCancel} style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={onCancel}>
					<XmarkIcon />
				</LinkButton>
			)}
			<Row pl="0" pr="0">
				<Col sm="12" md="6" className="mb-2 text-center">
					<ModalHeader data-testid="ModalHeaderHelpCentre">{translation.modals.helpCentre}</ModalHeader>
					<div className="mt-3 mb-2">{translation.modals.helpCentreText}</div>
					<div>
						<MainButton type="button" onClick={() => window.open(translation.modals.helpCentreLink, "_blank")} className="mx-auto mt-3" data-testid="helpCentreButton">
							{translation.modals.helpCentre}
						</MainButton>
					</div>
				</Col>
				<ColWithBorder sm="12" md="6" className="mb-2 text-center">
					<ModalHeader data-testid="ModalHeaderContactUs">{translation.modals.contactUs}</ModalHeader>
					<div className="mt-3 mb-2">{translation.modals.contactUsText}</div>
					<div>
						<MainButton type="button" onClick={() => (window.location.href = "mailto:" + translation.general.helpEmail + "?subject=" + translation.general.emailSubject)} className="mx-auto mt-3" data-testid="contactUsButton">
							{translation.modals.contactUs}
						</MainButton>
					</div>
				</ColWithBorder>
			</Row>
		</BaseModal>
	);
};

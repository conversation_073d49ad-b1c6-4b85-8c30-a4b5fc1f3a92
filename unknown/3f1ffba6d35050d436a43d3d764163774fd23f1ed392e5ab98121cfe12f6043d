import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { IAuthentication } from "../../../modules/authentication/authentication.interface";
import {
	APIErrorName,
	LocaleAPI
} from "../../../interfaces/apiTypes";
import { ISignupEmailPayload } from "../../../modules/signup/signup.interfaces";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { AuthenticationDBModel } from "../../../modules/authentication/authenticationDBModel";
import { FilterQuery } from "mongoose";
import { SignInMethod } from "../../../modules/signin/signin.interfaces";
import { IAccount } from "../../../modules/account/account.interfaces";

const expressApp = createServer();
initExpressRoutes(expressApp);


describe("GET /accounts/super", () => {
	let superAccessToken: string;
	let nonSuperAccessToken: string;
	const testHelper = new TestHelper(expressApp);

	const signupEmailPayloadSUPER: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	beforeAll(async () => {
		// create non super user
		({ accessToken: nonSuperAccessToken } = await testHelper.signupEmail(signupEmailPayload));

		// create super user
		const tokens = await testHelper.signupEmail(signupEmailPayloadSUPER);

		const authentication = await testHelper.getAuthentication(tokens.accessToken);
		const filter: FilterQuery<IAuthentication> = {
			_id: authentication._id
		};
		const update: Partial<IAuthentication> = {
			super: true
		};
		await AuthenticationDBModel.findOneAndUpdate(filter, update, {
			new: true
		});

		// generate a new accessToken based on modified Authentication as super
		const signingRes = await supertest(expressApp)
			.post("/api/auth/sign-in")
			.set("x-api-version", "2")
			.send({
				method: SignInMethod.REFRESH_TOKEN,
				token: tokens.refreshToken
			});

		superAccessToken = signingRes.body.accessToken;
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super")
			.set("Authorization", `Bearer ${superAccessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super")
			.set("Authorization", `Bearer ${superAccessToken}`)
			.set("x-api-version", "0");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super")
			.set("Authorization", `Bearer ${superAccessToken}`)
			.set("x-api-version", "x");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 400 [E_MISSING_AUTHORIZATION] for missing access token", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super")
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 403 [E_REQUEST_FORBIDDEN] when the user is not a super", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super")
			.set("Authorization", `Bearer ${nonSuperAccessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should return 200 [OK] when company name starts with latter, 'S' in this case", async () => {
		const res = await supertest(expressApp)
			.get("/api/accounts/super?search=s&searchType=SEARCH_PREFIX")
			.set("Authorization", `Bearer ${superAccessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("accounts");

		const accounts: IAccount[] = res.body.accounts;

		// the api call above will return one account starts with S
		// so depending on the order of tests run there should always be 1 or more
		expect(accounts.length).toBeGreaterThan(0);
	});
});

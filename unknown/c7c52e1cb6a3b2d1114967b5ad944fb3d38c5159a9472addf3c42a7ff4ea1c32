import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";


const createUserPayload = {
	firstName: "John",
	lastName: "get.users.positive.mode.tests",
	email: "<EMAIL>",
	password: "Password1!",
	companyName: "get.users.positive.mode.tests Co.",
	locale: LocaleAPI.EN_US,
	callbackEndpoint: "https://domain.tld/callback",
	legalAgreement: true
} as ISignupPayload;

describe("GET /users", () => {
	let accessToken: string;
	let accountToken: string;
	let expressApp: express.Express;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const testHelper = new TestHelper(expressApp);
		({ accessToken, accountToken } = await testHelper.createUserAndAccount(createUserPayload));
	});

	it("return 200 [OK] with one user documents", async () => {
		const res = await supertest(expressApp)
			.get("/api/users")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("users");
		expect(res.body.users.length).toEqual(1);
		const user = res.body.users[0];
		expect(user.firstName).toEqual(createUserPayload.firstName);
		expect(user.lastName).toEqual(createUserPayload.lastName);
		expect(user.email).toEqual(createUserPayload.email);
	});
});

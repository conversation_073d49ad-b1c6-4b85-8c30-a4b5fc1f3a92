import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";

import * as EmailService from "../services/email/email.service";


describe("POST /auth/forgot-password", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "forgot.password.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		await testHelper.signup(createUserPayload);
	});


	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "1")
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("[E_EMAIL_SIGN_IN_NO_EMAIL]. Should return 404", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "1")
			.send({
				email: "<EMAIL>",
				method: "email/password",
				callbackEndpoint: "https://domain.tld/reset-password"
			});
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_EMAIL_SIGN_IN_NO_EMAIL);
	});

	it("[E_SERVICE_FAILED]. Should return 500", async () => {
		jest.spyOn(EmailService, "sendTransactionalEmail").mockResolvedValueOnce(undefined);

		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				method: "email/password",
				callbackEndpoint: "https://domain.tld/reset-password"
			});
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("[Successfully sent reset password email]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "1")
			.send({
				email: createUserPayload.email,
				method: "email/password",
				callbackEndpoint: "https://domain.tld/reset-password"
			});
		expect(res.statusCode).toBe(200);
		expect(res.body).toEqual({});
	});
});

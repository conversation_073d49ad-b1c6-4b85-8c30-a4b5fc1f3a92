import supertest from "supertest";
import mongoose from "mongoose";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { createVideo } from "../../services/mongodb/video.service";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { IVideo } from "../../modules/video/video.interfaces";
import { UserModel } from "../../modules/user/user.model";
import { APIError } from "../../utils/helpers/apiError";
import { randomBytes } from "crypto";

import * as AccountService from "../../services/mongodb/account.service";
import TestHelper from "../mocks/testHelper";


describe("DELETE /videos/:videoId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let accountId: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.video.file.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		accountId = account._id.toString();
	});


	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - missing account", async () => {
		jest.spyOn(AccountService, "readAccount2").mockResolvedValueOnce(null);

		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 -  missing user", async () => {
		jest.spyOn(UserModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - missing video", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/videos/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_RESOURCE_CONFLICT]. Should return 409 - collection reference to video	", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());

		await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.delete(`/api/videos/${newVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(409);
		expect(res.body.error).toEqual(APIErrorName.E_RESOURCE_CONFLICT);
	});

	it("[OK]. Should return 200 - VideoStatus.ENCODED", async () => {
		const uniqueNumber = randomBytes(12).toString("hex");
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: `http://example.tld/example-${uniqueNumber}.mp4`,
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);
		const videoId = video._id.toString();


		const res = await supertest(expressApp)
			.delete(`/api/videos/${videoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);

		const videoDeleted: IVideo = res.body.video;
		expect(videoDeleted._id.toString()).toEqual(videoId);
	});



	it("[OK]. Should return 200 - VideoStatus.PROCESSING", async () => {
		const uniqueNumber = randomBytes(12).toString("hex");
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: `http://example.tld/example-${uniqueNumber}.mp4`,
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);
		const videoId = video._id.toString();

		const res = await supertest(expressApp)
			.delete(`/api/videos/${videoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);

		const videoDeleted: IVideo = res.body.video;
		expect(videoDeleted._id.toString()).toEqual(videoId);
	});

	it("[OK]. Should return 200 - VideoStatus.FAILED", async () => {
		const uniqueNumber = randomBytes(12).toString("hex");
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: `http://example.tld/example-${uniqueNumber}.mp4`,
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);
		const videoId = video._id.toString();

		const res = await supertest(expressApp)
			.delete(`/api/videos/${videoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
	});
});

import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import { deleteAssetsFromCloud } from "../../services/gp/bucket.service";
import { APIError } from "../../utils/helpers/apiError";
import { CDN_DIR } from "../../utils/helpers/gp.helper";

export const deleteFilesController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.accountToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header"
			);
		}

		const filename = req.params.filename;
		const accountId = req.accountToken.account._id;
		const filenameToBeDeleted = `${CDN_DIR.MEDIA}${accountId}/${filename}`;

		await deleteAssetsFromCloud([filenameToBeDeleted]);

		return res.status(200).json({});
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

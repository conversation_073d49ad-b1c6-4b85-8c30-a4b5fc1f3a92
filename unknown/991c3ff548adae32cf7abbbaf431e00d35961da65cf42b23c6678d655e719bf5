import React, { useState } from "react";
import ImageSelect from "./ImageSelect";
import { useTranslation } from "../hooks/translations";
import { CarouselImage, OnSiteImage, WidgetImage } from "@src/assets";
import { SnippetOptions, SnippetDisplayMode } from "@src/types/snippetOptions";
import { CarouselItem, ImgCarouselLeftArrow, ImgCarouselRightArrow } from "@src/styles/components";

interface Props {
	setSelectedOption: (opt: SnippetOptions) => void;
	selectedOption: SnippetOptions;
	displayTypeValue: SnippetDisplayMode;
}

interface Option {
	text: string;
	url: string;
	option: SnippetOptions;
}

const ImageSelectCarousel: React.FC<Props> = ({ setSelectedOption, selectedOption, displayTypeValue }) => {
	const translation = useTranslation();
	const defaultOptions = [
		{ text: translation.modals.snippetOpt1, url: CarouselImage, option: SnippetOptions.CAROUSEL },
		{ text: translation.modals.snippetOpt2, url: WidgetImage, option: SnippetOptions.WIDGET },
		{ text: translation.modals.snippetOpt3, url: OnSiteImage, option: SnippetOptions.ONSITE }
	];
	const [options, setOptions] = useState<Option[]>(defaultOptions);
	let isDefaultOrder = true;
	for (let i = 0; i < options.length; i++) {
		if (options[i].option !== defaultOptions[i].option) isDefaultOrder = false;
	}
	if (displayTypeValue === SnippetDisplayMode.ALL && !isDefaultOrder) setOptions(defaultOptions);

	const nextSlide = () => {
		const firstOpt = options[0];
		const newOptions: Option[] = options.filter((opt, index) => index !== 0);
		newOptions.push(firstOpt);
		setOptions(newOptions);
	};

	const previousSlide = () => {
		const lastOpt = options.pop();
		if (lastOpt) setOptions([lastOpt, ...options]);
	};

	return (
		<div
			style={{
				display: "flex",
				overflow: "hidden",
				justifyContent: "center"
			}}
		>
			{displayTypeValue !== SnippetDisplayMode.ALL && (
				<div onClick={previousSlide}>
					<ImgCarouselLeftArrow displayTypeValue={displayTypeValue} />
				</div>
			)}
			<CarouselItem displayTypeValue={displayTypeValue}>
				<ImageSelect displayTypeValue={displayTypeValue} imageText={options[0].text} imageURL={options[0].url} handleSelection={() => setSelectedOption(options[0].option)} selected={selectedOption === options[0].option} />
			</CarouselItem>
			{displayTypeValue !== SnippetDisplayMode.SINGLE && (
				<CarouselItem displayTypeValue={displayTypeValue}>
					<ImageSelect displayTypeValue={displayTypeValue} imageText={options[1].text} imageURL={options[1].url} handleSelection={() => setSelectedOption(options[1].option)} selected={selectedOption === options[1].option} />
				</CarouselItem>
			)}
			{displayTypeValue === SnippetDisplayMode.ALL && (
				<CarouselItem displayTypeValue={displayTypeValue}>
					<ImageSelect displayTypeValue={displayTypeValue} imageText={options[2].text} imageURL={options[2].url} handleSelection={() => setSelectedOption(options[2].option)} selected={selectedOption === options[2].option} />
				</CarouselItem>
			)}
			{displayTypeValue !== SnippetDisplayMode.ALL && (
				<div onClick={nextSlide}>
					<ImgCarouselRightArrow displayTypeValue={displayTypeValue} />
				</div>
			)}
		</div>
	);
};

export default ImageSelectCarousel;

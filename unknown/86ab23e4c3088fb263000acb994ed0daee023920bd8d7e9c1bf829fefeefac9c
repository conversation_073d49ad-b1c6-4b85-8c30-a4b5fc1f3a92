import { ClientSession } from "mongoose";
import { EventBuffer } from "../eventBuffer/eventBuffer.interfaces";
import { EventBufferDBModel } from "../eventBuffer/eventBufferDB.model";
import { EventModel } from "../events/events.model";
import { APIError } from "../../utils/helpers/apiError";

export class EventProcessorModel {
	public async processBuffer (session: ClientSession, limit: number, maxAttempts: number): Promise<number> {
		const filter = {
			$or: [
				{
					attempts: {
						$lt: maxAttempts
					}
				},
				{
					attempts: {
						$exists: false
					}
				}
			]
		};

		const cursor = EventBufferDBModel.find(filter).limit(limit).cursor();

		let updatedCount = 0;
		for await (const eventBufferDocument of cursor) {
			if (await this.processEvent(eventBufferDocument, session)) {
				updatedCount++;
			}
		}

		await this.deleteFailedBuffers(maxAttempts);

		return updatedCount;
	}

	private async deleteFailedBuffers (maxAttempts: number): Promise<void> {
		const filter = {
			attempts: {
				$gte: maxAttempts
			}
		};

		await EventBufferDBModel.deleteMany(filter);
	}

	public async processEvent (eventBuffer: EventBuffer, session: ClientSession): Promise<boolean> {
		try {
			const eventModel = new EventModel(session);
			await eventModel.createOne(eventBuffer);
			return true;
		} catch (error: unknown) {
			const apiError = APIError.fromUnknownError(error);
			apiError.setDetail({
				eventBuffer: eventBuffer
			}).log();

			if (apiError.isMongoNotConnectedError()) {
				return false;
			}

			await this.updateAttempts(eventBuffer);
			return false;
		}
	}

	private async updateAttempts (eventBuffer: EventBuffer): Promise<void> {
		await EventBufferDBModel.updateOne(
			{
				_id: eventBuffer._id
			},
			{
				$inc: {
					attempts: 1
				}
			}
		);
	}
}

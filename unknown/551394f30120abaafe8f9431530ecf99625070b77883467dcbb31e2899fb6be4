import React from "react";
import { Account } from "../../types/account";
import { EditSettingsPlanEnterprise } from "./EditSettingsPlanEnterprise";
import { EditSettingsPlanPro } from "./EditSettingsPlanPro";
import { EditSettingsPlanBasic } from "./EditSettingsPlanBasic";
import { PaymentMethod } from "../../types/payment";
import { ProductTypeEnum } from "../../types/product";

interface EditSettingsPlanProps {
	paymentMethod: PaymentMethod | null;
	accountData: Account | undefined;
	clockTime: number | undefined;
	setDisplayApPro: (value: boolean) => void;
}

export const EditSettingsPlan: React.FC<EditSettingsPlanProps> = (props: EditSettingsPlanProps) => {
	return (
		<>
			{props.accountData?.subscription.type === ProductTypeEnum.ENTERPRISE && (
				<EditSettingsPlanEnterprise accountData={props.accountData} />
			)}

			{props.accountData?.subscription.type === ProductTypeEnum.PRO && (
				<EditSettingsPlanPro
					accountData={props.accountData}
					clockTime={props.clockTime}
					paymentMethod={props.paymentMethod}
					setDisplayApPro={props.setDisplayApPro}
				/>
			)}

			{props.accountData?.subscription.type === ProductTypeEnum.BASIC && <EditSettingsPlanBasic />}
		</>
	);
};

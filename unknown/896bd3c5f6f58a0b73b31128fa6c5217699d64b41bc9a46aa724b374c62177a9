import express, {
	Request,
	Response
} from "express";

import { APIError } from "../../../utils/helpers/apiError";
import { Controller } from "../../base/base.controller";
import { BasePublicJoi } from "../../base/base.joi";
import { MetricVideoPlayTimeModel } from "../metricVideoPlayTime.model";

export class PlayTimeProcessorController extends Controller {
	constructor () {
		super();
		this.router.post("/", express.json({ limit: "2MB" }), this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		try {
			const validRequest = await BasePublicJoi.validateAsync({
				apiVersion: request.headers["x-api-version"]
			});

			await this.verifyAPIVersion(validRequest.apiVersion, 1);

			const metricVideoPlayTimeModel = new MetricVideoPlayTimeModel(null);
			const modifiedCount = await metricVideoPlayTimeModel.markPendingPlaytimeAsStopped();

			return response.status(200).send({ modifiedCount });
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).setRequest(request).log().setResponse(response);
		}
	}
}

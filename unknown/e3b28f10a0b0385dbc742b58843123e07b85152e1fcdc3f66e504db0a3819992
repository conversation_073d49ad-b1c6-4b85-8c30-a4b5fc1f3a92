/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";
import { accessToken, accountToken } from "@src/types/videos";
import { IEvent, IEventPayload } from "@src/types/events";
import { getEventDimensions, getDevice } from "./gpHelper";
import jwt_decode from "jwt-decode";
import SessionManagerSingleton from "./SessionManagerSingleton";

export const registerEvent = async (input: IEvent): Promise<any | null> => {
	try {
		const sessionManager = SessionManagerSingleton.getInstance();
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const API_VERSION = process.env.API_VERSION;

		if (API_ENDPOINT && API_VERSION) {
			let accountId = null;
			let dbUserId = null;
			const accessTokenValue = localStorage.getItem("accessToken");
			const accountToken = sessionStorage.getItem("token");

			const session = sessionManager.getSession();
			if (!session) {
				throw new Error("Session is undefined.");
			}

			if (accessTokenValue) {
				const decoded = jwt_decode(accessTokenValue) as accessToken;
				dbUserId = decoded?.userId;
			}

			if (accountToken) {
				const decoded2 = jwt_decode(accountToken) as accountToken;
				accountId = decoded2?.account?._id;
			}

			const eventPayload: IEventPayload = {
				eventName: input.eventName,
				appId: "gp-admin",
				user: {
					sessionId: session.id
				},
				device: getDevice(),
				eventDimensions: getEventDimensions()
			};

			if (dbUserId) {
				eventPayload.user.dbUserId = dbUserId;
			}

			if (accountId) {
				eventPayload.accountId = accountId;
			}

			if (input.videoId) {
				eventPayload.eventDimensions.videoId = input.videoId;
			}

			if (input.collectionId) {
				eventPayload.eventDimensions.collectionId = input.collectionId;
			}

			if (
				input.eventName === "installation_check" &&
				input?.installationUrl !== undefined &&
				input?.installationSuccess !== undefined &&
				input?.installationError !== undefined
			) {
				eventPayload.installation = {
					url: input.installationUrl,
					success: input.installationSuccess,
					error: input.installationError
				};
			}

			const response = await axios.post(`${API_ENDPOINT}/api/event/buffer`, eventPayload, {
				headers: {
					"x-api-version": API_VERSION
				}
			});
			if (response.status >= 200 && response.status < 300) {
				return response.data;
			}

			throw new Error(`registerEvent response is not ok status=${response.status}`);
		} else {
			throw new Error("Missing API_ENDPOINT.");
		}
	} catch (error: any) {
		return null;
	}
};

import React, { useEffect } from "react";
import { Container, Col } from "react-bootstrap";
import AddCompanyForm from "@src/components/authentication/AddCompanyForm";
import { backgroundImage } from "@src/assets";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Footer from "./Footer";
import ImageContentPanel from "@src/components/authentication/ImageContentPanel";
import { FlexCol, FullHeightRow } from "@src/styles/components";

const AddCompany: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.AddCompanyPage.pageTitle;
	}, [translation.AddCompanyPage.pageTitle]);

	return (
		<Container fluid>
			<FullHeightRow pl="0" pr="0">
				<Col sm="12" md={{ span: 8 }} style={{ paddingLeft: 0, paddingRight: 0 }}>
					<ImageContentPanel title={translation.AddCompanyPage.imgTitle} copy={translation.AddCompanyPage.imgCopy} backgroundUrl={backgroundImage} />
				</Col>
				<FlexCol sm="12" md={{ span: 4 }}>
					<Header auth={false} />
					<AddCompanyForm />
					<Footer />
				</FlexCol>
			</FullHeightRow>
		</Container>
	);
};

export default AddCompany;

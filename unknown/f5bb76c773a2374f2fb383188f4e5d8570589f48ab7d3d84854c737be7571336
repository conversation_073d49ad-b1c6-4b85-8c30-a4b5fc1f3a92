import React from "react";
import styled from "styled-components";

const Image = styled.img.attrs<Props>((props) => ({
	style: {
		margin: "auto",
		height: "100%",
		overflow: "hidden",
		display: "flex",
		position: "relative",
		...props.style
	}
}))`
  object-fit: cover !important;
  mask-image: -webkit-radial-gradient(white, black);
  -webkit-mask-image: -webkit-radial-gradient(white, black);
`;

interface Props {
	src: string;
	style?: React.CSSProperties;
}

export const ImageContainer = ({ src, style }: Props):JSX.Element => {
	return <Image src={src} style={{ ...style }} />;
};

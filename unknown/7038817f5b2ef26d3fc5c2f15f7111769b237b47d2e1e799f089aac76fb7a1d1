import mongoose, { Schema } from "mongoose";
import { EventBuffer } from "./eventBuffer.interfaces";

const EventBufferSchema: Schema = new Schema({
	attempts: {
		type: Number,
		default: 0,
		required: true
	},
	buffer: {
		type: Buffer,
		default: null,
		required: true
	},
	createdAt: {
		type: Date,
		default: () => new Date(),
		required: true
	}
});

EventBufferSchema.pre("save", function (next) {
	if (this.isNew) {
		this.attempts = 0;
	}
	next();
});

export const EventBufferDBModel = mongoose.model<EventBuffer>(
	"event_buffer",
	EventBufferSchema,
	"event_buffer"
);

import Jo<PERSON> from "joi";
import { BasePublicJoi } from "../base/base.joi";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	PostEventBuffer,
	PutEventBuffer
} from "./eventBuffer.interfaces";

export const PostEventBufferJoi = BasePublicJoi.append<PostEventBuffer>({
	data: Joi.object().required()
}).error(errors => {
	const messages = errors.map(err => err.message).join(", ");
	return new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, messages);
});

export const PutEventBufferJoi = BasePublicJoi.append<PutEventBuffer>({
	limit: Joi.number().integer().min(1).required()
}).error(errors => {
	const messages = errors.map(err => err.message).join(", ");
	return new APIError(APIErrorName.E_SCHEMA_VALIDATION_ERROR, messages);
});

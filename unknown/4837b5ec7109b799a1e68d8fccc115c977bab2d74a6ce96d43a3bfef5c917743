import Joi from "joi";
import { BaseAccessJoi } from "../base/base.joi";
import { BaseAccessRequest } from "../base/base.interfaces";
import {
	LevelEnum,
	pixelFormatEnum,
	CRFEnum,
	PresetEnum,
	AudioBitrateEnum
} from "./videoProfile.enum";

export interface VideoProfileCreateOnePayload extends BaseAccessRequest {
	name: string;
	default: boolean;
	level: LevelEnum;
	pixelFormat: pixelFormatEnum;
	constantRateFactor: CRFEnum;
	preset: PresetEnum;
	audioBitrate: AudioBitrateEnum;
	scale: number;
}
export const VideoProfileCreateOnePayloadSchema = BaseAccessJoi.append<VideoProfileCreateOnePayload>({
	name: Joi.string().min(1).required(),
	default: Joi.boolean().required(),
	level: Joi.string().valid(...Object.values(LevelEnum)).required(),
	pixelFormat: Joi.string().valid(...Object.values(pixelFormatEnum)).required(),
	constantRateFactor: Joi.number().valid(...Object.values(CRFEnum)).required(),
	preset: Joi.string().valid(...Object.values(PresetEnum)).required(),
	audioBitrate: Joi.string().valid(...Object.values(AudioBitrateEnum)).required(),
	scale: Joi.number().min(1).required()
});

export interface VideoProfileUpdateOnePayload extends BaseAccessRequest {
	videoProfileId: string;
	name?: string;
	default?: boolean;
	level?: LevelEnum;
	pixelFormat?: pixelFormatEnum;
	constantRateFactor?: CRFEnum;
	preset?: PresetEnum;
	audioBitrate?: AudioBitrateEnum;
	scale?: number;
}
export const VideoProfileUpdateOnePayloadSchema = BaseAccessJoi.append<VideoProfileUpdateOnePayload>({
	videoProfileId: Joi.string().hex().length(24).required(),
	name: Joi.string().min(1),
	default: Joi.boolean(),
	level: Joi.string().valid(...Object.values(LevelEnum)),
	pixelFormat: Joi.string().valid(...Object.values(pixelFormatEnum)),
	constantRateFactor: Joi.number().valid(...Object.values(CRFEnum)),
	preset: Joi.string().valid(...Object.values(PresetEnum)),
	audioBitrate: Joi.string().valid(...Object.values(AudioBitrateEnum)),
	scale: Joi.number().min(1)
});

export interface VideoProfileGetDeleteOnePayload extends BaseAccessRequest {
	videoProfileId: string;
}
export const VideoProfileGetDeleteOnePayloadSchema = BaseAccessJoi.append<VideoProfileGetDeleteOnePayload>({
	videoProfileId: Joi.string().hex().length(24).required()
});

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import mongoose from "mongoose";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { AccountModel } from "../../modules/account/account.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IGetShoppableVideoPayload,
	IShoppableVideo
} from "../../modules/interactiveVideo/interactiveVideo.interface";

export const getVideosController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		const payload = req.query as unknown as IGetShoppableVideoPayload;
		const defaultSortKey = "createdAt";
		const finalSortKey = payload.sortKey || defaultSortKey;
		const finalSortBy = payload.SortBy === "dsc" ? -1 : 1;

		const pipeline: any[] = [
			{
				$match: {
					accountId: new mongoose.Types.ObjectId(
						req.accountToken.account._id
					)
				}
			},
			{ $sort: { [finalSortKey]: finalSortBy } }
		];
		if (payload.limit) {
			pipeline.push({ $limit: parseInt(payload.limit) });
		}

		const interactiveVideoModel = new InteractiveVideoModel(null);
		const videoDocs: IShoppableVideo[] = await interactiveVideoModel.readManyByAccountId(
			{
				accountId: req.accountToken.account._id,
				sortKey: payload.sortKey,
				sortBy: payload.SortBy,
				limit: parseInt(payload.limit)
			}
		);

		const accountModel = new AccountModel(null);
		const accountDocument = await accountModel.readOneById(req.accountToken.account._id);

		if (accountDocument.subscription.enableEngagementMetrics !== true) {
			for (const video of videoDocs) {
				video.playPercentCount20 = 0;
				video.playPercentCount40 = 0;
				video.playPercentCount60 = 0;
				video.playPercentCount80 = 0;
				video.playPercentCount100 = 0;
				video.videoScore = 0;
				video.videoUniquePlayCount = 0;
				video.videoUniquePlayDurationSeconds = 0;
			}
		}

		if (Number(req.headers["x-api-version"]) >= 3) {
			const totalDocuments = await interactiveVideoModel.countDocuments({
				accountId: req.accountToken.account._id
			});

			const result = {
				documents: (await interactiveVideoModel.redactData(videoDocs)),
				totalDocuments: totalDocuments
			};
			return res.send(result);
		}

		const result = { shoppableVideos: (await interactiveVideoModel.redactData(videoDocs)) };
		return res.send(result);
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

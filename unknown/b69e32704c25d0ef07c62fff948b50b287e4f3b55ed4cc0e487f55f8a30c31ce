import React, { useState, useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";
import { shoppableVideo } from "@src/types/videos";

interface Props {
	video: shoppableVideo;
	visible: boolean;
	onCancel: () => void;
	videos: shoppableVideo[];
	setVideos: (value: shoppableVideo[]) => void;
}

export const DeleteCollectionVideo: React.FC<Props> = ({ video, visible, onCancel, videos, setVideos }) => {
	const translation = useTranslation();
	const [loading, setLoading] = useState(false);

	const handleDelete = () => {
		setLoading(true);
		const index = videos.indexOf(video);
		if (index >= 0) {
			const temp = [...videos];
			temp.splice(index, 1);
			setVideos(temp);
		}
		onCancel();
		setLoading(false);
	};

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header={translation.modals.removeVideo} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.removeCollectionVideo}</ModalText>
			<>
				<MainButton type="button" onClick={() => handleDelete()} className="mx-auto mt-3" data-testid="ModalConfirmButton" disabled={loading}>
					{translation.general.remove}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink">
					{translation.general.cancel}
				</ThirdButton>
			</>
		</BaseModal>
	);
};

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { IVideo } from "../../modules/video/video.interfaces";
import * as AccountService from "../../services/mongodb/account.service";
import * as VideoService from "../../services/mongodb/video.service";
import { UserModel } from "../../modules/user/user.model";
import { APIError } from "../../utils/helpers/apiError";


describe("GET /videos/:videoId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let newVideoId: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.video.file.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		const newVideo = await testHelper.createVideo(account._id.toString());
		newVideoId = newVideo._id.toString();
	});

	it("[OK]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.get(`/api/videos/${newVideoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		const video: IVideo = res.body.video;

		expect(video._id.toString()).toBe(newVideoId);
		expect(video.accountId.toString()).toBe(account._id.toString());
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - account not found", async () => {
		jest.spyOn(AccountService, "readAccount2").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.get(`/api/videos/${newVideoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - user not found", async () => {
		jest.spyOn(UserModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.get(`/api/videos/${newVideoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - video does not exist", async () => {
		jest.spyOn(VideoService, "readVideo").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.get(`/api/videos/${newVideoId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});
});

/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
	testEnvironment: "node",
	testMatch: ["**/**/*.test.ts"],
	modulePathIgnorePatterns: ["<rootDir>/dist/"],
	collectCoverageFrom: [
		"<rootDir>/src/**/*.{js,ts}",
		"!<rootDir>/src/__tests__/mocks/**/*.ts"
	],
	transform: {
		"\\.[jt]sx?$": [
			"ts-jest",
			{
				"isolatedModules": true
			}
		]
	},
	maxWorkers: "50%",
	setupFilesAfterEnv: [
		"./src/__tests__/mocks/setup.mock.gpLog.ts",
		"./src/__tests__/mocks/setupBeforeAndAfterAll.ts",
		"./src/__tests__/mocks/fetch.mock.ts"
	],
	bail: true,
	verbose: true,
	globalSetup: "./src/__tests__/mocks/globalSetup.ts",
	globalTeardown: "./src/__tests__/mocks/globalTeardown.ts"
};

# Autoplay Video Platform

A comprehensive video platform solution consisting of multiple components working together to provide an interactive video experience.

## Components

### Server (`/server`)
The backend service that powers the platform, built with Node.js, TypeScript, and Express. It handles:
- MongoDB database operations
- Event processing and metrics
- Stripe integration
- SendGrid email services
- GCP Cloud Run deployment

### Player App (`/player`)
An interactive video platform that enhances online site experiences with interactive video collections. Built with:
- React and TypeScript
- Styled-Components
- Jest for testing
- CloudFlare worker integration

### Admin Interface (`/admin`)
The administrative interface for managing the platform's content and settings.

### Docker Framework (`/docker-framework`)
Containerized deployment solution that orchestrates all platform components using Docker Compose.

## Getting Started

### Prerequisites

- Node.js (LTS Version)
- Docker Engine (version supporting compose 3.8 or higher)
- Docker Compose
- Git

### Detailed Installation Steps

1. **Create Volume Directories**
   ```bash
   # Create a folder for persistent data
   mkdir -p ap-volumes/secrets
   mkdir -p ap-volumes/storage
   ```

2. **<PERSON>lone and Setup Repository**
   ```bash
   git clone https://github.com/autoplayvideo/ap-platform
   cd ap-platform
   ```

3. **Server Setup**
   ```bash
   cd server
   npm install
   cp .env.sample .env
   cp secrets.sample.json ../ap-volumes/secrets/ap.json
   ```

4. **Admin Interface Setup**
   ```bash
   cd ../admin
   npm install
   cp .env.development.sample .env.development
   npm run build
   ```

5. **Player App Setup**
   ```bash
   cd ../player
   npm install
   npm run build
   cp .env.sample .env
   ```

6. **Docker Framework Configuration**
   ```bash
   cd ../docker-framework
   cp .env.sample .env
   ```

7. **Configure Environment Variables**
   - In `docker-framework/.env`:
     - Set `VOLUME_PATH` to your `ap-volumes` location
     - Set `ADMIN_PATH` to `ap-platform/admin/dist`
     - Set `PLAYER_PATH` to `ap-platform/player/dist`
     - Set `SERVER_PATH` to `ap-platform/server/dist`
   - In `player/.env`:
     - Configure appropriate ENDPOINTS (see player/.env.sample)
   - In `server/.env`:
     - Set `VOLUME_SECRETS_PATH` to `ap-volumes/secrets`
     - Set `VOLUME_STORAGE_PATH` to `ap-volumes/storage`
   - In `admin/.env.development`
	 - Add Stripe publishable key

8. **Copy Assets**
	Copy the `setup/cdn` contents into the `ap-volumes/storage` location.	

9. **Setup Cloudflare Worker
   ```
   cd player/cloudflare-worker
   npm install
   cp wrangler.toml.sample wrangler.toml
   npm start
   ```

10. **Start the Platform**
   ```bash
   cd docker-framework
   docker compose build
   docker compose up
   ```

### Service URLs
- Server: http://localhost:5004
- Player App: http://localhost:5003
- Admin Interface: http://localhost:5002
- SendGrid Email Mock: http://localhost:5005
- MongoDB GUI: Use Studio 3T or MongoDB Compass
  - Server: `localhost`
  - Port: `27017`
  - Username: `mongoroot`
  - Password: `password`
  - Authentication DB: `admin`

### Stripe Webhook Setup
```bash
# Login to Stripe CLI
stripe login

# Start webhook forwarding
stripe listen --forward-to localhost:5004/api/stripe/webhook
```
Add the webhook signing secret (starting with `whsec_`) to your `secrets.stripe.webhookSecret` in the server configuration.

### Testing

#### Server Testing
```bash
cd server
npm test
```

#### Player Testing
```bash
cd player
npm test
```

#### Admin Testing
```bash
cd admin
npm test
```

## Architecture

The platform consists of several interconnected services:
- Frontend applications (Player and Admin)
- Backend server with API endpoints
- MongoDB database
- CloudFlare worker for routing
- SendGrid for email services
- Stripe for payment processing
- GCP Cloud Storage for media assets

### Event Processing
Events are processed through the following flow:
1. Events are submitted to `POST api/event/buffer`
2. A scheduled task triggers `POST api/event/processor`
3. Events are processed and added to appropriate collections
4. Failed events are retried with a maximum attempt limit

## Contributing

Please follow the coding conventions and development guidelines outlined in each component's README file.

## Support

For technical support or questions, please refer to the documentation in each component's directory or contact the development team.


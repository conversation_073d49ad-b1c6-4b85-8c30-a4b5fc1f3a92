import {
	initPostMsgListener,
	initVideoSnippetData
} from "@player/core/core.controller";
import { onReceiveMessageAtCore } from "@player/core/core.message";
import * as coreService from "@player/core/core.service";
jest.mock("@player/core/core.service");

const setupMockDOM = (): void => {
	document.body.innerHTML = `
    <div data-gp-type="carousel" data-gp-collection="c1"></div>
    <div data-gp-type="widget" data-gp-collection="c1"></div>
    <div data-gp-type="onsite" data-gp-collection="c1"></div>
    `;
};

describe("Core Script - controller", () => {

	beforeEach(async () => {
		jest.clearAllMocks();
		jest.resetModules();
		jest.spyOn(console, "warn").mockImplementation(jest.fn());
		jest.spyOn(console, "error").mockImplementation(jest.fn());
	});

	afterAll(() => {
		jest.resetAllMocks();
	});

	it("[initPostMsgListener] should initialize post message listener.", () => {
		const addEventListenerSpy = jest.spyOn(window, "addEventListener");
		initPostMsgListener();
		expect(addEventListenerSpy).toHaveBeenCalledTimes(1);
		expect(addEventListenerSpy).toHaveBeenCalledWith(
			"message",
			onReceiveMessageAtCore,
			false
		);
	});

	it("[initVideoSnippetData] should output an error.", async () => {
		const originalPlayerEndpoint = process.env.GP_PLAYER_ENDPOINT;
		delete process.env.GP_PLAYER_ENDPOINT;
		jest.spyOn(console, "error").mockImplementation(jest.fn());
		await initVideoSnippetData();
		expect(console.error).toHaveBeenCalledWith(
			"initVideoSnippetData | Error: PLAYER_ENDPOINT is undefined."
		);
		process.env.GP_PLAYER_ENDPOINT = originalPlayerEndpoint;
	});

	it("[initVideoSnippetData] should fail to find interactive collection.", async () => {
		setupMockDOM();
		const apiErrorMsg = "Failed to fetch data.";
		(coreService.fetchSnippetData as jest.Mock).mockImplementationOnce(
			(_, callback) => {
				callback(undefined);
				throw new Error(apiErrorMsg);
			}
		);
		await initVideoSnippetData();
		expect(coreService.fetchSnippetData).toHaveBeenCalled();
		expect(console.error).toHaveBeenCalledWith(
			`initVideoSnippetData | Error: ${apiErrorMsg}`
		);
	});

});

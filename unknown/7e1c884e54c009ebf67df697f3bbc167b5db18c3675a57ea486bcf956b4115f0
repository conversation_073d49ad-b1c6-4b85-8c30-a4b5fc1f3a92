import Stripe from "stripe";
import { getSecrets } from "../../secrets/secrets.model";

export class StripePricesModel {
	constructor(public prices: Stripe.Price[] = []) {}

	public async getPricesByProductId(productId: string): Promise<Stripe.Price[]> {
		const secrets = await getSecrets();
		const stripe = new Stripe(secrets.stripe.privateKey);

		const stripePrices = await stripe.prices.list({
			active: true,
			product: productId
		});

		stripePrices.data = this.filterPrices(stripePrices.data);
		this.prices = stripePrices.data;

		return this.prices;
	}

	private filterPrices (stripePrices: Stripe.Price[]): Stripe.Price[] {
		return stripePrices.filter((price) => {
			return price.metadata.gp?.toLocaleLowerCase().trim() === "true";
		});
	}
}

import React, { useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { SignOutModal } from "../modals/SignOutModal";
import { HelpModal } from "../modals/HelpModal";
import { useNavigate } from "react-router-dom";
import { accountToken } from "@src/types/videos";
import jwt_decode from "jwt-decode";
import { FlexEnd, HeaderDiv, RightSide, LinkButton, CompanyImageHeader } from "@src/styles/components";
import { HelpIcon } from "@src/styles/forms";
import { Account } from "@src/types/account";
import { HeaderPlan } from "./HeaderPlan";

interface Props {
	auth: boolean;
	accountData?: Account | undefined;
	showAllOptions?: boolean;
	saveChanges?: boolean;
	setChangesModal?: (value: boolean) => void;
	setNavigationUrl?: (value: string) => void;
	refetch?: boolean;
	trialActive?: boolean;
}

const Header: React.FC<Props> = ({
	auth,
	accountData,
	showAllOptions,
	saveChanges,
	setChangesModal,
	setNavigationUrl,
	refetch,
	trialActive
}) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [helpModalStatus, setHelpModalStatus] = useState(false);
	const [companyImage, setCompanyImage] = useState("");
	const [companyName, setCompanyName] = useState("");
	const showHelpCentre = process.env.SHOW_HELP_CENTRE === "true";

	const handleNavigation = () => {
		if (setChangesModal && setNavigationUrl && saveChanges) {
			setNavigationUrl("/company");
			setChangesModal(true);
		} else {
			navigate("/company");
		}
	};

	useEffect(() => {
		const accountToken = sessionStorage.getItem("token");
		if (accountToken) {
			const decoded = jwt_decode(accountToken) as accountToken;
			if (decoded?.account?.companyLogo) {
				setCompanyImage(decoded.account.companyLogo);
			}
			if (decoded?.account?.companyName) {
				setCompanyName(decoded.account.companyName);
			}
		}
	}, [refetch]);

	if (auth) {
		return (
			<>
				<HeaderDiv>
					<RightSide>
						<HeaderPlan trialActive={trialActive} accountData={accountData} />
						<LinkButton
							data-testid="companyButton"
							onClick={() => handleNavigation()}
							style={{ marginRight: "1rem", fontSize: "14px" }}
						>
							{companyImage && (
								<CompanyImageHeader data-testid="companyImage" src={companyImage} style={{ marginRight: "1rem" }} />
							)}
							{companyName}
						</LinkButton>
						{showAllOptions && (
							<>
								{showHelpCentre && (
									<LinkButton
										data-testid="helpButton"
										onClick={() => setHelpModalStatus(true)}
										style={{ marginRight: "1rem", fontSize: "14px" }}
									>
										<HelpIcon /> {translation.general.help}
									</LinkButton>
								)}
								<LinkButton
									data-testid="SignOut"
									onClick={() => setModalStatus(true)}
									style={{ fontSize: "14px" }}
								>
									{translation.general.signOut}
								</LinkButton>
							</>
						)}
					</RightSide>
				</HeaderDiv>
				<HelpModal visible={helpModalStatus} onCancel={() => setHelpModalStatus(false)} />
				<SignOutModal visible={modalStatus} onCancel={() => setModalStatus(false)} />
			</>
		);
	}

	return showHelpCentre ? (
		<FlexEnd>
			<LinkButton data-testid="helpButton" onClick={() => setHelpModalStatus(true)} style={{ marginTop: "1rem" }}>
				<HelpIcon /> {translation.general.help}
			</LinkButton>
			<HelpModal visible={helpModalStatus} onCancel={() => setHelpModalStatus(false)} />
		</FlexEnd>
	) : <FlexEnd />;
};

export default Header;

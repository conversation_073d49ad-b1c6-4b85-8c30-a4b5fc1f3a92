import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import {
	VideoProfile,
	VideoProfileCreate,
	VideoProfileUpdate
} from "./videoProfile.interface";
import { VideoProfileDBModel } from "./videoProfile.db.model";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { VideoDBModel } from "../video/videoDB.model";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../services/mongodb/transaction.service";

export class VideoProfileModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	public async readOneById(videoProfileId: string): Promise<VideoProfile> {
		const filter: FilterQuery<VideoProfile> = {
			_id: new mongoose.Types.ObjectId(videoProfileId)
		};
		const document = await VideoProfileDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "VideoProfile not found.").suppressLog();
		}
		return document;
	}

	public async readDefault(): Promise<VideoProfile> {
		const filter: FilterQuery<VideoProfile> = {
			default: true
		};
		const document = await VideoProfileDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Default VideoProfile not found.");
		}
		return document;
	}

	public async readAll(): Promise<VideoProfile[]> {
		const documents = await VideoProfileDBModel.find({}).sort({ name: 1 }).session(this.session);
		return documents;
	}

	private async countAllProfiles(): Promise<number> {
		return VideoProfileDBModel.countDocuments({}).session(this.session);
	}

	private async unsetDefaultFromOthers(): Promise<void> {
		const filter: FilterQuery<VideoProfile> = {
			default: true
		};
		const update: mongoose.UpdateQuery<VideoProfile> = {
			$set: { default: false }
		};
		const options: mongoose.QueryOptions = {
			session: this.session
		};
		await VideoProfileDBModel.updateMany(filter, update, options);
	}

	public async doesNameExist(name: string): Promise<boolean> {
		const existingProfile = await VideoProfileDBModel.exists({ name }).session(this.session);
		return !!existingProfile;
	}

	public async createOne(profileData: VideoProfileCreate): Promise<VideoProfile> {
		await startDBTransaction(this.session);
		try {
			if (await this.doesNameExist(profileData.name)) {
				throw new APIError(APIErrorName.E_VIDEO_PROFILE_EXISTS,
					"A VideoProfile with this name already exists.").suppressLog();
			}
			const totalProfiles = await this.countAllProfiles();
			if (totalProfiles === 0) {
				profileData.default = true;
			}
			if (totalProfiles > 0 && profileData.default) {
				await this.unsetDefaultFromOthers();
			}
			const document = await new VideoProfileDBModel(profileData).save({ session: this.session });
			await completeDBTransaction(this.session);
			return document;
		} catch (error: unknown) {
			await cancelDBTransaction(this.session);
			throw error;
		}
	}

	public async updateOneById(_id: string, profileData: VideoProfileUpdate): Promise<VideoProfile> {
		await startDBTransaction(this.session);
		try {
			const isReferenced = await VideoDBModel.exists({ videoProfile: _id }).session(this.session);
			if (isReferenced) {
				throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
					"videoProfile cannot be updated because it is referenced by existing videos.").suppressLog();
			}
			if (profileData.name){
				const existingProfile =
				await VideoProfileDBModel.findOne({ name: profileData.name }).session(this.session);
				if (existingProfile && existingProfile._id.toString() !== _id) {
					throw new APIError(APIErrorName.E_VIDEO_PROFILE_EXISTS,
						"A VideoProfile with this name already exists.").suppressLog();
				}
			}

			if (profileData.default === true) {
				await this.unsetDefaultFromOthers();
			}
			const filter: FilterQuery<VideoProfile> = {
				_id: new mongoose.Types.ObjectId(_id)
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};
			const updatedDocument = await VideoProfileDBModel.findOneAndUpdate(filter, profileData, options);
			if (!updatedDocument) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "VideoProfile not found.").suppressLog();
			}
			await completeDBTransaction(this.session);
			return updatedDocument;
		} catch (error: unknown) {
			await cancelDBTransaction(this.session);
			throw error;
		}
	}

	public async deleteOneById(_id: string): Promise<void> {
		const profile = await this.readOneById(_id);
		if (profile.default) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Cannot delete a default videoProfile.").suppressLog();
		}
		const totalProfiles = await this.countAllProfiles();
		if (totalProfiles === 1) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"Cannot delete the only existing videoProfile.").suppressLog();
		}

		const isReferenced =
        await VideoDBModel.exists({ videoProfile: new mongoose.Types.ObjectId(_id) }).session(this.session);
		if (isReferenced) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"Cannot delete a videoProfile referenced by existing videos.").suppressLog();
		}

		await VideoProfileDBModel.deleteOne({ _id: profile._id }).session(this.session);
	}
}

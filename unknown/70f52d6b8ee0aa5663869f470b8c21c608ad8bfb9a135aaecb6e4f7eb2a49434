import { LocaleAPI } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../signup/signup.interfaces";
import { InteractiveVideoModel } from "./interactiveVideo.model";
import TestHelper from "../../__tests__/mocks/testHelper";
import { IAccount } from "../account/account.interfaces";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { IShoppableVideo } from "./interactiveVideo.interface";


describe("InteractiveVideoModel tests", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);
	const interactiveVideoModel = new InteractiveVideoModel(null);

	let first: IShoppableVideo;
	let second: IShoppableVideo;
	let third: IShoppableVideo;
	let fourth: IShoppableVideo;

	const createUserPayload = {
		firstName: "<PERSON>",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "interactive.video.model.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);

		const newVideo = await testHelper.createVideo(account._id.toString());

		first = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		second = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		third = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		fourth = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);
	});

	it("aggregate > all params", async () => {
		const limit = 3;

		const interactiveVideos = await interactiveVideoModel.readManyByAccountId(
			{
				accountId: account._id.toString(),
				sortKey: "createdAt",
				sortBy: "dsc",
				limit: limit
			}
		);

		expect(interactiveVideos.length).toBe(limit);

		expect(interactiveVideos[0]._id.toString()).toBe(fourth._id.toString());
		expect(interactiveVideos[1]._id.toString()).toBe(third._id.toString());
		expect(interactiveVideos[2]._id.toString()).toBe(second._id.toString());
	});

	it("aggregate > only account", async () => {
		const interactiveVideos = await interactiveVideoModel.readManyByAccountId(
			{
				accountId: account._id.toString()
			}
		);

		expect(interactiveVideos.length).toBe(4);

		expect(interactiveVideos[0]._id.toString()).toBe(first._id.toString());
		expect(interactiveVideos[1]._id.toString()).toBe(second._id.toString());
		expect(interactiveVideos[2]._id.toString()).toBe(third._id.toString());
		expect(interactiveVideos[3]._id.toString()).toBe(fourth._id.toString());
	});

	it("aggregate > only account and limit", async () => {
		const interactiveVideos = await interactiveVideoModel.readManyByAccountId(
			{
				accountId: account._id.toString(),
				limit: 1
			}
		);

		expect(interactiveVideos.length).toBe(1);
		expect(interactiveVideos[0]._id.toString()).toBe(first._id.toString());
	});
});

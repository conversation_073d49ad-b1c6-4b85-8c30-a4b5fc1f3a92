import mongoose, { Schema } from "mongoose";
import { IMetricConversion } from "../../modules/metricConversion/metricConversion.interfaces";

const MetricConversionSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	userSessionId: { type: Schema.Types.ObjectId, required: true },
	orderItemsCount: { type: Schema.Types.Number, required: true },
	createdAt: {
		type: Schema.Types.Date,
		default: () => new Date()
	}
});

export const MetricConversionDBModel = mongoose.model<IMetricConversion>(
	"metric_conversions",
	MetricConversionSchema
);

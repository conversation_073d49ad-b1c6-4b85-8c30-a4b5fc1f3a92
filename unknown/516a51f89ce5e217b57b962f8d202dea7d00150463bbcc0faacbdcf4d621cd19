import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../../modules/signup/signup.interfaces";
import TestHelper from "../../../__tests__/mocks/testHelper";


describe("POST /api/auth/link", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	let accessToken: string;
	let accountToken: string;

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "authlink.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("should fail when an x-api-version header is not greater or equal to 1", async (): Promise<void> => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "0")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 400) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(400);
		expect(res.body).toHaveProperty("error");
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body.error).toBe("E_INVALID_INPUT");
		expect(res.body.errorRef).toHaveLength(13);
	});

	it("should fail when an access token is not provided as a Authorization Bearer header", async (): Promise<void> => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 400) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(400);
		expect(res.body).toHaveProperty("error");
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body.error).toBe("E_INVALID_INPUT");
		expect(res.body.errorRef).toHaveLength(13);
	});

	it("should fail when a provided Authorization Bearer access token is invalid", async (): Promise<void> => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("Authorization", "Bearer invalid")
			.set("x-account-token", accountToken)
			.send();

		if (res.statusCode !== 401) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(401);
		expect(res.body).toHaveProperty("error");
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body.error).toBe("E_INVALID_AUTHORIZATION");
		expect(res.body.errorRef).toHaveLength(13);
	});

	it("should fail when an account token is not provided as a x-account-token header", async (): Promise<void> => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("Authorization", `Bearer ${accessToken}`)
			.send();

		if (res.statusCode !== 400) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(400);
		expect(res.body).toHaveProperty("error");
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body.error).toBe("E_INVALID_INPUT");
		expect(res.body.errorRef).toHaveLength(13);
	});

	it("should fail when a provided x-account-token is invalid", async (): Promise<void> => {
		const res = await supertest(expressApp)
			.post("/api/auth/link")
			.set("x-api-version", "1")
			.set("x-account-token", "token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", "invalid")
			.send();

		expect(res.statusCode).toBe(401);
		expect(res.body).toHaveProperty("error");
		expect(res.body).toHaveProperty("errorRef");
		expect(res.body.error).toBe("E_INVALID_AUTHORIZATION");
		expect(res.body.errorRef).toHaveLength(13);
	});
});

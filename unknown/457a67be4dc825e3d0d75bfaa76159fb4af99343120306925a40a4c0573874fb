import mongoose, { Schema } from "mongoose";
import { IMetricVideoShareCreated } from "./metricVideoShare.interface";

const MetricVideoShareCreatedSchema: Schema = new Schema({
	accountId: { type: Schema.Types.ObjectId, required: true },
	videoId: { type: Schema.Types.ObjectId, required: true },
	createdAt: {
		type: Schema.Types.Date,
		default: () => new Date()
	}
}
);

export const MetricVideoShareCreatedDBModel = mongoose.model<IMetricVideoShareCreated>(
	"video_share_link_copy_metric",
	MetricVideoShareCreatedSchema,
	"video_share_link_copy_metric"
);

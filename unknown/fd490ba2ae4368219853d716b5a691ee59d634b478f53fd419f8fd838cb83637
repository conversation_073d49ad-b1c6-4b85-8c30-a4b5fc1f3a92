import {
	ClientSession,
	FilterQuery,
	UpdateQuery,
	QueryOptions
} from "mongoose";
import { MetricVideoEngagementDBModel } from "./metricVideoEngagement.db.model";
import { MetricVideoEngagement } from "./metricVideoEngagement.interface";
import { IShoppableVideo } from "../interactiveVideo/interactiveVideo.interface";


export class MetricVideoEngagementModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async upsert(interactiveVideo: IShoppableVideo, createdAt: Date): Promise<MetricVideoEngagement> {
		const startOfToday =
		new Date(Date.UTC(createdAt.getUTCFullYear(), createdAt.getUTCMonth(), createdAt.getUTCDate()));
		const endOfToday = new Date(startOfToday.getTime() + (24 * 60 * 60 * 1000));

		const filter: FilterQuery<MetricVideoEngagement> = {
			videoId: interactiveVideo._id,
			createdAt: { $gte: startOfToday, $lt: endOfToday }
		};

		const updateData: UpdateQuery<MetricVideoEngagement> = {
			$set: {
				updatedAt: createdAt,
				playPercentCount20: interactiveVideo.playPercentCount20,
				playPercentCount40: interactiveVideo.playPercentCount40,
				playPercentCount60: interactiveVideo.playPercentCount60,
				playPercentCount80: interactiveVideo.playPercentCount80,
				playPercentCount100: interactiveVideo.playPercentCount100,
				videoScore: interactiveVideo.videoScore
			},
			$setOnInsert: {
				createdAt: createdAt,
				videoId: interactiveVideo._id
			}
		};

		const options: QueryOptions = {
			session: this.session,
			upsert: true,
			new: true
		};

		const updatedDocument = await MetricVideoEngagementDBModel.findOneAndUpdate(filter, updateData, options);
		if (!updatedDocument) {
			throw new Error("Failed to upsert metric_video_engagement document");
		}
		return updatedDocument;
	}
}

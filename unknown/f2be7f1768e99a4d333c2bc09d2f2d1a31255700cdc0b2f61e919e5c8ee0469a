import {
	Request,
	Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { BaseRequest } from "../../base/base.interfaces";
import { BaseJoi } from "../../base/base.joi";
import { AccountModel } from "../../account/account.model";
import { VideoJobModel } from "../job/video.job.model";

export class VideoJobController extends Controller {
	constructor() {
		super();
		this.router.get("/", this.get.bind(this));
	}

	private async get(request: Request, response: Response): Promise<Response> {
		let validPayload: BaseRequest;

		try {
			validPayload = await BaseJoi.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await this.verifyAccessToken(validPayload.accessToken);
			const accountToken = await this.verifyAccountToken(validPayload.accountToken);

			const accountModel = new AccountModel(null);
			const account = await accountModel.readOneById(accountToken.account._id);

			const videoJobModel = new VideoJobModel(null);
			const jobs = await videoJobModel.readProcessingVideosByAccountId(account._id.toString());

			return response.status(200).json({ jobs });

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}
}

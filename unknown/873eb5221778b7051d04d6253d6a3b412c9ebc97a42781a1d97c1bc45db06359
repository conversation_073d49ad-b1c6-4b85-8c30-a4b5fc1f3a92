import React, {
	useRef,
	useEffect,
	useState,
	useCallback
} from "react";
import styled from "styled-components/macro";
import { Box } from "@player/components/Box";
import {
	IVideoData,
	SnippetTypeEnum
} from "@player/app";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";

interface IWrapperProps {
	width: string;
	height: string;
}

const PlayerWrapper = styled(Box)<IWrapperProps>`
	position: relative;
	max-width: ${(props): string => props.width + "px"};
	width: 100%;
	aspect-ratio: ${(props): string => `${props.width} / ${props.height}`};
	overflow: hidden;
`;

const PlayerIFrame = styled.iframe`
	width: 100%;
	height: 100%;
	border: 0px;
	border-radius: ${(props): string => props.theme.inlineBorderRadius + "px"};
`;

interface Props {
	appUrl: string;
	videoData: IVideoData;
	playerWidth: string;
	playerHeight: string;
}

export const Player: React.FC<Props> = ({ appUrl, videoData, playerWidth, playerHeight }) => {
	const [wasViewable, setWasViewable] = useState<boolean>(false);
	const playerContainerRef = useRef<HTMLIFrameElement | null>(null);

	const sendViewableEvent = useCallback(() => {
		if (wasViewable) return;
		sendAppEvent({
			eventName: EventNameEnum.SNIPPET_VIEWABLE_IMPRESSION,
			accountId: videoData.accountId,
			videoId: videoData._id,
			snippet: { type: SnippetTypeEnum.PLAYER }
		});
		setWasViewable(true);
	}, [wasViewable, videoData]);

	const checkVisibility = useCallback(
		(entries: IntersectionObserverEntry[]) => {
			entries.forEach((entry) => {
				if (
					entry.target === playerContainerRef.current &&
                    entry.isIntersecting
				) {
					sendViewableEvent();
				}
			});
		},
		[sendViewableEvent]
	);

	useEffect(() => {
		const observer = new IntersectionObserver(checkVisibility, {
			root: null,
			threshold: 0.6
		});
		const currentOnsiteContainer = playerContainerRef.current;
		if (currentOnsiteContainer) {
			observer.observe(currentOnsiteContainer);
		}
		return () => {
			if (currentOnsiteContainer) {
				observer.unobserve(currentOnsiteContainer);
			}
		};
	}, [checkVisibility]);

	return (
		<PlayerWrapper width={playerWidth} height={playerHeight}>
			<PlayerIFrame
				ref={playerContainerRef}
				data-testid={"player-iframe"}
				src={appUrl}
				title="Video Player"
				allowFullScreen
			/>
		</PlayerWrapper>
	);
};

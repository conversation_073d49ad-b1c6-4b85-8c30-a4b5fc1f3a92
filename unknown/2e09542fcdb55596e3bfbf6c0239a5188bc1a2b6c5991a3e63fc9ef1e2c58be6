/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
import JestEnvironmentJsdom from "jest-environment-jsdom";
import MediaQueryListMock from "./src/tests/mocks/MediaQueryList.mock";

class CustomJestEnvironment extends JestEnvironmentJsdom {
	constructor(config: any, context: any) {
		super(config, context);
	}

	async setup(): Promise<void> {
		await super.setup();

		Object.defineProperty(this.global.window, "matchMedia", {
			value: (query: any) => new MediaQueryListMock(query),
			writable: true
		});

		Object.defineProperty(
			this.global.window.HTMLMediaElement.prototype,
			"play",
			{
				configurable: true,
				get() {
					return () => Promise.resolve();
				}
			}
		);

		Object.defineProperty(
			this.global.window.HTMLMediaElement.prototype,
			"pause",
			{
				configurable: true,
				get() {
					return () => {
						/* no-op */
					};
				}
			}
		);

		Object.defineProperty(
			this.global.window.HTMLMediaElement.prototype,
			"load",
			{
				configurable: true,
				get() {
					return () => {
						/* no-op */
					};
				}
			}
		);

		class IntersectionObserverMock {
			constructor(callback: any, options: any) {}
			observe(element: any):void {}
			disconnect():void {}
			unobserve(element: any):void {}
		}

		Object.defineProperty(this.global.window, "IntersectionObserver", {
			value: IntersectionObserverMock,
			writable: true
		});


	}

	async teardown():Promise<void> {
		await super.teardown();
	}
}

module.exports = CustomJestEnvironment;

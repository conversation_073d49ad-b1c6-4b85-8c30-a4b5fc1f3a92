import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IUser } from "./user.interfaces";
import { UserModel } from "./user.model";

export const getUserController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken?.userId) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Missing userId in access token");
		}

		if (req.accessToken.userId !== req.params.userId) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"accountId in parameter does not match accountId in access token");
		}

		const userModel = new UserModel(null);
		const userDocument: IUser = await userModel.readOneById(req.params.userId);

		return res.send({ user: userDocument });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};


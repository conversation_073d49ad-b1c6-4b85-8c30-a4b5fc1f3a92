import React from "react";
import { useRecoilValue } from "recoil";
import styled, { useTheme } from "styled-components/macro";
import { ActionButton } from "./button/ActionButton";
import { Heading } from "./Heading";
import { IconButton } from "./button/IconButton";
import { Flex } from "./Flex";
import { isPortraitAtom } from "@player/app";
import {
	ITheme,
	IThemeButtonVariants
} from "@player/theme";
import { default as TimesIcon } from "@player/assets/icon-times.svg";


const Container = styled(Flex)`
  background: ${(props):string => props.theme.colors.transparentBlack};
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
`;

const ColumnBody = styled(Flex)`
    padding-bottom: 1rem;
    flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    gap: 1rem;
`;

export type Props = {
	title?: string;
	titleStyle?: React.CSSProperties;
	body?: string;
	bodyStyle?: React.CSSProperties;
	children?: React.ReactNode;
	cancelText?: string;
	cancelVariant?: keyof IThemeButtonVariants;
	confirmText?: string;
	confirmVariant?: keyof IThemeButtonVariants;
	visible: boolean;
	disableConfirm?: boolean;
	disableCancel?: boolean;
	disableCloseIcon?: boolean;
	style?: React.CSSProperties;
	onCancel?: () => void;
	onConfirm?: () => void;
};

export const Modal: React.FC<Props> = ({
	title = "Are you sure?",
	titleStyle,
	body = "This action cannot be undone.",
	bodyStyle,
	children,
	cancelText = "Cancel",
	cancelVariant = "secondary",
	confirmText = "Continue",
	confirmVariant = "primary",
	visible,
	disableConfirm = false,
	disableCancel = false,
	disableCloseIcon = false,
	style,
	onCancel,
	onConfirm
}) => {

	const theme = useTheme() as ITheme;
	const isPortrait = useRecoilValue(isPortraitAtom);

	const modalStyle: React.CSSProperties = {
		padding: "0.7rem 1rem",
		width: isPortrait ? "80%" : "35rem",
		borderRadius: "1rem",
		backgroundColor: theme.colors.secondary,
		...style
	};

	const headingStyle: React.CSSProperties = {
		textAlign: "center",
		...titleStyle
	};

	if (!visible) return null;
	return (

		<Container>
			<Flex width={"100%"} justifyContent={"center"} alignItems="center">
				<Flex flexDirection={"column"} style={modalStyle}>
					<Flex justifyContent="flex-end">
						{(onCancel && !disableCloseIcon) &&
							<IconButton svgIcon={TimesIcon}
								isPortrait={isPortrait} onClick={():void => onCancel()}/>
						}
					</Flex>
					<ColumnBody>
						<Heading text={title} tag="h1" style={headingStyle}/>
						{children ?? (<Heading text={body} tag="p" style={bodyStyle}/>)}

						{!disableConfirm && (
							<ActionButton variantBtn={confirmVariant} text={confirmText} onClicked={onConfirm}/>
						)}

						{(onCancel && !disableCancel) && (
							<ActionButton variantBtn={cancelVariant} text={cancelText} onClicked={onCancel}/>
						)}
					</ColumnBody>

				</Flex>
			</Flex>
		</Container>
	);
};

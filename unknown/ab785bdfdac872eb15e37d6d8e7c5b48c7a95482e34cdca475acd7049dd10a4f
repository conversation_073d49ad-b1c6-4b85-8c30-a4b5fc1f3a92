import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";
import { AuthorizationServer } from "../oidc.authorization.server";

describe("OIDC Authentication Tests | POST /api/oauth/oidc/token", () => {
	let expressApp: express.Express;
	const oidcTokenPostApi = "/api/oauth/oidc/token";
	const oidcClientId = "client-id-1";
	const oidcCode = "valid-id-token";

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("should return 400 for missing required fields", testMissingRequiredFields);
	it("should return 400 for invalid API version", testInvalidApiVersion);
	it("should fail to submit invalid OIDC code and return 500", testOidcCodeSubmissionFailure);
	it("should return 500 for unsupported OIDC clientId", testInvalidOidcClientId);
	it("should handle missing tokens in response and return 500", testMissingTokensInResponse);

	async function testMissingRequiredFields(): Promise<void> {
		const res = await supertest(expressApp)
			.post(oidcTokenPostApi)
			.set("x-api-version", "1");

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testInvalidApiVersion(): Promise<void> {
		const res = await supertest(expressApp)
			.post(oidcTokenPostApi)
			.set("x-api-version", "0");

		expect(res.status).toBe(400);
		expect(res.body.error).toBe(APIErrorName.E_INVALID_INPUT);
	}

	async function testOidcCodeSubmissionFailure(): Promise<void> {
		jest.spyOn(AuthorizationServer.prototype, "fetchTokens").mockRejectedValueOnce(
			new APIError(
				APIErrorName.E_SERVICE_FAILED,
				"Failed to exchange OIDC tokens: Some Text. Error details: error description"
			)
		);

		const res = await supertest(expressApp)
			.post(oidcTokenPostApi)
			.set("x-api-version", "1")
			.field("oidcCode", oidcCode)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}

	async function testInvalidOidcClientId(): Promise<void> {
		const res = await supertest(expressApp)
			.post(oidcTokenPostApi)
			.set("x-api-version", "1")
			.field("oidcCode", oidcCode)
			.field("oidcClientId", oidcClientId + "-invalid");

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_INTERNAL_ERROR);
	}

	async function testMissingTokensInResponse(): Promise<void> {
		jest.spyOn(AuthorizationServer.prototype, "fetchTokens").mockResolvedValueOnce({});

		const res = await supertest(expressApp)
			.post(oidcTokenPostApi)
			.set("x-api-version", "1")
			.field("oidcCode", oidcCode)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(500);
		expect(res.body.error).toBe(APIErrorName.E_SERVICE_FAILED);
	}
});

import React from "react";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup,
	screen
} from "@testing-library/react";
import { act } from "react-dom/test-utils";
import {
	isPortraitAtom,
	appSessionIdAtom
} from "@player/app";
import { RecoilRoot } from "recoil";
import {
	Slider,
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider";
import { defaultTheme } from "@player/theme";
import {
	videoDataMock,
	sliderConfigMock,
	appSessionIdMock
} from "./mocks/data.mock";

import * as sliderHookMock from "@player/slider/useSlider.hook";
jest.mock("@player/slider/useSlider.hook");

import * as VideoFrameMock from "@player/interactive/VideoFrame";
jest.mock("@player/interactive/VideoFrame");


Object.defineProperty(global.window.HTMLElement.prototype, "scrollTo", {
	value: jest.fn()
});

beforeEach(async () => {
	jest.clearAllMocks();
});

afterEach(() => {
	jest.resetAllMocks();
	jest.restoreAllMocks();
});

describe("Slider Component.", () => {
	afterEach(cleanup);

	test("Should Render Slider Component with loading spinner", async () => {
		(sliderHookMock.useSlider as jest.Mock).mockReturnValue(
			{ theme: defaultTheme, loading: true }
		);
		await act(async () => {
			render(
				<RecoilRoot initializeState={(snap):void => snap.set(isPortraitAtom, true)}>
					<Slider />
				</RecoilRoot>
			);
		});

		expect(true).toBeTruthy();
		const loadingSpinnerElement = screen.getByTestId("loading-spinner");
		expect(loadingSpinnerElement).toBeInTheDocument();
	});

	test("Should Render Slider Component with Not found page", async () => {
		(sliderHookMock.useSlider as jest.Mock).mockReturnValue(
			{ theme: defaultTheme, loading: false, videoData: null, starterVideoIndex: null }
		);
		await act(async () => {
			render(
				<RecoilRoot initializeState={(snap):void => snap.set(isPortraitAtom, true)}>
					<Slider />
				</RecoilRoot>
			);
		});

		expect(true).toBeTruthy();
		const errorModalElement = screen.getByText("This video is unavailable.");
		expect(errorModalElement).toBeInTheDocument();

	});


	test("Should Render Slider Component with Mobile layout", async () => {
		(sliderHookMock.useSlider as jest.Mock).mockReturnValue({
			theme: defaultTheme,
			loading: false,
			videoData: videoDataMock.interactiveVideos,
			starterVideoIndex: 0 }
		);

		(VideoFrameMock.VideoFrame as jest.Mock).mockReturnValueOnce(<div>Mock Video Frame</div>);

		await act(async () => {
			render(
				<RecoilRoot initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}>
					<Slider />
				</RecoilRoot>
			);
		});

		expect(true).toBeTruthy();
		const mobileLayout = screen.getByTestId("mobile-layout");
		expect(mobileLayout).toBeInTheDocument();
	});


	test("Should Render Slider Component with Desktop layout", async () => {
		(sliderHookMock.useSlider as jest.Mock).mockReturnValue({
			theme: defaultTheme,
			loading: false,
			videoData: videoDataMock.interactiveVideos,
			starterVideoIndex: 0 }
		);

		(VideoFrameMock.VideoFrame as jest.Mock).mockReturnValueOnce(<div>Mock Video Frame</div>);

		await act(async () => {
			render(
				<RecoilRoot initializeState={(snap): void => {
					snap.set(isPortraitAtom, false);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}>
					<Slider />
				</RecoilRoot>
			);
		});

		expect(true).toBeTruthy();
		const mobileLayout = screen.getByTestId("desk-layout");
		expect(mobileLayout).toBeInTheDocument();
	});





});

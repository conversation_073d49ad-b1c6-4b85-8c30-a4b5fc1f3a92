/* eslint-disable max-lines-per-function */
import TestHelper from "../../../__tests__/mocks/testHelper";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import Stripe, { StripeProduct } from "../../../__mocks__/stripe";
import { StripeWebhookModel } from "./webhook.model";
import { IAccount } from "../../../modules/account/account.interfaces";
import { AccountModel } from "../../../modules/account/account.model";
import { StripeSubscriptionModel } from "../subscription/subscription.model";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../signup/signup.interfaces";

describe("stripe webhook model", () => {
	let expressApp: express.Express;
	let accessToken: string;
	let accountId: string;
	let stripeCustomerId: string;
	let stripeSubscriptionId: string;
	const accountModel = new AccountModel(null);
	const stripe = new Stripe("");

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const userPayload = {
			firstName: "Johnny",
			lastName: "WebhookModel",
			email: "<EMAIL>",
			password: "Password1!",
			companyName: "webhookmodel Co.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld/callback",
			legalAgreement: true
		} as ISignupPayload;

		const testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signup(userPayload));

		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		stripeCustomerId = account.stripeCustomerId;
		stripeSubscriptionId = account.subscription.stripeSubscriptionId;
	});

	it("setSubscription - no subscription id provided", async () => {
		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.hideVanityBranding).toBe(false);

		const stripeSubscriuption = await stripe.subscriptions.retrieve(stripeSubscriptionId);
		stripeSubscriuption.metadata.hideVanityBranding = "true";

		const stripeWebhookModel = new StripeWebhookModel();
		await stripeWebhookModel.setSubscription(stripeCustomerId);

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.stripeSubscriptionId).toBe(stripeSubscriptionId);
		expect(b.subscription.hideVanityBranding).toBe(true);
	});

	it("should update the subscription allowSharing flag to true then false", async () => {
		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.allowSharing).toBe(false);

		let stripeSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
		expect(stripeSubscription.metadata).toBeDefined();
		expect(stripeSubscription.metadata.allowSharing).toBe("false");

		stripeSubscription = await stripe.subscriptions.update(stripeSubscriptionId, {
			metadata: {
				...stripeSubscription.metadata,
				allowSharing: "true"
			}
		});

		expect(stripeSubscription.metadata.allowSharing).toBe("true");

		const stripeWebhookModel = new StripeWebhookModel();
		await stripeWebhookModel.setSubscription(stripeCustomerId);

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.allowSharing).toBe(true);

		stripeSubscription = await stripe.subscriptions.update(stripeSubscriptionId, {
			metadata: {
				...stripeSubscription.metadata,
				allowSharing: "false"
			}
		});

		expect(stripeSubscription.metadata.allowSharing).toBe("false");

		await stripeWebhookModel.setSubscription(stripeCustomerId);

		const c: IAccount = await accountModel.readOneById(accountId);
		expect(c.subscription.allowSharing).toBe(false);

	});

	it("should exit early on a invoice.payment_failed event when not a gp product", async () => {
		const products = await stripe.products.list();

		const product = products.data.find((p: StripeProduct) => p.metadata.gp !== "true");

		expect(product).toBeDefined();
		expect(product.metadata).toBeDefined();

		const invoice = {
			// eslint-disable-next-line camelcase
			attempt_count: 1,
			lines: {
				data: [
					{
						price: {
							product: product.id
						}
					}
				]
			},
			subscription: stripeSubscriptionId
		};

		const stripeEvent = {
			type: "invoice.payment_failed",
			data: {
				object: invoice
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		const result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(null);
	});

	it("should void an invoice for invoice.payment_failed event when it is a gp product", async () => {
		const products = await stripe.products.list();

		const product = products.data.find((p: StripeProduct) => p.metadata.gp === "true");

		expect(product).toBeDefined();
		expect(product.metadata).toBeDefined();

		const invoice = {
			// eslint-disable-next-line camelcase
			attempt_count: 1,
			lines: {
				data: [
					{
						price: {
							product: product.id
						}
					}
				]
			},
			subscription: stripeSubscriptionId
		};

		const stripeEvent = {
			type: "invoice.payment_failed",
			data: {
				object: invoice
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		const result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).not.toBe(null);
	});

	it("should ignore processing customer.subscription.created when not a gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.created",
			data: {
				object: subscription
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		let result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);

		subscription.metadata = {
			gp: "false"
		};

		stripeEvent.data.object = subscription;

		result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);
	});

	it("should process customer.subscription.created when is gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
				gp: "true"
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.created",
			data: {
				object: subscription
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		const result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(true);
	});

	it("should ignore processing customer.subscription.updated when not a gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.updated",
			data: {
				object: subscription
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		let result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);

		subscription.metadata = {
			gp: "false"
		};

		stripeEvent.data.object = subscription;

		result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);
	});

	it("should process customer.subscription.updated when is gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
				gp: "true"
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.updated",
			data: {
				object: subscription,
				// eslint-disable-next-line camelcase
				previous_attributes: {
					// eslint-disable-next-line camelcase
					current_period_start: subscription.current_period_start - 1000
				}
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		const result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(true);
	});

	it("should ignore processing customer.subscription.deleted when not a gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.deleted",
			data: {
				object: subscription
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		let result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);

		subscription.metadata = {
			gp: "false"
		};

		stripeEvent.data.object = subscription;

		result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(false);
		const account = await accountModel.readOneById(accountId);
		expect(account.subscription.stripeSubscriptionId).toBe(stripeSubscriptionId);
	});

	it("should process customer.subscription.deleted when is gp product", async () => {
		const subscription = {
			id: stripeSubscriptionId,
			customer: stripeCustomerId,
			metadata: {
				gp: "true"
			},
			// eslint-disable-next-line camelcase
			current_period_start: Math.floor(Date.now() / 1000)
		};

		const stripeEvent = {
			type: "customer.subscription.deleted",
			data: {
				object: subscription,
				// eslint-disable-next-line camelcase
				previous_attributes: {
					// eslint-disable-next-line camelcase
					current_period_start: subscription.current_period_start - 1000
				}
			}
		};

		const stripeWebhookModel = new StripeWebhookModel();
		const result = await stripeWebhookModel.handleEvent(stripeEvent as any);
		expect(result).toBe(true);

		const account = await accountModel.readOneById(accountId);
		expect(account.subscription.stripeSubscriptionId).not.toBe(stripeSubscriptionId);
	});

	it("setSubscription - no subscription id provided", async () => {
		const a: IAccount = await accountModel.readOneById(accountId);
		expect(a.subscription.price).toBe(0);
		const priceId = a.subscription.stripePriceId;

		const pricePro = stripe.prices._getPro();

		const subscriptionModel = new StripeSubscriptionModel();
		const newSubscription = await subscriptionModel.createSubscription(stripeCustomerId, pricePro);

		const stripeWebhookModel = new StripeWebhookModel();
		await stripeWebhookModel.setSubscription(stripeCustomerId, newSubscription.id);

		const b: IAccount = await accountModel.readOneById(accountId);
		expect(b.subscription.stripeSubscriptionId).not.toBe(stripeSubscriptionId);
		expect(b.subscription.stripePriceId).not.toBe(priceId);
		expect(b.subscription.price).toBeGreaterThan(0);
	});
});

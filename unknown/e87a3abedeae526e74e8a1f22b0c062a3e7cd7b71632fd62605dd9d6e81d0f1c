import { useTranslation } from "./translations";
import { Price } from "../../types/product";

const usePlansPricing = () => {
	const translation = useTranslation();

	const formatPrice = (price: Price) => {
		if (price.unitAmount > 0) {
			const newPrice = new Intl.NumberFormat("en-US", {
				style: "currency",
				currency: price.currency
			}).format(price.unitAmount / price.unitDevisor);
			return newPrice + translation.plansPage.apMonthPrice;
		}

		return translation.general.free;
	};

	const sortPrices = (prices: Price[]): Price[] => {
		return prices.sort((a, b) => a.unitAmount - b.unitAmount);
	};

	return { formatPrice, sortPrices, translation };
};

export default usePlansPricing;

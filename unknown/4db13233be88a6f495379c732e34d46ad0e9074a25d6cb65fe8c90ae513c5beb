import { ClientSession } from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricConversionDBModel } from "../../modules/metricConversion/metricConversionDB.model";
import { APIError } from "../../utils/helpers/apiError";
import path from "path";

const logTrace = path.basename(__filename);

export const aggregateMetricConversion = async (
	query: any[],
	session: ClientSession | null
): Promise<any | null> => {
	try {
		const documents: any | null = await MetricConversionDBModel.aggregate(
			query
		).session(session);
		return documents;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > aggregateMetricConversion | Failed to aggregate metric conversions. ` +
			`| ${error.name} | ${error.message}`
		);
	}
};

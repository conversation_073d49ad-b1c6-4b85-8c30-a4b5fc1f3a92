import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import {
	FlexCol,
	MainButton,
	VideoSectionModal,
	CodeSnippetModal,
	CustomHighlight,
	HighlightCodeModal
} from "@src/styles/components";
import { CloneIconDiv, CloneIcon } from "@src/styles/forms";
import { ModalText } from "@src/styles/modals";

interface Props {
	apiKey: string;
	visible: boolean;
	setVisible: (value: boolean) => void;
	onClose: () => void;
}

export const DisplayApiKeyModal: React.FC<Props> = ({ apiKey, visible, onClose }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onClose();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal
			modalStyle={{ maxWidth: "47rem" }}
			visible={visible}
			header={translation.modals.newApiKey}
			wrapperRef={wrapperRef}
		>
			<ModalText data-testid="ModalText">{translation.modals.newApiKeyText}</ModalText>
			<VideoSectionModal>
				<div style={{ position: "relative" }}>
					<CloneIconDiv style={{ width: "100%", position: "absolute" }}>
						<CloneIcon
							data-testid="CloneIcon"
							style={{ float: "right" }}
							onClick={() => {
								navigator.clipboard.writeText(apiKey);
							}}
						/>
					</CloneIconDiv>
					<CodeSnippetModal>
						<CustomHighlight data-testid="highlightBox">
							<HighlightCodeModal className="language-html">{apiKey}</HighlightCodeModal>
						</CustomHighlight>
					</CodeSnippetModal>
				</div>
			</VideoSectionModal>
			<FlexCol>
				<MainButton type="button" onClick={onClose} className="mx-auto mt-3" data-testid="ModalCloseButton">
					{translation.general.closeWindow}
				</MainButton>
			</FlexCol>
		</BaseModal>
	);
};

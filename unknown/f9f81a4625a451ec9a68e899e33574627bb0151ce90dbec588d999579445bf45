import Jo<PERSON> from "joi";
import {
	LocaleAPI,
	APIErrorName
} from "../../interfaces/apiTypes";
import { ISignInEmailPayload } from "../../modules/signin/signin.interfaces";

const emailSchema = Joi.string()
	.lowercase()
	.email({
		tlds: { allow: false }
	})
	.trim();

export const signInEmailSchema = {
	data: Joi.object<ISignInEmailPayload>({
		email: emailSchema.required(),
		callbackEndpoint: Joi.string().required(),
		locale: Joi.string().optional().custom((value, helper) => {
			if (Object.values(LocaleAPI).includes(value)) {
				return true;
			}
			return helper.message({
				custom: "language is not supported."
			});

		})
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

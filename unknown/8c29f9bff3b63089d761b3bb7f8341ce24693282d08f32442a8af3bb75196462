import {
	IShopify,
	initializeConversion
} from "@player/conversion/index";

import * as appStorage from "@player/core/core.storage";
jest.mock("@player/core/core.storage");



declare global {
	interface Window {
		Shopify: IShopify;
		localStorage: Storage;
		fetch: jest.Mock<Promise<Response>, [RequestInfo, RequestInit?]>;
	}
}


window.Shopify = {
	checkout: {
		// eslint-disable-next-line camelcase
		line_items: [{ quantity: 1 }, { quantity: 2 }]
	}
};

const localStorageMock: Storage = {
	removeItem: jest.fn(),
	getItem: jest.fn(),
	setItem: jest.fn(),
	clear: jest.fn(),
	key: jest.fn(),
	length: 0
};

window.localStorage = localStorageMock;

const fetchMock = jest.fn(() => Promise.resolve(true));
Object.defineProperty(window, "fetch", { value: fetchMock });

describe("[conversion script] - initializeConversion", () => {
	it("should initialize conversion when apDataObject is present", async () => {
		const apDataObject = {
			accountId: "mockedAccountId",
			gpSessionId: "gpSessionId",
			collections: []
		};
		(appStorage.getWithExpiry as jest.Mock).mockReturnValueOnce(apDataObject);

		await initializeConversion();

		expect(fetchMock).toHaveBeenCalledWith(
			process.env.GP_SERVER_API_ENDPOINT + "/api/metrics/conversion",
			expect.objectContaining({
				method: "POST",
				headers: {
					"x-api-version": "1"
				}
			})
		);
	});
});

import mongoose from "mongoose";
import { randomBytes } from "crypto";
import { countMetricVideoClicks } from "./metricVideoClicks.service";

describe("metricVideoClicks.service tests", () => {
	it("countMetricVideoClicks no documents results in count = 0", async () => {
		const accountId = randomBytes(12).toString("hex");
		const query = {
			accountId: new mongoose.Types.ObjectId(accountId),
			createdAt: {
				$gte: new Date(0),
				$lte: Date.now()
			}
		};
		const count = await countMetricVideoClicks(query, null);
		expect(count).toBe(0);
	});
});

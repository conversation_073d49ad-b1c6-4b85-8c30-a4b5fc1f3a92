import { Controller } from "../base/base.controller";
import express, {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { SignupEnterpriseModel } from "./signupEnterprise.model";
import {
	SignupEnterprisePayload,
	SignupEnterprisePayloadSchema
} from "./signupEnterprise.joi";
import { isSchemaValid } from "../../middleware/isSchemaValid.mw";
import { signUpSchema } from "../../middleware/auth/signup.mw";

export class SignupEnterpriseController extends Controller {
	constructor() {
		super();
		this.router.post("/",
			express.json({ limit: "2MB" }),
			isSchemaValid(signUpSchema.data),
			this.handleSignupEnterprise
		);
	}

	private handleSignupEnterprise = async (req: Request, res: Response): Promise<Response> => {
		let validPayload: SignupEnterprisePayload;
		try {
			validPayload = await SignupEnterprisePayloadSchema.validateAsync({
				apiVersion: req.headers["x-api-version"],
				accessToken: req.headers.authorization,
				email: req.body.email,
				password: req.body.password
			});
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(res);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			const superToken = await this.verifyAccessToken(validPayload.accessToken);
			this.assertSuperUserAccess(superToken);

			const signupPayload = req.body as ISignupPayload;
			const signupEnterpriseModel = new SignupEnterpriseModel(res.locals.session);
			const result = await signupEnterpriseModel.createEnterpriseSignup(signupPayload);

			return res.status(200).json({
				accessToken: result.accessToken,
				refreshToken: result.refreshToken
			});
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(res);
		}
	};
}

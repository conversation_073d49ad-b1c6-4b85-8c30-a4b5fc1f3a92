import { APIErrorName } from "../../interfaces/apiTypes";
import {
	ClientSession,
	QueryOptions
} from "mongoose";
import { DeleteResult } from "mongodb";
import { IInvitation } from "../../modules/invitation/invitation.interfaces";
import { InvitationDBModel } from "../../modules/invitation/invitationDB.model";

export const readInvitations = async (query: any, session: ClientSession | null): Promise<IInvitation[]> => {
	try {
		const documents: IInvitation[] = await InvitationDBModel.find(query).session(session);
		return documents;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> readInvitations| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const readInvitation = async (query: any, session: ClientSession | null): Promise<IInvitation | null> => {
	try {
		const document: IInvitation | null = await InvitationDBModel.findOne(query).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> readInvitation| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const updateInvitation = async (
	query: any, update: any, session: ClientSession | null
): Promise<IInvitation | null> => {
	try {
		const options: QueryOptions<IInvitation> = {
			new: true,
			upsert: false
		};

		const document: IInvitation | null = await InvitationDBModel.findOneAndUpdate(
			query, update, options).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> updateInvitation| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const deleteInvitation = async (query: any, session: ClientSession | null): Promise<IInvitation | null> => {
	try {
		const options: QueryOptions<IInvitation> = {};

		const document: IInvitation | null = await InvitationDBModel.findOneAndDelete(query, options).session(session);
		return document;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> deleteInvitation| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const deleteInvitations = async (filter: any, session: ClientSession | null): Promise<DeleteResult> => {
	try {
		const options: QueryOptions<IInvitation> = {};
		const result: DeleteResult = await InvitationDBModel.deleteMany(filter, options).session(session);
		return result;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> deleteInvitations| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};

export const aggregateInvitations = async (query: any, session: ClientSession | null): Promise<IInvitation[]> => {
	try {
		const documents: IInvitation[] = await InvitationDBModel.aggregate(query).session(session);
		return documents;
	} catch (error: any) {
		const err = new Error();
		err.message = `<invitations.service> aggregateInvitations| ${error.name} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};


import React from "react";
import { useTheme } from "styled-components";
import { useRecoilValue } from "recoil";
import { isPortraitAtom } from "@player/app";

import { ITheme } from "@player/theme";


interface BaseProps {
	text?: string;
	children?: React.ReactNode;
	style?: React.CSSProperties;
}

interface DesktopProps extends BaseProps {
	font: "desktop";
	tag: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p";
}

interface MobileProps extends BaseProps {
	font?: "mobile";
	tag?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p";
}

type Props = DesktopProps | MobileProps;

export const Heading: React.FC<Props> = ({
	tag = "h1",
	text = "",
	font = "desktop",
	style,
	children,
	...otherProps
}) => {

	const isPortrait = useRecoilValue(isPortraitAtom);
	if (isPortrait) {
		font = "mobile";
	}

	const theme = useTheme() as ITheme;
	const fontStyle: React.CSSProperties = {
		margin: 0,
		padding: 0,
		color: theme.textColor,
		fontFamily: theme.fonts.family,
		...theme.fonts[font][tag],
		...style
	};

	const StyledHeading = tag;

	return (
		<StyledHeading style={fontStyle} {...otherProps}>
			{children ?? text}
		</StyledHeading>
	);
};

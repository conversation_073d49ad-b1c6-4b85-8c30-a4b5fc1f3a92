import {
	Request,
	Response
} from "express";
import bcrypt from "bcryptjs";
import { AuthMethods } from "../authentication/authentication.enums";
import path from "path";
import { UserUpdateOneInput } from "../user/user.interfaces";
import { AuthenticationModel } from "../authentication/authentication.model";
import { UserModel } from "../user/user.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IResetPasswordPayload } from "./resetpassword.interfaces";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	readUnverifiedJwt,
	verifyJwt
} from "../../utils/helpers/gpJwt.helper";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";
import { EmailPasswordAuthentication } from "../authentication/emailpassword.authentication.model";

const logTrace = path.basename(__filename);

export const resetPasswordController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const payload = req.body as IResetPasswordPayload;
		const secrets: ISecrets = await getSecrets();
		const gpSecretKey = secrets.hashkey.key;

		if (!gpSecretKey) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to read secrets");
		}

		const decodedAuthentication = readUnverifiedJwt(payload.token);

		const authenticationId = decodedAuthentication?.authenticationId;

		if (!authenticationId) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Failed to fetch authenticationId");
		}

		const authModel = new AuthenticationModel(null);
		const authDoc = await authModel.readOneById(authenticationId);

		if (authDoc.method === AuthMethods.APIKEY) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Cannot reset password for api key");
		}

		if (authDoc.method !== AuthMethods.EMAIL_PASSWORD) {
			throw new APIError(APIErrorName.E_AUTH_METHOD_NOT_SUPPORTED,
				"Password reset is not available for this authentication method");
		}

		const saltedHash = `${gpSecretKey}${authDoc.salt}`;
		const decodedJWT = verifyJwt(payload.token, saltedHash);

		if (!decodedJWT) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Failed to verify Token");
		}

		const passwordHash = bcrypt.hashSync(payload.password, authDoc.salt);

		const authenticationModel = new EmailPasswordAuthentication(null);
		const authenticationModelResponse = await authenticationModel.updatePasswordAndVerify(authDoc, passwordHash);
		authenticationModelResponse.throwIfError();
		const updatedAuthDocument = authenticationModelResponse.getData();

		const userSet: UserUpdateOneInput = {
			_id: updatedAuthDocument.userId.toString(),
			isPasswordSet: true
		};

		const userModel = new UserModel(null);
		const userDocument = await userModel.updateOneById(userSet);

		const accessToken = await createAccessToken(updatedAuthDocument, userDocument);
		const refreshToken = await createRefreshToken(updatedAuthDocument);

		gpLog({
			message: "Password is reset successfully",
			trace: logTrace,
			scope: LogScope.INFO
		});

		return res.status(200).send({ accessToken, refreshToken });
	} catch (error: any) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

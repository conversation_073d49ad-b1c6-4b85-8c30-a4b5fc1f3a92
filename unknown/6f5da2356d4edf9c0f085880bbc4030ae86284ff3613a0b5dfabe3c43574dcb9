import {
	Request,
	Response
} from "express";
import {
	APIErrorName,
	Permission
} from "../../interfaces/apiTypes";
import { IShoppableCollection } from "./interactiveCollection.interface";
import { deleteAssetsFromCloud } from "../../services/gp/bucket.service";
import { readAccount } from "../../services/mongodb/account.service";
import {
	readShoppableCollections,
	deleteShoppableCollection
} from "../../services/mongodb/shoppableCollection.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import { CDN_DIR } from "../../utils/helpers/gp.helper";

export const deleteShoppableCollectionController = async (
	req: Request,
	res: Response
): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version"
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version"
			);
		}

		if (!req.accountToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header"
			);
		}

		await startDBTransaction(res.locals.session);

		const accountDocument = await readAccount({
			query: {
				_id: req.accountToken.account._id
			},
			path: req.url,
			permissions: [Permission.ALL],
			dbSession: res.locals.session
		});

		if (!accountDocument || accountDocument === null) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`Failed to read the account document with _id ${req.accountToken.account._id} supplied in access token`
			);
		}

		if (
			req.params.collectionId ===
            accountDocument.defaultCollectionId.toString()
		) {
			throw new APIError(
				APIErrorName.E_REQUEST_FORBIDDEN,
				"Request to delete default collection denied"
			);
		}

		const collectionDocuments: IShoppableCollection[] = await readShoppableCollections({
			query: {
				_id: req.params.collectionId
			},
			path: req.url,
			permissions: [Permission.ALL],
			dbSession: res.locals.session
		});

		if (!collectionDocuments?.length) {
			throw new APIError(
				APIErrorName.E_COLLECTION_NOT_FOUND,
				`Failed to read shoppable collection with id: ${req.params.collectionId}`
			);
		}

		if (
			req.accountToken.account._id !==
            collectionDocuments[0].accountId.toString()
		) {
			throw new APIError(
				APIErrorName.E_INVALID_AUTHORIZATION,
				"Request to delete collection from another account denied"
			);
		}

		const collectionDocument: IShoppableCollection | null = await deleteShoppableCollection(
			{
				_id: req.params.collectionId
			},
			res.locals.session
		);

		if (!collectionDocument) {
			throw new APIError(
				APIErrorName.E_DOCUMENT_NOT_FOUND,
				"Shoppable Collection not found"
			);
		}

		const filenameToBeDeleted =
		`${CDN_DIR.DATACACHE}collections/${collectionDocument._id}-collection.json`;
		const fileDeleted = await deleteAssetsFromCloud([filenameToBeDeleted]);
		if (!fileDeleted) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Failed to delete Shoppable Collection DataCache file."
			);
		}

		await completeDBTransaction(res.locals.session);
		return res.status(200).send({});
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

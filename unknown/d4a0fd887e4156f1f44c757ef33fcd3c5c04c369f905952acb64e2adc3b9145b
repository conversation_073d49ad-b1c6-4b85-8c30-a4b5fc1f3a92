import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { IAccount } from "../../modules/account/account.interfaces";
import { AccountDBModel } from "../../modules/account/accountDB.model";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";

const expressApp = createServer();
initExpressRoutes(expressApp);

const emailAddress = "<EMAIL>";

describe("GET accounts", () => {
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;

	beforeAll(async () => {
		const signupPayload: ISignupPayload = {
			email: emailAddress,
			password: "Password1!",
			firstName: "John",
			lastName: "Doe",
			companyName: "John Doe Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		expect(signUpResponse.statusCode).toBe(200);

		accessToken = signUpResponse.body.accessToken;

		const accountsResponse = await supertest(expressApp)
			.get("/api/accounts/self")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json");

		expect(accountsResponse.statusCode).toBe(200);
		expect(accountsResponse.body.accounts.length).toBe(1);

		account = accountsResponse.body.accounts[0];

		const accountTokenResponse = await supertest(expressApp)
			.post("/api/accounts/token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json")
			.field("accountId", account._id.toString());

		expect(accountTokenResponse.statusCode).toBe(200);
		expect(accountTokenResponse.body).toHaveProperty("token");
		accountToken = accountTokenResponse.body.token;
	});

	it("Should return 401 [E_INVALID_AUTHORIZATION] when missing account token", async () => {
		const res = await supertest(expressApp)
			.get(`/api/accounts/${account._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the account document does not exist", async () => {
		const spy = jest.spyOn(AccountDBModel, "findOne").mockImplementationOnce((): any => {
			return Promise.reject(new Error("failed to read account document."));
		});

		const res = await supertest(expressApp)
			.get("/api/accounts/669ea491a31e1537c6336bb6")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		spy.mockRestore();

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 200 with document result", async () => {
		const res = await supertest(expressApp)
			.get(`/api/accounts/${account._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("account");
		expect(res.body.account).toHaveProperty("_id");
		expect(res.body.account).toHaveProperty("companyName");
		expect(res.body.account).toHaveProperty("createdAt");
		expect(res.body.account).toHaveProperty("updatedAt");
		expect(res.body.account).toHaveProperty("defaultCollectionId");
		expect(res.body.account._id).toBe(account._id.toString());
		expect(res.body.account.companyName).toBe("John Doe Inc.");
		expect(res.body.account.createdAt).toBeTruthy();
		expect(res.body.account.updatedAt).toBeTruthy();
		expect(res.body.account.defaultCollectionId).toBe(account.defaultCollectionId);
	});
});

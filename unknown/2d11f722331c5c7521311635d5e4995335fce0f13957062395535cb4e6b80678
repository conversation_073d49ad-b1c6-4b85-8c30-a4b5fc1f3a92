import {
	Request,
	Response
} from "express";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";
import {
	IAccount,
	GetSearchPayload
} from "../account.interfaces";
import { AccountModel } from "../account.model";
import { getSearchSchema } from "../account.validator";

export class AccountSuperController {
	async delete (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async get (request: Request, response: Response): Promise<Response> {
		return getAccountsSuperController(request, response);
	}

	async list (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async post (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async patch (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}

	async put (request: Request, response: Response): Promise<Response> {
		return new APIError(APIErrorName.E_NOT_IMPLEMENTED, "Not Implemented").setResponse(response);
	}
}

const validateUserIsSuper = (req: Request): boolean => {
	if (req.accessToken && req.accessToken.super) {
		return true;
	}

	return false;
};

const getAccountsSuperController = async (
	req: Request,
	res: Response
): Promise<Response> => {
	try {
		if (!validateUserIsSuper(req)) {
			throw new APIError(
				APIErrorName.E_REQUEST_FORBIDDEN,
				"User is not allowed to make this request."
			);
		}

		let validPayload: GetSearchPayload;

		try {
			validPayload = await getSearchSchema.validateAsync({
				apiVersion: req.headers["x-api-version"],
				accessToken: req.headers.authorization,
				search: req.query.search,
				searchType: req.query.searchType
			});
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(res);
		}

		const accountModel = new AccountModel(null);
		const accounts: IAccount[] | null = await accountModel.readAccountsWithFilter(
			validPayload.search,
			validPayload.searchType
		);

		if (accounts === null) {
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED,
				"Failed to read accounts."
			);
		}

		return res.send({ accounts });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

import React from "react";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup
} from "@testing-library/react";
import { Player } from "@player/core/components/Player";
import {
	videoDataMock,
	playerUrlMock
} from "../mocks/data.mock";

beforeEach(async () => {
	jest.clearAllMocks();
});

afterEach(() => {
	jest.resetAllMocks();
});

describe("Player Snippet. Player Component", () => {
	afterEach(cleanup);

	test("Should Render Player Component", async () => {
		const videoId = videoDataMock.interactiveVideos[0]._id;
		const appUrl = `${playerUrlMock}?videoId=${videoId}&snippetType=onsite`;
		const playerWidth = "600";
		const playerHeight = "350";

		const { getByTestId } = render(
			<Player
				appUrl={appUrl}
				videoData={videoDataMock.interactiveVideos[0]}
				playerWidth={playerWidth}
				playerHeight={playerHeight}
			/>
		);

		const playerIframe = getByTestId("player-iframe");

		expect(playerIframe).toHaveAttribute("src", appUrl);
		expect(playerIframe).toBeVisible();
	});
});

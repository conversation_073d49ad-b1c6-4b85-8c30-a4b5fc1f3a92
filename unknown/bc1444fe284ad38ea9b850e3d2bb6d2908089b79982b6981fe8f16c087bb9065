import {
	appLog,
	LogScope
} from "@player/app/app.log.util";
import { validateBackslash } from "@player/app/app.util";

export const sendEmail =
async (fromEmail: string, toEmail: string, message: string, subject: string): Promise<void> => {
	try {
		const API_ENDPOINT = process.env.GP_SERVER_API_ENDPOINT;
		if (!API_ENDPOINT) {
			throw new Error("Missing API_ENDPOINT.");
		}
		if (!fromEmail || !toEmail || !message || !subject) {
			throw new Error("Missing fromEmail, toEmail, message, or subject.");
		}

		const url = validateBackslash(API_ENDPOINT) + "api/email";
		const headers = new Headers();
		headers.append("x-api-version", "3");

		const formData = new FormData();
		formData.append("fromEmail", fromEmail);
		formData.append("toEmail", toEmail);
		formData.append("message", message);
		formData.append("subject", subject);
		formData.append("template", "email-lead");
		formData.append("locale", "en_US");

		const response = await fetch(url, {
			method: "POST",
			headers: headers,
			body: formData
		});

		if (!response.ok) {
			throw new Error(`sendEmail response is not ok, status=${response.status}`);
		}
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n sendEmail",
			scope: LogScope.ERROR
		});
	}
};

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { InvitationStatus } from "../modules/invitation/invitation.enum";
import { ISignupEmailPayload } from "../modules/signup/signup.interfaces";
import { LocaleAPI } from "../interfaces/apiTypes";
import TestHelper from "./mocks/testHelper";
import { IAccount } from "../modules/account/account.interfaces";
import { IInvitation } from "../modules/invitation/invitation.interfaces";

describe("PUT /invitations", () => {
	let accessToken: string;
	let accountToken: string;
	let testHelper: TestHelper;
	let accountId: string;

	const expressApp = createServer();
	initExpressRoutes(expressApp);
	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		testHelper = new TestHelper(expressApp);
		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));

		const account :IAccount = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();

		const accountTokenResponse = await supertest(expressApp)
			.post("/api/accounts/token")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.accept("json")
			.field("accountId", account._id.toString());

		accountToken = accountTokenResponse.body.token;
	});


	it("Return 200 [OK] when status is ACCEPTED and updating the authentication is successful", async () => {
		const signupFriendEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		const invitation: IInvitation = await testHelper.createInvitation(
			accountToken, signupFriendEmailPayload.email, accessToken);
		const invitationId = invitation._id.toString();

		expect(invitation.accountId.toString()).toBe(accountId);
		expect(invitation.email).toBe(signupFriendEmailPayload.email);
		expect(invitation.status as InvitationStatus).toBe(InvitationStatus.PENDING);

		const { accessToken: accessTokenFriend } = await testHelper.signupEmail(signupFriendEmailPayload);

		const res = await supertest(expressApp)
			.put(`/api/invitations/${invitationId}`)
			.set("Authorization", `Bearer ${accessTokenFriend}`)
			.set("x-api-version", "3")
			.field("status", InvitationStatus.ACCEPTED);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitation");
		expect(res.body.invitation).toHaveProperty("_id");
		expect(res.body.invitation).toHaveProperty("accountId", accountId);
		expect(res.body.invitation).toHaveProperty("createdAt");
		expect(res.body.invitation).toHaveProperty("email", signupFriendEmailPayload.email);
		expect(res.body.invitation).not.toHaveProperty("salt");
		expect(res.body.invitation).toHaveProperty("status", InvitationStatus.ACCEPTED);
	});

	it("return 200 [OK] when status is DECLINED and updating the authentication is successful", async () => {
		const signupFriendEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		const invitation: IInvitation = await testHelper.createInvitation(
			accountToken, signupFriendEmailPayload.email, accessToken);
		const invitationId = invitation._id.toString();

		expect(invitation.accountId.toString()).toBe(accountId);
		expect(invitation.email).toBe(signupFriendEmailPayload.email);
		expect(invitation.status as InvitationStatus).toBe(InvitationStatus.PENDING);

		const { accessToken: accessTokenFriend } = await testHelper.signupEmail(signupFriendEmailPayload);

		const res = await supertest(expressApp)
			.put(`/api/invitations/${invitationId}`)
			.set("Authorization", `Bearer ${accessTokenFriend}`)
			.set("x-api-version", "2")
			.field("status", InvitationStatus.DECLINED);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("invitation");
		expect(res.body.invitation).toHaveProperty("_id");
		expect(res.body.invitation).toHaveProperty("accountId", accountId);
		expect(res.body.invitation).toHaveProperty("createdAt");
		expect(res.body.invitation).toHaveProperty("email", signupFriendEmailPayload.email);
		expect(res.body.invitation).not.toHaveProperty("salt");
		expect(res.body.invitation).toHaveProperty("status", InvitationStatus.DECLINED);
	});
});

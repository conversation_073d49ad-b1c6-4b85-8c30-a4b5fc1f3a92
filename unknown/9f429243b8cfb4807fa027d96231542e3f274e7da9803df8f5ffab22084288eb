import express, {
	Request,
	Response
} from "express";

import { APIError } from "../../utils/helpers/apiError";
import { Controller } from "../base/base.controller";
import { APIErrorName } from "../../interfaces/apiTypes";
import { EventBufferModel } from "./eventBuffer.model";
import { PostEventBufferJoi } from "./eventBuffer.joi";

export class EventBufferController extends Controller {
	constructor () {
		super();
		this.router.post("/", express.json({ limit: "2MB" }), this.post.bind(this));
		this.router.post("/text", express.text({ type: "text/plain", limit: "2MB" }), this.postText.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		try {
			const validatedRequest = await PostEventBufferJoi.validateAsync({
				apiVersion: request.headers["x-api-version"],
				data: request.body
			});

			await this.verifyAPIVersion(validatedRequest.apiVersion, 1);

			const eventBuffer = new EventBufferModel();
			const result = await eventBuffer.createOne(validatedRequest.data);
			const responseData = {
				id: result._id.toString()
			};
			return response.status(201).send(responseData);
		} catch (error: unknown) {
			return APIError
				.fromUnknownError(error)
				.setRequest(request)
				.suppressLogIf([APIErrorName.E_DOCUMENT_NOT_FOUND])
				.log()
				.setResponse(response);
		}
	}

	private async postText(request: Request, response: Response): Promise<Response> {
		if (!request.is("text/plain") || typeof request.body !== "string") {
			return new APIError(APIErrorName.E_INVALID_INPUT, "Expected text/plain body").setResponse(response);
		}

		try {
			request.body = JSON.parse(request.body);
			if (request.body["x-api-version"]){
				request.headers["x-api-version"] = request.body["x-api-version"];
				delete request.body["x-api-version"];
			}
		} catch (error: unknown) {
			return new APIError(APIErrorName.E_INVALID_INPUT, error).setResponse(response);
		}

		return await this.post(request, response);
	}
}

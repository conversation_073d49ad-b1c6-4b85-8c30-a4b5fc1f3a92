import {
	AppMessageTypeEnum,
	AppMessageSecret
} from "@player/app/app.message";

interface IAppStorage {
	accountId: string;
	gpSessionId: string;
	collections: string[];
	expiry?: number;
}
export const setWithExpiry = (key: string, value: IAppStorage): void => {
	// 14 days in milliseconds
	const ttl = 14 * 24 * 60 * 60 * 1000;
	const now = new Date();
	const item: IAppStorage = {
		accountId: value.accountId,
		gpSessionId: value.gpSessionId,
		collections: value.collections,
		expiry: now.getTime() + ttl
	};

	localStorage.setItem(key, JSON.stringify(item));
};

export const getWithExpiry = (key: string): IAppStorage | null => {
	const itemStr = localStorage.getItem(key);
	if (!itemStr) {
		return null;
	}

	const data = JSON.parse(itemStr) as IAppStorage;
	const now = new Date();

	if (data.expiry && now.getTime() > data.expiry) {
		localStorage.removeItem(key);
		return null;
	}

	return data;
};

export const replyToAppWithSessionStorage = (appWindow: Window): void => {
	const apSessionStorage = {
		likedVideos: sessionStorage.getItem("apVideosLiked") || []
	};
	const message = {
		type: AppMessageTypeEnum.RESPONSE_SESSION_STORAGE,
		referrer: window.location.href,
		data: apSessionStorage
	};
	const postEvent = AppMessageSecret + JSON.stringify(message);
	appWindow.postMessage(postEvent, "*");
};


export const addLikedVideoToSessionStorage = (videoId: string): void => {
	if (!videoId) {
		console.error(`addLikedVideoToSessionStorage: videoId is undefined ${videoId}`);
		return;
	}
	const existingVideos = sessionStorage.getItem("apVideosLiked");
	const videosLiked = existingVideos ? JSON.parse(existingVideos) : [];

	if (!videosLiked.includes(videoId)) {
		videosLiked.push(videoId);
		sessionStorage.setItem("apVideosLiked", JSON.stringify(videosLiked));
	}
};

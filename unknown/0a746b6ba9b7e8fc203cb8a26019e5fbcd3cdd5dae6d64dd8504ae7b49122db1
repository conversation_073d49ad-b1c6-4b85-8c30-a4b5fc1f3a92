/* eslint-disable camelcase */
type StripeCustomer = {
	id: string;
	email: string,
	name: string,
	deleted?: boolean;
	invoice_settings: {
		default_payment_method: string | null;
	};
	metadata: {
		billingCycleStart?: number;
	};
};

export type StripeProduct = {
	id: string,
	name: string,
	description: string,
	metadata: {
		gp: string,
		type: string
	}
}

type StripePaymentMethod = {
	id: string;
	type: string;
	card: {
		brand: string;
		last4: string;
	};
	customer?: string;
};

type StripeSubscription = {
	id: string;
	customer: StripeCustomer;
	current_period_start: number;
	items: {
		data: Array<{
			price: StripePrice
		}>;
	};
	metadata: {
		hideVanityBranding?: boolean;
		enableConversionMetrics?: boolean;
		enableEngagementMetrics?: boolean;
		maxClicksMetricPerMonth?: number;
		maxImpressionsPerCycle?: number;
		maxInteractiveCollectionLimit?: number;
		maxInteractiveVideoLimit?: number;
		maxPlaysMetricPerMonth?: number;
		maxVideoProductLinksLimit?: number;
		maxUserLimit?: number;
		trialActive?: number;
		firstPaymentDate?: number;
		lastPaymentDate?: number;
		hasPaymentMethod?: boolean;
		allowLandscape?: boolean;
		allowCTALead?: boolean;
		trialAvailable?: boolean;
		trialStartDate?: boolean;
		trialEndDate?: number;
		trialDaysTotal?: number;
		pendingChangeDate?: number;
		pendingChangePriceId?: number;
		pendingChangeProductId?: number;
		nextBillingDate?: number;
		// clockTime?: number;
		price?: number;
	}
};

type StripeInvoice = {
	id: string;
	amount_due: number;
	created: number;
	currency: string;
	status: string;
	customer: string;
	subscription: string;
	amount_paid: number;
	attempt_count: number;
};

type StripePrice = {
	id: string;
	nickname: string;
	unit_amount: number;
	currency: string;
	product: StripeProduct
	metadata: {
		gp: string;
	};
};


class Stripe {
	constructor(public key: string) {}

	private static customerMap: Map<string, StripeCustomer> = new Map();
	private static paymentMap: Map<string, StripePaymentMethod> = new Map();
	private static subscriptionMap: Map<string, StripeSubscription> = new Map();
	private static invoiceMap: Map<string, StripeInvoice> = new Map();
	private static productMap: Map<string, StripeProduct> = new Map();
	private static priceMap: Map<string, StripePrice> = new Map();

	private static generateId(): string {
		return `${Date.now()}-${Math.floor(Math.random() * 10000)}`;
	}

	static {
		// generate product list
		let id: string;
		let item: any;

		id = `price_${Stripe.generateId()}`;
		const productFree = {
			id: id,
			name: "Test Product BASIC",
			description: "Test Product Description",
			metadata: {
				gp: "true",
				type: "basic",
				allowCTALead: "false"
			}
		};
		Stripe.productMap.set(id, productFree);

		id = `price_${Stripe.generateId()}`;
		const productPro = {
			id: id,
			name: "Test Product PRO",
			description: "the good stuff",
			metadata: {
				gp: "true",
				type: "pro",
				allowCTALead: "true"
			}
		};
		Stripe.productMap.set(id, productPro);

		id = `price_${Stripe.generateId()}`;
		const productGPFalse = {
			id: id,
			name: "Test Product gp-false",
			description: " 2",
			metadata: {
				gp: "false",
				type: "gp-false"
			}
		};
		Stripe.productMap.set(id, productGPFalse);

		// generate Price list
		id = `price_${Stripe.generateId()}`;
		item = {
			id: id,
			nickname: "Test Price Free",
			unit_amount: 0,
			currency: "usd",
			product: productFree.id,
			metadata: { gp: "true" }
		};
		Stripe.priceMap.set(id, item);

		id = `price_${Stripe.generateId()}`;
		item = {
			id: id,
			nickname: "Test Price Paid",
			unit_amount: 2900,
			currency: "usd",
			product: productPro.id,
			metadata: { gp: "true" }
		};
		Stripe.priceMap.set(id, item);

		id = `price_${Stripe.generateId()}`;
		item = {
			id: id,
			nickname: "Test Price 3 gp=false",
			unit_amount: 1000,
			currency: "usd",
			product: productGPFalse.id,
			metadata: { gp: "false" }
		};
		Stripe.priceMap.set(id, item);
	}

	customers = {
		create: jest.fn().mockImplementation((params: any) => {
			const id = `cus_${Stripe.generateId()}`;

			const customer: StripeCustomer = {
				id: id,
				email: params.email,
				name: params.name,
				invoice_settings: {
					default_payment_method: null
				},
				metadata: params.metadata
			};

			Stripe.customerMap.set(id, customer);
			return Promise.resolve(customer);
		}),

		retrieve: jest.fn().mockImplementation((id: string) => {
			const customer = Stripe.customerMap.get(id);
			return Promise.resolve(customer || { id, deleted: true });
		}),

		update: jest.fn().mockImplementation((id: string, updateData: Partial<StripeCustomer>) => {
			const customer = Stripe.customerMap.get(id);
			let promise: any;

			if (customer) {
				const updatedCustomer = { ...customer, ...updateData };
				if ("metadata" in customer && "metadata" in updateData) {
					const updatedMetadata = { ...customer.metadata, ...updateData.metadata };
					updatedCustomer.metadata = updatedMetadata;
				}

				Stripe.customerMap.set(id, updatedCustomer);
				promise = Promise.resolve(updatedCustomer);
			} else {
				promise = Promise.reject(new Error("Customer not found"));
			}
			return promise;
		}),

		del: jest.fn().mockImplementation((id: string) => {
			const customer = Stripe.customerMap.get(id);
			Stripe.customerMap.delete(id);
			return customer;
		})
	};

	invoices = {
		create: jest.fn().mockImplementation((params: any) => {
			const id = `in_${Stripe.generateId()}`;

			const invoice: StripeInvoice = {
				id: id,
				amount_due: params.amount_due,
				created: params.created,
				currency: params.currency,
				status: params.status,
				customer: params.customer,
				subscription: params.subscription,
				amount_paid: params.amount_paid,
				attempt_count: params.attempt_count
			};

			Stripe.invoiceMap.set(id, invoice);
			return Promise.resolve(invoice);
		}),

		list: jest.fn().mockImplementation((params: any) => {
			const customerId = params.customer;
			const status = params.status;
			const invoices = Array.from(Stripe.invoiceMap.values());

			const invoicesFiltered = invoices.filter(invoice =>
				invoice.customer === customerId && invoice.status === status);

			const ret: any = {
				data: []
			};

			if (invoicesFiltered) {
				ret.data = Array.from(invoicesFiltered);
			}

			return Promise.resolve(ret);
		}),
		deleteAll: jest.fn().mockImplementation((params: any) => {
			const customerId = params.customer;
			const status = params.status;

			const invoices = Array.from(Stripe.invoiceMap.values());

			const invoicesFiltered = invoices.filter(invoice =>
				invoice.customer === customerId && invoice.status === status);

			invoicesFiltered.forEach(invoice => Stripe.invoiceMap.delete(invoice.id));
		}),
		delete: jest.fn().mockImplementation((invoiceId: string) => {
			Stripe.invoiceMap.delete(invoiceId);
		})
	};

	paymentMethods = {
		create: jest.fn().mockImplementation((params: any) => {
			const id = `pm_${Stripe.generateId()}`;

			const payment: StripePaymentMethod = {
				id: id,
				type: params.type,
				card: params.card
			};

			Stripe.paymentMap.set(id, payment);
			return Promise.resolve(payment);
		}),

		list: jest.fn().mockImplementation((params: any) => {
			const customerId = params?.customer;
			const type = params?.type;
			const payments = Array.from(Stripe.paymentMap.values());

			const paymentsFiltered = payments.filter(payment => {
				// Apply filters only if the parameters are defined
				const matchesCustomer = customerId === undefined || payment.customer === customerId;
				const matchesType = type === undefined || payment.type === type;

				return matchesCustomer && matchesType;
			});

			const ret: any = {
				data: []
			};

			if (paymentsFiltered) {
				ret.data = Array.from(paymentsFiltered);
			}

			return Promise.resolve(ret);
		}),

		attach: jest.fn().mockImplementation((paymentId: string, params: any) => {
			const payment = Stripe.paymentMap.get(paymentId);
			if (!payment) {
				throw new StripeMockError(`paymentId=${paymentId} was passed to stripe.paymentMethods.attach 
					but no paymentMethod with this id can be found`);
			}

			const customerId = params.customer;
			const customer = Stripe.customerMap.get(customerId);
			if (!customer) {
				throw new StripeMockError(`params.customer=${params.customer} was passed to stripe.subscriptions.create 
					but no customer with this id can be found`);
			}

			payment.customer = customerId;
			customer.invoice_settings.default_payment_method = paymentId;

			return payment;
		}),
		detach: jest.fn().mockImplementation((paymentId: string) => {
			const payment = Stripe.paymentMap.get(paymentId);
			if (payment) {
				payment.customer = undefined;
			}

			// if any customers are using this paymentMethod we will
			// remove it as the default.
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			Stripe.customerMap.forEach((customer, key) => {
				if (customer.invoice_settings.default_payment_method === paymentId) {
					customer.invoice_settings.default_payment_method = null;
				}
			});

			return payment;
		}),
		retrieve: jest.fn().mockImplementation((paymentId: string) => {
			const payment = Stripe.paymentMap.get(paymentId);

			let ret;
			// payment.customer is critical to the caller
			// if it is not there they did not call attach or called detach
			if (payment && payment.customer) {
				payment.customer = undefined;
				ret = payment;
			} else {
				ret = undefined;
			}

			return ret;
		}),

		// delete is not an actual Stripe function.  It is here for unit test cleanup
		delete: jest.fn().mockImplementation((paymentId: string) => {
			// if any customers are using this paymentMethod we will
			// remove it as the default.
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			Stripe.customerMap.forEach((customer, key) => {
				if (customer.invoice_settings.default_payment_method === paymentId) {
					customer.invoice_settings.default_payment_method = null;
				}
			});

			Stripe.paymentMap.delete(paymentId);
		})
	};

	products = {
		retrieve: jest.fn().mockImplementation((id: string) => {
			const product = Stripe.productMap.get(id);
			return Promise.resolve(product);
		}),

		list: jest.fn().mockResolvedValue({
			data: Array.from(Stripe.productMap.values())
		})
	};

	prices = {
		list: jest.fn().mockResolvedValue({
			data: Array.from(Stripe.priceMap.values())
		}),

		retrieve: jest.fn().mockImplementation((id: string) => {
			const price = Stripe.priceMap.get(id);
			return Promise.resolve(price);
		}),

		_getPro: (): any => {
			const prices = Array.from(Stripe.priceMap.values());
			const pricePro = prices.find((price) => {
				return price.unit_amount > 0 && price.metadata.gp?.toLocaleLowerCase().trim() === "true";
			});

			return pricePro;
		}
	};

	subscriptions = {
		create: jest.fn().mockImplementation((params: any) => {
			const id = `sub_${Stripe.generateId()}`;

			const priceId = params.items[0].price;

			const priceGet: StripePrice | undefined = Stripe.priceMap.get(priceId);
			if (priceGet === undefined) {
				throw new StripeMockError(`price.id=${priceId} was passed to stripe.subscriptions.create 
					but no price with this id can be found`);
			}
			const price = priceGet as StripePrice;

			const customerId = params.customer;

			const subscription: StripeSubscription = {
				id: id,
				customer: customerId,
				current_period_start: Date.now(),
				metadata: params.metadata,
				items: {
					data: [{ price: price }
					]
				}
			};

			Stripe.subscriptionMap.set(id, subscription);
			return Promise.resolve(subscription);
		}),

		retrieve: jest.fn().mockImplementation((id: string) => {
			const subscription = Stripe.subscriptionMap.get(id);
			return Promise.resolve(subscription);
		}),

		cancel: jest.fn().mockImplementation((id: string) => {
			// not implemented
			return Stripe.subscriptionMap.get(id);
		}),
		update: jest.fn().mockImplementation((id: string, params: any) => {
			const updateSubscription = Stripe.subscriptionMap.get(id);
			if (!updateSubscription) {
				throw new StripeMockError(`subscription id=${id} was passed to stripe.subscriptions.update 
					but no subscription with this id can be found`);
			}

			if (params?.items?.length > 0) {
				const priceId = params.items[0].price;
				const price = Stripe.priceMap.get(priceId);
				if (!price) {
					throw new StripeMockError(`params.items[0].price=${priceId} was passed to 
						stripe.subscriptions.update but no price with this id can be found`);
				}

				updateSubscription.items.data = [{
					price: price
				}];
			}

			const metadata = params.metadata;
			updateSubscription.metadata = metadata;

			return Promise.resolve(updateSubscription);
		})
	};

	subscriptionSchedules = {
		id: "not_sure_what_to_put_here",
		release: jest.fn(),
		update: jest.fn(),
		create: jest.fn().mockReturnThis(),
		retrieve: jest.fn()
	};

	webhooks = {
		constructEvent: jest.fn().mockImplementation((payload: any) => {
			return JSON.parse(payload.toString("utf8"));
		})
	};
}

class StripeMockError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "StripeMockError";
		this.stack = (new Error()).stack;
	}
}

export default Stripe;

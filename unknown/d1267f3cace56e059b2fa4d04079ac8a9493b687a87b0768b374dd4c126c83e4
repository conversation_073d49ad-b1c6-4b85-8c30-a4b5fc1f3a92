import express, {
	Express,
	Request,
	Response
} from "express";
import { APIKeyController } from "./apiKey.controller";
import { decodeAccess } from "../../middleware/decodeAccess.mw";
import { requireSuperWritePermission } from "../../middleware/requireSuperWritePermission.mw";

export class APIKeyRouter {
	private controller: APIKeyController = new APIKeyController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[express.json({ limit: "2MB" }), decodeAccess, requireSuperWritePermission],
			(request: Request, response: Response): Promise<Response> => {
				return this.controller.post(request, response);
			}
		);

		this.router.get(
			"/",
			[decodeAccess],
			(request: Request, response: Response): Promise<Response> => {
				return this.controller.list(request, response);
			}
		);

		this.router.delete(
			"/:keyId",
			[express.json({ limit: "2MB" }), decodeAccess, requireSuperWritePermission],
			(request: Request, response: Response): Promise<Response> => {
				return this.controller.delete(request, response);
			}
		);
	}

	public use (expressServer: Express): void {
		expressServer.use("/api/keys", this.router);
	}
}

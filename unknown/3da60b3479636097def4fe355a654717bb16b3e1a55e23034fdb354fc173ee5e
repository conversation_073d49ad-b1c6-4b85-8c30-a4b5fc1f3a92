import { ClientSession } from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricUserEngagementDBModel } from "../../modules/metricUserEnagement/metricUserEnagementDB.model";
import { APIError } from "../../utils/helpers/apiError";
import path from "path";

const logTrace = path.basename(__filename);

export const aggregateMetricUserEngagement = async (
	query: any[],
	session: ClientSession | null
): Promise<any | null> => {
	try {
		const documents: any | null = await MetricUserEngagementDBModel.aggregate(
			query
		).session(session);
		return documents;
	} catch (error: any) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`${logTrace} > aggregateMetricUserEngagement | Failed to aggregate metric user engagement. ` +
			`| ${error.name} | ${error.message}`
		);
	}
};

import { MongoMemoryServer } from "mongodb-memory-server";

export = async function globalSetup(): Promise<void> {
	process.env.VOLUME_SECRETS_PATH = "./src/__tests__/secrets";

	// If this is not here you will get the following error
	// MongoError: Transaction numbers are only allowed on a replica set member or mongos.
	process.env.SUPPORTS_DB_TRANSACTIONS = "FALSE";

	const instance = await MongoMemoryServer.create();
	(global as any).__MONGOINSTANCE = instance;

	const uri = instance.getUri();
	const slicedURI = uri.slice(0, uri.lastIndexOf("/"));
	process.env.MONGO_URI = slicedURI;

	// keep lines below around around in case needed when troubleshooting
	// mongoose.set("debug", true);
	// mongoose.set("debug", (collectionName, method, query, doc) => {
	// 	// eslint-disable-next-line no-console
	// 	console.log(`${collectionName}.${method}`, JSON.stringify(query), doc);
	// });
};

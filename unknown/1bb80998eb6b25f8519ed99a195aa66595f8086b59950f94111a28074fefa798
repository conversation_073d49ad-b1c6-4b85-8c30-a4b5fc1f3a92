import mongoose from "mongoose";
import { MetricImpressionModel } from "./metricImpression.model";

describe("metricImpression.model tests", () => {
	it("countDocuments no documents results in count = 0", async () => {
		const bogusAccountId = 0;
		const query = {
			accountId: new mongoose.Types.ObjectId(bogusAccountId),
			createdAt: {
				$gte: new Date(0),
				$lte: Date.now()
			}
		};
		const metricImpressionModel = new MetricImpressionModel(null);
		const count = await metricImpressionModel.countDocuments(query);
		expect(count).toBe(0);
	});
});

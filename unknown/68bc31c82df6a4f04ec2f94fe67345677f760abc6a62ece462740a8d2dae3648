import express from "express";
import { getMetricConversionController } from "../modules/metricConversion/getMetricConversion.controller";
import { postMetricConversionController } from "../modules/metricConversion/postMetricConversion.controller";
import { getMetricsController } from "../modules/metrics/getMetrics.controller";

import multer from "multer";
import { decodeAccess } from "../middleware/decodeAccess.mw";
import { isSchemaValid } from "../middleware/isSchemaValid.mw";
import {
	getMetricsSchema,
	postMetricConversionSchema
} from "../middleware/metrics.mw";
import { validateHeaders } from "../middleware/validateHeaders.mw";

const router = express.Router();

router.get(
	"/",
	[
		express.json({ limit: "2MB" }),
		decodeAccess,
		validateHeaders,
		isSchemaValid(getMetricsSchema.data)
	],
	getMetricsController
);

router.get(
	"/conversion/",
	[
		express.json({ limit: "2MB" }),
		decodeAccess,
		validateHeaders,
		isSchemaValid(getMetricsSchema.data)
	],
	getMetricConversionController
);

router.post(
	"/conversion/",
	[
		multer().none(),
		isSchemaValid(postMetricConversionSchema.data)
	],
	postMetricConversionController
);

export default router;

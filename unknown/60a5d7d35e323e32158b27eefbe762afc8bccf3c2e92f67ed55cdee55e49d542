import React, { useEffect, useState } from "react";
import { Container, Col } from "react-bootstrap";
import { useTranslation, FormatString } from "../hooks/translations";
import Header from "../header/Header";
import { FooterText } from "@src/styles/forms";
import ImageContentPanel from "@src/components/authentication/ImageContentPanel";
import { signInBackgroundImage } from "@src/assets";
import { FlexCol, FullHeightRow } from "@src/styles/components";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import CreateAccountEmailForm from "../authentication/CreateAccountEmailForm";

const CreateAccountEmail: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.general.createAccount;
		registerEvent({
			eventName: EventNameEnum.CREATE_ACCOUNT_IMPRESSION
		});
	}, [translation.general.createAccount]);

	const [registrationCompleted, setRegistrationCompleted] = useState(false);

	return (
		<Container fluid>
			<FullHeightRow pl="0" pr="0">
				<Col sm="12" md={{ span: 8 }} style={{ paddingLeft: 0, paddingRight: 0 }}>
					{!registrationCompleted ? (
						<ImageContentPanel title={translation.createAccountPage.createAccTitle} copy={translation.createAccountPage.createAccCopy} backgroundUrl={signInBackgroundImage} />
					) : (
						<ImageContentPanel title={translation.createAccountPage.emailConfirmedTitle} copy={translation.createAccountPage.verifyEmailSubText} backgroundUrl={signInBackgroundImage} />
					)}
				</Col>
				<FlexCol sm="12" md={{ span: 4 }}>
					<Header auth={false} />
					<CreateAccountEmailForm registrationCompleted={registrationCompleted} setRegistrationCompleted={setRegistrationCompleted} />
					<FooterText data-testid="copyright">{FormatString(translation.general.copyright, new Date().getFullYear().toString())}</FooterText>
				</FlexCol>
			</FullHeightRow>
		</Container>
	);
};

export default CreateAccountEmail;

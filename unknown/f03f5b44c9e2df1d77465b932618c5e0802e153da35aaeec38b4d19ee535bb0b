export type Breakpoint = "xs" | "sm" | "md" | "lg" | "xl" | string | number;

const mapBreakPoint = (breakpoint: Breakpoint): string | number => {
	if (typeof breakpoint === "string") {
		switch (breakpoint) {
			case "xl":
				breakpoint = 1920;
				break;
			case "lg":
				breakpoint = 1280;
				break;
			case "md":
				breakpoint = 960;
				break;
			case "sm":
				breakpoint = 400;
				break;
			case "xs":
				breakpoint = 0;
				break;
			default:
				breakpoint = parseInt(breakpoint);
				break;
		}
	}
	return breakpoint;
};

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export const mediaQueries = (breakpoint: Breakpoint, aspectRatio?: string) => {
	breakpoint = mapBreakPoint(breakpoint);
	const _aspectRatio = aspectRatio
		? `and (min-aspect-ratio: ${aspectRatio})`
		: "";

	// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
	return (style: TemplateStringsArray | string) => {
		return {
			default: `
        @media only screen and (min-width: ${breakpoint}px) ${_aspectRatio} {
            ${style}
          }
        `,
			retina: `
          @media
          only screen and (-webkit-min-device-pixel-ratio: 2)      and (min-width: ${breakpoint}px) ${_aspectRatio},
          only screen and (   min--moz-device-pixel-ratio: 2)      and (min-width: ${breakpoint}px) ${_aspectRatio},
          only screen and (     -o-min-device-pixel-ratio: 2/1)    and (min-width: ${breakpoint}px) ${_aspectRatio},
          only screen and (        min-device-pixel-ratio: 2)      and (min-width: ${breakpoint}px) ${_aspectRatio},
          only screen and (                min-resolution: 192dpi) and (min-width: ${breakpoint}px) ${_aspectRatio},
          only screen and (                min-resolution: 2dppx)  and (min-width: ${breakpoint}px) ${_aspectRatio} {
            ${style}
          }
        `
		};
	};
};

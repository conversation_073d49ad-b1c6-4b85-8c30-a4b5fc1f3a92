import { Worker } from "worker_threads";
import { ISecrets } from "../secrets/secrets.model";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import { APIError } from "../../utils/helpers/apiError";

export class WorkersModel {
	constructor (secrets: ISecrets, workerFiles: string[]) {
		for (const workerFile of workerFiles) {
			this.run(workerFile, secrets);
		}
	}

	public run (workerFile: string, secrets: ISecrets): void {
		const worker = new Worker(workerFile, {
			workerData: {
				secrets: secrets
			}
		});

		worker.on("error", (error: unknown) => {
			gpLog({
				message: `Error in worker ${workerFile}`,
				scope: LogScope.ERROR,
				objData: APIError.fromUnknownError(error)
			});
		});

		worker.on("exit", (code: number) => {
			gpLog({
				message: `worker ${workerFile} exited with code ${code}`,
				scope: LogScope.INFO
			});
		});

		worker.on("message", (message) => {
			gpLog({
				message: `worker ${workerFile} message`,
				scope: LogScope.INFO,
				objData: message
			});
		});
	}
}

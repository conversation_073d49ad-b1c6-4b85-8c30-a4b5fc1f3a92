import {
	ObjectId,
	Document
} from "mongoose";
import { InvitationStatus } from "./invitation.enum";
import { LocaleAPI } from "../../interfaces/apiTypes";

export interface IInvitation extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	createdAt: Date;
	email: string;
	status: InvitationStatus;
	salt: string;
	userId: ObjectId;
}

export interface IPostInvitationPayload {
	callbackEndpoint: string;
	email: string;
	locale: LocaleAPI;
}

export interface IPutInvitationPayload {
	status: InvitationStatus;
}

export interface IInvitationToken {
	invitationId: string;
	accountId: string;
	email: string;
	companyName: string;
	userExists: boolean;
}

export interface InvitationCreateOne 			{
	accountId: string,
	email: string,
	status: InvitationStatus,
	salt: string
}

export interface InvitationCreateOneResult {
	document: IInvitation;
	token: string;
}

export interface InvitationEmailInput {
	email: string,
	companyName: string,
	callbackEndpoint: string,
	locale: LocaleAPI,
	invitationToken: string
}

export interface PostInvitationResponse {
	_id: ObjectId;
	accountId: ObjectId;
	createdAt: Date;
	email: string;
	status: InvitationStatus;
	userId: ObjectId;
}

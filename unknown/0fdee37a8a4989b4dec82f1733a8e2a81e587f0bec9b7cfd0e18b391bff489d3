import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../../interfaces/apiTypes";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../../modules/signup/signup.interfaces";
import { VideoModel } from "../../../modules/video/video.model";
import { IAccount } from "../../../modules/account/account.interfaces";

describe("POST /api/videos/signed-url", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const filename = "user-video.mp4";
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		await testHelper.createVideo(account._id.toString());
	});

	it("Missing payload data | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.post("/api/videos/signed-url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Video limit reached | 403 E_REQUEST_FORBIDDEN ", async () => {
		jest.spyOn(VideoModel.prototype, "countDocuments")
			.mockResolvedValueOnce(account.subscription.maxInteractiveVideoLimit);
		const res = await supertest(expressApp)
			.post("/api/videos/signed-url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ fileName: filename });
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});


	it("Successfully generated signed URL | 200 ", async () => {
		const res = await supertest(expressApp)
			.post("/api/videos/signed-url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ fileName: filename });
		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("signedURL");
		expect(res.body).toHaveProperty("tempFilename");
	});

});

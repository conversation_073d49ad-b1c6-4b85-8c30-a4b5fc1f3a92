import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { EmailTemplateEnum } from "./email.interface";

describe("POST /api/email", () => {
	let expressApp: express.Express;
	const emailPostApi = "/api/email";
	const fromEmail = "<EMAIL>";
	const toEmail = "<EMAIL>";
	const subject = "Test Email";
	const message = "Test Message";
	const template = EmailTemplateEnum.EMAIL_LEAD;
	const locale = LocaleAPI.EN_US;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	beforeEach(async () => {
		jest.clearAllMocks();
	});

	afterEach(() => {
		jest.restoreAllMocks();
		jest.resetAllMocks();
	});

	it("should fail to send email because of missing payload.", async () => {
		global.fetch = jest.fn().mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({}),
			status: 200,
			statusText: "OK"
		});
		const res = await supertest(expressApp)
			.post(emailPostApi)
			.set("x-api-version", "1")
			.field("fromEmail", fromEmail)
			.field("toEmail", toEmail)
			.field("message", message)
			.field("template", template)
			.field("locale", locale);
		expect(res.status).toBe(400);
	});

	it("should fail to send email because of network call failure.", async () => {
		global.fetch = jest.fn().mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({}),
			status: 500,
			statusText: "Network Error"
		});
		const res = await supertest(expressApp)
			.post(emailPostApi)
			.set("x-api-version", "1")
			.field("fromEmail", fromEmail)
			.field("toEmail", toEmail)
			.field("subject", subject)
			.field("message", message)
			.field("template", template)
			.field("locale", locale);
		expect(res.status).toBe(200);
	});

	it("should send email successfully 200.", async () => {
		global.fetch = jest.fn().mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({}),
			status: 200,
			statusText: "OK"
		});
		const res = await supertest(expressApp)
			.post(emailPostApi)
			.set("x-api-version", "1")
			.field("fromEmail", fromEmail)
			.field("toEmail", toEmail)
			.field("subject", subject)
			.field("message", message)
			.field("template", template)
			.field("locale", locale);
		expect(res.status).toBe(200);
	});
});

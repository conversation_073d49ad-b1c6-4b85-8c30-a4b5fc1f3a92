import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { UserModel } from "../modules/user/user.model";
import { APIError } from "../utils/helpers/apiError";
import { IUser } from "../modules/user/user.interfaces";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { randomBytes } from "crypto";


describe("PATCH api/users/:userId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let userId: string;
	let originalUser: IUser;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "patch.users.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		userId = testHelper.getUserId(accessToken);

		const userModel = new UserModel(null);
		originalUser = await userModel.readOneById(userId);
	});

	it("return 401 [E_INVALID_AUTHORIZATION] - invalid userId in the authentication token", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.patch(`/api/users/${mockId}`)
			.set("x-api-version", "2")
			.set("Authorization", "Bearer 123");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("return 403 [E_REQUEST_FORBIDDEN] - userId in parameter does not match userId in access token",	async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.patch(`/api/users/${mockId}`)
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("return 404 [E_DOCUMENT_NOT_FOUND] when the user document does not exist", async () => {
		jest.spyOn(UserModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.patch(`/api/users/${userId}`)
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("return 200 with document result with no update data", async () => {
		const res = await supertest(expressApp)
			.patch(`/api/users/${userId}`)
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("user");
		expect(res.body.user).toHaveProperty("_id");
		expect(res.body.user).toHaveProperty("firstName");
		expect(res.body.user).toHaveProperty("lastName");
		expect(res.body.user).toHaveProperty("createdAt");
		expect(res.body.user).toHaveProperty("updatedAt");
		expect(res.body.user._id.toString()).toBe(userId);
		expect(res.body.user.firstName).toBe(originalUser.firstName);
		expect(res.body.user.lastName).toBe(originalUser.lastName);
	});

	it("return 200 with document result containing new firstName and lastName", async () => {
		const res = await supertest(expressApp)
			.patch(`/api/users/${userId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("firstName", "updated_firstname")
			.field("lastName", "updated_lastname");

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("user");
		expect(res.body.user).toHaveProperty("_id");
		expect(res.body.user).toHaveProperty("firstName");
		expect(res.body.user).toHaveProperty("lastName");
		expect(res.body.user).toHaveProperty("createdAt");
		expect(res.body.user).toHaveProperty("updatedAt");
		expect(res.body.user._id).toBe(userId);
		expect(res.body.user.firstName).toBe("updated_firstname");
		expect(res.body.user.lastName).toBe("updated_lastname");
	});
});

import mongoose, { ClientSession } from "mongoose";
import { IAccount } from "../../modules/account/account.interfaces";
import { AccountModel } from "../../modules/account/account.model";
import { MetricVideoPlayTimeModel } from "../../modules/metricVideoPlayTime/metricVideoPlayTime.model";
import { MetricVideoClickModel } from "../metricVideoClick/metricVideoClick.model";
import {
	MetricImpressionModel,
	MetricImpressionCreateOne
} from "../metricImpression/metricImpression.model";
import { MetricUserEngagementModel } from "../metricUserEnagement/metricUserEnagement.model";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IPostEventsPayload } from "./events.interfaces";
import { aggregateMetricUserEngagement } from "../../services/mongodb/metricUserEngagement.service";
import { readShoppableCollection } from "../../services/mongodb/shoppableCollection.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import {
	EventNames,
	EventSnippetTypes
} from "./events.enums";
import { MetricVideoShareModel } from "../metricVideoShare/metricVideoShare.model";
import { MetricCollectionShareModel } from "../metricCollectionShare/metricCollectionShare.model";
import { MetricVideoEmbedModel } from "../metricVideoEmbed/metricVideoEmbed.model";
import { MetricPhonePressModel } from "../metricPhonePress/metricPhonePress.model";
import { MetricEmailSubmitModel } from "../metricEmailSubmit/metricEmailSubmit.model";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { EventBuffer } from "../eventBuffer/eventBuffer.interfaces";
import { PostEventJoi } from "./events.joi";
import { EventBufferModel } from "../eventBuffer/eventBuffer.model";
import { IVideoPlayTimeInput } from "../metricVideoPlayTime/metricVideoPlayTime.interfaces";

import { MetricVisibleImpressionModel } from "../metricVisibleImpression/metricVisibleImpression.model";
import { MetricVisibleImpressionCreateOne } from "../metricVisibleImpression/metricVisibleImpression.interface";
import { MetricVideoEngagementModel } from "../metricVideoEngagement/metricVideoEngagement.model";


export class EventModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	public static shouldIgnoreEvent = (eventName: string): boolean => {
		if (eventName === EventNames.THUMBNAIL_CTA_IMPRESSION) {
			return true;
		}

		return false;
	};

	public createOne = async (eventBuffer: EventBuffer): Promise<void> => {
		const inputPayload: IPostEventsPayload = EventBufferModel.fromBinaryBuffer(eventBuffer) as IPostEventsPayload;
		const validatedPayload = await PostEventJoi.validateAsync(inputPayload);

		if (EventModel.shouldIgnoreEvent(validatedPayload.eventName)) {
			return;
		}

		try {
			await startDBTransaction(this.session);

			let accountDocument: IAccount | null = null;
			if (validatedPayload.accountId) {
				const accountModel = new AccountModel(this.session);
				accountDocument = await accountModel.readOneById(validatedPayload.accountId);
			}

			await this.processMetrics(inputPayload, accountDocument, eventBuffer.createdAt);

			const eventBufferModel = new EventBufferModel();
			await eventBufferModel.deleteEventBuffer(eventBuffer, this.session);

			await completeDBTransaction(this.session);

		} catch (error: unknown) {
			await cancelDBTransaction(this.session);
			throw error;
		}
	};

	private async processMetrics(
		inputPayload: IPostEventsPayload,
		accountDocument: IAccount | null,
		createdAt: Date
	): Promise<void> {

		if (inputPayload.eventName === EventNames.SNIPPET_IMPRESSION && accountDocument) {
			await this.processSnippetImpression(inputPayload, accountDocument, createdAt);
		}

		if (inputPayload.eventName === EventNames.SNIPPET_VIEWABLE_IMPRESSION && accountDocument) {
			await this.processSnippetVisibleImpression(inputPayload, accountDocument, createdAt);
		}

		else if (inputPayload.eventName === EventNames.VIDEO_PLAY_POSITION && accountDocument) {
			await this.processVideoPosition(inputPayload, accountDocument, createdAt);
		}

		else if (inputPayload.eventName === EventNames.FEATURED_LINK_PRESS && accountDocument) {
			await this.processVideoClick(inputPayload, accountDocument, createdAt);
		}

		else if (inputPayload.eventName === EventNames.THUMBNAIL_CTA_PRESS &&
			inputPayload.snippet.type !== EventSnippetTypes.PLAYER && accountDocument) {
			await this.processUserEngagementForCollection(inputPayload, accountDocument, createdAt);
		}

		else if (inputPayload.eventName === EventNames.THUMBNAIL_CTA_PRESS &&
			inputPayload.snippet.type === EventSnippetTypes.PLAYER && accountDocument) {
			await this.processUserEngagementForVideo(inputPayload, accountDocument, createdAt);
		}

		else if (inputPayload.eventName === EventNames.VIDEO_SHARE_LINK_COPY) {
			if (inputPayload?.eventDimensions?.videoId) {
				const MetricVideoShare = new MetricVideoShareModel(this.session);
				await MetricVideoShare.createOne(
					{
						createdAt: createdAt,
						accountId: new mongoose.Types.ObjectId(inputPayload.accountId),
						videoId: new mongoose.Types.ObjectId(inputPayload.eventDimensions.videoId)
					}
				);
			}
		}

		else if (inputPayload.eventName === EventNames.COLLECTION_SHARE_LINK_COPY) {
			if (inputPayload?.eventDimensions?.collectionId) {
				const MetricCollectionShare = new MetricCollectionShareModel(this.session);
				await MetricCollectionShare.createOne(
					{
						createdAt: createdAt,
						accountId: new mongoose.Types.ObjectId(inputPayload.accountId),
						collectionId: new mongoose.Types.ObjectId(inputPayload.eventDimensions.collectionId)
					}
				);
			}
		}

		else if (inputPayload.eventName === EventNames.VIDEO_EMBED_COPY_THUMBNAIL) {
			if (inputPayload?.eventDimensions?.videoId) {
				const MetricVideoEmbed = new MetricVideoEmbedModel(this.session);
				await MetricVideoEmbed.createOne(
					{
						createdAt: createdAt,
						accountId: new mongoose.Types.ObjectId(inputPayload.accountId),
						videoId: new mongoose.Types.ObjectId(inputPayload.eventDimensions.videoId)
					}
				);
			}
		}

		else if (inputPayload.eventName === EventNames.FEATURED_PHONE_PRESS) {
			if (inputPayload?.eventDimensions?.videoId) {
				const metricPhonePressModel = new MetricPhonePressModel(this.session);
				await metricPhonePressModel.registerPhonePress(
					inputPayload.eventDimensions.videoId,
					inputPayload.accountId,
					createdAt
				);
			}
		}

		else if (inputPayload.eventName === EventNames.FEATURED_EMAIL_SUBMIT) {
			if (inputPayload?.eventDimensions?.videoId) {
				const metricEmailSubmitModel = new MetricEmailSubmitModel(this.session);
				await metricEmailSubmitModel.registerEmailSubmit(
					inputPayload.eventDimensions.videoId,
					inputPayload.accountId,
					createdAt
				);
			}
		}
	}

	public async processVideoPosition(inputPayload: IPostEventsPayload, accountDocument: IAccount, createdAt: Date
	): Promise<void> {
		const videoState = inputPayload.eventDimensions.videoState;
		const videoSecondsPosition = inputPayload.eventDimensions.videoSecondsPosition;
		const accountId = inputPayload.accountId;
		const videoId = inputPayload.eventDimensions.videoId;
		const playId = inputPayload.playId;
		const appSessionId = inputPayload.appSessionId;
		const videoSecondsLength = inputPayload.eventDimensions.videoSecondsLength;
		const isShareMode = inputPayload.isShareMode;
		const videoPlayStatus = inputPayload.eventDimensions.videoPlayStatus;

		if (videoSecondsPosition < 1 || videoState != "loaded" || videoSecondsLength < 1) {
			return;
		}
		const playedPercent = Math.floor((videoSecondsPosition / videoSecondsLength) * 100);

		if (playedPercent < 0 || playedPercent > 100) {
			throw new APIError(APIErrorName.E_INVALID_METRIC_INPUT,
				`Invalid playedPercent=${playedPercent} for videoId=${videoId} and appSessionId=${appSessionId}`);
		}

		const metricVideoPlayTimeModel = new MetricVideoPlayTimeModel(this.session);
		if (await metricVideoPlayTimeModel.isPlaysMetricLimitReached(accountDocument)) {
			return;
		}

		const videoPlayTimeInput: IVideoPlayTimeInput = {
			accountId: accountId,
			videoId: videoId,
			playId: playId,
			appSessionId: appSessionId,
			videoPlayStatus: videoPlayStatus,
			videoSecondsPosition: videoSecondsPosition,
			videoSecondsLength: videoSecondsLength
		};

		if (isShareMode && inputPayload.eventDimensions.collectionId) {
			videoPlayTimeInput.collectionShare = inputPayload.eventDimensions.collectionId;
		} else if (isShareMode && inputPayload.eventDimensions.videoId) {
			videoPlayTimeInput.videoShare = inputPayload.eventDimensions.videoId;
		}
		const playTimeMetricResult = await metricVideoPlayTimeModel
			.upsertVideoPlayTimeAndComputeMetrics(videoPlayTimeInput, accountDocument, createdAt);

		const interactiveVideoModel = new InteractiveVideoModel(this.session);
		const interactiveVideo = await interactiveVideoModel
			.updateVideoMetrics(videoPlayTimeInput, playTimeMetricResult);
		const metricVideoEngagementModel = new MetricVideoEngagementModel(this.session);
		await metricVideoEngagementModel.upsert(interactiveVideo, createdAt);
	}

	private async processVideoClick(
		inputPayload: IPostEventsPayload,
		accountDocument: IAccount,
		createdAt: Date
	): Promise<void> {
		if (!inputPayload.eventDimensions.videoId) {
			throw new APIError(
				APIErrorName.E_INVALID_METRIC_INPUT,
				"undefined videoId in inputPayload for processVideoClick"
			);
		}

		if (!inputPayload.eventDimensions.productId) {
			throw new APIError(
				APIErrorName.E_INVALID_METRIC_INPUT,
				"undefined productId in inputPayload for processVideoClick"
			);
		}

		const videoId = new mongoose.Types.ObjectId(
			inputPayload.eventDimensions.videoId
		);

		const productId = new mongoose.Types.ObjectId(
			inputPayload.eventDimensions.productId
		);

		const shoppableVideoModel = new InteractiveVideoModel(this.session);
		const shoppableVideoDoc = await shoppableVideoModel.readOneById(videoId.toString());

		const product = shoppableVideoDoc.products.find(
			(p) => p._id.toString() === productId.toString()
		);

		if (!product) {
			throw new APIError(
				APIErrorName.E_DOCUMENT_NOT_FOUND,
				`Product with ID ${productId} not found in shoppableVideo with ID ${videoId}.`
			);
		}

		const metricVideoClickModel: MetricVideoClickModel = new MetricVideoClickModel(this.session);

		if (await metricVideoClickModel.isClickMetricLimitReached(accountDocument) === true) {
			return;
		}

		const updatedShoppableVideoDoc = await shoppableVideoModel.incrementProductLinkClick(
			videoId.toString(),
			product
		);

		if (!updatedShoppableVideoDoc) {
			throw new APIError(
				APIErrorName.E_SERVICE_FAILED,
				`Failed to update linkClicks in shoppableVideo for videoId=${videoId}`
			);
		}

		const metricVideoClickDocument = await metricVideoClickModel.createOne({
			createdAt: createdAt,
			videoId: videoId.toString(),
			productId: productId.toString()
		}, accountDocument);
		if (metricVideoClickDocument.hasError()) {
			metricVideoClickDocument.getError().throwIfNot([APIErrorName.E_REQUEST_FORBIDDEN]);
		}
	}

	public async processSnippetImpression(
		inputPayload: IPostEventsPayload,
		accountDocument: IAccount,
		createdAt: Date
	): Promise<void> {
		try {
			const collectionId = inputPayload.eventDimensions.collectionId;
			const videoId = inputPayload.eventDimensions.videoId;
			const isShareMode = inputPayload.isShareMode;

			const createData: MetricImpressionCreateOne = {
				createdAt: createdAt,
				collectionId: collectionId ? new mongoose.Types.ObjectId(collectionId) : undefined,
				videoId: videoId ? new mongoose.Types.ObjectId(videoId) : undefined
			};

			if (isShareMode && collectionId) {
				createData.collectionShare = new mongoose.Types.ObjectId(collectionId);
			} else if (isShareMode && videoId) {
				createData.videoShare = new mongoose.Types.ObjectId(videoId);
			}

			const metricImpressionModel: MetricImpressionModel = new MetricImpressionModel(this.session);
			await metricImpressionModel.createOne(accountDocument, createData);
		} catch (error: unknown) {
			APIError.fromUnknownError(error).throwIfNot([APIErrorName.E_IMPRESSION_LIMIT_REACHED]);
		}
	}

	public async processSnippetVisibleImpression(inputPayload: IPostEventsPayload, accountDocument: IAccount,
		createdAt: Date): Promise<void> {
		const collectionId = inputPayload.eventDimensions.collectionId;
		const videoId = inputPayload.eventDimensions.videoId;
		const createData: MetricVisibleImpressionCreateOne = {
			createdAt: createdAt,
			collectionId: collectionId ? new mongoose.Types.ObjectId(collectionId) : undefined,
			videoId: videoId ? new mongoose.Types.ObjectId(videoId) : undefined
		};
		const metricVisibleImpressionModel = new MetricVisibleImpressionModel(this.session);
		await metricVisibleImpressionModel.createOne(accountDocument, createData);
	}

	protected async processUserEngagementForCollection(
		inputPayload: IPostEventsPayload,
		accountDocument: IAccount,
		createdAt: Date
	): Promise<void> {
		const accountId = new mongoose.Types.ObjectId(inputPayload.accountId);
		const collectionId = new mongoose.Types.ObjectId(inputPayload.eventDimensions.collectionId);
		const sessionId = new mongoose.Types.ObjectId(inputPayload.user.sessionId);

		if (!accountId || !collectionId || !sessionId) {
			throw new APIError(
				APIErrorName.E_INVALID_METRIC_INPUT,
				`undefined accountId=${accountId} or collectionId=${collectionId} or sessionId=${sessionId}`
			);
		}

		const collectionDocument = await readShoppableCollection({ _id: collectionId }, this.session);

		if (!collectionDocument) {
			throw new APIError(
				APIErrorName.E_DOCUMENT_NOT_FOUND,
				`shoppableCollection with collectionId=${collectionId} does not exists.`
			);
		}

		const metricUserEngagementModel = new MetricUserEngagementModel(this.session);
		await metricUserEngagementModel.createOne(
			{
				createdAt: createdAt,
				collectionId: collectionId,
				userSessionId: sessionId
			},
			accountDocument
		);

		const pipeline = [
			{ $match: { accountId: accountId, collectionId: collectionId } },
			{ $group: { _id: "$userSessionId" } },
			{ $count: "uniqueSessions" }
		];

		const userEngagements = await aggregateMetricUserEngagement(pipeline, this.session);
		const engagementCount = userEngagements?.length > 0 ? userEngagements[0].uniqueSessions : 1;
		const orderCount = collectionDocument.orderCount || 0;
		const newUserCVR = (orderCount / engagementCount) * 100;

		const interactiveCollectionModel = new InteractiveCollectionModel(this.session);
		await interactiveCollectionModel.updateSessionMetrics(collectionId.toString(), engagementCount, newUserCVR);
	}

	private async processUserEngagementForVideo(
		inputPayload: IPostEventsPayload,
		accountDocument: IAccount,
		createdAt: Date
	): Promise<void> {
		const accountId = new mongoose.Types.ObjectId(inputPayload.accountId);
		const videoId = new mongoose.Types.ObjectId(inputPayload.eventDimensions.videoId);
		const sessionId = new mongoose.Types.ObjectId(inputPayload.user.sessionId);

		if (!accountId || !videoId || !sessionId) {
			throw new APIError(
				APIErrorName.E_INVALID_METRIC_INPUT,
				`undefined accountId=${accountId} or videoId=${videoId} or sessionId=${sessionId}`
			);
		}

		const interactiveVideoModel = new InteractiveVideoModel(this.session);
		const interactiveVideoDoc = await interactiveVideoModel.readOneById(videoId.toString());

		const metricUserEngagementModel = new MetricUserEngagementModel(this.session);
		await metricUserEngagementModel.createOne(
			{
				createdAt: createdAt,
				videoId: interactiveVideoDoc._id,
				userSessionId: sessionId
			},
			accountDocument
		);
	}
}

import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import mongoose from "mongoose";
import {
	InteractiveCollectionModel,
	InteractiveCollectionUpdateOne
} from "./interactiveCollection.model";
import {
	APIErrorName,
	Permission
} from "../../interfaces/apiTypes";
import { IPutCollectionPayload } from "./interactiveCollection.interface";
import { IShoppableVideo } from "../interactiveVideo/interactiveVideo.interface";
import { readAccount } from "../../services/mongodb/account.service";
import { readShoppableVideos } from "../../services/mongodb/shoppableVideo.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";

export const putShoppableCollectionController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		await startDBTransaction(res.locals.session);

		const requestBody = req.body as IPutCollectionPayload;

		const accountDocument = await readAccount({
			query: {
				_id: req.accountToken.account._id
			},
			path: req.url,
			permissions: [
				Permission.ALL
			],
			dbSession: res.locals.session
		});

		if (!accountDocument || accountDocument === null) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR,
				"Failed to read the account document with _id " +
				`${req.accountToken.account._id} supplied in access token`);
		}

		const updateSet: InteractiveCollectionUpdateOne = {};

		if (requestBody.title !== undefined) {
			updateSet.title = requestBody.title;
		}
		if (requestBody.buttonBackgroundColor !== undefined) {
			updateSet.buttonBackgroundColor = requestBody.buttonBackgroundColor;
		}
		if (requestBody.buttonBackgroundBlur !== undefined) {
			updateSet.buttonBackgroundBlur = requestBody.buttonBackgroundBlur;
		}
		if (requestBody.iconTextColor !== undefined) {
			updateSet.iconTextColor = requestBody.iconTextColor;
		}
		if (requestBody.displayFont !== undefined) {
			updateSet.displayFont = requestBody.displayFont;
		}
		if (requestBody.carouselBorderRadius !== undefined){
			updateSet.carouselBorderRadius = requestBody.carouselBorderRadius;
		}
		if (requestBody.carouselIsCentered !== undefined) {
			updateSet.carouselIsCentered = requestBody.carouselIsCentered;
		}
		if (requestBody.carouselMargin !== undefined){
			updateSet.carouselMargin = requestBody.carouselMargin;
		}
		if (requestBody.carouselGap !== undefined){
			updateSet.carouselGap = requestBody.carouselGap;
		}
		if (requestBody.widgetBorderRadius !== undefined){
			updateSet.widgetBorderRadius = requestBody.widgetBorderRadius;
		}
		if (requestBody.widgetPosition !== undefined){
			updateSet.widgetPosition = requestBody.widgetPosition;
		}
		if (requestBody.inlineBorderRadius !== undefined) {
			updateSet.inlineBorderRadius = requestBody.inlineBorderRadius;
		}


		let videoDocuments: IShoppableVideo[] | null = null;
		if (requestBody.shoppableVideos) {
			updateSet.shoppableVideos = requestBody.shoppableVideos.map(id => new mongoose.Types.ObjectId(id));
			if (req.params.collectionId === accountDocument.defaultCollectionId.toString()) {
				videoDocuments = await readShoppableVideos({
					query: {
						accountId: req.accountToken.account._id
					},
					path: req.url,
					permissions: [Permission.ALL],
					dbSession: res.locals.session
				});


				const videoIds = videoDocuments.map((videoDocument: IShoppableVideo) => videoDocument._id.toString());
				const updateSetVideoIds =
				updateSet.shoppableVideos.map((videoId: mongoose.Types.ObjectId) => videoId.toString());
				if (videoIds.length !== updateSetVideoIds.length ||
					!videoIds.every((videoId: string) => updateSetVideoIds.includes(videoId))
				) {
					throw new APIError(
						APIErrorName.E_INVALID_INPUT,
						"shoppableVideos in request do not include all Shoppable Videos in default collection"
					);
				}
			} else {
				videoDocuments = await readShoppableVideos({
					query: {
						_id: { $in: requestBody.shoppableVideos }
					},
					path: req.url,
					permissions: [Permission.ALL],
					dbSession: res.locals.session
				});

				if (
					!videoDocuments.every(
						videoDocument => videoDocument.accountId.toString() === req.accountToken?.account._id
					)
				) {
					throw new APIError(APIErrorName.E_INVALID_INPUT,
						"One or more shoppable videos in the request body do not " +
						"belong to the account referenced in the account token.");
				}
			}
		}

		const collectionModel: InteractiveCollectionModel = new InteractiveCollectionModel(res.locals.session);
		const updatedCollection = await collectionModel.updateOneById(req.params.collectionId, updateSet);

		const result = {
			shoppableCollection: (await collectionModel.redactData([updatedCollection]))[0]
		};

		await completeDBTransaction(res.locals.session);
		return res.status(200).json(result);

	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

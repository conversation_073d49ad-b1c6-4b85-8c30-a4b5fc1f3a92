import express, {
	Express,
	Request,
	Response
} from "express";
import { AccountSyncController } from "./account.sync.controller";

export class AccountSyncRouter {
	private controller: AccountSyncController = new AccountSyncController();
	private router = express.Router();

	constructor () {
		this.router.post(
			"/",
			[express.json({ limit: "2MB" })],
			(request: Request, response: Response) => {
				return this.controller.post(request, response);
			}
		);
	}

	public use = (expressServer: Express): void => {
		expressServer.use("/api/accounts/sync", this.router);
	};
}

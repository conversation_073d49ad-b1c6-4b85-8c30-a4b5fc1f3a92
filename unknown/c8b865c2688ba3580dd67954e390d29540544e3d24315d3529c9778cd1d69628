import {
	CoreMessageTypeEnum,
	CoreMessageSecret
} from "@player/core/core.message";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";
import { validateBackslash } from "@player/app/app.util";

export const postLikeVideo = async (videoId: string):Promise<void> => {
	try {
		const API_ENDPOINT = process.env.GP_SERVER_API_ENDPOINT;
		if (!API_ENDPOINT) {
			throw new Error("Missing API_ENDPOINT.");
		}
		const url = validateBackslash(API_ENDPOINT) + `api/shoppable-videos/${videoId}/like`;
		const myHeaders = new Headers();
		myHeaders.append("x-api-version", "3");
		const response = await fetch(url, {
			method: "POST",
			headers: myHeaders
		});
		if (!response.ok) {
			throw new Error(`postLikeVideo response is not ok, status=${response.status}`);
		}
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n postLikeVideo",
			scope: LogScope.ERROR
		});
	}
};

export const notifyCoreToStoreLikedVideo = (videoId: string): void => {
	try {
		const message = {
			type: CoreMessageTypeEnum.STORE_LIKED_VIDEO,
			referrer: window.location.href,
			data: { videoId }
		};
		const postEvent = CoreMessageSecret + JSON.stringify(message);
		window?.parent?.postMessage(postEvent, "*");
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n notifyCoreToStoreLikedVideo",
			scope: LogScope.ERROR
		});
	}
};

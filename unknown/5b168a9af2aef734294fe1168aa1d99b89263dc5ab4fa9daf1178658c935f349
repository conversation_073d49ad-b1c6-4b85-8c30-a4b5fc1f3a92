import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { AuthMethods } from "./authentication.enums";
import {
	IAuthentication,
	IAuthenticationOptions,
	UserAuthenticationData
} from "./authentication.interface";
import { AuthenticationModel } from "./authentication.model";
import bcrypt from "bcryptjs";
import { AuthenticationDBModel } from "./authenticationDBModel";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import { UserModel } from "../user/user.model";


export class EmailPasswordAuthentication extends AuthenticationModel {
	constructor (session: ClientSession | null) {
		super(session);
	}

	public async createOne(
		userId: string,
		accountId: string,
		password: string,
		options?: IAuthenticationOptions): Promise<IAuthentication>
	{
		const userModel = new UserModel(this.session);
		const user = await userModel.readOneById(userId);

		const authenticationSalt: string = bcrypt.genSaltSync(10);
		const passwordHash = bcrypt.hashSync(
			password,
			authenticationSalt
		);

		const authData: UserAuthenticationData = {
			email: user.email,
			passwordHash: passwordHash
		};

		if (await this.exists(authData)) {
			throw new APIError(APIErrorName.E_AUTH_EXISTS_DUPLICIATE, "authentication already exists.");
		}

		const createData: Partial<IAuthentication> = {
			data: authData,
			method: AuthMethods.EMAIL_PASSWORD,
			salt: authenticationSalt,
			userId: new mongoose.Types.ObjectId(user._id.toString()),
			verified: options?.verified ?? false,
			legalAgreement: options?.legalAgreement ?? true,
			super: false
		};

		const authenticationDocument = await super.createInternal(createData, accountId);

		return authenticationDocument;
	}

	public async testEmailPassword (email: string, password: string): Promise<IAuthentication> {
		const authRecord = await this.readOneByEmail(email);
		const passwordHash = bcrypt.hashSync(password, authRecord.salt);

		if (passwordHash !== (authRecord.data as UserAuthenticationData).passwordHash) {
			throw new APIError(APIErrorName.E_SIGN_IN_INCORRECT, "Passwords don't match");
		}

		return authRecord;
	}

	async updatePasswordAndVerify (authentication: IAuthentication, passwordHash: string):
	Promise<ModelResponse<IAuthentication>> {
		try {

			const authData = authentication.data as UserAuthenticationData;
			authData.passwordHash = passwordHash;

			const filter: FilterQuery<IAuthentication> = {
				_id: authentication._id
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};
			const update: Partial<IAuthentication> = {
				verified: true,
				data: authData
			};

			const document = await AuthenticationDBModel.findOneAndUpdate(filter, update, options);
			if (!document) {
				return new ModelResponse<IAuthentication>(
					null,
					new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Authentication not found")
				);
			}

			await this.syncVerify(document.userId.toString());

			return new ModelResponse<IAuthentication>(document, null);
		} catch (error: unknown) {
			return new ModelResponse<IAuthentication>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message)
			);
		}
	}
}

import {
	type Request,
	type Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import <PERSON><PERSON> from "joi";
import multer from "multer";
import { VersionJoi } from "../../base/base.joi";
import { OIDCModel } from "../oidc.model";

interface TokenPostPayload {
	apiVersion: number;
	oidcCode: string;
	oidcClientId: string;
}

const TokenPostPayloadSchema = Joi.object<TokenPostPayload>({
	apiVersion: VersionJoi.required(),
	oidcCode: Joi.string().required(),
	oidcClientId: Joi.string().required()
});

interface TokenPutPayload {
	apiVersion: number;
	oidcRefreshToken: string;
	oidcClientId: string;
}

const TokenPutPayloadSchema = Joi.object<TokenPutPayload>({
	apiVersion: VersionJoi.required(),
	oidcRefreshToken: Joi.string().required(),
	oidcClientId: Joi.string().required()
});

export class OIDCTokenController extends Controller {
	constructor() {
		super();
		this.router.post("/", [multer().none()], this.post.bind(this));
		this.router.put("/", [multer().none()], this.put.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: TokenPostPayload;
		try {
			validPayload = await TokenPostPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				oidcCode: request.body.oidcCode,
				oidcClientId: request.body.oidcClientId
			}
			);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);

			const oidcModel = new OIDCModel({ clientId: validPayload.oidcClientId });
			const result = await oidcModel.submitCode(validPayload.oidcCode);

			return response.status(200).json(result);

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	private async put(request: Request, response: Response): Promise<Response> {
		let validPayload: TokenPutPayload;
		try {
			validPayload = await TokenPutPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				oidcRefreshToken: request.body.oidcRefreshToken,
				oidcClientId: request.body.oidcClientId
			});
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.log().setResponse(response);
		}

		try {
			const version = Number(validPayload.apiVersion);
			await this.verifyAPIVersion(version, 1);

			const oidcModel = new OIDCModel({ clientId: validPayload.oidcClientId });
			const result = await oidcModel.refreshAccessToken(validPayload.oidcRefreshToken);
			return response.status(200).json(result);
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}
}

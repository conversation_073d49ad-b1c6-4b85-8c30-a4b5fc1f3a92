import mongoose, { ClientSession } from "mongoose";
import {
	IMetricVideoEmbedCreateInput,
	IMetricVideoEmbedCreated
} from "./metricVideoEmbed.interface";
import { MetricVideoEmbedCreatedDBModel } from "./metricVideoEmbedDB.model";

export class MetricVideoEmbedModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	async createOne(
		createData: IMetricVideoEmbedCreateInput
	): Promise<IMetricVideoEmbedCreated> {
		const options: mongoose.SaveOptions = {
			session: this.session
		};

		const newDocument = await new MetricVideoEmbedCreatedDBModel({
			createdAt: createData.createdAt,
			accountId: createData.accountId,
			videoId: createData.videoId
		}).save(options);

		return newDocument;
	}
}

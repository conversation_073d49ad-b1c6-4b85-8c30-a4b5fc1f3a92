import { ClientSession } from "mongoose";
import { EventBuffer } from "./eventBuffer.interfaces";
import { EventBufferDBModel } from "./eventBufferDB.model";

export class EventBufferModel {
	public async createOne (postEventBuffer: object): Promise<EventBuffer> {
		const binaryBuffer = EventBufferModel.toBinaryBuffer(postEventBuffer);

		const eventBufferDBModel = new EventBufferDBModel({
			attempts: 0,
			committed: false,
			buffer: binaryBuffer
		});

		const eventBuffer: EventBuffer = await eventBufferDBModel.save({});

		return eventBuffer;
	}

	public async deleteEventBuffer (eventBuffer: EventBuffer, session: ClientSession | null): Promise<void> {
		await EventBufferDBModel.deleteOne(
			{
				_id: eventBuffer._id
			},
			{
				session
			}
		);
	}

	public static toBinaryBuffer (postEventBuffer: object): Buffer {
		const binaryBuffer = Buffer.from(JSON.stringify(postEventBuffer));
		return binaryBuffer;
	}

	public static fromBinaryBuffer (eventBuffer: EventBuffer): object {
		const postEventBuffer = JSON.parse(eventBuffer.buffer.toString());
		return postEventBuffer;
	}
}

import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { MetricConversionDBModel } from "./metricConversionDB.model";
import { IAccount } from "../account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import {
	IMetricConversion,
	MetricConversionCreateOne
} from "./metricConversion.interfaces";

export class MetricConversionModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IMetricConversion>): Promise<number> {
		const count: number = await MetricConversionDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	private isConversionMetricsDisabled (account: IAccount): boolean {
		return (this.isConversionMetricsEnabled(account) !== true);
	}

	private isConversionMetricsEnabled (account: IAccount): boolean {
		return account?.subscription?.enableConversionMetrics ?? false;
	}

	async createOne (
		createData: MetricConversionCreateOne,
		account: IAccount
	): Promise<IMetricConversion> {
		try {
			if (this.isConversionMetricsDisabled(account)) {
				throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Metric conversions is not enabled").suppressLog();
			}

			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new MetricConversionDBModel({
				orderItemsCount: createData.orderItemsCount,
				userSessionId: new mongoose.Types.ObjectId(createData.userSessionId),
				accountId: account._id
			}).save(options);

			return newDocument;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}



	async readTotalPrevDayByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, number>>> {
		const now = new Date();
		const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
		const startOfPreviousDay = new Date(startOfToday.getTime() - (24 * 60 * 60 * 1000));
		const endOfPreviousDay = new Date(startOfToday.getTime() - 1);

		const accountIds =
		accounts.filter(account => account.subscription.type !== "basic").map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousDay, $lte: endOfPreviousDay }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalConversionDaily: { $sum: 1 }
				}
			}
		];

		const results = await MetricConversionDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, number>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_conversions found for the previous day")
			);
		}

		const totalConversionByAccountId: Map<string, number> = new Map(
			results.map(item => [item._id.toString(), item.totalConversionDaily])
		);

		return new ModelResponse<Map<string, number>>(totalConversionByAccountId, null);
	}

	async readTotalPrevWeekByAccounts(accounts: IAccount[]): Promise<ModelResponse<Map<string, number>>> {
		const now = new Date();
		const startOfThisWeek = new Date(Date.UTC(
			now.getUTCFullYear(),
			now.getUTCMonth(),
			now.getUTCDate() - now.getUTCDay() + 1
		));
		const startOfPreviousWeek = new Date(
			startOfThisWeek.getTime() - (7 * 24 * 60 * 60 * 1000)
		);
		const endOfPreviousWeek = new Date(startOfThisWeek.getTime() - 1);

		const accountIds =
		accounts.filter(account => account.subscription.type !== "basic").map(account => account._id);

		const pipeline = [
			{
				$match: {
					accountId: { $in: accountIds },
					createdAt: { $gte: startOfPreviousWeek, $lte: endOfPreviousWeek }
				}
			},
			{
				$group: {
					_id: "$accountId",
					totalConversionWeekly: { $sum: 1 }
				}
			}
		];

		const results = await MetricConversionDBModel.aggregate(pipeline).session(this.session);

		if (!results || results.length === 0) {
			return new ModelResponse<Map<string, number>>(
				null,
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
					"No metric_conversions found for the previous week")
			);
		}

		const totalConversionByAccountId: Map<string, number> = new Map(
			results.map(item => [item._id.toString(), item.totalConversionWeekly])
		);

		return new ModelResponse<Map<string, number>>(totalConversionByAccountId, null);
	}



}

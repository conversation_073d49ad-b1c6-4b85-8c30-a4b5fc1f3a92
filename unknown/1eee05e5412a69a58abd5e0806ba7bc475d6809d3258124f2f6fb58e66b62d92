import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import <PERSON><PERSON> from "joi";
import { BaseJoi } from "../../base/base.joi";
import { BaseRequest } from "../../base/base.interfaces";
import { VideoJobModel } from "./video.job.model";
import { cancelDBTransaction } from "../../../services/mongodb/transaction.service";

interface VideoJobStatusPayload extends BaseRequest {
	tempFilename: string;
}

const VideoJobStatusPayloadSchema = BaseJoi.append<VideoJobStatusPayload>({
	tempFilename: Joi.string().min(1).required()
});

export class VideoJobStatusController extends Controller {
	constructor() {
		super();
		this.router.get("/:tempFilename", [express.json()], this.get.bind(this));
	}

	private async get(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoJobStatusPayload;
		try {
			validPayload = await VideoJobStatusPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization,
				tempFilename: request.params.tempFilename
			}
			);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await this.verifyAccessToken(validPayload.accessToken);
			await this.verifyAccountToken(validPayload.accountToken);

			const videoJobModel: VideoJobModel = new VideoJobModel(null);
			const result = await videoJobModel.readVideoJobStatusByTempFilename(validPayload.tempFilename);

			return response.status(200).json(result);
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}


}

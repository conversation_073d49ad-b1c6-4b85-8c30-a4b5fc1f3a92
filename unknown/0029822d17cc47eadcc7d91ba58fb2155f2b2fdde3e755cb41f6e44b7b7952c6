/* eslint-disable no-await-in-loop */
import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import mongoose from "mongoose";
import { MetricConversionModel } from "./metricConversion.model";
import { IPostMetricConversionPayload } from "./metricConversion.interfaces";
import { readAccount2 } from "../../services/mongodb/account.service";
import { aggregateMetricUserEngagement } from "../../services/mongodb/metricUserEngagement.service";
import {
	readShoppableCollection,
	updateShoppableCollection
} from "../../services/mongodb/shoppableCollection.service";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";

export const postMetricConversionController = async (req: Request, res: Response): Promise<Response> => {
	try {
		const payload = req.body as IPostMetricConversionPayload;
		const accountId = new mongoose.Types.ObjectId(payload.accountId);
		const orderItemsCount = payload.orderItemsCount;
		const collectionIds = payload.collectionIds;
		await startDBTransaction(res.locals.session);

		if (!req.headers["x-api-version"]) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Missing API version."
			);
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(
				APIErrorName.E_INVALID_INPUT,
				"Invalid API version."
			);
		}

		const accountDocument = await readAccount2({ _id: accountId }, res.locals.session);

		if (!accountDocument) {
			throw new APIError(
				APIErrorName.E_MISSING_ACCOUNT_ID,
				"Account not found."
			);
		}

		const metricConversionModel = new MetricConversionModel(res.locals.session);
		await metricConversionModel.createOne(
			{
				userSessionId: payload.userSessionId,
				orderItemsCount: orderItemsCount
			},
			accountDocument
		);

		for (const collectionId of collectionIds) {
			const collectionObjectId = new mongoose.Types.ObjectId(collectionId);

			const collectionDocument = await readShoppableCollection({ _id: collectionObjectId }, res.locals.session);

			if (!collectionDocument) {
				continue;
			}

			const pipeline = [
				{ $match: { accountId: accountId, collectionId: collectionObjectId } },
				{ $group: { _id: "$userSessionId" } },
				{ $count: "uniqueSessions" }
			];

			const userEngagements = await aggregateMetricUserEngagement(pipeline, res.locals.session);
			const engagementCount = userEngagements?.length > 0 ? userEngagements[0].uniqueSessions : 1;

			const newOrderCount = (collectionDocument.orderCount || 0) + 1;
			const newUserCVR = (newOrderCount / engagementCount) * 100;

			await updateShoppableCollection(
				{ _id: collectionObjectId },
				{ $set: { orderCount: newOrderCount, userSessionCount: engagementCount, userCVR: newUserCVR } },
				res.locals.session
			);
		}

		await completeDBTransaction(res.locals.session);
		return res.sendStatus(201);
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

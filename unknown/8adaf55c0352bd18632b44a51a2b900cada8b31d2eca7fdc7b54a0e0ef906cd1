import React, { useEffect } from "react";
import { Container, Col } from "react-bootstrap";
import ResetPasswordForm from "@src/components/authentication/ResetPasswordForm";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Footer from "./Footer";
import ImageContentPanel from "@src/components/authentication/ImageContentPanel";
import { backgroundImage } from "@src/assets";
import { FlexCol, FullHeightRow } from "@src/styles/components";

const ResetPassword: React.FC = () => {
	const translation = useTranslation();

	useEffect(() => {
		document.title = translation.passwordResetPage.resetPassword;
	}, [translation.passwordResetPage.resetPassword]);

	return (
		<Container fluid>
			<FullHeightRow pl="0" pr="0">
				<Col sm="12" md={{ span: 8 }} style={{ paddingLeft: 0, paddingRight: 0 }}>
					<ImageContentPanel
						title={translation.passwordResetPage.imgTitle}
						copy={translation.passwordResetPage.imgCopy}
						backgroundUrl={backgroundImage}
					/>
				</Col>
				<FlexCol sm="12" md={{ span: 4 }}>
					<Header auth={false} />
					<ResetPasswordForm />
					<Footer />
				</FlexCol>
			</FullHeightRow>
		</Container>
	);
};

export default ResetPassword;

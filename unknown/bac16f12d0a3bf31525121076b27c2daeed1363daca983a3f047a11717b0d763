import { StripeProductsModel } from "./products.model";

describe("stripe products model", () => {
	it("should return a list of stripe products", async () => {
		const stripeProductsModel = new StripeProductsModel();
		const products = await stripeProductsModel.getProducts();
		expect(products).toBeInstanceOf(Array);

		for (const product of products) {
			expect(product).toHaveProperty("id");
			expect(product).toHaveProperty("name");
			expect(product).toHaveProperty("metadata");
			expect(product).toHaveProperty("description");
			expect(product.metadata).toHaveProperty("gp");
			expect(product.metadata.gp).toBe("true");
			product.metadata.type === "basic" && expect(product.metadata.allowCTALead).toBe("false");
			product.metadata.type === "pro" && expect(product.metadata.allowCTALead).toBe("true");
		}
	});
});

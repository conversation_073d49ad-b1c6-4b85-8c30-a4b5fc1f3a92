import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import mongoose from "mongoose";
import { aggregateInvitations } from "../../services/mongodb/invitations.service";
import { IInvitation } from "../../modules/invitation/invitation.interfaces";
import { InvitationStatus } from "../../modules/invitation/invitation.enum";

export const getInvitationsController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing account token");
		}

		const invitations: IInvitation[] = await aggregateInvitations([
			{
				$match: {
					accountId: new mongoose.Types.ObjectId(req.accountToken.account._id),
					status: {
						$in: [InvitationStatus.PENDING, InvitationStatus.DECLINED]
					}
				}
			},
			{
				$unset: [
					"salt"
				]
			}
		], null);

		return res.status(200).send({ invitations: invitations });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

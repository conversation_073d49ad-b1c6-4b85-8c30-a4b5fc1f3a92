import {
	Request,
	Response
} from "express";
import { APIError } from "../../../utils/helpers/apiError";
import {
	IPostAccountsTokenPayload,
	IAccount,
	IAccountToken
} from "../account.interfaces";
import jwt from "jsonwebtoken";
import {
	APIErrorName,
	Permission
} from "../../../interfaces/apiTypes";
import {
	getSecrets,
	ISecrets
} from "../../../modules/secrets/secrets.model";
import { readAccount } from "../../../services/mongodb/account.service";
import { AuthenticationModel } from "../../../modules/authentication/authentication.model";
export class AccountTokenController {
	async delete (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async get (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async list (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async post (request: Request, response: Response): Promise<Response> {
		try {
			if (!request.headers["x-api-version"]) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
			}

			const apiVersion = Number(request.headers["x-api-version"]);
			if (isNaN(apiVersion) || apiVersion < 1) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
			}

			const payload = request.body as IPostAccountsTokenPayload;

			if (!payload.accountId) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing accountId in request body");
			}

			if (!request.accessToken?.userId) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing userId in request token");
			}

			const authModel = new AuthenticationModel(null);
			const authenticationDocument = await authModel.readOneById(request.accessToken.authenticationId.toString());

			const accountIndex = authenticationDocument.accounts.findIndex(
				(account) =>
					account._id.toString() === payload.accountId.toString()
			);

			if (!authenticationDocument.super) {
				if (accountIndex === -1) {
					throw new APIError(
						APIErrorName.E_ACCESS_FORBIDDEN,
						"the account does not belong to the user"
					);
				}
			}

			const passwordRequiredToWrite = (accountIndex === -1);

			const accountDocument: IAccount | null = await readAccount({
				query: {
					_id: payload.accountId
				},
				path: request.url,
				permissions: [Permission.ALL]
			});

			if (!accountDocument) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "the account document does not exist");
			}

			const accountsToken: IAccountToken = {
				account: {
					_id: accountDocument._id.toString(),
					companyName: accountDocument.companyName,
					companyLogo: accountDocument.companyLogo ?? ""
				},
				authenticationId: authenticationDocument._id.toString(),
				userId: request.accessToken.userId,
				permissions: [Permission.ALL],
				passwordRequiredToWrite: passwordRequiredToWrite
			};

			const secrets: ISecrets = await getSecrets();
			const privateKey = `${secrets.hashkey.key}${authenticationDocument.salt}`;

			const token = jwt.sign(accountsToken, privateKey, {
				algorithm: "HS256"
			});

			return response.send({ token: token });
		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}

	async patch (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}

	async put (request: Request, response: Response): Promise<Response> {
		return response.status(501).send("Not Implemented");
	}
}

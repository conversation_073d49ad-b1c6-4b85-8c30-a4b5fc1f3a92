import {
	ISecrets,
	getSecrets
} from "../modules/secrets/secrets.model";
import { signJwt } from "./helpers/gpJwt.helper";
import jwt from "jsonwebtoken";
import { IAccount } from "../modules/account/account.interfaces";
import { IUser } from "../modules/user/user.interfaces";
import {
	IInvitation,
	IInvitationToken
} from "../modules/invitation/invitation.interfaces";
import {
	APITokenType,
	APIErrorName
} from "../interfaces/apiTypes";
import { IAccessToken } from "../modules/accessToken/accessToken.interface";
import { IAuthentication } from "../modules/authentication/authentication.interface";
import { APIError } from "./helpers/apiError";

interface IRefreshToken {
	type: APITokenType;
	authenticationId: string;
}

export const createAccessToken =
	async (authRecord: IAuthentication, userRecord: IUser): Promise<string | undefined> => {
		const secrets: ISecrets = await getSecrets();

		if (!secrets.hashkey.key) {
			const err = new Error();
			err.message = "Failed to retrieve hashkey from secrets.";
			err.name = APIErrorName.E_INTERNAL_ERROR;
			throw err;
		}

		const accessToken: IAccessToken = {
			type: APITokenType.ACCESS_TOKEN,
			authenticationId: authRecord._id.toString(),
			userId: authRecord.userId.toString(),
			firstName: userRecord.firstName ?? "",
			super: authRecord.super
		};

		const signedToken = signJwt(accessToken, `${secrets.hashkey.key}${authRecord.salt}`, {
			expiresIn: "30m"
		});

		return signedToken;
	};

export const createRefreshToken = async (authRecord: IAuthentication): Promise<string | undefined> => {
	const secrets: ISecrets = await getSecrets();

	if (!secrets.hashkey.key) {
		const err = new Error();
		err.message = "Failed to retrieve hashkey from secrets.";
		err.name = APIErrorName.E_INTERNAL_ERROR;
		throw err;
	}

	const refreshToken: IRefreshToken = {
		type: APITokenType.REFRESH_TOKEN,
		authenticationId: authRecord._id.toString()
	};

	const signedToken = signJwt(refreshToken, `${secrets.hashkey.key}${authRecord.salt}`, {
		expiresIn: "14d"
	});

	return signedToken;
};

export const createInvitationToken =
	async (invitationDocument: IInvitation, accountDocument: IAccount, userExists: boolean): Promise<string> => {
		const invitationToken: IInvitationToken = {
			invitationId: invitationDocument._id.toString(),
			accountId: accountDocument._id.toString(),
			email: invitationDocument.email,
			companyName: accountDocument.companyName,
			userExists: userExists
		};

		if (!invitationDocument.salt) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "IInvitation must have a defined property salt");
		}

		const secrets: ISecrets = await getSecrets();
		const inviteToken = jwt.sign(invitationToken, `${secrets.hashkey.key}${invitationDocument.salt}`);

		return inviteToken;
	};

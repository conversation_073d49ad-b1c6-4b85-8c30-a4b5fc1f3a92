import TestHelper from "./mocks/testHelper";
import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { LocaleAPI } from "../interfaces/apiTypes";
import { UserModel } from "../modules/user/user.model";
import { signJwt } from "../utils/helpers/gpJwt.helper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import {
	IAuthentication,
	UserAuthenticationData
} from "../modules/authentication/authentication.interface";
import {
	getSecrets,
	ISecrets
} from "../modules/secrets/secrets.model";


describe("POST /auth/reset-password positive mode tests", () => {
	let expressApp: express.Express;
	let accessToken: string;
	let originalAuthentication: IAuthentication;
	let token: string;
	let testHelper: TestHelper;

	const newUserPayload = {
		firstName: "Johnny",
		lastName: "Signin",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signin.positive.mode.test Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		testHelper = new TestHelper(expressApp);
		const authorizationData = await testHelper.signup(newUserPayload);
		accessToken = authorizationData.accessToken;

		originalAuthentication = await testHelper.getAuthentication(accessToken);

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(originalAuthentication.userId.toString());
		expect(userDocument.isPasswordSet).toBe(true);

		const res = await supertest(expressApp)
			.post("/api/auth/forgot-password")
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.send({
				email: newUserPayload.email,
				method: "email/password",
				callbackEndpoint: "https://domain.tld/reset-password"
			});
		expect(res.statusCode).toBe(200);

		// generate the same token that is sent to the user via email
		// with the /api/auth/forgot-password call
		const secrets: ISecrets = await getSecrets();
		token = signJwt(
			{ authenticationId: originalAuthentication._id },
			`${secrets.hashkey.key}${originalAuthentication.salt}`
		);
	});

	it("[Account verified successfully]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/reset-password")
			.set("x-api-version", "2")
			.set("Content-Type", "application/json")
			.send({
				token: token,
				password: "not same as Password1!"
			});

		expect(res.statusCode).toBe(200);

		const newAuthentication: IAuthentication = await testHelper.getAuthentication(accessToken);
		expect(newAuthentication._id.toString()).toBe(originalAuthentication._id.toString());
		expect(newAuthentication.userId.toString()).toBe(originalAuthentication.userId.toString());
		expect(newAuthentication.salt).toBe(originalAuthentication.salt);

		const originalAuthData = originalAuthentication.data as UserAuthenticationData;
		const newAuthData = newAuthentication.data as UserAuthenticationData;
		expect(originalAuthData.email).toBe(newAuthData.email);
		expect(originalAuthData.passwordHash).not.toBe(newAuthData.passwordHash);

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(newAuthentication.userId.toString());
		expect(userDocument.isPasswordSet).toBe(true);
	});
});

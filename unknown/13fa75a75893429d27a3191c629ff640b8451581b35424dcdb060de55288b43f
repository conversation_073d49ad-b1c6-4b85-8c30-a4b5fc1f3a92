import express from "express";
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";


describe("OIDC Authentication Tests | POST /api/oauth/oidc/token", () => {
	let expressApp: express.Express;
	const oidcClientId = "client-id-1";
	const oidcCode = "valid-id-token";

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
	});

	it("should submit oidc code and return 200 with oidc tokens", async () => {
		const res = await supertest(expressApp)
			.post("/api/oauth/oidc/token")
			.set("x-api-version", "1")
			.field("oidcCode", oidcCode)
			.field("oidcClientId", oidcClientId);

		expect(res.status).toBe(200);
		expect(res.body).toEqual({
			oidcAccessToken: "mockedAccessToken",
			oidcRefreshToken: "mockedRefreshToken",
			oidcIdToken: "mockedIdToken"
		});
	});
});

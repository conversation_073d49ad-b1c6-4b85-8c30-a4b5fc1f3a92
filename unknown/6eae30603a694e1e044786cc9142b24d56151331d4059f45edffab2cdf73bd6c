import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../../express";

const expressApp = createServer();
initExpressRoutes(expressApp);

describe("PlayTimeProcessorController", () => {
	it("should accept a request to process video play times.", async () => {
		const response = await supertest(expressApp)
			.post("/api/playtime/processor")
			.set("x-api-version", "1")
			.send();

		if (response.statusCode !== 200) {
			console.error(response.body);
		}
		expect(response.statusCode).toBe(200);
		expect(response.body).toHaveProperty("modifiedCount");
	});

	it("should reject the request if the api version is not supplied", async () => {
		const response = await supertest(expressApp)
			.post("/api/playtime/processor")
			.send();
		expect(response.statusCode).toBe(400);
	});
});

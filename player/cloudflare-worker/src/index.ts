interface Env {
	CDN_URL: string;
	ORIGIN_URL: string;
	AUTHOR_URL: string;
}

interface OpenGraphData {
	title: string;
	image: string;
	description: string;
	imageWidth: number;
	imageHeight: number;
	author: string;
	authorURL: string;
	createdAt: string;
	updatedAt: string;
}

interface InteractiveVideo {
	title: string;
	description: string;
	videoPosterPlayEmbedURL: string;
	videoPosterURL: string;
	videoWidthPx: number;
	videoHeightPx: number;
	createdAt: string;
	updatedAt: string;
}

interface InteractiveVideoDataCache {
	accountId: string;
	interactiveVideo: InteractiveVideo;
}
interface InteractiveCollectionDataCache {
	accountId: string;
	interactiveVideos: InteractiveVideo[];
}

export default {
	async fetch(request: Request, env: Env): Promise<Response> {
		const url = new URL(request.url);

		if (isAppRoute(url)) {
			const response = await processAppRoute(url, env);
			if (response) {
				return response;
			}
		}

		return await originResponse(url, env);
	}
};

async function originResponse (url: URL, env: Env): Promise<Response> {
	const cacheKey = `${env.ORIGIN_URL}${url.pathname}`;

	const cfOptions = {
		cacheTtl: 0,
		cacheEverything: false,
		cacheKey: cacheKey
	};

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let response = await fetch(`${env.ORIGIN_URL}${url.pathname}`, { cf: cfOptions } as any);


	response = new Response(response.body, response);
	response.headers.set("Cache-Control", "max-age=0");
	return response;
}

function isAppRoute (url: URL): boolean {
	const appRoutes = ["/c/", "/v/"];
	return appRoutes.some(route => url.pathname.startsWith(route)) || url.pathname === "/";
}

async function processAppRoute (url: URL, env: Env): Promise<Response> {
	let html: string | null = null;
	let response: Response | null = null;

	try {
		response = await fetch(env.ORIGIN_URL);
		if (!response.ok) {
			throw new Error("Failed to fetch origin");
		}

		html = await response.text();
		html = adjustAssetPaths(html, url);

		const data: OpenGraphData | null = await fetchDataCache(url, env);

		if (data) {
			html = adjustOpenGraphMeta(html, data);
		}

		return new Response(html, {
			headers: { "content-type": "text/html", ...response.headers },
			status: response.status,
			statusText: response.statusText
		});
	} catch (error: unknown) {
		if (response && response?.ok && html) {
			return new Response(html, {
				headers: { "content-type": "text/html", ...response.headers },
				status: response.status,
				statusText: response.statusText
			});
		}

		const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
		return new Response(errorMessage, { status: 500 });
	}
}

function adjustOpenGraphMeta (html: string, data: OpenGraphData): string {
	return html.replace(
		/<head>/,
		`<head>
		<meta name="title" content="${data.title}">
		<meta property="og:title" content="${data.title}">
		<meta name="author" content="${data.author}">
		<meta property="og:author" content="${data.authorURL}">
		<meta name="description" content="${data.description}">
		<meta property="og:description" content="${data.description}">
		<meta name="image" content="${data.image}">
		<meta property="og:image" content="${data.image}">
		<meta property="og:image:width" content="${data.imageWidth}">
		<meta property="og:image:height" content="${data.imageHeight}">
		<meta property="article:published_time" content="${data.createdAt}">
		<meta property="article:modified_time" content="${data.updatedAt}">
		<meta property="og:updated_time" content="${data.updatedAt}">
		`
	);
}

function adjustAssetPaths(html: string, url: URL): string {
	const origin = url.origin;
	// Replace all relative paths for assets with absolute paths
	return html.replace(/(href|src)="(\/?[^"]+)"/g, (match, p1, p2) => {
		// If the path is already absolute, do nothing
		if (p2.startsWith("http") || p2.startsWith("//")) {
			return match;
		}
		// If the path is relative, convert it to an absolute path
		const absolutePath = p2.startsWith("/")
			? `${origin}${p2}`
			: `${origin}/${p2}`;
		return `${p1}="${absolutePath}"`;
	});
}

async function fetchDataCache (url: URL, env: Env): Promise<OpenGraphData | null> {
	const pathname = url.pathname;

	const id = pathname.split("/")[2];

	if (pathname.startsWith("/c/")) {
		return await fetchCollectionData(id, env);
	}

	if (pathname.startsWith("/v/")) {
		return await fetchVideoData(id, env);
	}

	return null;
}

function createOpenGraphData (interactiveVideo: InteractiveVideo, env: Env): OpenGraphData {
	return {
		title: interactiveVideo.title,
		author: "Video",
		authorURL: env.AUTHOR_URL,
		description: interactiveVideo.description,
		image: interactiveVideo.videoPosterPlayEmbedURL,
		imageWidth: interactiveVideo.videoWidthPx,
		imageHeight: interactiveVideo.videoHeightPx,
		createdAt: new Date(interactiveVideo?.createdAt ?? Date.now()).toISOString().replace(/\.\d{3}Z$/, "Z"),
		updatedAt: new Date(interactiveVideo?.updatedAt ?? Date.now()).toISOString().replace(/\.\d{3}Z$/, "Z")
	};
}

async function fetchCollectionData (id: string, env: Env): Promise<OpenGraphData> {
	const url = `${env.CDN_URL}/data-cache/collections/${id}-collection.json`;
	const response = await fetch(url);
	const data = await response.json() as InteractiveCollectionDataCache;
	const videoData: InteractiveVideo = data.interactiveVideos[0];
	return createOpenGraphData(videoData, env);
}

async function fetchVideoData (id: string, env: Env): Promise<OpenGraphData> {
	const url = `${env.CDN_URL}/data-cache/videos/${id}-video.json`;
	const response = await fetch(url);
	const data = await response.json() as InteractiveVideoDataCache;
	const videoData = data.interactiveVideo;
	return createOpenGraphData(videoData, env);
}

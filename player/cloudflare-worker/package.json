{"name": "cloudflare-worker", "version": "0.0.0", "private": true, "scripts": {"start": "wrangler dev --config wrangler.toml", "types": "wrangler types --experimental-include-runtime", "audit": "better-npm-audit audit --level=low", "typecheck": "tsc --noEmit"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240903.0", "typescript": "^5.5.2", "wrangler": "^3.60.3"}, "dependencies": {"better-npm-audit": "^3.11.0"}}
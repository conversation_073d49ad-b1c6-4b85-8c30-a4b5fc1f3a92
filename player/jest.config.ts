import type { Config } from "@jest/types";
import { pathsToModuleNameMapper } from "ts-jest";
/*
 * For a detailed explanation regarding each configuration property and type check, visit:
 * https://jestjs.io/docs/configuration
 */

const config: Config.InitialOptions = {
	testEnvironment: "./CustomJestEnvironment.ts",
	testMatch: ["**/**/*.test.tsx", "**/**/*.test.ts"],
	clearMocks: true,
	collectCoverage: true,
	coverageDirectory: "coverage",
	moduleNameMapper: pathsToModuleNameMapper(
		{
			"@player/*": ["src/*"],
			"\\.(css|less)$": ["identity-obj-proxy"]
		},
		{
			prefix: "<rootDir>/"
		}
	),
	collectCoverageFrom: [
		"<rootDir>/src/**/*.{js,ts,tsx}"
	],
	setupFilesAfterEnv: ["<rootDir>/jest.setup.tsx"]
};
export default config;

/* eslint-disable @typescript-eslint/explicit-function-return-type */
/* eslint-disable @typescript-eslint/no-var-requires */
const webpack = require("webpack");
const Dotenv = require("dotenv-webpack");
const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const dotenv = require('dotenv');

module.exports = (env, options) => {
	const isDevelopment = options.mode !== "production";

	dotenv.config({ path: "./.env" });
	const cdnEndpoint = `${process.env.GP_CDN_ENDPOINT}/app/favicon.png`;

	return {
		mode: isDevelopment ? "development" : "production",
		entry: {
			main: "./src/index.tsx",
			"core": "./src/core/core.ts",
			"conversion": "./src/conversion/index.ts"
		},
		output: {
			path: path.resolve("./dist"),
			filename: "[name].js",
			publicPath: "/"
		},
		resolve: {
			extensions: [".ts", ".tsx", ".js"],
			fallback: {
				crypto: false
			},
			alias: {
				"@player": path.resolve("./src")
			}
		},
		module: {
			rules: [
				{
					test: /\.ts(x)?$/,
					exclude: /node_modules/,
					use: {
						loader: "babel-loader",
						options: {
							cacheDirectory: true
						}
					}
				},
				{
					test: /\.svg$/,
					use: ["@svgr/webpack"]
				}
			]
		},
		devtool: isDevelopment
			? "eval-cheap-module-source-map"
			: "nosources-source-map",
		optimization: {
			minimize: !isDevelopment
		},
		performance: {
			hints: false
		},
		plugins: [
			new Dotenv(),
			new CopyPlugin({
				patterns: [{ from: "./src/assets", to: "../dist/assets" }]
			}),
			new HtmlWebpackPlugin({
				minify: isDevelopment
					? false
					: {
						collapseWhitespace: true,
						keepClosingSlash: true,
						minifyCSS: true,
						minifyJS: true,
						minifyURLs: true,
						removeComments: true,
						removeEmptyAttributes: true,
						removeRedundantAttributes: true,
						removeScriptTypeAttributes: true,
						removeStyleLinkTypeAttributes: true,
						useShortDoctype: true
					},
				template: "./src/index.html",
				templateParameters: {
					faviconURL: cdnEndpoint
				},
				chunks: ["main"]
			}),
			...(isDevelopment ? [new webpack.HotModuleReplacementPlugin()] : [])
		],
		stats: {
			assetsSort: "!size",
			colors: true,
			entrypoints: false,
			errors: true,
			errorDetails: true,
			groupAssetsByChunk: false,
			groupAssetsByExtension: false,
			groupAssetsByInfo: false,
			groupAssetsByPath: false,
			modules: false,
			relatedAssets: true,
			timings: false,
			version: false
		},
		devServer: {
			static: path.join(__dirname, "/"),
			historyApiFallback: {
				index: "/index.html"
			},
			host: "0.0.0.0",
			hot: true,
			port: 5003
		}
	};
};

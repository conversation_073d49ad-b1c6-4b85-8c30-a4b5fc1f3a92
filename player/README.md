# Player App  - Interactive Video

Welcome to the Player App, an interactive video platform designed to enhance online site experiences with interactive video collections. 
This document serves as a guide for onboarding, development practices, and project conventions to ensure a smooth workflow and maintain code quality.

## Onboarding Steps

### 💻 Environment Setup

Prerequisites
- Node.js (LTS Version)
- Docker Engine (version supporting compose 3.8 or higher)
- Docker Compose
- Git (Pre-installed on macOS)

Getting Started
1. Follow Docker framework setup instructions
2. Run Player Client Application:</br>

   1. `Configure the .env to point to each of the appropriate ENDPOINTS, see player/.env.sample as a reference.`
   2. `cd ap-platform/player && git pull && npm install && npm run build`
   3. `Application can be accessed at: http://localhost:5003 where PLAYER_PORT=5003`

3. Load Player Client App with shoppable collection parameters:</br>

   1. `http://localhost:5003?collectionId=someId&gpSession=someId&videoId=someId`</br>
      PS: replace the parameters' values with the correct Ids.

4. Run the CloudFlare worker
   <br>	
   The CloudFlare worker can be run locally, connecting to the player application locally.
  
   1. Navigate to the `cloudflare-worker` folder.
   2. Copy the `wrangler.toml.sample` to `wrangler.toml`.
   3. Modify `wrangler.toml` URLs to the local application and CDN locations.
   4. Run `npm install`.
   5. Run `npm start`.
   6. The application can be accessed at: `http://localhost:8787/c/{collectionid}` or `http://localhost/v/{videoid}`.

   The following environment and secret variables are required to be set in GitHub when deploying per environment.
   - CLOUDFLARE_API_TOKEN as a secret.
   - CLOUDFLARE_ACCOUNT_ID as a secret.
   - CLOUDFLARE_CDN_URL as an env. eg. https://cdn.domain.tld
   - CLOUDFLARE_ORIGIN_URL as an env. eg. https://player.domain.tld
   - CLOUDFLARE_ROUTE_URL as an env. eg. https://share.domain.tld/*
   - CLOUDFLARE_WORKER_NAME as an env. eg. production-share-worker

   `.github/workflows/deploy/index.js` will use these values and dynamically generate a `wrangler.toml` file for deployment.

### 📚 Tech Stack
- Frontend: React, TypeScript
- Build Tools: Babel, Webpack
- Testing: Jest
- Linting: ESLint
- Styles: Styled-Components

## Development Guidelines

### Project Structure
- `src/assets`: Manages static assets like images and media files.
- `src/components`: Contains reusable React components.
- `src/conversion`: Home to Conversion script aka gp-conversion.js.
- `src/core`: Home to Core script aka core.js.
- `src/event`: Manages event sending to API and supported event names.
- `src/interactive`: Main interactive video frame component for the slider.
- `src/messaging`: Manages postMessage communication between core script and Player App.
- `src/session`: Handles visitor session ID creation, reading, and storage in cookies.
- `src/slider`: Parent component rendering the slider in mobile and desktop layouts.
- `src/theme`: Defines the application theme used globally.
- `src/app`: General utilities, global states, utility functions, and services.
- `src/tests`: Contains unit tests for the application.

### Coding Conventions
- `Variables & Functions`: Use descriptive names that clearly indicate purpose. Follow the camelCase naming convention.
- `Enums`: Suffix enum names with Enum, e.g., ThemeColorsEnum. Define enums in .enum.ts files.
- `Interfaces`: Prefix interface names with I, e.g., IThemeFonts. Define interfaces in .interface.ts files.
- `Components` & Hooks: Use PascalCase for naming. Place custom hooks in their own .hook.ts files within the feature/context directory they pertain to.

### Writing and Structuring New Features
- `Component Structure`: Organize components by feature within the src directory. Common components should reside in src/components.
- `State Management`: Utilize Recoil for global state management. Define atoms and selectors in their respective feature/context directories.

### Unit Testing
- Place unit tests within the src/tests directory, mirroring the structure of the src directory.
- Name test files using the format <component>.test.tsx or <function>.test.ts.
- Follow the Arrange-Act-Assert (AAA) pattern to structure your tests:
   - In the Arrange phase, you set up the testing environment. This includes initializing objects, creating mock data, configuring stubs or mocks (if necessary)
   - The Act phase involves executing the actual code that you're testing. This usually means calling a method or function with the arranged parameters and capturing its output.
   - In the Assert phase, you verify the outcome of the Act step. This involves checking whether the actual results match the expected results using assertions provided by Jest framework. 
- Run tests regularly during development to ensure new changes do not break existing functionality.


## By following these guidelines and conventions, we aim to maintain a coherent codebase that is easy to read, understand, and contribute to. Happy coding!



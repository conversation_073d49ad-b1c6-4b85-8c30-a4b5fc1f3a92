
export enum LayoutPosEnum {
	LEFT = "left",
	CENTER = "center",
	RIGHT = "right"
}
interface ILayoutPosObject {
	height: string;
	width: string;
}

export const LayoutPosEnumObj: Record<LayoutPosEnum, ILayoutPosObject> = {
	[LayoutPosEnum.LEFT]: { height: "70%", width: "75%" },
	[LayoutPosEnum.CENTER]: { height: "85%", width: "90%" },
	[LayoutPosEnum.RIGHT]: { height: "70%", width: "75%" }
};

export enum SwipeDirectionEnum {
	UP = "Up",
	DOWN = "Down"
}

import React, { useState } from "react";
import styled from "styled-components/macro";
import { useRecoilValue } from "recoil";
import {
	useSwipeable,
	SwipeEventData
} from "react-swipeable";
import {
	Flex,
	Box
} from "@player/components";
import { VideoFrame } from "@player/interactive";
import {
	IVideoData,
	SnippetTypeEnum,
	getVideosToRender,
	getCurrentVideoIndex,
	getPrevVideoIndex,
	adjustPercentString,
	readAppParams,
	appSessionIdAtom,
	isAppInPreviewModeAtom,
	videoInfoPayloadAtomFamily
} from "@player/app";
import { SwipeDirectionEnum } from "./slider.enum";
import {
	sliderVideoPlayerAtom,
	sliderConfigAtom
} from "./slider.state";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";

const TransitionContainer = styled(Box).attrs<{translateY: number}>(
	({ translateY }) => ({
		style: {
			transition: "transform 0.6s cubic-bezier(0.22, 0.61, 0.36, 1)",
			transform: `translateY(${translateY}px)`
		}
	})
)<{translateY: number}>`
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: black;
`;

const VideoFrameWrapper = styled(Box)`
  position: absolute;
  width: 100%;
  height: 100%;
`;

interface Props {
	videoData: IVideoData[];
	starterVideo: number;
}

enum SpecialKeyEnum {
	ElmCA = "elmCA",
	ElmNA = "elmNA",
	ElmDA = "elmDA"
}


enum CarouselElmEnum {
	PREV = "SV-Prev",
	CURRENT = "SV-Current",
	NEXT = "SV-Next"
}

export const MobileLayout: React.FC<Props> = ({
	videoData,
	starterVideo
}) => {
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
	const initialVideoIndex = videoData.findIndex(
		video => sliderVideoPlayer && video._id === sliderVideoPlayer.videoId
	);
	const [currentVideo, setCurrentVideo] =
	useState<number>(initialVideoIndex !== -1 ? initialVideoIndex : starterVideo);

	const [translateY, setTranslateY] = useState<number>(0);
	const [positionY, setPositionY] = useState<number>(0);
	const videosToRender = getVideosToRender(
		videoData.length,
		currentVideo
	);
	const [elmCA, setElmCA] = useState<number>(videosToRender[0]);
	const [elmNA, setElmNA] = useState<number>(videosToRender[1]);
	const [elmDA, setElmDA] = useState<number>(videosToRender[2]);

	const snippetType = readAppParams()?.snippetType === SnippetTypeEnum.ONSITE ? SnippetTypeEnum.ONSITE : undefined;

	const [showVideoOverlay, setShowVideoOverlay] = useState<boolean>(snippetType ? true : false);
	const [swipeEnabled, setSwipeEnabled] = useState<boolean>(!snippetType && !isAppInPreviewMode);

	const [isViewableElm, setIsViewableElm] = useState<Record<SpecialKeyEnum, boolean>>({
		[SpecialKeyEnum.ElmCA]: false,
		[SpecialKeyEnum.ElmNA]: true,
		[SpecialKeyEnum.ElmDA]: false
	});

	const updateIsViewableElm = (key: SpecialKeyEnum): void => {
		const newViewableState: Record<SpecialKeyEnum, boolean> = {
			[SpecialKeyEnum.ElmCA]: false,
			[SpecialKeyEnum.ElmNA]: false,
			[SpecialKeyEnum.ElmDA]: false
		};
		newViewableState[key] = true;
		setIsViewableElm(newViewableState);
	};

	const updateSVElmIndex = (elm: SpecialKeyEnum | null, value: number): void => {
		switch (elm) {
			case SpecialKeyEnum.ElmCA:
				setElmCA(value);
				break;
			case SpecialKeyEnum.ElmNA:
				setElmNA(value);
				break;
			case SpecialKeyEnum.ElmDA:
				setElmDA(value);
				break;
			default:
				break;
		}
	};

	const onSwiping = (eventData: SwipeEventData): void => {
		if (eventData.dir === SwipeDirectionEnum.UP || eventData.dir === SwipeDirectionEnum.DOWN) {
			setTranslateY(eventData.deltaY);
		}
	};
	const onSwipedUp = (): void => {
		sendAppEvent({
			accountId: sliderConfig.accountId,
			collectionId: sliderConfig.collectionId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.VIDEO_PLAY_POSITION,
			sliderVideoPlayer: sliderVideoPlayer,
			endedMethod: "next",
			videoPlayStatus: "stopped",
			isShareMode: sliderConfig.isShareMode
		}, isAppInPreviewMode);
		setTranslateY(0);
		setCurrentVideo((prev) =>
			getCurrentVideoIndex(videoData.length, prev)
		);
		setPositionY((prev) => prev - 1);
		MoveDOMOnSwipe(SwipeDirectionEnum.UP);
	};
	const onSwipedDown = (): void => {
		sendAppEvent({
			accountId: sliderConfig.accountId,
			collectionId: sliderConfig.collectionId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.VIDEO_PLAY_POSITION,
			sliderVideoPlayer: sliderVideoPlayer,
			endedMethod: "previous",
			videoPlayStatus: "stopped",
			isShareMode: sliderConfig.isShareMode
		}, isAppInPreviewMode);
		setTranslateY(0);
		setPositionY((prev) => prev + 1);
		setCurrentVideo((prev) => getPrevVideoIndex(videoData.length, prev));
		MoveDOMOnSwipe(SwipeDirectionEnum.DOWN);
	};

	const swipeHandlers = useSwipeable({
		onSwiping: swipeEnabled ? onSwiping : undefined,
		onSwipedUp: swipeEnabled ? onSwipedUp : undefined,
		onSwipedDown: swipeEnabled ? onSwipedDown : undefined,
		trackMouse: swipeEnabled,
		trackTouch: swipeEnabled
	});


	const MoveDOMOnSwipe = (dir: SwipeDirectionEnum): void => {
		const prevElm = document.getElementById(CarouselElmEnum.PREV);
		const currentElm = document.getElementById(CarouselElmEnum.CURRENT);
		const nextElm = document.getElementById(CarouselElmEnum.NEXT);
		if (!prevElm || !currentElm || !nextElm) return;

		const prevElmTemp = prevElm;
		const nextElmTemp = nextElm;

		if (dir === SwipeDirectionEnum.UP) {
			const nextVideosToRender = getVideosToRender(
				videoData.length,
				getCurrentVideoIndex(videoData.length, currentVideo)
			);
			currentElm.id = CarouselElmEnum.PREV;
			nextElm.id = CarouselElmEnum.CURRENT;

			prevElm.id = CarouselElmEnum.NEXT;
			prevElm.style.top = adjustPercentString(nextElmTemp.style.top, true);
			updateSVElmIndex(
				prevElmTemp.getAttribute("data-elm-key") as SpecialKeyEnum,
				nextVideosToRender[2]
			);
			updateIsViewableElm(
				nextElmTemp.getAttribute("data-elm-key") as SpecialKeyEnum
			);
		} else if (dir === SwipeDirectionEnum.DOWN) {
			const prevVideosToRender = getVideosToRender(
				videoData.length,
				getPrevVideoIndex(videoData.length, currentVideo)
			);
			prevElm.id = CarouselElmEnum.CURRENT;
			currentElm.id = CarouselElmEnum.NEXT;
			nextElm.id = CarouselElmEnum.PREV;
			nextElm.style.top = adjustPercentString(prevElmTemp.style.top, false);
			updateSVElmIndex(
				nextElmTemp.getAttribute("data-elm-key") as SpecialKeyEnum,
				prevVideosToRender[0]
			);
			updateIsViewableElm(
				prevElmTemp.getAttribute("data-elm-key") as SpecialKeyEnum
			);
		}
	};


	const handleCTAChevronClick = (dir: SwipeDirectionEnum): void => {
		if (dir === SwipeDirectionEnum.UP) {
			onSwipedDown();
		} else if (dir === SwipeDirectionEnum.DOWN) {
			onSwipedUp();
		}
	};

	const renderVideoFrame = (idx: number, elmName: SpecialKeyEnum): JSX.Element => {
		return (
			<VideoFrame
				isViewable={isViewableElm[elmName]}
				videoData={videoData[idx]}
				snippetType= {snippetType}
				setShowVideoOverlay={setShowVideoOverlay}
				showVideoOverlay={showVideoOverlay}
				handleCTAChevronClick={handleCTAChevronClick}
				setSwipeEnabled={setSwipeEnabled}
				videoInfoPayloadAtom={videoInfoPayloadAtomFamily(idx)}
			/>
		);
	};

	return (
		<Flex
			data-testid={"mobile-layout"}
			{...swipeHandlers}
			flexDirection={"column"}
			width={"100%"}
			height={"100%"}
			justifyContent="center"
			alignItems="center"
			position="relative"
			overflow="hidden"
		>
			<TransitionContainer
				data-testid={"mobile-transition-con"}
				translateY={positionY * window.innerHeight + translateY}
			>
				<VideoFrameWrapper
					id={CarouselElmEnum.PREV}
					style={{ top: "-100%" }}
					data-elm-key={SpecialKeyEnum.ElmCA}
				>
					{renderVideoFrame(elmCA, SpecialKeyEnum.ElmCA)}
				</VideoFrameWrapper>
				<VideoFrameWrapper
					id={CarouselElmEnum.CURRENT}
					style={{ top: "0%" }}
					data-elm-key={SpecialKeyEnum.ElmNA}
				>
					{renderVideoFrame(elmNA, SpecialKeyEnum.ElmNA)}
				</VideoFrameWrapper>
				<VideoFrameWrapper
					id={CarouselElmEnum.NEXT}
					style={{ top: "100%" }}
					data-elm-key={SpecialKeyEnum.ElmDA}
				>
					{renderVideoFrame(elmDA, SpecialKeyEnum.ElmDA)}
				</VideoFrameWrapper>
			</TransitionContainer>
		</Flex>
	);
};

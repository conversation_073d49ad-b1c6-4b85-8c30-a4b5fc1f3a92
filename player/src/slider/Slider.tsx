import React from "react";
import styled from "styled-components/macro";
import { ThemeProvider } from "styled-components";
import { MobileLayout } from "./Slider.mobile";
import { DeskLayout } from "./Slider.desktop";
import { useRecoilValue } from "recoil";
import { isPortraitAtom } from "@player/app";
import { useSlider } from "./useSlider.hook";
import { isAppInPreviewModeAtom } from "@player/app/app.state";
import {
	LoadingSpinner,
	Flex
} from "@player/components";
import { useParams } from "react-router-dom";
import { NotFound } from "@player/components/NotFound";
import { Player } from "@player/player/Player";

const Container = styled(Flex)<{isShare: boolean, isAppInPreviewMode?: boolean}>`
	background: ${({ isAppInPreviewMode, isShare }): string => {
		if (isAppInPreviewMode) return "none";
		if (isShare) return "black";
		return "rgba(0, 0, 0, 0.5)";
	}};
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	height: 100%;
`;

const PreviewContainer = styled(Flex)`
	background: black;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	height: 100%;
`;

interface Props {
	sharedCollectionId?: string;
	sharedVideoId?: string;
}

export const Slider: React.FC<Props> = ({ sharedCollectionId, sharedVideoId }) => {
	const {
		theme,
		loading,
		videoData,
		starterVideoIndex,
		isVideoPlayer
	} = useSlider(sharedCollectionId, sharedVideoId);

	const isPortrait = useRecoilValue(isPortraitAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const isShare = (sharedCollectionId || sharedVideoId) ? true : false;

	const renderContent = (): JSX.Element => {
		if (loading) {
			return (
				<LoadingSpinner />
			);
		} else if (!videoData && isAppInPreviewMode) {
			return (
				<PreviewContainer/>
			);
		} else if (!videoData || starterVideoIndex === null) {
			return (
				<NotFound />
			);
		} else if (isVideoPlayer) {
			return (
				<Player videoData={videoData[0]} />
			);
		} else if (isPortrait) {
			return (
				<MobileLayout
					videoData={videoData}
					starterVideo={starterVideoIndex}
				/>
			);
		}
		return (
			<DeskLayout
				videoData={videoData}
				starterVideo={starterVideoIndex}
			/>
		);
	};

	return (
		<ThemeProvider theme={theme}>
			<Container isShare={isShare} isAppInPreviewMode={isAppInPreviewMode}>
				{renderContent()}
			</Container>
		</ThemeProvider>
	);
};

export const SliderShare: React.FC = () => {
	const params = useParams<"collectionId" | "videoId">();
	const collectionId = params.collectionId;
	const videoId = params.videoId;
	return <Slider sharedCollectionId={collectionId} sharedVideoId={videoId} />;
};

import {
	appLog,
	LogScope
} from "@player/app/app.log.util";
import { ISliderConfig } from "./slider.interface";
import {
	IVideoData,
	AccountManifest,
	defaultVideo,
	SubscriptionTypeEnum,
	CollectionDataCache,
	VideoDataCache,
	SnippetTypeEnum
} from "@player/app/app.interface";
import { generateCustomTheme } from "@player/theme/defaults";
import { ITheme } from "@player/theme/theme.interface";
import {
	validateEnvVariables,
	readAppParams,
	validateBackslash
} from "@player/app/app.util";


export const defaultAccountManifest: AccountManifest = {
	allowLandscape: false,
	allowCTALead: false,
	allowThemes: false,
	allowSharing: false,
	hideVanityBranding: false,
	subscriptionType: SubscriptionTypeEnum.BASIC
};

interface IFetchSliderOutput {
	videoData: IVideoData[];
	starterVideoIndex: number;
	sliderConfig: ISliderConfig;
	collectionTheme: ITheme;
	isVideoPlayer: boolean;
}

const errorFromUnknown = (error: unknown): Error => {
	if (error instanceof Error) {
		return error;
	}

	return new Error("An error occurred.");
};

export const fetchAccountManifest = async (accountId: string | undefined): Promise<AccountManifest> => {
	try {
		if (!accountId) {
			throw new Error("Failed to read account id while fetching account manifest.");
		}

		const CDN_ENDPOINT = validateBackslash(process.env.GP_CDN_ENDPOINT);

		if (!CDN_ENDPOINT) {
			throw new Error("Failed to read CDN endpoint while fetching account manifest.");
		}

		const accountUrl = `${CDN_ENDPOINT}manifests/${accountId}/account-manifest.json`;
		const accountResponse = await fetch(accountUrl);

		if (!accountResponse.ok) {
			throw new Error("Failed to fetch account manifest file.");
		}

		const accountManifest: AccountManifest = await accountResponse.json();

		if (!accountManifest || !accountManifest.subscriptionType) {
			throw new Error("Invalid account manifest data format.");
		}

		return accountManifest;
	} catch (error: unknown) {
		appLog({
			message: errorFromUnknown(error).message,
			trace: (error as Error).stack + "\n slider.service|fetchAccountManifest",
			scope: LogScope.ERROR
		});

		return defaultAccountManifest;
	}
};

const fetchCollectionDataCache = async (collectionId: string): Promise<CollectionDataCache> => {
	if (!collectionId) {
		throw new Error("Failed to read collection id while fetching collection manifest.");
	}

	const CDN_ENDPOINT = validateBackslash(process.env.GP_CDN_ENDPOINT);

	if (!CDN_ENDPOINT) {
		throw new Error("Failed to read CDN endpoint while fetching collection manifest.");
	}

	const colUrl = `${CDN_ENDPOINT}data-cache/collections/${collectionId}-collection.json`;
	const collectionResponse = await fetch(colUrl);

	if (!collectionResponse.ok) {
		throw new Error("Failed to fetch collection manifest file.");
	}

	const collectionDataCache: CollectionDataCache = await collectionResponse.json();

	if (!collectionDataCache ||
		!collectionDataCache.interactiveVideos ||
		collectionDataCache.interactiveVideos.length === 0) {
		throw new Error("Invalid collection manifest data format.");
	}

	return collectionDataCache;
};

const fetchVideoDataCache = async (videoId: string): Promise<VideoDataCache> => {
	if (!videoId) {
		throw new Error("Failed to fetch Video Data Cache. Missing videoId.");
	}

	const CDN_ENDPOINT = validateBackslash(process.env.GP_CDN_ENDPOINT);

	if (!CDN_ENDPOINT) {
		throw new Error("Failed to read CDN endpoint while fetching Video Data Cache.");
	}

	const colUrl = `${CDN_ENDPOINT}data-cache/videos/${videoId}-video.json`;
	const videoResponse = await fetch(colUrl);

	if (!videoResponse.ok) {
		throw new Error("Failed to fetch video data cache file.");
	}

	const videoDataCache: VideoDataCache = await videoResponse.json();

	if (videoDataCache.interactiveVideo.captionData) {
		if ("fontSizeRem" in videoDataCache.interactiveVideo.captionData) {
			videoDataCache.interactiveVideo.captionData.fontSize =
				videoDataCache.interactiveVideo.captionData.fontSizeRem as number;
		} else if (!videoDataCache.interactiveVideo.captionData.fontSize) {
			videoDataCache.interactiveVideo.captionData.fontSize = 1;
		}
	}

	if (!videoDataCache || !videoDataCache.interactiveVideo) {
		throw new Error("Invalid video data cache format.");
	}

	return videoDataCache;
};


export const fetchSliderData =
async (sharedCollectionId?: string, sharedVideoId?: string): Promise<IFetchSliderOutput | null> => {
	try {
		validateEnvVariables();
		let isShareMode = false;
		if (sharedCollectionId || sharedVideoId) {
			isShareMode = true;
		}
		const urlParams = readAppParams(sharedCollectionId, sharedVideoId);
		if (!urlParams) {
			throw new Error("Failed to read url params.");
		}

		let accountId: string;
		let videoData: IVideoData[];
		let collectionDataCache: CollectionDataCache | null = null;
		let accountManifest: AccountManifest;

		if (urlParams.collectionId) {
			collectionDataCache = await fetchCollectionDataCache(urlParams.collectionId);
			accountId = collectionDataCache.accountId;
			accountManifest = await fetchAccountManifest(accountId);
			videoData = collectionDataCache.interactiveVideos.map(
				(video: Partial<IVideoData>) => {
					const videoWithDefault = { ...defaultVideo, ...video };
					if (!accountManifest.allowLandscape) {
						videoWithDefault.videoDisplayMode = defaultVideo.videoDisplayMode;
					}
					if (!accountManifest.allowCTALead) {
						videoWithDefault.phone = undefined;
						videoWithDefault.email = undefined;
					}
					videoWithDefault.accountId = accountId;

					if (videoWithDefault.captionData) {
						if ("fontSizeRem" in videoWithDefault.captionData) {
							videoWithDefault.captionData.fontSize = videoWithDefault.captionData.fontSizeRem as number;
						} else if (!videoWithDefault.captionData.fontSize) {
							videoWithDefault.captionData.fontSize = 1;
						}
					}
					return videoWithDefault;
				}
			);
		} else if (!urlParams.collectionId && urlParams.videoId) {
			const videoDataCache = await fetchVideoDataCache(urlParams.videoId);
			accountId = videoDataCache.accountId;
			accountManifest = await fetchAccountManifest(accountId);
			videoData = [{
				...defaultVideo,
				...videoDataCache.interactiveVideo,
				accountId,
				videoDisplayMode: accountManifest.allowLandscape
					? videoDataCache.interactiveVideo.videoDisplayMode
					: defaultVideo.videoDisplayMode,
				phone: accountManifest.allowCTALead ? videoDataCache.interactiveVideo.phone : undefined,
				email: accountManifest.allowCTALead ? videoDataCache.interactiveVideo.email : undefined
			}];
		} else {
			throw new Error("Failed to fetch slider data. Missing both collectionId and videoId.");
		}

		if ((sharedCollectionId || sharedVideoId) && !accountManifest.allowSharing) {
			throw new Error("Access denied. This feature is not available.");
		}

		const sliderConfig: ISliderConfig = {
			accountId,
			collectionId: urlParams.collectionId || undefined,
			hideVanityBranding: accountManifest.hideVanityBranding,
			isShareMode: isShareMode
		};

		const starterVideoIndex = urlParams.videoId
			? videoData.findIndex((v) => v._id === urlParams.videoId)
			: 0;

		return {
			videoData,
			starterVideoIndex: starterVideoIndex === -1 ? 0 : starterVideoIndex,
			sliderConfig,
			collectionTheme: generateCustomTheme(accountManifest.allowThemes, collectionDataCache),
			isVideoPlayer: urlParams.snippetType === SnippetTypeEnum.PLAYER ? true : false
		};

	} catch (error: unknown) {
		appLog({
			message: errorFromUnknown(error).message,
			trace: (error as Error).stack + "\n slider.service|fetchSliderData",
			scope: LogScope.ERROR
		});
		return null;
	}
};

import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { Box } from "@player/components/Box";

export const SliderExit = styled(Flex)`
	top: 10px;
	right: 10px;
  position: absolute;
`;

export const SliderContainer = styled(Flex)`
	gap: 10px;
	overflow: hidden;
	align-items: center;
`;

export const VideoWrapper = styled(Box)<{ isLandscape: boolean }>`
	min-width: ${(props): string => (props.isLandscape ? "1135px" : "360px")};
	aspect-ratio: ${(props): string => (props.isLandscape ? "16/9" : "9/16")};
	cursor: pointer;
	border-radius: 10px;
	${(props): string => {
		if (props.isLandscape) {
			return `
				@media (min-width: 769px) and (max-width: 1024px) {
					min-width: 650px;
				}
				@media (min-width: 1025px) and (max-width: 1324px) {
					min-width: 880px;
				}
			`;
		}
		return "";
	}}
`;

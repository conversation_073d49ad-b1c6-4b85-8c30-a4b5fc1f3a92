import {
	useState,
	useEffect
} from "react";
import {
	useSetRecoilState,
	useRecoilValue
} from "recoil";
import { ITheme } from "@player/theme/theme.interface";
import { defaultTheme } from "@player/theme/defaults";
import { fetchSliderData } from "./slider.service";
import { sliderConfigAtom } from "./slider.state";
import { IVideoData } from "@player/app/app.interface";
import {
	CoreMessageTypeEnum,
	CoreMessageSecret
} from "@player/core/core.message";
import {
	isAppInPreviewModeAtom,
	appPreviewDataAtom
} from "@player/app/app.state";
import { sendAppEvent } from "@player/event/event.service";
import { EventNameEnum } from "@player/event/event.enum";

interface IUseSlider {
	theme: ITheme;
	loading: boolean;
	videoData: IVideoData[] | null;
	starterVideoIndex: number | null;
	isVideoPlayer: boolean;
}

export const useSlider = (sharedCollectionId?: string, sharedVideoId?: string): IUseSlider => {
	const setSliderConfig = useSetRecoilState(sliderConfigAtom);
	const setIsAppInPreviewMode = useSetRecoilState(isAppInPreviewModeAtom);
	const appPreviewData = useRecoilValue(appPreviewDataAtom);

	const [theme, setTheme] = useState<ITheme>(defaultTheme);
	const [loading, setLoading] = useState<boolean>(true);
	const [videoData, setVideoData] = useState<IVideoData[] | null>(null);
	const [starterVideoIndex, setStarterVideoIndex] = useState<number | null>(null);
	const [isVideoPlayer, setIsVideoPlayer] = useState<boolean>(false);

	const requestSessionStorageFromCore = async (): Promise<void> => {
		const message = {
			type: CoreMessageTypeEnum.REQUEST_SESSION_STORAGE
		};
		const postEvent = CoreMessageSecret + JSON.stringify(message);
		window?.parent?.postMessage(postEvent, "*");
	};

	const requestPreviewDataFromAdmin = async (): Promise<void> => {
		const postEvent = "gp_preview_initialized";
		window?.parent?.postMessage(postEvent, "*");
	};

	const isAppInPreviewMode = (): boolean => {
		const params = new URLSearchParams(window.location.search);
		const previewMode = params.get("previewMode");
		if (previewMode && previewMode === "true") {
			return true;
		}
		return false;
	};

	const registerShareImpression = (account: string, collection?: string, video?:string): void => {
		sendAppEvent({
			eventName: EventNameEnum.SNIPPET_IMPRESSION,
			accountId: account,
			collectionId: collection,
			videoId: video,
			isShareMode: true
		});
	};

	useEffect(() => {
		if (appPreviewData) {
			setSliderConfig({
				accountId: "preview-account",
				collectionId: "preview-collection",
				hideVanityBranding: appPreviewData.account.hideVanityBranding,
				isShareMode: false
			});
			setVideoData([appPreviewData.video]);
			setStarterVideoIndex(0);
		}
	}, [appPreviewData, setSliderConfig]);

	useEffect(() => {
		const init = async (): Promise<void> => {
			const previewModeActive = isAppInPreviewMode();
			setIsAppInPreviewMode(previewModeActive);
			if (previewModeActive) {
				requestPreviewDataFromAdmin();
				setLoading(false);
				return;
			}
			requestSessionStorageFromCore();
			const sliderData = await fetchSliderData(sharedCollectionId, sharedVideoId);
			if (!sliderData) {
				setLoading(false);
				return;
			}
			setSliderConfig(sliderData.sliderConfig);
			setVideoData(sliderData.videoData);
			setStarterVideoIndex(sliderData.starterVideoIndex);
			setIsVideoPlayer(sliderData.isVideoPlayer);
			setTheme(sliderData.collectionTheme);
			sliderData.sliderConfig.isShareMode &&
			registerShareImpression(sliderData.sliderConfig.accountId, sharedCollectionId, sharedVideoId);
			setLoading(false);
		};
		init();
	}, [sharedCollectionId, setIsAppInPreviewMode, setSliderConfig, sharedVideoId]);

	return { theme, loading, videoData, starterVideoIndex, isVideoPlayer };
};

import React from "react";
import <PERSON><PERSON> from "react-lottie-player";
import emailSentAnimation from "./emailSentAnim.json";

interface Props {
	onAnimationComplete: () => void;
}

export const EmailSentAnimation: React.FC<Props> = ({ onAnimationComplete }) => (
	<Lottie
		loop={false}
		animationData={emailSentAnimation}
		play
		style={{ width: 200, height: 200 }}
		onComplete={onAnimationComplete}
	/>
);

import React from "react";
import styled, {
	css,
	FlattenSimpleInterpolation
} from "styled-components/macro";
import { Flex } from "@player/components/Flex";

const FormWrapper = styled(Flex)<{ isVisible: boolean; fullWidth: boolean; }>`
	max-height: ${({ fullWidth }): string => (fullWidth ? "calc(100% - 45px)" : "calc(95% - 45px)")};

	flex-direction: column;
	position: absolute;
	right: 0;
	left: 0;
	background: white;
	z-index: 2;
	pointer-events: ${({ isVisible }): string => (isVisible ? "auto" : "none")};
	height: fit-content;
	opacity: ${({ isVisible }): number => (isVisible ? 1 : 0)};
	transition: opacity 0.5s, transform 0.5s, top 0.5s;
	border-radius: ${({ fullWidth }): string => (fullWidth ? "10px 10px 0 0" : "10px")};
	${(props): false | FlattenSimpleInterpolation =>
		!props.fullWidth &&
		css`
			margin: auto;
			width: 50%;
		`}
	${({ fullWidth, isVisible }): FlattenSimpleInterpolation => {
		if (fullWidth) {
			return css`
				top: 100%;
				transform: ${isVisible ? "translateY(-100%)" : "translateY(100%)"};
			`;
		}
		return css`
			top: 50%;
			transform: ${isVisible ? "translateY(-50%)" : "translateY(100%)"};
		`;
	}}
`;

interface Props {
	isVisible: boolean;
	fullWidth: boolean;
	children: React.ReactNode;
}

export const AnimatedForm: React.FC<Props> = ({ isVisible, fullWidth, children }) => (
	<FormWrapper data-testid={"animated-form-wrapper"}
		isVisible={isVisible} fullWidth={fullWidth} onClick={(e): void => {
			e.stopPropagation();
		}}>
		{children}
	</FormWrapper>
);

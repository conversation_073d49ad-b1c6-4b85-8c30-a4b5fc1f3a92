import { CSSObject } from "styled-components";
import { ThemeColorsEnum } from "./theme.enum";

export interface IThemeFonts {
	h1: CSSObject;
	h2: CSSObject;
	h3: CSSObject;
	h4: CSSObject;
	h5: CSSObject;
	h6: CSSObject;
	button: CSSObject;
	p: CSSObject;
}

export interface IThemeColors {
	primary: ThemeColorsEnum;
	secondary: ThemeColorsEnum;
	accent: ThemeColorsEnum;
	error: ThemeColorsEnum;
	transparentBlack: ThemeColorsEnum;
}

interface IButtonShades {
	color: ThemeColorsEnum;
	main: ThemeColorsEnum;
	hover: ThemeColorsEnum;
	active: ThemeColorsEnum;
}

export interface IThemeButtonVariants {
	primary: IButtonShades;
	secondary: IButtonShades;
}


export interface ITheme {
	colors: IThemeColors;
	textColor: string;
	buttons: {
		variants: IThemeButtonVariants;
		backgroundColor: string;
		backgroundBlur: string;
	};
	fonts: {
		family: string;
		desktop: IThemeFonts;
		mobile: IThemeFonts;
	};
	carouselBorderRadius: number;
	carouselIsCentered: boolean;
	carouselMargin: number;
	carouselGap: number;
	widgetBorderRadius: number;
	widgetPosition: "left" | "right";
	inlineBorderRadius: number;
}

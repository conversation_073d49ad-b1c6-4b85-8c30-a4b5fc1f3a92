import { IThemeFonts } from "./theme.interface";

export const desktopFont: IThemeFonts = {
	h1: {
		fontWeight: 700,
		fontSize: "1.67rem",
		lineHeight: "1.83rem"
	},
	h2: {
		fontWeight: 600,
		fontSize: "1.33rem",
		lineHeight: "1.5rem"
	},
	h3: {
		fontWeight: 600,
		fontSize: "min(1.17em, 1.17rem)",
		lineHeight: "1.2"
	},
	h4: {
		fontWeight: 300,
		fontSize: "1rem",
		lineHeight: "1.33rem"
	},
	h5: {
		fontWeight: 400,
		fontSize: "1rem",
		lineHeight: "1.33rem"
	},
	h6: {
		fontWeight: 300,
		fontSize: "0.75rem",
		lineHeight: "0.92rem"
	},
	button: {
		fontWeight: 500,
		fontSize: "1rem",
		lineHeight: "1.33rem",
		borderRadius: "0.5rem"
	},
	p: {
		// do 600 for bold
		fontWeight: 300,
		fontSize: "0.92rem",
		lineHeight: "1.08rem"
	}
};

export const mobileFont: IThemeFonts = {
	h1: {
		fontWeight: 700,
		fontSize: "1.33rem",
		lineHeight: "1.5rem"
	},
	h2: {
		fontWeight: 600,
		fontSize: "1.17rem",
		lineHeight: "1.33rem"
	},
	h3: {
		fontWeight: 600,
		fontSize: "min(1em, 1rem)",
		lineHeight: "1.2"
	},
	h4: {
		fontWeight: 300,
		fontSize: "1rem",
		lineHeight: "1.33rem"
	},
	h5: {
		fontWeight: 400,
		fontSize: "0.83rem",
		lineHeight: "1.17rem"
	},
	h6: {
		fontWeight: 400,
		fontSize: "0.67rem",
		lineHeight: "0.92rem"
	},
	button: {
		fontWeight: 500,
		fontSize: "1rem",
		lineHeight: "1.33rem",
		borderRadius: "0.5rem"
	},
	p: {
		// do 600 for bold
		fontWeight: 300,
		fontSize: "0.6rem",
		lineHeight: "0.733rem"
	}
};

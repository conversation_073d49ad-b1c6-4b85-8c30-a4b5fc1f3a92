import React from "react";
import styled from "styled-components/macro";
import { Flex } from "./Flex";

const Container = styled(Flex)`
	background: black;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	height: 100%;
	padding: 1rem;
	justify-content: center;
  	align-items: center;
	flex-direction: column;
`;

const Title = styled.h1`
  color: white;
  font-family: 'Readex Pro', sans-serif;
  font-weight: 700;
  font-size: 1.67rem;
  line-height: 1.83rem;
  text-align: center;
`;
const Message = styled.p`
  color: white;
  font-family: 'Readex Pro', sans-serif;
  font-weight: 300;
  font-size: 0.92rem;
  line-height: 1.08rem;
  text-align: center;
`;

export const NotFound: React.FC = () => {
	return (
		<Container>
			<Title>This video is unavailable.</Title>
			<Message>The video you are trying to view is deleted or hidden.</Message>
		</Container>
	);
};

import React from "react";
import styled, { useTheme } from "styled-components";
import { useRecoilValue } from "recoil";
import { Box } from "../Box";
import { Flex } from "../Flex";
import { IconButton } from "./IconButton";

import {
	ITheme,
	IThemeButtonVariants
} from "@player/theme";
import { isPortraitAtom } from "@player/app";
import { StyledButton } from "./StyledButton";


const ButtonText = styled(Box)`
	font-weight: bold;
  	text-transform: initial;
  	font-family: ${(props):string => props.theme.fonts.family};
`;

interface Props {
	text: string;
	svgIcon?: React.FC<React.SVGProps<SVGSVGElement>>;
	variantBtn?: keyof IThemeButtonVariants;
	onClicked?: () => void;
	btnStyle?: React.CSSProperties;
	spin?: boolean;
	font?: "desktop" | "mobile";
	fullWidth?: boolean;
	isBorder?: boolean;
}

export const ActionButton: React.FC<Props> = ({
	text,
	svgIcon,
	variantBtn = "primary",
	onClicked,
	btnStyle = {},
	font = "desktop",
	spin = false,
	fullWidth = false,
	isBorder = false
}) => {
	const isPortrait = useRecoilValue(isPortraitAtom);
	if (isPortrait) {
		font = "mobile";
	}

	const theme = useTheme() as ITheme;
	const buttonStyle: React.CSSProperties = {
		...theme.fonts[font]["button"],
		...btnStyle
	};
	isBorder && (buttonStyle.border = `2px solid ${theme.colors.primary}`);

	const ButtonContent = (
		<Flex justifyContent={"center"} alignItems={"center"}>
			<Box style={spin ? { animation: "spin infinite 5s linear" } : {}}>
				{svgIcon && <IconButton svgIcon={svgIcon} isPortrait={isPortrait}/>}
			</Box>
			<ButtonText ml={svgIcon ? "0.75rem" : "0rem"} as="span">
				{text}
			</ButtonText>
		</Flex>
	);

	return (
		<StyledButton
			theme={theme}
			variant={variantBtn}
			onClick={onClicked}
			style={buttonStyle}
			fullWidth={fullWidth}
		>
			{ButtonContent}
		</StyledButton>
	);
};

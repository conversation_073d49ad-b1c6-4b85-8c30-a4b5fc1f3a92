
import styled, { css } from "styled-components";
import {
	typography,
	space,
	TypographyProps,
	SpaceProps
} from "styled-system";
import {
	ITheme,
	IThemeButtonVariants
} from "@player/theme";

interface IBaseButton extends TypographyProps, SpaceProps {
	fullWidth?: boolean;
}

const styles = css<IBaseButton>`
  padding: 0.7rem clamp(1rem, 4cqw, 1.8rem);
  text-align: center;
  text-transform: uppercase;
  appearance: none;
  border: 0;
  outline: none;
  cursor: pointer;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  &:hover,
  &:focus,
  &:active {
    outline: none;
  }

  ${typography}
  ${space}

  width: ${(props): string =>props.fullWidth ? "100%" : "fit-content"};
`;


interface Props extends IBaseButton {
	theme: ITheme;
	variant: keyof IThemeButtonVariants;
}

export const StyledButton = styled.button<Props>`
  ${styles}
  color: ${(props): string => props.theme.buttons.variants[props.variant].color};
  background-color: ${(props): string =>
		props.theme.buttons.variants[props.variant].main};
  &:hover {
    background-color: ${(props): string =>
		props.theme.buttons.variants[props.variant].hover};
  }
  &:active {
    background-color: ${(props): string =>
		props.theme.buttons.variants[props.variant].active};
  }
`;

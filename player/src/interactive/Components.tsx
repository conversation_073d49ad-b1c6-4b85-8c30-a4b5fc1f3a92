import React from "react";
import styled from "styled-components/macro";

import {
	Box,
	Flex,
	ImageContainer
} from "@player/components";
import { openExternalSite } from "@player/app";

export const VideoContainer = styled(Flex)`
  position: relative;
  flex-direction: column;
  overflow: hidden;
  //Mask image used to resolve Safari bug not hiding overflow on rounded corner divs.
  //https://forum.webflow.com/t/safari-not-hiding-overflow-on-rounded-corner-divs/55060
  mask-image: -webkit-radial-gradient(white, black);
  -webkit-mask-image: -webkit-radial-gradient(white, black);
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);
  max-width: 100%;
  max-height: 100%;
  width: 100%;
  height: 100%;
`;

export const FrameContainer = styled(Flex)`
  flex-direction: column;
  height: 100%;
  padding: min(1em, 1rem);
`;

export const ProgressBarCon = styled(Box)`
  position: relative;
  width: 100%;
  height: min(0.3em, 0.3rem);
  background: rgba(255, 255, 255, 0.5);
  border-radius: 1rem;
  touch-action: none;
`;

interface ProgressBarProps {
	progressBarPercent: number;
	showThumb?: boolean;
}

export const ProgressBar = styled(Box).attrs<ProgressBarProps>(
	({ progressBarPercent }) => ({
		style: {
			width: `${progressBarPercent}%`
		}
	})
)<ProgressBarProps>`
    position: relative;
    height: 100%;
    border-radius: 1rem;
    background: white;
    transition: width 0.27s ease-out;

  &:after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(50%, -50%);
    width: min(0.9em, 0.9rem);
    height: min(0.9em, 0.9rem);
    background: white;
    border-radius: 50%;
    display: ${(props): string => (props.showThumb ? "block" : "none")};
  }
`;

export const ProgressBarWrapper = styled(Box)`
	position: relative;
	width: 100%;
	height: min(2em, 2rem);
	box-sizing: content-box;
	display: flex;
	align-items: center;
	touch-action: none;
`;

export const ControlsCon = styled(Flex)`
  justify-content: space-between;
`;

export const renderVanityBrand = (): JSX.Element => {
	const brandAsset = process.env.GP_CDN_ENDPOINT + "/app/brand.png";
	const boxStyle: React.CSSProperties = {
		cursor: "pointer",
		width: "70px",
		marginRight: "8px",
		marginLeft: "4px"
	};
	return (
		<Box
			style={boxStyle} onClick={(event: React.MouseEvent<HTMLDivElement>): void => {
				event.stopPropagation();
				openExternalSite(process.env.PLAYER_BRAND_URL ?? "");
			}}
		>
			<ImageContainer src={brandAsset} style={{ width: "100%" }} />
		</Box>
	);
};

export const VideoPlayContainer = styled(Flex)`
    position: absolute;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    bottom: 0;
    z-index: 2;
`;

import React from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { useRecoilValue } from "recoil";
import { ProductSlider } from "@player/product/ProductSlider";
import { FrameContainer } from "@player/interactive/Components";
import { IVideoData } from "@player/app/app.interface";
import { LayoutPosEnum } from "@player/slider/slider.enum";
import { isPortraitAtom } from "@player/app/app.state";
import { IconButton } from "@player/components/button/IconButton";
import { default as ArrowIcon } from "@player/assets/icon-arrow.svg";

const InactiveContainer = styled(Flex).attrs<{layoutPos: string}>(
	({ layoutPos }) => ({
		style: {
			alignItems: layoutPos === LayoutPosEnum.LEFT ? "flex-end" : "flex-start"
		}
	})
)<{layoutPos: string}>`
	background: rgba(0, 0, 0, 0.5);
	position: absolute;
	flex-direction: column;
	height: 100%;
	width: 100%;
	z-index: 2;
	justify-content: center;
`;

interface Props {
	videoData: IVideoData;
	position: LayoutPosEnum;
}
export const InactiveVideoFrame: React.FC<Props> = ({ videoData, position }) => {
	const isPortrait = useRecoilValue(isPortraitAtom);

	return (
		<>
			<InactiveContainer layoutPos={position}>
				<IconButton svgIcon={ArrowIcon} isPortrait={isPortrait} style={{ margin: "1rem", width: "40px",
					transform: position === LayoutPosEnum.LEFT ? "" : "rotate(180deg)" }}/>
			</InactiveContainer>
			<FrameContainer justifyContent={"flex-end"}>
				<ProductSlider videoData={videoData} />
			</FrameContainer>
		</>
	);
};

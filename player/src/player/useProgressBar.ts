import React, {
	useState,
	useRef
} from "react";

export interface IUseProgressBarResult {
	displayProgressPercent: number;
	showThumb: boolean;
	handleMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
	handleMouseMove: (e: React.MouseEvent<HTMLDivElement>) => void;
	handleTouchStart: (e: React.TouchEvent<HTMLDivElement>) => void;
	handleTouchMove: (e: React.TouchEvent<HTMLDivElement>) => void;
	handleTouchEnd: (e: React.TouchEvent<HTMLDivElement>) => void;
	handleProgressClick: (
		e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
	) => void;
}

interface Props {
	videoDuration: number;
	progressBarPercent: number;
	seekVideo: (time: number) => void;
	playingVideo: boolean;
	setPlayingVideo: React.Dispatch<React.SetStateAction<boolean>>;
}

export const useProgressBar = ({ videoDuration, progressBarPercent, seekVideo, playingVideo, setPlayingVideo }: Props):
IUseProgressBarResult => {
	const [isDragging, setIsDragging] = useState(false);
	const isDraggingRef = useRef(false);
	const [dragPercent, setDragPercent] = useState<number | null>(null);
	const [showThumb, setShowThumb] = useState(false);
	const wasPlayingBeforeDragRef = useRef<boolean>(false);
	const displayProgressPercent: number = isDragging && dragPercent !== null ? dragPercent : progressBarPercent;

	const updateDragPercent = (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>): void => {
		const rect = e.currentTarget.getBoundingClientRect();
		let clientX = 0;

		if ("clientX" in e) {
			clientX = e.clientX;
		} else if (e.touches && e.touches.length > 0) {
			clientX = e.touches[0].clientX;
		} else if (e.changedTouches && e.changedTouches.length > 0) {
			clientX = e.changedTouches[0].clientX;
		} else {
			console.error("Cannot get clientX from event", e);
			return;
		}

		const dragX = clientX - rect.left;
		let newPercent = (dragX / rect.width) * 100;
		newPercent = Math.max(0, Math.min(newPercent, 100));
		setDragPercent(newPercent);
		seekVideo((newPercent / 100) * videoDuration);
	};

	const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>): void => {
		e.preventDefault();
		e.stopPropagation();
		window.addEventListener("mouseup", handleMouseUpWin);
		setIsDragging(true);
		isDraggingRef.current = true;
		setShowThumb(true);
		updateDragPercent(e);
		if (playingVideo) {
			wasPlayingBeforeDragRef.current = true;
			setPlayingVideo(false);
		} else {
			wasPlayingBeforeDragRef.current = false;
		}
	};

	const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>): void => {
		if (isDragging) {
			e.preventDefault();
			e.stopPropagation();
			updateDragPercent(e);
		}
	};

	const handleMouseUpWin = (e: MouseEvent): void => {
		e.preventDefault();
		e.stopPropagation();
		if (isDraggingRef.current) {
			setIsDragging(false);
			isDraggingRef.current = false;
			setDragPercent(null);
			if (wasPlayingBeforeDragRef.current) {
				setPlayingVideo(true);
			}
			setShowThumb(false);
		}
		window.removeEventListener("mouseup", handleMouseUpWin);
	};

	const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>): void => {
		e.stopPropagation();
		setIsDragging(true);
		isDraggingRef.current = true;
		setShowThumb(true);
		updateDragPercent(e);
		if (playingVideo) {
			wasPlayingBeforeDragRef.current = true;
			setPlayingVideo(false);
		} else {
			wasPlayingBeforeDragRef.current = false;
		}
	};

	const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>): void => {
		if (!isDragging) return;
		e.stopPropagation();
		updateDragPercent(e);
	};

	const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>): void => {
		e.stopPropagation();
		if (isDragging) {
			setIsDragging(false);
			isDraggingRef.current = false;
			setDragPercent(null);
			if (wasPlayingBeforeDragRef.current) {
				setPlayingVideo(true);
			}
			setShowThumb(false);
		}
	};

	const handleProgressClick = (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>): void => {
		e.stopPropagation();
		const rect = e.currentTarget.getBoundingClientRect();
		let clientX = 0;

		if ("clientX" in e) {
			clientX = e.clientX;
		} else if (e.touches && e.touches.length > 0) {
			clientX = e.touches[0].clientX;
		} else if (e.changedTouches && e.changedTouches.length > 0) {
			clientX = e.changedTouches[0].clientX;
		} else {
			console.error("Cannot get clientX from event", e);
			return;
		}

		const clickX = clientX - rect.left;
		let clickPercent = (clickX / rect.width) * 100;
		clickPercent = Math.max(0, Math.min(clickPercent, 100));
		seekVideo((clickPercent / 100) * videoDuration);

		setShowThumb(true);
		setTimeout(() => {
			setShowThumb(false);
		}, 1000);
	};

	return {
		displayProgressPercent,
		showThumb,
		handleMouseDown,
		handleMouseMove,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleProgressClick
	};
};

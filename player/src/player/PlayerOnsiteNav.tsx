import React from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { useRecoilValue } from "recoil";
import { IconButton } from "@player/components/button/IconButton";
import { default as ArrowIcon } from "@player/assets/icon-arrow.svg";
import { SwipeDirectionEnum } from "@player/slider/slider.enum";
import { isPortraitAtom } from "@player/app/app.state";

const Container = styled(Flex)`
	flex-direction: column;
	gap: 0.6rem;
	align-items: end;
`;

interface Props {
	isOnSite: boolean;
	handleCTAChevronClick?: (dir: SwipeDirectionEnum) => void;
}

export const PlayerOnsiteNav: React.FC<Props> = ({ isOnSite, handleCTAChevronClick }) => {
	const isPortrait = useRecoilValue(isPortraitAtom);

	if (!isOnSite || !handleCTAChevronClick) {
		return null;
	}
	return (
		<Container>
			<IconButton svgIcon={ArrowIcon} style={{ padding: "8px", transform: "rotate(90deg)" }}
				isPortrait={isPortrait} onClick={(e): void => {
					e.stopPropagation();
					handleCTAChevronClick(SwipeDirectionEnum.UP);
				}}/>
			<IconButton svgIcon={ArrowIcon} style={{ padding: "8px", transform: "rotate(-90deg)" }}
				isPortrait={isPortrait} onClick={(e): void => {
					e.stopPropagation();
					handleCTAChevronClick(SwipeDirectionEnum.DOWN);
				}}/>
		</Container>
	);
};

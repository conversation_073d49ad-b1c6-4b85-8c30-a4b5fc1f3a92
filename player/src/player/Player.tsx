import React, {
	useRef,
	useState
} from "react";
import { Flex } from "@player/components/Flex";
import styled from "styled-components";
import {
	IVideoData,
	SnippetTypeEnum
} from "@player/app/app.interface";
import { VideoFrame } from "@player/interactive/VideoFrame";
import { videoInfoPayloadAtomFamily } from "@player/app";

const PlayerContainer = styled(Flex)`
	overflow: hidden;
	width: 100%;
	height: 100%;
`;

interface Props {
	videoData: IVideoData;
}

export const Player: React.FC<Props> = ({ videoData }) => {
	const playerContainerRef = useRef<HTMLDivElement>(null);
	const [showVideoOverlay, setShowVideoOverlay] = useState<boolean>(true);

	return (
		<PlayerContainer
			ref={playerContainerRef}
			data-testid="video-player"
		>
			<VideoFrame
				isViewable={true}
				videoData={videoData}
				snippetType= {SnippetTypeEnum.PLAYER}
				setShowVideoOverlay={setShowVideoOverlay}
				showVideoOverlay={showVideoOverlay}
				playerContainerRef={playerContainerRef}
				videoInfoPayloadAtom={videoInfoPayloadAtomFamily(0)}
			/>
		</PlayerContainer>
	);
};

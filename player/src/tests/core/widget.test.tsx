import React from "react";
import { ThemeProvider } from "styled-components";
import { defaultTheme } from "@player/theme/defaults";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup,
	fireEvent
} from "@testing-library/react";
import { Widget } from "@player/core/components/Widget";
import { videoDataMock } from "../mocks/data.mock";

beforeEach(async () => {
	jest.clearAllMocks();
});

afterEach(() => {
	jest.resetAllMocks();
	jest.restoreAllMocks();
});

describe("Core Snippet. Widget Component", () => {
	afterEach(cleanup);

	test("Should Render Widget Component with video element.", async () => {
		const handleCTAClick = jest.fn();
		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Widget
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
				/>
			</ThemeProvider>
		);

		const widgetContainer = getByTestId("widget-container");
		const videoElement = widgetContainer.querySelector("video");

		if (!videoElement) {
			throw new Error("Missing video element in Widget component.");
		}

		expect(videoElement.getAttribute("src")).toBe(
			videoDataMock.interactiveVideos[0].videoURL
		);
	});

	test("Should trigger a click on the Widget component with videoId.", async () => {
		const handleCTAClick = jest.fn();
		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Widget
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
				/>
			</ThemeProvider>
		);

		const widgetContainer = getByTestId("widget-container");

		if (!widgetContainer) {
			throw new Error("Missing Widget container.");
		}

		fireEvent.click(widgetContainer);
		expect(handleCTAClick).toHaveBeenCalledWith(
			videoDataMock.interactiveVideos[0]._id
		);
	});


	test("Should Close/hide Widget Component.", async () => {
		const handleCTAClick = jest.fn();
		const { queryByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Widget
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
				/>
			</ThemeProvider>
		);
		const closeIcon = queryByTestId("widget-icon-times");
		if (!closeIcon) {
			throw new Error("Missing closeIcon element in Widget component.");
		}

		fireEvent.click(closeIcon.childNodes[0]);
		const widgetContainer = queryByTestId("widget-container");
		expect(widgetContainer).toHaveStyle("display: none");
	});

	test("Should turn the volume ON.", async () => {
		const handleCTAClick = jest.fn();
		const { queryByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Widget
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
				/>
			</ThemeProvider>
		);
		const volumeIcon = queryByTestId("widget-icon-volume");
		expect(volumeIcon).toBeTruthy();
	});


	test("Render Widget Component should have title and description.", async () => {
		const handleCTAClick = jest.fn();

		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Widget
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
				/>
			</ThemeProvider>
		);

		const widgetContainer = getByTestId("widget-container");

		const videoWrapper = widgetContainer.children[0];
		const videoTag = videoWrapper.querySelector("video");
		expect(videoTag?.title).toBe(videoDataMock.interactiveVideos[0].title);

		if (videoDataMock.interactiveVideos[0].description) {
			const descriptionId = `video-description-${videoDataMock.interactiveVideos[0]._id}`;
			expect(videoTag?.getAttribute("aria-describedby")).toBe(descriptionId);

			const descriptionElement = document.getElementById(descriptionId);
			expect(descriptionElement?.textContent).toBe(videoDataMock.interactiveVideos[0].description);
		}
	});
});

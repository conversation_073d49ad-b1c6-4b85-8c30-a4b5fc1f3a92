import React from "react";
import { ThemeProvider } from "styled-components";
import { defaultTheme } from "@player/theme/defaults";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup,
	fireEvent
} from "@testing-library/react";
import { Carousel } from "@player/core/components/Carousel";
import { videoDataMock } from "../mocks/data.mock";
import { EventNameEnum } from "@player/event";
import { SnippetTypeEnum } from "@player/app";

import * as eventService from "@player/event/event.service";
jest.mock("@player/event/event.service");


beforeEach(async () => {
	jest.clearAllMocks();
});

afterEach(() => {
	jest.resetAllMocks();
	jest.restoreAllMocks();
});

describe("Core Snippet. Carousel Component", () => {
	afterEach(cleanup);

	test("Should Render Carousel Component with poster images.", async () => {
		const handleCTAClick = jest.fn();
		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Carousel
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
					collectionId={videoDataMock.interactiveVideos[0].collectionId}
				/>
			</ThemeProvider>

		);
		const carouselContainer = getByTestId("carousel-container");
		for (let i = 0; i < carouselContainer.children.length; i++) {
			const carouselWrapper = carouselContainer.children[i];
			expect(carouselWrapper)
				.toHaveStyle(`background-image: url("${videoDataMock.interactiveVideos[i].videoPosterURL}")`);
		}
	});


	test("Should trigger a click on the Carousel component with videoId.", async () => {
		const handleCTAClick = jest.fn();
		const { getByTestId } = render(

			<ThemeProvider theme={defaultTheme}>
				<Carousel
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
					collectionId={videoDataMock.interactiveVideos[0].collectionId}
				/>
			</ThemeProvider>
		);

		const carouselContainer = getByTestId("carousel-container");

		for (let i = 0; i < carouselContainer.children.length; i++) {
			const carouselWrapper = carouselContainer.children[i];
			fireEvent.click(carouselWrapper);
			expect(handleCTAClick).toHaveBeenCalledWith(
				videoDataMock.interactiveVideos[i]._id
			);
		}
	});

	test("Should Render Carousel with first video auto playing", async () => {
		const handleCTAClick = jest.fn();
		window.innerWidth = 1000;

		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Carousel
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
					collectionId={videoDataMock.interactiveVideos[0].collectionId}
				/>
			</ThemeProvider>
		);

		const carouselContainer = getByTestId("carousel-container");

		const firstVideoWrapper = carouselContainer.children[0];
		const secondVideoWrapper = carouselContainer.children[1];

		const firstVideoTag = firstVideoWrapper.querySelector("video");
		expect(firstVideoTag?.src).toBeTruthy();

		const secondVideoTag = secondVideoWrapper.querySelector("video");
		expect(secondVideoTag?.src).toBeFalsy();

	});

	test("Should handle mouse enter on the second video and start auto play.", async () => {
		(eventService.sendAppEvent as jest.Mock).mockReturnValueOnce(true);

		const handleCTAClick = jest.fn();
		window.innerWidth = 1000;

		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Carousel
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
					collectionId={videoDataMock.interactiveVideos[0].collectionId}
				/>
			</ThemeProvider>
		);

		const carouselContainer = getByTestId("carousel-container");

		const firstVideoWrapper = carouselContainer.children[0];
		const secondVideoWrapper = carouselContainer.children[1];

		fireEvent.mouseEnter(secondVideoWrapper);
		const firstVideoTag = firstVideoWrapper.querySelector("video");
		expect(firstVideoTag?.src).toBeFalsy();

		const secondVideoTag = secondVideoWrapper.querySelector("video");
		expect(secondVideoTag?.src).toBeTruthy();

		expect(eventService.sendAppEvent).toHaveBeenCalledWith(
			{
				eventName: EventNameEnum.HOVER_PLAY_START,
				accountId: videoDataMock.interactiveVideos[1].accountId,
				collectionId: videoDataMock.interactiveVideos[1].collectionId,
				videoId: videoDataMock.interactiveVideos[1]._id,
				snippet: { type: SnippetTypeEnum.CAROUSEL }
			}
		);

	});

	test("Render Carousel video should have title and description.", async () => {
		const handleCTAClick = jest.fn();

		const { getByTestId } = render(
			<ThemeProvider theme={defaultTheme}>
				<Carousel
					videoData={videoDataMock.interactiveVideos}
					handleCTAClick={handleCTAClick}
					collectionId={videoDataMock.interactiveVideos[0].collectionId}
				/>
			</ThemeProvider>
		);

		const carouselContainer = getByTestId("carousel-container");

		const firstVideoWrapper = carouselContainer.children[0];
		const firstVideoTag = firstVideoWrapper.querySelector("video");
		expect(firstVideoTag?.title).toBe(videoDataMock.interactiveVideos[0].title);

		if (videoDataMock.interactiveVideos[0].description) {
			const descriptionId = `video-description-${videoDataMock.interactiveVideos[0]._id}`;
			expect(firstVideoTag?.getAttribute("aria-describedby")).toBe(descriptionId);

			const descriptionElement = document.getElementById(descriptionId);
			expect(descriptionElement?.textContent).toBe(videoDataMock.interactiveVideos[0].description);
		}

		const secondVideoWrapper = carouselContainer.children[1];
		const secondVideoTag = secondVideoWrapper.querySelector("video");
		expect(secondVideoTag?.title).toBe(videoDataMock.interactiveVideos[1].title);

		if (videoDataMock.interactiveVideos[1].description) {
			const descriptionId = `video-description-${videoDataMock.interactiveVideos[1]._id}`;
			expect(secondVideoTag?.getAttribute("aria-describedby")).toBe(descriptionId);

			const descriptionElement = document.getElementById(descriptionId);
			expect(descriptionElement?.textContent).toBe(videoDataMock.interactiveVideos[1].description);
		}
	});

});

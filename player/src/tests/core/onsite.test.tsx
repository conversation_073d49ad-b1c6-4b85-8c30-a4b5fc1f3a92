import React from "react";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup
} from "@testing-library/react";
import { Onsite } from "@player/core/components/Onsite";
import { videoDataMock } from "../mocks/data.mock";

beforeEach(async () => {
	jest.clearAllMocks();
});

afterEach(() => {
	jest.resetAllMocks();
});

describe("Core Snippet. Onsite Component", () => {
	afterEach(cleanup);

	test("Should Render Onsite Component", async () => {
		const accountId = videoDataMock.interactiveVideos[0].accountId;
		const collectionId = videoDataMock.interactiveVideos[0].collectionId;
		const videoId = videoDataMock.interactiveVideos[0]._id;
		const appUrl =
        `http://localhost?accountId=${accountId}` +
         `&collectionId=${collectionId}&videoId=${videoId}&snippetType=onsite`;

		const { getByTestId } = render(
			<Onsite
				appUrl={appUrl}
				videoData={videoDataMock.interactiveVideos}
				collectionId={collectionId}
			/>
		);

		const onsiteIframe = getByTestId("onsite-iframe");

		expect(onsiteIframe).toHaveAttribute("src", appUrl);
		expect(onsiteIframe).toBeVisible();
	});
});

import {
	renderHook,
	act,
	RenderHookResult
} from "@testing-library/react";
import {
	useProgressBar,
	IUseProgressBarResult
} from "@player/player/useProgressBar";
import React from "react";

// eslint-disable-next-line max-lines-per-function
describe("useProgressBar Custom Hook Test.", () => {
	let videoDuration: number;
	let progressBarPercent: number;
	let seekVideo: jest.Mock;
	let playingVideo: boolean;
	let setPlayingVideo: jest.Mock;

	beforeEach((): void => {
		videoDuration = 100;
		progressBarPercent = 50;
		seekVideo = jest.fn();
		setPlayingVideo = jest.fn();
		playingVideo = true;
	});

	const setup = (): RenderHookResult<IUseProgressBarResult, unknown> => {
		return renderHook(() =>
			useProgressBar({
				videoDuration,
				progressBarPercent,
				seekVideo,
				playingVideo,
				setPlayingVideo
			})
		);
	};

	test("should initialize correctly", (): void => {
		const { result } = setup();
		expect(result.current.displayProgressPercent).toBe(progressBarPercent);
		expect(result.current.showThumb).toBe(false);
	});

	test("handleMouseDown starts dragging, updates dragPercent, pauses video if playing", (): void => {
		const { result } = setup();

		const event = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 150
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseDown(event);
		});

		expect(event.preventDefault).toHaveBeenCalled();
		expect(event.stopPropagation).toHaveBeenCalled();
		expect(result.current.displayProgressPercent).toBe(75);
		expect(setPlayingVideo).toHaveBeenCalledWith(false);
		expect(result.current.showThumb).toBe(true);
	});

	test("handleMouseMove updates dragPercent while dragging", (): void => {
		const { result } = setup();

		const startEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 50
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseDown(startEvent);
		});

		const moveEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 100
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseMove(moveEvent);
		});

		expect(result.current.displayProgressPercent).toBe(50);
		expect(seekVideo).toHaveBeenCalledWith(50);
	});

	test("handleMouseUp ends dragging and resumes video if it was playing before", (): void => {
		const { result } = setup();

		const startEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 50
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseDown(startEvent);

			const mouseUpEvent = new MouseEvent("mouseup");
			window.dispatchEvent(mouseUpEvent);
		});

		act((): void => {
			const mouseUpEvent = new MouseEvent("mouseup");
			window.dispatchEvent(mouseUpEvent);
		});

		expect(setPlayingVideo).toHaveBeenCalledWith(true);
		expect(result.current.showThumb).toBe(false);
	});

	test("handleProgressClick seeks video to correct time", (): void => {
		const { result } = setup();

		const clickEvent = {
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 100
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleProgressClick(clickEvent);
		});

		expect(clickEvent.stopPropagation).toHaveBeenCalled();
		expect(seekVideo).toHaveBeenCalledWith(50);
		expect(result.current.showThumb).toBe(true);
		expect(result.current.displayProgressPercent).toBe(50);
	});

	test("handleTouchStart starts dragging and pauses video if playing", (): void => {
		const { result } = setup();

		const touchEvent = {
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			touches: [{ clientX: 100 }]
		} as unknown as React.TouchEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleTouchStart(touchEvent);
		});

		expect(touchEvent.stopPropagation).toHaveBeenCalled();
		expect(result.current.displayProgressPercent).toBe(50);
		expect(setPlayingVideo).toHaveBeenCalledWith(false);
		expect(result.current.showThumb).toBe(true);
	});

	test("handleTouchMove updates dragPercent while dragging", (): void => {
		const { result } = setup();

		const startEvent = {
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			touches: [{ clientX: 50 }]
		} as unknown as React.TouchEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleTouchStart(startEvent);
		});

		const moveEvent = {
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			touches: [{ clientX: 150 }]
		} as unknown as React.TouchEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleTouchMove(moveEvent);
		});

		expect(result.current.displayProgressPercent).toBe(75);
		expect(seekVideo).toHaveBeenCalledWith(75);
	});

	test("handleTouchEnd ends dragging and resumes video if it was playing before", (): void => {
		const { result } = setup();

		const startEvent = {
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			touches: [{ clientX: 50 }]
		} as unknown as React.TouchEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleTouchStart(startEvent);
		});

		const endEvent = {
			stopPropagation: jest.fn()
		} as unknown as React.TouchEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleTouchEnd(endEvent);
		});

		expect(setPlayingVideo).toHaveBeenCalledWith(true);
		expect(result.current.showThumb).toBe(false);
	});

	test("video remains paused if it was paused before dragging", (): void => {
		playingVideo = false;
		const { result } = setup();

		const startEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 50
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseDown(startEvent);
		});

		expect(setPlayingVideo).not.toHaveBeenCalled();

		act((): void => {
			const mouseUpEvent = new MouseEvent("mouseup");
			window.dispatchEvent(mouseUpEvent);
		});

		expect(setPlayingVideo).not.toHaveBeenCalled();
	});

	test("displayProgressPercent updates correctly during dragging", (): void => {
		const { result, rerender } = setup();

		expect(result.current.displayProgressPercent).toBe(50);

		const dragStartEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: {
				getBoundingClientRect: (): { left: number; width: number } => ({
					left: 0,
					width: 200
				})
			},
			clientX: 100
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseDown(dragStartEvent);
		});

		expect(result.current.displayProgressPercent).toBe(50);
		expect(result.current.showThumb).toBe(true);

		const dragMoveEvent = {
			preventDefault: jest.fn(),
			stopPropagation: jest.fn(),
			currentTarget: dragStartEvent.currentTarget,
			clientX: 150
		} as unknown as React.MouseEvent<HTMLDivElement>;

		act((): void => {
			result.current.handleMouseMove(dragMoveEvent);
		});

		expect(result.current.displayProgressPercent).toBe(75);

		act((): void => {
			const mouseUpEvent = new MouseEvent("mouseup");
			window.dispatchEvent(mouseUpEvent);
		});

		act((): void => {
			rerender();
		});

		expect(result.current.displayProgressPercent).toBe(50);
		expect(result.current.showThumb).toBe(false);
	});
});

import React from "react";
import "@testing-library/jest-dom/extend-expect";
import { ThemeProvider } from "styled-components";
import { defaultTheme } from "@player/theme/defaults";
import {
	render,
	fireEvent
} from "@testing-library/react";
import { RecoilRoot } from "recoil";
import { PhoneCall } from "@player/phone/PhoneCall";
import {
	isPortraitAtom,
	appSessionIdAtom
} from "@player/app/app.state";
import {
	sliderConfigMock,
	appSessionIdMock
} from "./mocks/data.mock";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider/slider.state";

describe("PhoneCall Component", () => {
	const originalOpen = window.open;
	const mockedWindowOpen = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
		window.open = mockedWindowOpen;
		global.fetch = jest.fn().mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({}),
			status: 200,
			statusText: "OK"
		});
	});

	afterEach(() => {
		jest.restoreAllMocks();
		jest.resetAllMocks();
	});

	afterAll(() => {
		window.open = originalOpen;
	});

	test("Should render PhoneCall component with the phone icon", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<PhoneCall phone="+**********" videoId="v1" />
				</ThemeProvider>
			</RecoilRoot>
		);
		const phoneIcon = container.querySelector("icon-phone");
		expect(phoneIcon).toBeInTheDocument();
	});

	test("Should handle phone click and open tel link", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<PhoneCall phone="+**********" videoId="v1" />
				</ThemeProvider>
			</RecoilRoot>
		);

		const phoneIcon = container.querySelector("icon-phone");
		if (!phoneIcon) {
			throw new Error("Phone icon not found");
		}
		fireEvent.click(phoneIcon);
		expect(mockedWindowOpen).toHaveBeenCalledWith("tel:+**********");
	});

	test("Should format phone number correctly and open tel link", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<PhoneCall phone="(*************" videoId="v1" />
				</ThemeProvider>
			</RecoilRoot>
		);

		const phoneIcon = container.querySelector("icon-phone");
		if (!phoneIcon) {
			throw new Error("Phone icon not found");
		}
		fireEvent.click(phoneIcon);
		expect(mockedWindowOpen).toHaveBeenCalledWith("tel:**********");
	});
});

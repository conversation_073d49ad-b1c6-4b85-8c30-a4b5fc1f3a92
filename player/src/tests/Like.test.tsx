import React from "react";
import { ThemeProvider } from "styled-components";
import "@testing-library/jest-dom/extend-expect";
import { defaultTheme } from "@player/theme/defaults";
import {
	render,
	cleanup,
	fireEvent
} from "@testing-library/react";
import { RecoilRoot } from "recoil";
import { LikeVideo } from "@player/like/LikeVideo";
import {
	appSessionStorageAtom,
	isPortraitAtom,
	appSessionIdAtom
} from "@player/app/app.state";
import {
	sliderConfigMock,
	appSessionIdMock
} from "./mocks/data.mock";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider/slider.state";

const mockedPostMessage = jest.fn();
Object.defineProperty(window, "postMessage", {
	value: mockedPostMessage
});

beforeEach(async () => {
	jest.clearAllMocks();
	global.fetch = jest.fn().mockResolvedValue({
		ok: true,
		json: () => Promise.resolve({}),
		status: 200,
		statusText: "OK"
	});
});

afterEach(() => {
	jest.restoreAllMocks();
	jest.resetAllMocks();
});

describe("Like Component.", () => {
	afterEach(cleanup);

	test("Should Render Like component in its default unlike state.", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap):void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionStorageAtom, { likedVideos: [] });
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<LikeVideo videoId={"v1"} />
				</ThemeProvider>
			</RecoilRoot>
		);

		const likeIcon = container.querySelector("icon-heart-outline");
		expect(likeIcon).toBeInTheDocument();

	});

	test("Should Render Like component in its default like state.", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap):void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionStorageAtom, { likedVideos: ["v1"] });
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<LikeVideo videoId={"v1"} />
				</ThemeProvider>
			</RecoilRoot>
		);

		const likeIcon = container.querySelector("icon-heart-fill-red");
		expect(likeIcon).toBeInTheDocument();
	});

	test("Should handle like action and result in heart-fill icon.", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap):void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionStorageAtom, { likedVideos: [] });
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<LikeVideo videoId={"v1"} />
				</ThemeProvider>
			</RecoilRoot>
		);

		const likeIcon = container.querySelector("icon-heart-outline");
		if (!likeIcon) {
			throw new Error("Like icon not found");
		}
		fireEvent.click(likeIcon);

		const likeRedIcon = container.querySelector("icon-heart-fill-red");
		expect(likeRedIcon).toBeInTheDocument();

		expect(fetch).toHaveBeenCalled();
		expect(mockedPostMessage).toHaveBeenCalled();
	});

});

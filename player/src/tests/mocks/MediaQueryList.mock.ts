class MediaQueryListMock extends EventTarget implements MediaQueryList {
	matches: boolean;
	media: string;
	onchange: ((this: MediaQueryList, ev: Event) => unknown) | null;

	constructor(query: string) {
		super();
		this.matches = query === "(orientation: portrait)";
		this.media = query;
		this.onchange = null;
	}

	addListener(listener: EventListener): void {
		this.addEventListener("change", listener);
	}

	removeListener(listener: EventListener): void {
		this.removeEventListener("change", listener);
	}

	// For newer MediaQueryList APIs
	addEventListener(type: string, listener: EventListener): void {
		super.addEventListener(type, listener);
	}

	removeEventListener(type: string, listener: EventListener): void {
		super.removeEventListener(type, listener);
	}

	dispatchEvent(event: Event): boolean {
		return super.dispatchEvent(event);
	}
}

export default MediaQueryListMock;

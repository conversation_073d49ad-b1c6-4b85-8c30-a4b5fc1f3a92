import React from "react";
import "@testing-library/jest-dom/extend-expect";
import {
	cleanup,
	renderHook,
	waitFor
} from "@testing-library/react";
import { isPortraitAtom } from "@player/app";
import { RecoilRoot } from "recoil";
import { defaultTheme } from "@player/theme";
import { useSlider } from "@player/slider/useSlider.hook";
import {
	videoDataMock,
	collectionDataCacheMock,
	gpSessionIdMock,
	cdnUrlMock,
	accountManifestMock
} from "./mocks/data.mock";

const accountId = videoDataMock.interactiveVideos[0].accountId;
const collectionId = videoDataMock.interactiveVideos[0].collectionId;
const collectionUrl = `${cdnUrlMock}data-cache/collections/${collectionId}-collection.json`;
const accountUrl = `${cdnUrlMock}manifests/${accountId}/account-manifest.json`;

beforeEach(async () => {
	jest.clearAllMocks();
	global.URLSearchParams = jest.fn(() => ({
		get: jest.fn((param) => {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			const params: any = {
				accountId: accountId,
				collectionId: collectionId,
				videoId: videoDataMock.interactiveVideos[0]._id,
				gpSession: gpSessionIdMock
			};
			return params[param];
		})
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	})) as any;
	jest.spyOn(console, "error").mockImplementation(jest.fn());
});

afterEach(() => {
	jest.resetAllMocks();
	jest.restoreAllMocks();
});

describe("Slider Hook - useSlider.", () => {
	afterEach(cleanup);

	test("Should fetch slider data and update state accordingly", async () => {
		global.fetch = jest.fn(url =>
			Promise.resolve({
				ok: true,
				json: () => {
					if (url === collectionUrl) {
						return Promise.resolve(collectionDataCacheMock);
					} else if (url === accountUrl) {
						return Promise.resolve(accountManifestMock);
					}
				},
				status: 200,
				statusText: "OK"
			})
		) as jest.Mock;


		const { result } = renderHook(() => useSlider(), {
			wrapper: ({ children }) => (
				<RecoilRoot initializeState={(snap):void => snap.set(isPortraitAtom, true)}>
					{children}
				</RecoilRoot>
			)
		});

		expect(result.current.loading).toBe(true);
		expect(result.current.theme).toEqual(defaultTheme);
		expect(result.current.videoData).toBeNull();
		expect(result.current.starterVideoIndex).toBeNull();

		await waitFor(() => expect(result.current.loading).toBe(false));
		await waitFor(() => expect(result.current.videoData).toEqual(videoDataMock.interactiveVideos));
		await waitFor(() => expect(result.current.starterVideoIndex).toEqual(0));
	});


	test("Should handle fetch failure gracefully", async () => {
		global.fetch = jest.fn(url =>
			Promise.resolve({
				ok: false,
				json: () => {
					if (url === collectionUrl) {
						return Promise.resolve(collectionDataCacheMock);
					} else if (url === accountUrl) {
						return Promise.resolve(accountManifestMock);
					}
				},
				status: 500,
				statusText: "NOT OK"
			})
		) as jest.Mock;

		const { result } = renderHook(() => useSlider(), {
			wrapper: ({ children }) => (
				<RecoilRoot initializeState={(snap):void => snap.set(isPortraitAtom, true)}>
					{children}
				</RecoilRoot>
			)
		});

		expect(result.current.loading).toBe(true);
		expect(result.current.theme).toEqual(defaultTheme);
		expect(result.current.videoData).toBeNull();
		expect(result.current.starterVideoIndex).toBeNull();

		await waitFor(() => expect(result.current.loading).toBe(false));
		expect(result.current.videoData).toBeNull();
		expect(result.current.starterVideoIndex).toBeNull();
		expect(console.error).toHaveBeenCalledWith(
			expect.objectContaining({
				message: expect.stringContaining("Failed to fetch collection manifest file.")
			})
		);
	});
});

import React from "react";
import { ThemeProvider } from "styled-components";
import "@testing-library/jest-dom/extend-expect";
import {
	render,
	cleanup,
	screen
} from "@testing-library/react";
import { act } from "react-dom/test-utils";
import {
	isPortraitAtom,
	appSessionIdAtom,
	videoInfoPayloadAtomFamily
} from "@player/app";
import { RecoilRoot } from "recoil";
import {
	sliderConfig<PERSON>tom,
	sliderVideoPlayerAtom
} from "@player/slider";
import { VideoFrame } from "@player/interactive/VideoFrame";
import { defaultTheme } from "@player/theme";
import {
	videoDataMock,
	sliderConfigMock,
	appSessionIdMock,
	gpSessionIdMock
} from "./mocks/data.mock";
import * as gpLogMock from "@player/app/app.log.util";
jest.mock("@player/app/app.log.util");

const accountId = videoDataMock.interactiveVideos[0].accountId;
const collectionId = videoDataMock.interactiveVideos[0].collectionId;

const mockSetShowVideoOverlay = jest.fn();
const mockHandleCTAChevronClick = jest.fn();
const mockSetSwipeEnabled = jest.fn();

beforeEach(async () => {
	jest.clearAllMocks();
	global.URLSearchParams = jest.fn(() => ({
		get: jest.fn((param) => {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			const params: any = {
				accountId: accountId,
				collectionId: collectionId,
				videoId: videoDataMock.interactiveVideos[0]._id,
				gpSession: gpSessionIdMock
			};
			return params[param];
		})
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	})) as any;

	global.fetch = jest.fn(() =>
		Promise.resolve({
			ok: true,
			status: 200,
			statusText: "OK"
		})
	) as jest.Mock;

	(gpLogMock.appLog as jest.Mock).mockImplementation(jest.fn());
});

afterEach(() => {
	jest.resetAllMocks();
	jest.restoreAllMocks();
});

describe("Slider Component.", () => {
	afterEach(cleanup);

	test("Should Render Video Frame Component and send START and COUNT events.", async () => {
		await act(async () => {
			render(
				<RecoilRoot initializeState={(snap): void => {
					snap.set(isPortraitAtom, false);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}>
					<ThemeProvider theme={defaultTheme}>

						<VideoFrame
							isViewable={true}
							videoData={videoDataMock.interactiveVideos[0]}
							snippetType={undefined}
							setShowVideoOverlay={mockSetShowVideoOverlay}
							showVideoOverlay={false}
							handleCTAChevronClick={mockHandleCTAChevronClick}
							setSwipeEnabled={mockSetSwipeEnabled}
							videoInfoPayloadAtom={videoInfoPayloadAtomFamily(0)}
						/>

					</ThemeProvider>
				</RecoilRoot>
			);
		});

		expect(true).toBeTruthy();
		const videoFrameElement = screen.getByTestId("shoppable-video-con");
		expect(videoFrameElement).toBeInTheDocument();
	});

});

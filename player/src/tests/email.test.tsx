import React from "react";
import "@testing-library/jest-dom/extend-expect";
import { ThemeProvider } from "styled-components";
import { defaultTheme } from "@player/theme/defaults";
import {
	render,
	fireEvent,
	waitFor
} from "@testing-library/react";
import { RecoilRoot } from "recoil";
import { SendEmail } from "@player/email/SendEmail";
import {
	isPortraitAtom,
	appSessionIdAtom
} from "@player/app/app.state";
import {
	sliderConfigMock,
	appSessionIdMock
} from "./mocks/data.mock";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider/slider.state";

describe("SendEmail Component", () => {

	beforeEach(async () => {
		jest.clearAllMocks();
		global.fetch = jest.fn().mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({}),
			status: 200,
			statusText: "OK"
		});
	});

	afterEach(() => {
		jest.restoreAllMocks();
		jest.resetAllMocks();
	});

	test("Should render SendEmail component with email icon", async () => {
		const { container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<SendEmail email="<EMAIL>" fullWidth={true} videoId="v1" videoTitle="v1-title"
						groupEmail="<EMAIL>" />
				</ThemeProvider>
			</RecoilRoot>
		);
		const emailIcon = container.querySelector("icon-email");
		expect(emailIcon).toBeInTheDocument();
	});

	test("Should open email form when email icon is clicked", async () => {
		const { getByTestId, queryByText, container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<SendEmail email="<EMAIL>" fullWidth={true} videoId="v1" videoTitle="v1-title"
						groupEmail="<EMAIL>" />
				</ThemeProvider>
			</RecoilRoot>
		);
		const emailIcon = container.querySelector("icon-email");
		if (!emailIcon) {
			throw new Error("Email icon not found");
		}
		fireEvent.click(emailIcon);
		const formWrapper = getByTestId("animated-form-wrapper");
		expect(formWrapper).toHaveStyle("opacity: 1");
		expect(queryByText("Send a Message")).toBeInTheDocument();
	});

	test("Should send email and plays the 'sent' lottie animation when form is submitted ", async () => {
		const { getByPlaceholderText, getByText, container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<SendEmail email="<EMAIL>" fullWidth={true} videoId="v1" videoTitle="v1-title"
						groupEmail="<EMAIL>" />
				</ThemeProvider>
			</RecoilRoot>
		);

		const emailIcon = container.querySelector("icon-email");
		if (!emailIcon) {
			throw new Error("Email icon not found");
		}
		fireEvent.click(emailIcon);

		const emailInput = getByPlaceholderText("Your email address");
		const messageInput = getByPlaceholderText("Message");

		fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
		fireEvent.change(messageInput, { target: { value: "Test message for the email form." } });

		const sendButton = getByText("Send Email");
		fireEvent.click(sendButton);
		await waitFor(() => {
			expect(fetch).toHaveBeenCalledTimes(3);
			expect(fetch).toHaveBeenCalledWith(expect.stringContaining("/api/email"), expect.any(Object));
			expect(getByText("Mock Lottie Animation")).toBeInTheDocument();
		});
	});

	test("Should close the email form when close button is clicked", async () => {
		const { getByTestId, container } = render(
			<RecoilRoot
				initializeState={(snap): void => {
					snap.set(isPortraitAtom, true);
					snap.set(appSessionIdAtom, appSessionIdMock);
					snap.set(sliderConfigAtom, sliderConfigMock);
					snap.set(sliderVideoPlayerAtom, null);
				}}
			>
				<ThemeProvider theme={defaultTheme}>
					<SendEmail email="<EMAIL>" fullWidth={true} videoId="v1" videoTitle="v1-title"
						groupEmail="<EMAIL>" />
				</ThemeProvider>
			</RecoilRoot>
		);

		const emailIcon = container.querySelector("icon-email");
		if (!emailIcon) {
			throw new Error("Email icon not found");
		}
		fireEvent.click(emailIcon);

		const closeIcon = container.querySelector("icon-times");
		if (!closeIcon) {
			throw new Error("close icon not found");
		}
		fireEvent.click(closeIcon);

		const formWrapper = getByTestId("animated-form-wrapper");
		expect(formWrapper).toHaveStyle("opacity: 0");
	});
});

import React from "react";
import { Flex } from "@player/components/Flex";
import { IconButton } from "@player/components/button/IconButton";
import {
	isAppInPreviewModeAtom,
	isPortraitAtom,
	appSessionIdAtom
} from "@player/app/app.state";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider/slider.state";
import { useRecoilValue } from "recoil";
import { default as PhoneIcon } from "@player/assets/icon-phone.svg";
import { sendAppEvent } from "@player/event/event.service";
import { EventNameEnum } from "@player/event/event.enum";
interface Props {
	phone?: string;
	videoId: string;
}

export const PhoneCall: React.FC<Props> = ({ phone, videoId }) => {
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const isPortrait = useRecoilValue(isPortraitAtom);
	if (!phone) {
		return null;
	}

	const handlePhoneClick = (): void => {
		const formattedPhone = phone.replace(/[^+\d]/g, "");
		window.open(`tel:${formattedPhone}`);
		sendAppEvent({
			collectionId: sliderConfig.collectionId,
			accountId: sliderConfig.accountId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.FEATURED_PHONE_PRESS,
			videoId: videoId,
			sliderVideoPlayer: sliderVideoPlayer
		}, isAppInPreviewMode);
	};

	return (
		<Flex alignSelf={"flex-end"}>
			<IconButton svgIcon = {PhoneIcon}
				isPortrait={isPortrait}
				onClick={(e): void => {
					e.stopPropagation();
					handlePhoneClick();
				}}/>
		</Flex>
	);
};

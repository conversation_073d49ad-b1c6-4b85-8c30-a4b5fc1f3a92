import React, {
	useState,
	useEffect
} from "react";
import styled from "styled-components/macro";
import { useRecoilValue } from "recoil";
import { Heading } from "@player/components/Heading";
import { Flex } from "@player/components/Flex";
import {
	Box,
	BlurBox
} from "@player/components/Box";
import { ImageContainer } from "@player/components/ImageContainer";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";
import {
	openExternalSite,
	getURI,
	IProductData,
	IVideoData,
	appSessionIdAtom,
	isAppInPreviewModeAtom
} from "@player/app";

const ProductSliderCon = styled(Flex)`
  margin-right: max(-1em, -1rem);
  overflow-x: auto;
  gap: min(0.5em, 0.5rem);
  &::-webkit-scrollbar {
    display: none;
  }
  & > :last-child {
    margin-right: min(1em, 1rem);
  }
`;

const ProductCon = styled(Flex)`
  position: relative;
  cursor: pointer;
  min-width: min(4em, 4rem);
  height: min(4.38em, 4.38rem);
  flex-shrink: 0;
  padding: min(0.44em,0.44rem) min(0.63em, 0.63rem) min(0.44em,0.44rem) min(0.44em,0.44rem);
  border-radius: min(0.75em, 0.75rem);
  max-width: 90%;
`;

const ProductInfoBlock = styled(Flex)`
  padding-left: min(0.31em, 0.31rem);
  justify-content: center;
  align-items: flex-start;
  background: transparent;
  gap: min(0.19em, 0.19rem);
  flex-direction: column;
`;

const ProductTitle = styled(Box)`
  margin-right: auto;
  font-weight: 500;
  font-family: ${(props):string => props.theme.fonts.family};
  font-size: min(0.88em, 0.88rem);
  color: ${(props):string => props.theme.textColor};
`;

const ProductSubTitle = styled(Box)`
  font-family: ${(props):string => props.theme.fonts.family};
  font-weight: 300;
  font-size: min(0.75em, 0.75rem);
  color: ${(props):string => props.theme.textColor};
`;

interface Props {
	videoData: IVideoData;
	onProductLinkOpen?: (newWindow: Window | null) => void;
}

export const ProductSlider: React.FC<Props> = ({ videoData, onProductLinkOpen }) => {
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const [showProductSlider, setShowProductSlider] = useState(true);

	useEffect(() => {
		const handleResize = (): void => {
			const iframeHeight = window.innerHeight;
			setShowProductSlider(iframeHeight > 260);
		};
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	const truncateString = (str: string, maxLength: number): string => {
		if (str.length > maxLength) {
			return str.slice(0, maxLength) + "...";
		}
		return str;
	};

	const handleProductClick =
	(product: IProductData) => (event: React.MouseEvent<HTMLDivElement>) => {
		event.stopPropagation();
		sendAppEvent({
			collectionId: sliderConfig.collectionId,
			accountId: sliderConfig.accountId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.FEATURED_LINK_PRESS,
			videoId: videoData._id,
			sliderVideoPlayer: sliderVideoPlayer,
			productActionedURL: product.url,
			productId: product._id
		}, isAppInPreviewMode);

		const newWindow = openExternalSite(product.url);
		if (onProductLinkOpen) {
			onProductLinkOpen(newWindow);
		}
	};

	if (!showProductSlider) {
		return null;
	}

	return (
		<Flex flexDirection={"column"} style={{ gap: "min(0.65em, 0.65rem)" }}>
			{videoData.showTitle && (
				<Heading
					text={videoData.title}
					tag="h3"
				/>
			)}

			<ProductSliderCon
				className="allowScroll"
				mb={
					sliderConfig.hideVanityBranding
						? "min(1em, 1rem)"
						: "0em"
				}
			>
				{videoData.products?.map((p, index) => (
					<ProductCon
						key={"product-" + index}
						onClick={handleProductClick(p)}
					>
						<BlurBox radius="min(0.75em, 0.75rem)" />

						{p.productThumbnail && (
							<ImageContainer
								src={getURI(p.productThumbnail)}
								style={{
									zIndex: 1,
									borderRadius: "min(0.31em, 0.31rem)",
									backgroundColor: "white",
									maxWidth: "min(3.5em, 3.5rem)"
								}}
							/>
						)}

						<ProductInfoBlock zIndex={1}>
							<ProductTitle>
								{truncateString(p.title, 40)}
							</ProductTitle>
							<ProductSubTitle>
								{p.subTitle && truncateString(p.subTitle, 20)}
							</ProductSubTitle>
						</ProductInfoBlock>
					</ProductCon>
				))}
			</ProductSliderCon>
		</Flex>
	);
};

import {
	CoreMessageTypeEnum,
	CoreMessageSecret
} from "@player/core/core.message";
import {
	ISliderVideoPlayer,
	ISliderConfig
} from "@player/slider/slider.interface";
import { EventNameEnum } from "@player/event/event.enum";
import { sendAppEvent } from "@player/event/event.service";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";

export const AppMessageSecret = "!gpapp:";

export enum AppMessageTypeEnum {
	RESPONSE_SESSION_STORAGE = "response_session_storage",
	RENDER_VIDEO_PREVIEW = "render_video_preview"
}

export interface ICloseAppData {
	appSessionId: string;
	videoPlayer: ISliderVideoPlayer | null;
	sliderConfig: ISliderConfig | null;
}

export const notifyCoreToCloseApp =
async (appData: ICloseAppData):Promise<void> => {
	try {
		const message = {
			type: CoreMessageTypeEnum.CLOSE_APPLICATION,
			referrer: window.location.href,
			data: appData
		};
		const postEvent = CoreMessageSecret + JSON.stringify(message);
		window?.parent?.postMessage(postEvent, "*");
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n notifyCoreToCloseApp",
			scope: LogScope.ERROR
		});
	}
};



export const sendCloseAppEvent =
(videoPlayer:ISliderVideoPlayer, sliderConfig: ISliderConfig, appSessionId: string):void => {
	const sharedVideoProps = {
		accountId: sliderConfig.accountId,
		collectionId: sliderConfig.collectionId
	};
	sendAppEvent({
		appSessionId: appSessionId,
		eventName: EventNameEnum.VIDEO_PLAY_POSITION,
		sliderVideoPlayer: videoPlayer,
		endedMethod: "exit",
		videoPlayStatus: "stopped",
		isShareMode: sliderConfig.isShareMode,
		...sharedVideoProps
	});
	sendAppEvent({
		appSessionId: appSessionId,
		eventName: EventNameEnum.SHOPPABLE_VIDEO_EXIT,
		sliderVideoPlayer: videoPlayer,
		...sharedVideoProps
	});
};

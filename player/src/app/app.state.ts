import {
	atom,
	atomFamily
} from "recoil";
import ObjectID from "bson-objectid";
import {
	IAppSessionStorage,
	IAppPreviewData,
	VideoInfoPayload,
	defaultVideoInfo
} from "./app.interface";


export const isPortraitAtom = atom<boolean | null>({
	key: "isPortrait",
	default: null
});

export const videoInfoPayloadAtomFamily = atomFamily<VideoInfoPayload, number>({
	key: "videoInfoPayload",
	default: defaultVideoInfo
});

export const showCCAtom = atom<boolean>({
	key: "showCC",
	default: true
});

export const isControlsVisibleAtom = atom<boolean>({
	key: "isControlsVisible",
	default: true
});

export const isPlayerControlsVisibleAtom = atom<boolean>({
	key: "isPlayerControlsVisible",
	default: true
});

export const isUserInteractingAtom = atom<boolean>({
	key: "isUserInteracting",
	default: false
});

export const appSessionIdAtom = atom<string>({
	key: "appSessionId",
	default: ObjectID().toHexString()
});

export const appSessionStorageAtom = atom<IAppSessionStorage | null>({
	key: "appSessionStorage",
	default: null
});

export const isAppInPreviewModeAtom = atom<boolean>({
	key: "isAppInPreviewMode",
	default: false
});

export const appPreviewDataAtom = atom<IAppPreviewData | null>({
	key: "appPreviewData",
	default: null
});

import {
	appLog,
	LogScope
} from "./app.log.util";

interface IAppParams {
	collectionId: string | null;
	videoId?: string | null;
	gpSession?: string | null;
	gpFingerprint?: string | null;
	snippetType?: string | null;
}

export const validateEnvVariables = (): void => {
	if (!process.env.GP_SERVER_API_ENDPOINT) {
		throw new Error("Failed to read server endpoint.");
	}
	if (!process.env.GP_CDN_ENDPOINT) {
		throw new Error("Failed to read cdn endpoint.");
	}
	if (!process.env.GP_PLAYER_ENDPOINT) {
		throw new Error("Failed to read player endpoint.");
	}
};

export const readAppParams = (sharedCollectionId?:string, sharedVideoId?:string): IAppParams | null => {
	if (sharedCollectionId) {
		return { collectionId: sharedCollectionId };
	}

	if (sharedVideoId) {
		return { videoId: sharedVideoId, collectionId: null };
	}

	const params = new URLSearchParams(window.location.search);
	const collectionId = params.get("collectionId");
	const videoId = params.get("videoId");
	const gpSession = params.get("gpSession");
	const gpFingerprint = params.get("gpFingerprint");
	const snippetType = params.get("snippetType");

	if (collectionId || videoId) {
		return {
			collectionId,
			videoId,
			gpSession,
			gpFingerprint,
			snippetType
		};
	}

	return null;
};

export const isScreenPortrait = ():boolean => {
	const portraitMediaQuery = "(orientation: portrait)";
	return window.matchMedia(portraitMediaQuery).matches;
};

export const reloadApp = ():void => window.location.reload();

export const validateBackslash = (
	url: string | undefined
): string | null => {
	if (!url) {
		return null;
	}
	if (url[url.length - 1] === "/") {
		return url;
	}
	return url + "/";

};

export const openExternalSite = (url: string): Window | null => {
	try {
		if (!url) {
			throw new Error("URL is undefined.");
		}
		const newTab = window.open(url, "_blank");
		return newTab;
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n app.util | openExternalSite",
			scope: LogScope.ERROR
		});
		return null;
	}
};

export const isIOSDevice = (): boolean => {
	const userAgent = window.navigator.userAgent;
	const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !("MSStream" in window);
	return isIOS;
};

export const getVideosToRender = (
	videoDataLen: number,
	currentVideoIndex: number,
	isDesktop = false
): number[] => {
	if (videoDataLen === 1) {
		if (isDesktop) {
			return [0];
		}
		return [0, 0, 0];

	} else if (videoDataLen === 2) {
		return [
			(currentVideoIndex + 1) % 2,
			currentVideoIndex,
			(currentVideoIndex + 1) % 2
		];
	}
	return [
		(currentVideoIndex - 1 + videoDataLen) % videoDataLen,
		currentVideoIndex,
		(currentVideoIndex + 1) % videoDataLen
	];

};

export const getCurrentVideoIndex = (videoDataLen: number, currentVideoIndex: number): number => {
	return currentVideoIndex === videoDataLen - 1
		? 0
		: currentVideoIndex + 1;
};

export const getPrevVideoIndex = (videoDataLen: number, currentVideoIndex: number): number => {
	return currentVideoIndex === 0 ? videoDataLen - 1 : currentVideoIndex - 1;
};

export const adjustPercentString = (inputString: string, increment: boolean):string => {
	let percentValue = Number(inputString.slice(0, -1));
	percentValue += increment ? 100 : -100;
	const outputString = percentValue + "%";
	return outputString;
};

export const getURI = (URL: string): string => {
	let src;
	try {
		const PLAYER_ENDPOINT = validateBackslash(process.env.GP_PLAYER_ENDPOINT);
		const fallbackUrl = `${PLAYER_ENDPOINT}assets/link-icon.jpg`;

		src = URL ? URL : fallbackUrl;
		return src;
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n app.util | getURI",
			scope: LogScope.ERROR
		});
		src = URL;
		return src;
	}
};


export const isDebugMode = (): boolean => {
	try {
		const checkDebugParam = (url: URL): boolean => {
			const params = new URLSearchParams(url.search);
			return params.get("gp-debug") === "true";
		};

		const currentUrl = new URL(window.location.href);
		return checkDebugParam(currentUrl);
	} catch (error: unknown) {
		return false;
	}
};

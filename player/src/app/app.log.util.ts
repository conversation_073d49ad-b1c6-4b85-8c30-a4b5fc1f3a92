import { isDebugMode } from "@player/app/app.util";
export enum LogScope {
	INFO = "info",
	ERROR = "error"
}

interface ILog {
	message: string;
	data?: unknown;
	trace?: string;
	scope: LogScope;
}

interface ILogEntry {
	message: string;
	data: unknown;
	trace?: string;
	scope: LogScope;
	timestamp: string;
}

export const appLog = (input: ILog): void => {
	if (input.scope === LogScope.INFO && !isDebugMode()) {
		return;
	}

	const timestamp = new Date().toISOString();
	const logEntry: ILogEntry = {
		message: input.message,
		data: input.data || "No data is available.",
		scope: input.scope,
		timestamp,
		trace: input.trace
	};

	const log: Record<LogScope, () => void> = {
		[LogScope.INFO]: () => {
			console.info(logEntry);
		},
		[LogScope.ERROR]: () => {
			console.error(logEntry);
		}
	};

	if (log[input.scope]) {
		log[input.scope]();
	} else {
		console.error("Undefined log scope:", input.scope);
	}

};


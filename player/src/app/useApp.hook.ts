import {
	useEffect,
	useCallback,
	useRef
} from "react";
import {
	useRecoilState,
	useSetRecoilState,
	useRecoilValue
} from "recoil";
import {
	isPortraitAtom,
	isControlsVisibleAtom,
	isPlayerControlsVisibleAtom,
	isUserInteractingAtom
} from "@player/app/app.state";

export const useApp = (): void => {

	const [isPortrait, setIsPortrait] = useRecoilState(isPortraitAtom);
	const setIsControlsVisible = useSetRecoilState(isControlsVisibleAtom);
	const setIsPlayerControlsVisible = useSetRecoilState(isPlayerControlsVisibleAtom);

	const controlsTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
	const playerControlsTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

	const isUserInteracting = useRecoilValue(isUserInteractingAtom);
	const isUserInteractingRef = useRef<boolean>(isUserInteracting);

	const initialTouchX = useRef<number | null>(null);
	const initialTouchY = useRef<number | null>(null);


	useEffect(() => {
		isUserInteractingRef.current = isUserInteracting;
	}, [isUserInteracting]);

	const handleUserActivity = useCallback(() => {
		setIsControlsVisible(true);
		setIsPlayerControlsVisible(true);

		if (controlsTimerRef.current) {
			clearTimeout(controlsTimerRef.current);
		}
		if (playerControlsTimerRef.current) {
			clearTimeout(playerControlsTimerRef.current);
		}

		controlsTimerRef.current = setTimeout(() => {
			if (!isUserInteractingRef.current){
				setIsControlsVisible(false);
			}
		}, 20000);

		playerControlsTimerRef.current = setTimeout(() => {
			if (!isUserInteractingRef.current){
				setIsPlayerControlsVisible(false);
			}
		}, 2000);

	}, [setIsControlsVisible, setIsPlayerControlsVisible]);

	const isScreenPortrait = (): boolean => {
		const portraitMediaQuery = "(orientation: portrait)";
		return window.matchMedia(portraitMediaQuery).matches;
	};

	const onTouchStart = useCallback((event: TouchEvent) => {
		if (event.touches.length === 1) {
			initialTouchX.current = event.touches[0].clientX;
			initialTouchY.current = event.touches[0].clientY;
		}
		handleUserActivity();
	}, [handleUserActivity]);

	const preventVerticalSwipeOutsideAllowedArea = useCallback((event: TouchEvent) => {
		let targetElement: HTMLElement | null = event.target as HTMLElement;

		while (targetElement != null) {
			if (targetElement.classList && targetElement.classList.contains("allowScroll")) {
				if (!initialTouchX.current || !initialTouchY.current) return;

				const deltaX = event.touches[0].clientX - initialTouchX.current;
				const deltaY = event.touches[0].clientY - initialTouchY.current;
				// even inside allowScroll area, still prevent vertical scrolls.
				if (Math.abs(deltaY) > Math.abs(deltaX)) {
					event.preventDefault();
				}

				return;
			}
			targetElement = targetElement.parentNode as HTMLElement | null;
		}
	}, []);

	const onTouchMove = useCallback((event: TouchEvent) => {
		preventVerticalSwipeOutsideAllowedArea(event);
		handleUserActivity();
	}, [handleUserActivity, preventVerticalSwipeOutsideAllowedArea]);

	const onPointerMove = useCallback(() => {
		handleUserActivity();
	}, [handleUserActivity]);


	useEffect(() => {
		window.addEventListener("pointermove", onPointerMove);
		window.addEventListener("mousedown", onPointerMove);
		window.addEventListener("touchstart", onTouchStart);
		window.addEventListener("touchmove", onTouchMove, {
			passive: false
		});

		return () => {
			window.removeEventListener("pointermove", onPointerMove);
			window.removeEventListener("mousedown", onPointerMove);
			window.removeEventListener("touchstart", onTouchStart);
			window.removeEventListener("touchmove", onTouchMove);
		};
	}, [onTouchStart, onTouchMove, onPointerMove]);

	useEffect(() => {
		if (typeof isPortrait === "undefined") {
			setIsPortrait(isScreenPortrait());
		}
		const handleOrientationChange = (e: MediaQueryListEvent): void => {
			setIsPortrait(e.matches);
		};

		const portraitMediaQueryList = window.matchMedia("(orientation: portrait)");
		portraitMediaQueryList.addEventListener("change", handleOrientationChange);

		return () => {
			portraitMediaQueryList.removeEventListener(
				"change",
				handleOrientationChange
			);
		};
	});

	useEffect(() => {
		if (!isPortrait) {
			setIsPortrait(isScreenPortrait());
		}

		const handleOrientationChange = (e: MediaQueryListEvent): void => {
			setIsPortrait(e.matches);
		};
		const portraitMediaQueryList = window.matchMedia("(orientation: portrait)");
		portraitMediaQueryList.addEventListener("change", handleOrientationChange);

		return () => {
			portraitMediaQueryList.removeEventListener(
				"change",
				handleOrientationChange
			);
		};
	}, [isPortrait, setIsPortrait]);
};

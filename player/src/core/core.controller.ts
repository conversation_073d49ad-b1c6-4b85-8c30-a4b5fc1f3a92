import {
	SnippetTypeEnum,
	validateBackslash
} from "@player/app";
import { onReceiveMessageAtCore } from "@player/core/core.message";
import { SessionManagerSingleton } from "@player/session";
import { fetchSnippetData } from "./core.service";
import { renderSnippet } from "./renderSnippet";
import {
	fetchAccountManifest,
	defaultAccountManifest
} from "../slider/slider.service";
import {
	IVideoData,
	AccountManifest,
	defaultVideo,
	CollectionDataCache,
	VideoDataCache
} from "@player/app/app.interface";
import { generateCustomTheme } from "@player/theme/defaults";
import { loadGoogleFont } from "@player/core/core.helper";
import { isDebugMode } from "@player/app/app.util";

export const initPostMsgListener = (): void => {
	window.addEventListener("message", onReceiveMessageAtCore, false);
};

export const observeSnippetElements = (): void => {
	const callback = (mutationsList: MutationRecord[]): void => {
		const addedSnippetElements: HTMLElement[] = [];
		const gpTypes = Object.values(SnippetTypeEnum);

		for (const mutation of mutationsList) {
			if (mutation.type === "childList") {
				mutation.addedNodes.forEach((node: Node) => {
					if (node.nodeType === Node.ELEMENT_NODE) {
						const element = node as HTMLElement;
						if (
							element.getAttribute("data-gp-type") &&
								gpTypes.includes(
									element.getAttribute("data-gp-type") as SnippetTypeEnum
								)
						) {
							addedSnippetElements.push(element);
						}
					}
				});
			}
		}

		if (addedSnippetElements.length > 0) {
			initVideoSnippetData(addedSnippetElements);
		}
	};

	const observeSnippetElementsInDocument = (doc: Document): void => {
		const observer = new MutationObserver(callback);
		// eslint-disable-next-line no-undef
		const config: MutationObserverInit = {
			childList: true,
			subtree: true
		};
		observer.observe(doc, config);
	};

	observeSnippetElementsInDocument(document);
};

export function waitForSnippetElements(timeout = 10000, pollInterval = 100): Promise<number> {
	return new Promise((resolve) => {
		const deadline = Date.now() + timeout;

		function check(): void {
			const elements = Array.from(document.querySelectorAll("[data-gp-type]")) as HTMLElement[];

			if (elements.length > 0 || Date.now() > deadline) {
				resolve(elements.length);
			} else {
				setTimeout(check, pollInterval);
			}
		}

		check();
	});
}

export const initVideoSnippetData = async (targetElements?: HTMLElement[]): Promise<void> => {
	try {
		const CDN_ENDPOINT = validateBackslash(process.env.GP_CDN_ENDPOINT);
		const PLAYER_ENDPOINT = validateBackslash(
			process.env.GP_PLAYER_ENDPOINT
		);
		if (!PLAYER_ENDPOINT) {
			throw new Error("PLAYER_ENDPOINT is undefined.");
		}
		if (!CDN_ENDPOINT) {
			throw new Error("CDN_ENDPOINT is undefined.");
		}

		const gpTypeSelectors = Object.values(SnippetTypeEnum)
			.map((type) => `[data-gp-type="${type}"]`)
			.join(", ");

		const shopElements: Array<HTMLElement> = targetElements
			? targetElements
			: Array.from(document.querySelectorAll(gpTypeSelectors));

		const collectionURLSnippetMap: Map<string, HTMLElement[]> = new Map();
		const collectionURLDataMap: Map<string, CollectionDataCache> = new Map();

		const videoURLSnippetMap: Map<string, HTMLElement[]> = new Map();
		const videoURLDataMap: Map<string, VideoDataCache> = new Map();

		shopElements.forEach((element) => {
			const dataGpType = element.getAttribute("data-gp-type") as SnippetTypeEnum;
			if (!dataGpType) return;

			switch (dataGpType) {
				case SnippetTypeEnum.CAROUSEL:
				case SnippetTypeEnum.WIDGET:
				case SnippetTypeEnum.ONSITE: {
					const collectionId = element.getAttribute("data-gp-collection");
					if (!collectionId) {
						console.error(`Element is missing data-gp-collection attribute for type ${dataGpType}`);
						return;
					}
					const collectionUrl = `${CDN_ENDPOINT}data-cache/collections/${collectionId}-collection.json`;
					if (!collectionURLSnippetMap.has(collectionUrl)) {
						collectionURLSnippetMap.set(collectionUrl, []);
					}
					collectionURLSnippetMap.get(collectionUrl)?.push(element);
					break;
				}

				case SnippetTypeEnum.PLAYER: {
					const videoId = element.getAttribute("data-gp-video");
					if (!videoId) {
						console.error("Element is missing data-gp-video attribute for type player");
						return;
					}
					const videoUrl = `${CDN_ENDPOINT}data-cache/videos/${videoId}-video.json`;
					if (!videoURLSnippetMap.has(videoUrl)) {
						videoURLSnippetMap.set(videoUrl, []);
					}
					videoURLSnippetMap.get(videoUrl)?.push(element);
					break;
				}
				default:
					console.error(`Unsupported data-gp-type: ${dataGpType}`);
			}
		});

		await fetchSnippetData<CollectionDataCache>(
			Array.from(collectionURLSnippetMap.keys()),
			(url: string, data: CollectionDataCache) => {
				if (data) {
					collectionURLDataMap.set(url, data);
				}
			}
		);

		await fetchSnippetData<VideoDataCache>(
			Array.from(videoURLSnippetMap.keys()),
			(url: string, data: VideoDataCache) => {
				if (data) {
					videoURLDataMap.set(url, data);
				}
			}
		);

		const accountIds = Array.from(
			new Set([
				...Array.from(collectionURLDataMap.values()).map((data) => data.accountId),
				...Array.from(videoURLDataMap.values()).map((data) => data.accountId)
			])
		);
		const accountManifestMap: Map<string, AccountManifest> = new Map();

		await Promise.all(
			accountIds.map(async (accountId) => {
				if (!accountManifestMap.has(accountId)) {
					const accountManifest = await fetchAccountManifest(accountId);
					accountManifestMap.set(accountId, accountManifest);
				}
			})
		);

		collectionURLSnippetMap.forEach((elements, url) => {
			const data = collectionURLDataMap.get(url);
			if (!data) return;
			if (data.interactiveVideos.length === 0) return;
			const accountManifest = accountManifestMap.get(data.accountId) ?? defaultAccountManifest;
			processSnippetElements(url, data, collectionURLSnippetMap, {
				endpoint: PLAYER_ENDPOINT,
				accountManifest
			});
		});

		videoURLSnippetMap.forEach((elements, url) => {
			const data = videoURLDataMap.get(url);
			if (!data) return;
			const accountManifest = accountManifestMap.get(data.accountId) ?? defaultAccountManifest;
			processSnippetElements(url, data, videoURLSnippetMap, {
				endpoint: PLAYER_ENDPOINT,
				accountManifest
			});
		});
	} catch (error: unknown) {
		console.error(`initVideoSnippetData | Error: ${(error as Error).message}`);
	}
};

export const processSnippetElements = (
	dataURL: string,
	dataCache: CollectionDataCache | VideoDataCache,
	urlSnippetMap: Map<string, HTMLElement[]>,
	playerAppData: {
		endpoint: string;
		accountManifest: AccountManifest;
	}
): void => {
	const elements = urlSnippetMap.get(dataURL);
	if (!elements) {
		return;
	}

	const sessionManager = SessionManagerSingleton.getInstance();
	const session = sessionManager.getSession();
	if (!session) {
		console.error("processSnippetElements | Failed to get session.");
	}

	let videoData: IVideoData[];
	let collectionTheme: CollectionDataCache | null = null;
	const applyVideoRules = (video: Partial<IVideoData>, accountId: string): IVideoData => {
		const videoWithDefault = { ...defaultVideo, ...video };
		if (!playerAppData.accountManifest.allowLandscape) {
			videoWithDefault.videoDisplayMode = defaultVideo.videoDisplayMode;
		}
		if (!playerAppData.accountManifest.allowCTALead) {
			videoWithDefault.phone = undefined;
			videoWithDefault.email = undefined;
		}
		videoWithDefault.accountId = accountId;
		return videoWithDefault;
	};

	if ("interactiveVideos" in dataCache) {
		videoData = dataCache.interactiveVideos.map((video) =>
			applyVideoRules(video, dataCache.accountId)
		);
		collectionTheme = dataCache;
	} else if ("interactiveVideo" in dataCache) {
		videoData = [applyVideoRules(dataCache.interactiveVideo, dataCache.accountId)];
	} else {
		throw new Error("processSnippetElements | Invalid Data Cache");
	}

	elements.forEach((element: HTMLElement) => {
		const collectionId = element.getAttribute("data-gp-collection");
		const videoId = element.getAttribute("data-gp-video");
		const gpSession = session?.id ?? null;
		const gpFingerprint = session?.fingerprint ?? null;
		const queryParams = [
			collectionId && `collectionId=${collectionId}`,
			videoId && `videoId=${videoId}`,
			gpSession && `gpSession=${gpSession}`,
			isDebugMode() && "gp-debug=true",
			gpFingerprint && `gpFingerprint=${gpFingerprint}`
		]
			.filter(Boolean)
			.join("&");

		const appUrl = `${playerAppData.endpoint}?${queryParams}`;
		const dataGpType = <SnippetTypeEnum>(
            element.getAttribute("data-gp-type")
        );

		const theme = generateCustomTheme(playerAppData.accountManifest.allowThemes, collectionTheme);
		loadGoogleFont(theme.fonts.family);

		if (Object.values(SnippetTypeEnum).includes(dataGpType) && dataCache.accountId) {
			renderSnippet({
				accountId: dataCache.accountId,
				collectionId: collectionId || undefined,
				videoId: videoId || undefined,
				element,
				dataGpType,
				appUrl,
				videoData,
				theme
			});
		} else {
			console.error(`unsupported gp snippet data type: ${dataGpType}`);
		}
	});
};

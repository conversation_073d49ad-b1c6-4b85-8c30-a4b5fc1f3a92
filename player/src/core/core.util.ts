import {
	setWithExpiry,
	getWithExpiry
} from "@player/core/core.storage";

import {
	hasSameOrigin,
	isCoreScriptInParent,
	buildIframeApp
} from "./core.helper";
import {
	CoreMessageTypeEnum,
	sendMessageFromCoreToParent
} from "@player/core/core.message";

export interface ICTAEngagementData {
	gpSessionId: string;
	accountId: string;
	collectionId: string;
}

export const toggleWidgetContainers = (show: boolean): void => {
	const elements = document.querySelectorAll(
		"[data-gp-core=\"gp-widget-cta\"]"
	);
	elements.forEach((element) => {
		const htmlElement = element as HTMLElement;
		htmlElement.style.display = show ? "" : "none";
	});
};

export const closePlayerApp = (): void => {
	const iframe = document.getElementById("player-app") as HTMLIFrameElement;
	const parent = iframe.parentNode;
	parent?.removeChild(iframe);
	const style = document.getElementById(
		"player-host-style"
	) as HTMLStyleElement;
	if (style) {
		style.parentNode?.removeChild(style);
	}
	toggleWidgetContainers(true);
};

export const storeCTAEngagementData = (input: ICTAEngagementData): void => {
	const apData = getWithExpiry("apDataObject") as
                    { accountId: string; gpSessionId: string; collections: string[] } | null;
	if (!apData) {
		setWithExpiry("apDataObject", {
			accountId: input.accountId,
			gpSessionId: input.gpSessionId,
			collections: [input.collectionId]
		});
		return;
	}

	apData.accountId = input.accountId;
	apData.gpSessionId = input.gpSessionId;
	if (!apData.collections.includes(input.collectionId)) {
		apData.collections.push(input.collectionId);
	}
	setWithExpiry("apDataObject", apData);
};

export const startPlayerApp = (url: string): void => {
	try {
		if (!url) {
			console.error(`startPlayerApp | url is undefined. url=${url}`);
			return;
		}
		if (window.self !== window.parent && (!hasSameOrigin() || isCoreScriptInParent(url))) {
			sendMessageFromCoreToParent(CoreMessageTypeEnum.OPEN_PARENT_APP, { url });
		} else {
			buildIframeApp(url);
		}
	} catch (error: unknown) {
		console.error(`startPlayerApp | Error: ${(error as Error).message}`);
		// when calling sendPostMessage, in some cases, window.parent.postMessage is not accessible
		// so lets open the app in the current context.
		buildIframeApp(url);
	}


};

export const startCTAEngagement =
(gpSessionId: string, accountId: string | null, collectionId: string | null): void => {
	if (!gpSessionId || !accountId || !collectionId) {
		console.error(`startEngagement | undefined gpSessionId=${gpSessionId} or ` +
		`accountId=${accountId} or collectionId=${collectionId}`);
		return;
	}
	const engagementData: ICTAEngagementData = {
		gpSessionId: gpSessionId,
		accountId: accountId,
		collectionId: collectionId
	};

	try {
		if (window.self !== window.parent) {
			sendMessageFromCoreToParent(CoreMessageTypeEnum.STORE_CTA_ENGAGEMENT_DATA, engagementData);
		} else {
			storeCTAEngagementData(engagementData);
		}
	} catch (error: unknown) {
		console.error(`startEngagement | Error: ${(error as Error).message}`);
		// when calling sendPostMessage, in some cases, window.parent.postMessage is not accessible
		// so lets storeCTAEngagementData in the current context.
		storeCTAEngagementData(engagementData);
	}
};

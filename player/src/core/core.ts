import {
	initPostMsgListener,
	initVideoSnippetData,
	observeSnippetElements,
	waitForSnippetElements
} from "./core.controller";
import { SessionManagerSingleton } from "@player/session";
import {
	appLog,
	LogScope
} from "@player/app/app.log.util";

try {
	appLog({
		message: "Core Script initializing...",
		scope: LogScope.INFO
	});
	const sessionManager = SessionManagerSingleton.getInstance();
	sessionManager.initSession();
	initPostMsgListener();

	appLog({
		message: "Core Script waiting for elements...",
		scope: LogScope.INFO
	});

	waitForSnippetElements().then((elementCount: number) => {
		appLog({
			message: `Core Script discovered ${elementCount} elements.`,
			scope: LogScope.INFO
		});

		appLog({
			message: "Core Script initializing elements.",
			scope: LogScope.INFO
		});
		initVideoSnippetData();

		appLog({
			message: "Core Script now observing for new elements.",
			scope: LogScope.INFO
		});
		observeSnippetElements();

		appLog({
			message: "Core Script initialized!",
			scope: LogScope.INFO
		});
	});
} catch (error) {
	console.error(error);
}
